services:
  streamlit:
    build:
      context: .
      dockerfile: Dockerfile
    networks:
      - network
    environment:
      COMPARISON_DB_DSN: ***************************************/test
      DEVMODE: True
      DEFAULT_DATASET: 392ea03b-ed21-4826-9e5e-d79bb8a86327
    volumes:
      - ${HOME}/.aws:/root/.aws:ro
      - ./:/app
    ports:
      - "8501:8501"
    depends_on:
      postgres:
        condition: service_healthy

  postgres:
    image: postgres:13
    networks:
      - network
    environment:
      POSTGRES_PASSWORD: testing
      POSTGRES_USER: test
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test -h 127.0.0.1"]
      interval: 5s
      timeout: 5s
      retries: 5

  test:
    build:
      context: .
      dockerfile: Dockerfile
    profiles:
      - test
    volumes:
      - ./:/app
    entrypoint: ["./lint.sh"]

networks:
  network:
    driver: bridge