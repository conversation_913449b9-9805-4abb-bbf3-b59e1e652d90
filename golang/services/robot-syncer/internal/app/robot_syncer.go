package app

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/auth0/go-auth0/management"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cloudwatchlogs"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/kelseyhightower/envconfig"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/config/schema"
	httplib "github.com/carbonrobotics/crgo/http"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/build"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/clients/portal"
)

type RobotSyncerService struct {
	// Environment variables
	AWSBucket              string `envconfig:"AWS_BUCKET" default:"carbon-robot-syncer-testing"`
	AWSCredentialsFile     string `envconfig:"AWS_CREDENTIALS_FILE"`
	AWSRegion              string `envconfig:"AWS_REGION" default:"us-west-2"`
	Auth0Domain            string `envconfig:"AUTH0_TENANT_DOMAIN" default:"carbonrobotics-dev.us.auth0.com"`
	Auth0GRPCAudience      string `envconfig:"AUTH0_GRPC_AUDIENCE" default:"https://robot.carbonrobotics.com"`
	Auth0ServerId          string `envconfig:"AUTH0_SERVER_ID"`
	Auth0ServerSecret      string `envconfig:"AUTH0_SERVER_SECRET"`
	CloudWatchLogGroup     string `envconfig:"CONFIG_AUDIT_LOG_GROUP_NAME" default:"robot-config-audit-testing"`
	ConfigFile             string `envconfig:"CONFIG_FILE" default:"config.json"`
	Environment            string `envconfig:"ENVIRONMENT" default:"PRODUCTION"`
	LogLevel               string `envconfig:"LOG_LEVEL" default:"debug"`
	Port                   int    `envconfig:"ROBOT_SYNCER_PORT" default:"8080"`
	PortalPort             int    `envconfig:"PORTAL_PORT" default:"8080"`
	PortalUrl              string `envconfig:"PORTAL_URL" default:"http://portal.testing.svc.cluster.local"`
	GRPCPort               int    `envconfig:"ROBOT_SYNCER_GRPC_PORT" default:"9090"`
	SharedConfigSchemaPath string `envconfig:"SHARED_CONFIG_SCHEMA_PATH" default:"/data/shared_config_schemas"`

	isProduction bool

	auditLogger *audit.Logger
	s3          *awslib.AWSS3Facade
}

func (syncer *RobotSyncerService) Run(serviceContext context.Context) (err error) {
	log.Infof("Version %s", build.Version)
	log.Infof("Commit %s", build.Commit)
	log.Infof("Built On %s", build.BuiltOn)

	if err := envconfig.Process("", syncer); err != nil {
		return err
	}
	syncer.isProduction = syncer.Environment == "PRODUCTION"
	if syncer.isProduction {
		// Note that staging k8s runs in production mode
		log.Infof("Mode %s", "PRODUCTION")
	} else {
		log.Infof("Mode %s", "DEVELOPMENT")
	}
	httplib.ConfigureGin(syncer.isProduction)

	/**
	 * Logging
	 * Trace, error, fatal, and panic levels can be added as needed
	 **/
	logLevel, err := log.ParseLevel(syncer.LogLevel)

	if err != nil {
		log.Errorf("Failed to parse log level %s", syncer.LogLevel)
		log.SetLevel(log.DebugLevel)
		log.Infof("Log Level: %s", log.DebugLevel.String())
	} else {
		log.SetLevel(logLevel)
		log.Infof("Log Level: %s", logLevel.String())
	}

	/**
	 * AWS Sessions
	 **/
	var creds *credentials.Credentials
	if syncer.AWSCredentialsFile != "" {
		creds = credentials.NewSharedCredentials(syncer.AWSCredentialsFile, "")
	}
	awsSession, err := session.NewSession(&aws.Config{
		Region:      aws.String(syncer.AWSRegion),
		Credentials: creds,
	})
	if err != nil {
		return fmt.Errorf("failed to start AWS session %w", err)
	}
	syncer.s3 = awslib.NewAWSS3Facade(s3.New(awsSession))

	syncer.auditLogger = audit.NewAuditLogger(awslib.NewCWLFacade(cloudwatchlogs.New(awsSession)), syncer.CloudWatchLogGroup)
	log.Info("AWS Sessions: Initialized")

	/**
	 * Auth0
	 **/
	auth0Client, err := management.New(syncer.Auth0Domain, management.WithClientCredentials(serviceContext, syncer.Auth0ServerId, syncer.Auth0ServerSecret))
	if err != nil {
		return fmt.Errorf("failed to initialize Auth0 %w", err)
	}
	log.Info("Auth0: Initialized")

	/**
	 * Config Schema Reader
	 **/
	schemaReader, err := schema.NewReader(syncer.SharedConfigSchemaPath, "roots")
	if err != nil {
		return fmt.Errorf("failed to initialize Config Schema Reader %w", err)
	}
	log.Info("Config Schema Reader: Initialized")

	/**
	 * Portal Client
	 **/
	portalClient := portal.NewClient(syncer.PortalUrl, syncer.PortalPort)
	log.Info("Portal Client: Initialized")

	/**
	 * Config Cache from S3
	 **/
	configCache := cache.NewConfigCache(serviceContext, syncer.s3, syncer.AWSBucket, schemaReader)
	log.Info("Config Cache: Initialized")

	/**
	 * Cache of gRPC streams
	 * map[serial string]ConfigNotificationService_SubscribeServer
	 **/
	streamCache := cache.NewStreamCache()
	log.Info("Robot gRPC Stream Cache: Initialized")

	/**
	 * Cache of customer Profiles
	 * map[profileId string]*Profile
	 **/
	profileCache, err := cache.NewProfileCache(serviceContext, syncer.s3, syncer.AWSBucket, portalClient)
	if err != nil {
		return fmt.Errorf("failed to initialize Profile Cache %w", err)
	}
	log.Info("Profile Cache: Initialized")

	/**
	 * Start REST server
	 **/
	restServerAddr := fmt.Sprintf(":%d", syncer.Port)
	server := &http.Server{
		Addr: restServerAddr,
		Handler: GetREST(
			auth0Client,
			syncer.Auth0Domain,
			syncer.Auth0GRPCAudience,
			syncer.auditLogger,
			schemaReader,
			configCache,
			profileCache,
			streamCache,
			syncer.Environment,
		),
	}
	go func() {
		log.Infof("REST server: Listening on (%v)", restServerAddr)
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.WithError(err).Fatalf("Failed to start server")
		}
	}()
	defer func() {
		serviceContext, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		log.Info("Shutting down rest server...")
		if shutdownErr := server.Shutdown(serviceContext); shutdownErr != nil {
			// overwrite return value of outer function Run
			err = fmt.Errorf("server forced to shutdown: %w", shutdownErr)
		}
	}()

	/**
	 * Start GRPC server
	 **/
	gRPCHandler := GetGRPC(
		auth0Client,
		syncer.Auth0Domain,
		syncer.Auth0GRPCAudience,
		syncer.isProduction,
		syncer.auditLogger,
		portalClient,
		configCache,
		profileCache,
		streamCache,
	)
	grpcServerAddr := fmt.Sprintf(":%d", syncer.GRPCPort)

	go func() {
		listener, err := net.Listen("tcp", grpcServerAddr)
		if err != nil {
			log.WithError(err).Fatal("Failed to listen")
		}
		log.Infof("GRPC server: Listening on (%v)", grpcServerAddr)
		if err := gRPCHandler.Serve(listener); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.WithError(err).Fatalf("Failed to start server")
		}
	}()
	defer func() {
		log.Info("Shutting down grpc server...")
		gRPCHandler.GracefulStop()
	}()

	// graceful shutdown
	<-serviceContext.Done()
	log.Info("Shutting down...")
	return nil
}
