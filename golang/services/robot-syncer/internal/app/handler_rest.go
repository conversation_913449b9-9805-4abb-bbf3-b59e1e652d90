package app

import (
	"net/http"

	"github.com/auth0/go-auth0/management"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/penglongli/gin-metrics/ginmetrics"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/config/schema"
	httplib "github.com/carbonrobotics/crgo/http"
	"github.com/carbonrobotics/crgo/metrics"

	"github.com/carbonrobotics/cloud/golang/pkg/auth/middleware"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/api/handlers/rest"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
)

func GetREST(
	auth0Client *management.Management,
	domain string,
	defaultAudience string,
	auditLogger *audit.Logger,
	schemaReader *schema.Reader,
	configCache *cache.ConfigCache,
	profileCache *cache.ProfileCache,
	streamCache *cache.StreamCache,
	environment string,
) *gin.Engine {
	// Compose Gin Engine from default cors config
	router := gin.New()
	router.Use(gin.LoggerWithWriter(gin.DefaultWriter, "/health", "/prometheus/write"))

	// must happen before gin recovery
	router.Use(sentrygin.New(sentrygin.Options{}))
	router.Use(httplib.HttpLogger(log.New()))
	router.Use(gin.Recovery())
	router.Use(metrics.PrometheusMiddlewareGin())

	// auth middleware
	_ = middleware.Auth0JWTDynamicValidatorGin(domain, defaultAudience, []string{defaultAudience})

	// gin metrics middleware
	m := ginmetrics.GetMonitor()
	m.SetMetricPath("/gin-metrics")
	m.Use(router)

	// health and metrics endpoints
	router.GET("/metrics", metrics.HandlerGin())
	router.GET("/healthz", func(c *gin.Context) { c.String(http.StatusOK, "I am alive!") })

	// unauthed internal routes
	internal := router.Group("internal")

	// serve APIs
	internalV1 := internal.Group("v1")
	internalV1.Use(gzip.Gzip(gzip.DefaultCompression))
	rest.RegisterProfilesRoutes(internalV1, profileCache, environment)
	rest.RegisterReady(internalV1)
	rest.RegisterRobotConfigRoutes(internalV1, auditLogger, configCache, streamCache)
	rest.RegisterSchemasRoutes(internalV1, schemaReader)
	rest.RegisterTemplatesRoutes(internalV1, auditLogger, configCache)
	rest.RegisterUnsyncedConfigKeysRoutes(internalV1, configCache)
	return router
}
