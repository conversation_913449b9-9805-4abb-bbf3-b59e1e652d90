package cache

import (
	"testing"

	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
)

func TestReconciledUnsyncedKeys(t *testing.T) {
	serial, err := carbon.ParseSerial("devserver6")
	assert.NoError(t, err)
	serial2, err := carbon.ParseSerial("devserver66")
	assert.NoError(t, err)

	// initialize unsynced keys cache
	unsyncedKeys := newWriteSafeUnsyncedKeys()

	tests := []struct {
		name              string
		serial            *carbon.ValidSerial
		entry             config.UnsyncedKey
		added<PERSON>ey          bool
		removedKey        bool
		expectedRemaining int
		tsOfEntry         uint64
		newValueOfEntry   string
		oldValueOfEntry   string
	}{
		{
			name:   "no existing unsynced keys in cache for serial",
			serial: serial2,
			entry: config.UnsyncedKey{
				Key:      "test_key",
				Ts:       1234567890,
				NewValue: "new_value1",
				OldValue: "old_value1",
			},
			addedKey:          false,
			removedKey:        false,
			expectedRemaining: 1,
			tsOfEntry:         1234567890,
			newValueOfEntry:   "new_value1",
			oldValueOfEntry:   "old_value1",
		},
		{
			name:   "new key added",
			serial: serial,
			entry: config.UnsyncedKey{
				Key:      "new_test_key",
				Ts:       1234567890,
				NewValue: "new_value",
				OldValue: "old_value",
			},
			addedKey:          true,
			removedKey:        false,
			expectedRemaining: 2,
			tsOfEntry:         1234567890,
			newValueOfEntry:   "new_value",
			oldValueOfEntry:   "old_value",
		},
		{
			name:   "older unsynced key (no change)",
			serial: serial,
			entry: config.UnsyncedKey{
				Key:      "test_key",
				Ts:       1234567880,
				NewValue: "new_value1",
				OldValue: "old_value1",
			},
			addedKey:          false,
			removedKey:        false,
			expectedRemaining: 1,
			tsOfEntry:         1234567890,
			newValueOfEntry:   "new_value",
			oldValueOfEntry:   "old_value",
		},
		{
			name:   "newer unsynced key",
			serial: serial,
			entry: config.UnsyncedKey{
				Key:      "test_key",
				Ts:       1234567891,
				NewValue: "new_value1",
				OldValue: "new_value",
			},
			addedKey:          false,
			removedKey:        false,
			expectedRemaining: 1,
			tsOfEntry:         1234567891,
			newValueOfEntry:   "new_value1",
			oldValueOfEntry:   "old_value",
		},
		{
			name:   "value reverted to original",
			serial: serial,
			entry: config.UnsyncedKey{
				Key:      "test_key",
				Ts:       1234567891,
				NewValue: "old_value",
				OldValue: "new_value",
			},
			addedKey:          false,
			removedKey:        true,
			expectedRemaining: 0,
			tsOfEntry:         1234567891,
			newValueOfEntry:   "new_value1",
			oldValueOfEntry:   "old_value",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			entry := config.UnsyncedKey{
				Key:      test.entry.Key,
				Ts:       test.entry.Ts,
				NewValue: test.entry.NewValue,
				OldValue: test.entry.OldValue,
			}
			if test.serial == serial {
				unsyncedKeys.Contents = []config.UnsyncedKey{
					{
						Key:      "test_key",
						Ts:       1234567890,
						NewValue: "new_value",
						OldValue: "old_value",
					},
				}
			} else {
				unsyncedKeys.Contents = []config.UnsyncedKey{}
			}

			reconciledUnsyncedKeys := unsyncedKeys.reconcileUnsyncedKeys(entry)
			log.Infof("Reconciled unsynced keys: %v", reconciledUnsyncedKeys)
			assert.Equal(t, test.expectedRemaining, len(reconciledUnsyncedKeys))
			if test.addedKey {
				assert.Equal(t, test.tsOfEntry, reconciledUnsyncedKeys[1].Ts)
				assert.Equal(t, test.newValueOfEntry, reconciledUnsyncedKeys[1].NewValue)
				assert.Equal(t, test.oldValueOfEntry, reconciledUnsyncedKeys[1].OldValue)
			} else if test.removedKey {
				// no additional checks
			} else {
				assert.Equal(t, test.tsOfEntry, reconciledUnsyncedKeys[0].Ts)
				assert.Equal(t, test.newValueOfEntry, reconciledUnsyncedKeys[0].NewValue)
				assert.Equal(t, test.oldValueOfEntry, reconciledUnsyncedKeys[0].OldValue)
			}
		})
	}
}
