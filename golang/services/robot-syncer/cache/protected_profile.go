package cache

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"path"
	"sync"
	"time"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"

	"github.com/carbonrobotics/cloud/golang/pkg/iterable"
	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

/**
 * Carbon provided profiles are stored in S3 under "CarbonProvided".
 * For example: carbon_provided/profiles/{profile_type}/{profileId}.json
 * Customers cannot make changes to the carbon provided profiles. QuickTune settings
 * will be separated from Almanac and Discriminator profiles so that those profiles
 * can be synced without ever being modified.
 *
 * Intentionally, all exported function pass *rosy.Profile while all internal functions
 * use *writeSafeProfile. This is to ensure that the caller can modify the profile
 * contents without acquiring the lock. To achieve this we must always deepcopy profile
 * contents before returning it to the caller.
 *
 * Carbon Provided profiles marked deleted and not updated for over a year
 * are permanently removed from S3.
 **/

func (c *ProfileCache) GetCarbonProvidedProfiles() ([]*rosy.Profile, error) {
	// fetch all items from the sync map
	profiles, err := listAllProfilesInSyncMap(&c.carbonProvidedProfiles)
	if err != nil {
		return nil, err
	}

	// deep copy profile data so caller can read/manipulate without locking
	profilesCopy := make([]*rosy.Profile, 0)
	for _, profile := range profiles {
		if profile.Contents == nil {
			log.Warnf("nil carbon profile: %v", profile)
			continue
		}

		mu := c.keyedMutex.Get(profile.Contents.Id.String())
		mu.RLock()
		profileCopy, err := c.copyProfileContents(profile)
		mu.RUnlock()
		if err != nil {
			continue
		}
		if profileCopy != nil && !profileCopy.Deleted {
			profilesCopy = append(profilesCopy, profileCopy)
		}

	}

	carbonProvidedProfiles.Set(float64(len(profilesCopy)))
	return profilesCopy, nil
}

func (c *ProfileCache) GetCarbonProvidedProfile(profileId uuid.UUID) (*rosy.Profile, bool) {
	mu := c.keyedMutex.Get(profileId.String())
	mu.RLock()
	defer mu.RUnlock()

	profile, ok := c.getCarbonProvidedProfile(profileId)
	if ok {
		profileCopy, err := c.copyProfileContents(profile)
		if err != nil {
			return nil, false
		}
		return profileCopy, true
	}

	return nil, false
}

func (c *ProfileCache) getCarbonProvidedProfile(profileId uuid.UUID) (*writeSafeProfile, bool) {
	rawProfile, ok := c.carbonProvidedProfiles.Load(profileId)
	if ok {
		profile, ok := rawProfile.(*writeSafeProfile)
		if !ok {
			log.Errorf("Failed to cast Carbon Provided profile %s to *writeSafeProfile", profileId)
			return nil, false
		}
		return profile, true
	}

	return nil, false
}

func (c *ProfileCache) fetchCarbonProvidedProfilesFromS3(ctx context.Context) ([]*rosy.Profile, error) {
	var profiles sync.Map
	for _, profileType := range frontend.ProfileType_name {
		key := path.Join(constants.CARBON_PROVIDED_FOLDER_NAME, constants.PROFILES_KEY_PREFIX, profileType)
		profileIds, err := c.fetchProfileIds(key)
		if err != nil {
			return []*rosy.Profile{}, err
		}

		err = iterable.Concurrently(ctx, profileIds, 20, func(profileId uuid.UUID) error {
			profile, err := c.fetchProfileFromS3(c.s3Facade, c.s3Bucket, profileType, uuid.Nil, profileId, true)
			if err != nil {
				return fmt.Errorf("failed to fetch profile %s: %w", profileId, err)
			}

			if profile.Deleted && time.Since(time.UnixMilli(profile.UpdatedAt)) > 365*24*time.Hour {
				// delete the profile from S3 if it's marked as deleted and hasn't been updated for over a year
				err := c.deleteProfileFromS3(profile)
				if err != nil {
					log.WithError(err).Errorf("Failed to remove expired profile %s from S3", profile.Id)
				}
			}

			profiles.Store(profile.Id, profile)
			return nil
		})
		if err != nil {
			return []*rosy.Profile{}, err
		}
	}

	profileList := make([]*rosy.Profile, 0)
	profiles.Range(func(key, value interface{}) bool {
		profileList = append(profileList, value.(*rosy.Profile))
		return true
	})

	return profileList, nil
}

func (c *ProfileCache) SaveCarbonProvidedProfile(profile *rosy.Profile) error {
	mu := c.keyedMutex.Get(profile.Id.String())
	mu.Lock()
	defer mu.Unlock()

	// not to be used by GRPC api
	if profile.UpdatedAt > time.Now().Add(24*time.Hour).UnixMilli() {
		log.Warnf("Protected profile %s update dropped due to updated time being more than 24 hours in the future", profile.Id)
		return nil
	}

	oldProfile, ok := c.getCarbonProvidedProfile(profile.Id)
	if ok {
		if profile.UpdatedAt > oldProfile.Contents.UpdatedAt {
			oldProfile.Contents = profile
			return c.writeCarbonProvidedProfile(oldProfile)
		} else {
			log.Warnf("Protected profile %s update dropped due to updated time being older than the current profile", profile.Id)
			return nil
		}
	} else {
		return c.writeCarbonProvidedProfile(newWriteSafeProfile(profile))
	}
}

func (c *ProfileCache) fetchCarbonProvidedProfiles() ([]*writeSafeProfile, error) {
	profiles, err := listAllProfilesInSyncMap(&c.carbonProvidedProfiles)
	if err != nil {
		return nil, err
	}

	return profiles, nil
}

func (c *ProfileCache) writeCarbonProvidedProfile(profile *writeSafeProfile) error {
	// assumes the profile's cache lock is already held
	profile.Contents.Protected = true
	profile.Contents.CustomerId = uuid.Nil
	jsonData, err := json.Marshal(profile.Contents)
	if err != nil {
		return fmt.Errorf("failed to marshal profile to json: %w", err)
	}

	_, ok := c.carbonProvidedProfiles.Load(profile.Contents.Id)
	if !ok {
		log.Infof("Creating new carbon provided profile %s", profile.Contents.Id)
	}

	c.carbonProvidedProfiles.Store(profile.Contents.Id, profile)

	data := bytes.NewReader(jsonData)
	err = c.writeCarbonProvidedProfileToS3(profile.Contents, data)
	if err != nil {
		return err
	}
	return nil
}

func (c *ProfileCache) writeCarbonProvidedProfileToS3(profile *rosy.Profile, content io.Reader) error {
	profileCacheWritesToS3.Inc()
	key := generateProfileS3Path(uuid.Nil, profile.Id, profile.ProfileType.String(), true)
	err := c.s3Facade.PutObject(c.s3Bucket, key, content)
	if err != nil {
		return fmt.Errorf("failed to write Carbon Provided profile %s to S3: %w", profile.Id, err)
	}

	return nil
}
