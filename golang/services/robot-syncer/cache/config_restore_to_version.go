package cache

import (
	"context"
	"fmt"
	"path"

	"google.golang.org/protobuf/proto"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	cloudconfig "github.com/carbonrobotics/cloud/golang/pkg/carbon/configs"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
)

/**
 * RestoreConfigToVersion restores the config to a specific version.
 * It updates the config to match the values of each leaf node in the target
 * config, updating the timestamp of each value to the provided timestamp if
 * the values do not match. For list nodes, it adds or removes children
 * as necessary to match the target config. For internal nodes, it recursively
 * updates the children to match the target config.
 **/
type RestoreConfigToVersionParameters struct {
	AuditLogger *audit.Logger
	Notifier    RobotNotifier
	Serial      *carbon.ValidSerial
	Timestamp   uint64
	UserID      string
	Target      *config_service.ConfigNode
}

func (c *ConfigCache) RestoreConfigToVersion(ctx context.Context, parameters RestoreConfigToVersionParameters) ([]*robot_syncer.EditRecord, error) {
	mu := c.keyedMutex.Get(parameters.Serial.String())
	mu.Lock()
	defer mu.Unlock()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	cachedConfig, err := c.getRobotConfigRoot(ctx, parameters.Serial)
	if err != nil {
		return nil, fmt.Errorf("failed to get robot config root: %w", err)
	}

	var editRecords []*robot_syncer.EditRecord
	oldValueCache := make(map[string]string)

	///////////////////////////////////////////////////////////////////////////
	// CRITICAL SECTION
	// Cached config remains locked through all operations
	err = func() error {
		// Create a copy of the config to work on
		configCopy := proto.Clone(cachedConfig.Root).(*config_service.ConfigNode)

		// Recursively update the config to match the target version
		editRecords, err = c.updateConfigToMatchVersion(ctx, parameters.Serial, configCopy, parameters.Target, "", parameters.Timestamp, oldValueCache)
		if err != nil {
			return fmt.Errorf("failed to update config to match version: %w", err)
		}

		// Write the updated config to S3 and update the cache
		cachedConfig.Root = configCopy
		if err := c.writeConfig(parameters.Serial, cachedConfig); err != nil {
			return fmt.Errorf("failed to write config: %w", err)
		}
		return nil
	}()
	///////////////////////////////////////////////////////////////////////////

	if err != nil {
		return editRecords, err
	}

	go c.logAndNotifyEditRecords(
		parameters.AuditLogger,
		parameters.Notifier,
		parameters.Serial,
		parameters.Timestamp,
		parameters.UserID,
		editRecords,
		oldValueCache,
	)

	return editRecords, nil
}

func (c *ConfigCache) updateConfigListToTarget(
	ctx context.Context,
	serial *carbon.ValidSerial,
	current *config_service.ConfigNode,
	target *config_service.ConfigNode,
	path string,
	timestamp uint64,
	oldValueCache map[string]string,
) ([]*robot_syncer.EditRecord, error) {
	var editRecords []*robot_syncer.EditRecord
	if current.GetDef().GetType() != config_service.ConfigType_LIST {
		return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
	}

	for _, targetChild := range target.GetChildren() {
		childName := targetChild.GetName()
		childPath := composeChildPath(path, childName)
		currentChild, ok := config.FindChildNode(current, childName)
		if !ok {
			if err := config.AddToConfigNodeList(c.schemaReader, current, serial.Class(), path, childName, timestamp); err != nil {
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action: &robot_syncer.Action{Action: &robot_syncer.Action_Add{
						Add: &robot_syncer.AddAction{Name: childName}},
					},
					Key:     childPath,
					Message: err.Error(),
					Outcome: robot_syncer.Outcome_FAILURE,
				})
				continue
			}

			editRecords = append(editRecords, &robot_syncer.EditRecord{
				Action: &robot_syncer.Action{Action: &robot_syncer.Action_Add{
					Add: &robot_syncer.AddAction{Name: childName}},
				},
				Key:     childPath,
				Outcome: robot_syncer.Outcome_SUCCESS,
			})

			// fetch the new child node so it can be updated to match the target
			currentChild, ok = config.FindChildNode(current, childName)
			if !ok {
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action: &robot_syncer.Action{Action: &robot_syncer.Action_Add{
						Add: &robot_syncer.AddAction{Name: childName}},
					},
					Key:     childPath,
					Message: fmt.Sprintf("failed to find child node %s", childPath),
					Outcome: robot_syncer.Outcome_FAILURE,
				})
				continue
			}
		}

		// recursively update the child node
		childEditRecords, err := c.updateConfigToMatchVersion(ctx, serial, currentChild, targetChild, childPath, timestamp, oldValueCache)
		if err != nil {
			return nil, err
		}

		editRecords = append(editRecords, childEditRecords...)
	}

	// remove children not in target
	for _, child := range current.GetChildren() {
		childName := child.GetName()
		childPath := composeChildPath(path, childName)
		_, ok := config.FindChildNode(target, childName)
		if !ok {
			if err := config.RemoveFromConfigNodeList(ctx, current, childPath, "failed to remove list node", timestamp); err != nil {
				editRecords = append(editRecords, &robot_syncer.EditRecord{
					Action: &robot_syncer.Action{Action: &robot_syncer.Action_Remove{
						Remove: &robot_syncer.RemoveAction{Name: childName}},
					},
					Key:     childPath,
					Message: err.Error(),
					Outcome: robot_syncer.Outcome_FAILURE,
				})
				continue
			}

			editRecords = append(editRecords, &robot_syncer.EditRecord{
				Action: &robot_syncer.Action{Action: &robot_syncer.Action_Remove{
					Remove: &robot_syncer.RemoveAction{Name: childName}},
				},
				Key:     childPath,
				Outcome: robot_syncer.Outcome_SUCCESS,
			})
			continue
		}
	}

	return editRecords, nil
}

func (c *ConfigCache) updateConfigNodeToTarget(
	ctx context.Context,
	serial *carbon.ValidSerial,
	current *config_service.ConfigNode,
	target *config_service.ConfigNode,
	path string,
	timestamp uint64,
	oldValueCache map[string]string,
) ([]*robot_syncer.EditRecord, error) {
	var editRecords []*robot_syncer.EditRecord
	if current.GetDef().GetType() != config_service.ConfigType_NODE {
		return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
	}

	// assumes same schema version for both target and current (as that is pinned by RoSy)
	for _, targetChild := range target.GetChildren() {
		childName := targetChild.GetName()
		childPath := composeChildPath(path, childName)
		currentChild, ok := config.FindChildNode(current, childName)
		if !ok {
			editRecords = append(editRecords, &robot_syncer.EditRecord{
				Action: &robot_syncer.Action{Action: &robot_syncer.Action_Add{
					Add: &robot_syncer.AddAction{Name: childName}},
				},
				Key:     childPath,
				Message: fmt.Sprintf("failed to find child node in target %s", childPath),
				Outcome: robot_syncer.Outcome_FAILURE,
			})
			continue
		}

		// recursively update the child node
		childEditRecords, err := c.updateConfigToMatchVersion(ctx, serial, currentChild, targetChild, childPath, timestamp, oldValueCache)
		if err != nil {
			return nil, err
		}

		editRecords = append(editRecords, childEditRecords...)
	}

	return editRecords, nil
}

// updateConfigToMatchVersion recursively updates the current config to match the target version
func (c *ConfigCache) updateConfigToMatchVersion(
	ctx context.Context,
	serial *carbon.ValidSerial,
	current *config_service.ConfigNode,
	target *config_service.ConfigNode,
	path string,
	timestamp uint64,
	oldValueCache map[string]string,
) ([]*robot_syncer.EditRecord, error) {
	var editRecords []*robot_syncer.EditRecord
	select {
	case <-ctx.Done():
		return editRecords, ctx.Err()
	default:
	}

	switch target.GetDef().GetType() {
	case config_service.ConfigType_LIST:
		records, err := c.updateConfigListToTarget(ctx, serial, current, target, path, timestamp, oldValueCache)
		if err != nil {
			return nil, fmt.Errorf("failed to update config list to target: %w", err)
		}
		editRecords = append(editRecords, records...)
	case config_service.ConfigType_NODE:
		records, err := c.updateConfigNodeToTarget(ctx, serial, current, target, path, timestamp, oldValueCache)
		if err != nil {
			return nil, fmt.Errorf("failed to update config list to target: %w", err)
		}
		editRecords = append(editRecords, records...)
	case config_service.ConfigType_STRING:
		if current.GetDef().GetType() != config_service.ConfigType_STRING {
			return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
		}

		if target.GetValue().GetStringVal() != current.GetValue().GetStringVal() {
			editRecord, err := c.copyConfigValue(current, target, path, timestamp, oldValueCache)
			if err != nil {
				return nil, fmt.Errorf("failed to copy config value at %s: %w", path, err)
			}
			editRecords = append(editRecords, editRecord)
		}
	case config_service.ConfigType_INT:
		if current.GetDef().GetType() != config_service.ConfigType_INT {
			return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
		}

		if target.GetValue().GetInt64Val() != current.GetValue().GetInt64Val() {
			editRecord, err := c.copyConfigValue(current, target, path, timestamp, oldValueCache)
			if err != nil {
				return nil, fmt.Errorf("failed to copy config value at %s: %w", path, err)
			}
			editRecords = append(editRecords, editRecord)
		}
	case config_service.ConfigType_UINT:
		if current.GetDef().GetType() != config_service.ConfigType_UINT {
			return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
		}

		if target.GetValue().GetUint64Val() != current.GetValue().GetUint64Val() {
			editRecord, err := c.copyConfigValue(current, target, path, timestamp, oldValueCache)
			if err != nil {
				return nil, fmt.Errorf("failed to copy config value at %s: %w", path, err)
			}
			editRecords = append(editRecords, editRecord)
		}
	case config_service.ConfigType_FLOAT:
		if current.GetDef().GetType() != config_service.ConfigType_FLOAT {
			return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
		}

		if target.GetValue().GetFloatVal() != current.GetValue().GetFloatVal() {
			editRecord, err := c.copyConfigValue(current, target, path, timestamp, oldValueCache)
			if err != nil {
				return nil, fmt.Errorf("failed to copy config value at %s: %w", path, err)
			}
			editRecords = append(editRecords, editRecord)
		}
	case config_service.ConfigType_BOOL:
		if current.GetDef().GetType() != config_service.ConfigType_BOOL {
			return nil, fmt.Errorf("type mismatch for path %s: %s != %s", path, current.GetDef().GetType(), target.GetDef().GetType())
		}

		if target.GetValue().GetBoolVal() != current.GetValue().GetBoolVal() {
			editRecord, err := c.copyConfigValue(current, target, path, timestamp, oldValueCache)
			if err != nil {
				return nil, fmt.Errorf("failed to copy config value at %s: %w", path, err)
			}
			editRecords = append(editRecords, editRecord)
		}
	default:
		return nil, fmt.Errorf("unknown config type at %s %s", path, current.GetDef().GetType())
	}

	return editRecords, nil
}

func (c *ConfigCache) copyConfigValue(
	current, target *config_service.ConfigNode,
	path string,
	timestamp uint64,
	oldValueCache map[string]string,
) (*robot_syncer.EditRecord, error) {
	valueCopy := proto.Clone(target.GetValue()).(*config_service.ConfigValue)
	valueCopy.TimestampMs = timestamp

	// drop previous oldValue if it exists
	oldValueCache[path] = cloudconfig.ConfigValueToString(current.GetValue())

	// update the config value in place
	current.Value = valueCopy

	return &robot_syncer.EditRecord{
		Action: &robot_syncer.Action{
			Action: &robot_syncer.Action_Set{
				Set: &robot_syncer.SetAction{Value: valueCopy},
			},
		},
		Key:     path,
		Outcome: robot_syncer.Outcome_SUCCESS,
	}, nil
}

func composeChildPath(configPath, childName string) string {
	if configPath == "" {
		return childName
	}
	return path.Join(configPath, childName)
}
