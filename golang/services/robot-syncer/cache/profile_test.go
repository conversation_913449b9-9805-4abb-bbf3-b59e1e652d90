package cache

import (
	"encoding/json"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
)

func TestListAllProfilesInSyncMap(t *testing.T) {
	m := sync.Map{}
	id1 := uuid.New()
	profile := &rosy.Profile{
		CustomerId:  id1,
		Id:          id1,
		ProfileType: frontend.ProfileType_CATEGORY,
		Profile:     "{}",
		UpdatedAt:   time.Now().UnixMilli(),
		Deleted:     false,
	}

	writeSafeProfile := newWriteSafeProfile(profile)

	m.Store(id1, writeSafeProfile)

	profiles, err := listAllProfilesInSyncMap(&m)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(profiles))
	assert.Equal(t, profile, profiles[0].Contents)

	id2 := uuid.New()
	profile2 := &rosy.Profile{
		CustomerId:  id2,
		Id:          id2,
		ProfileType: frontend.ProfileType_CATEGORY,
		Profile:     "{}",
		UpdatedAt:   time.Now().UnixMilli(),
		Deleted:     false,
	}
	writeSafeProfile2 := newWriteSafeProfile(profile2)

	m.Store(id2, writeSafeProfile2)
	profiles, err = listAllProfilesInSyncMap(&m)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(profiles))
}

func TestGenerateProfileS3Path(t *testing.T) {
	customerId := uuid.New()
	profileId := uuid.New()
	tests := []struct {
		name     string
		profile  *rosy.Profile
		expected string
	}{
		{
			name: "valid Category profile",
			profile: &rosy.Profile{
				CustomerId:  customerId,
				Id:          profileId,
				ProfileType: frontend.ProfileType_CATEGORY,
				Profile:     "{}",
				Deleted:     false,
				Protected:   false,
			},
			expected: fmt.Sprintf("customers/%s/profiles/CATEGORY/%s.json", customerId, profileId),
		},
		{
			name: "valid Category Collection profile",
			profile: &rosy.Profile{
				CustomerId:  customerId,
				Id:          profileId,
				ProfileType: frontend.ProfileType_CATEGORY_COLLECTION,
				Profile:     "{}",
				Deleted:     false,
				Protected:   false,
			},
			expected: fmt.Sprintf("customers/%s/profiles/CATEGORY_COLLECTION/%s.json", customerId, profileId),
		},
		{
			name: "carbon provided Category profile",
			profile: &rosy.Profile{
				CustomerId:  uuid.Nil,
				Id:          profileId,
				ProfileType: frontend.ProfileType_CATEGORY,
				Profile:     "{}",
				Deleted:     false,
				Protected:   true,
			},
			expected: fmt.Sprintf("carbon_provided/profiles/CATEGORY/%s.json", profileId),
		},
		{
			name: "carbon provided Category Collection profile",
			profile: &rosy.Profile{
				CustomerId:  uuid.Nil,
				Id:          profileId,
				ProfileType: frontend.ProfileType_CATEGORY_COLLECTION,
				Profile:     "{}",
				Deleted:     false,
				Protected:   true,
			},
			expected: fmt.Sprintf("carbon_provided/profiles/CATEGORY_COLLECTION/%s.json", profileId),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, test.expected, generateProfileS3Path(test.profile.CustomerId, test.profile.Id, test.profile.ProfileType.String(), test.profile.Protected))
		})
	}
}

func TestMarshalProfilesToJSON(t *testing.T) {
	profile := &rosy.Profile{
		CustomerId:  uuid.New(),
		Id:          uuid.New(),
		ProfileType: frontend.ProfileType_CATEGORY,
		Profile:     "{}",
		UpdatedAt:   time.Now().UnixMilli(),
		Deleted:     false,
	}

	profiles := []*rosy.Profile{profile}

	jsonData, err := json.Marshal(profiles)
	assert.NoError(t, err)

	var unmarshaledProfiles []*rosy.Profile
	err = json.Unmarshal(jsonData, &unmarshaledProfiles)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(unmarshaledProfiles))
	for _, p := range unmarshaledProfiles {
		assert.Equal(t, profile.CustomerId, p.CustomerId)
		assert.Equal(t, profile.Id, p.Id)
		assert.Equal(t, profile.ProfileType, p.ProfileType)
		assert.Equal(t, profile.Profile, p.Profile)
		assert.Equal(t, profile.UpdatedAt, p.UpdatedAt)
		assert.Equal(t, profile.Deleted, p.Deleted)
	}

	profiles = []*rosy.Profile{profile, profile}
	jsonData, err = json.Marshal(profiles)
	assert.NoError(t, err)

	err = json.Unmarshal(jsonData, &unmarshaledProfiles)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(unmarshaledProfiles))
}
