package cache

import (
	"bytes"
	"context"
	"fmt"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/crgo/config/schema"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

// Configs are maintained per robot classification, per serial. They live under the
// the <classification>/<serial> key prefix (e.g. "slayer/slayer101/robot_config.json",
// "reapers/reaper0/robot_config.json", etc).

const (
	AddToListOperation        = "AddToList"
	RemoveFromListOperation   = "RemoveFromList"
	UpdateConfigNodeOperation = "UpdateConfigNode"
)

type writeSafeRobotConfig struct {
	Root *config_service.ConfigNode
}

// N.B. Cache Items should ONLY be created in the getRobotConfigRoot and getTemplate functions.
// This is to ensure that the cache item is always locked during and after creation.
// This is important because, if misused, multiple cache items can be created
// concurrently, leading to data loss as only one of the items will end up being
// inserted into the config cache and written to S3.
func newWriteSafeRobotConfig(root *config_service.ConfigNode) *writeSafeRobotConfig {
	return &writeSafeRobotConfig{
		Root: root,
	}
}

var (
	listNodeNameRegExp = regexp.MustCompile(`^[0-9A-Za-z_.-]+$`)
)

type ConfigCache struct {
	keyedMutex           *KeyedMutex
	cache                sync.Map // map[serial string]*writeSafeRobotConfig
	unsyncedKeys         sync.Map // map[serial string]*writeSafeUnsyncedKeys
	unsyncedListRemovals sync.Map // map[serial string]*writeSafeUnsyncedKeys
	templateCache        sync.Map // map[robot_class string]*writeSafeRobotConfig

	s3Bucket     string
	s3Facade     *awslib.AWSS3Facade
	schemaReader *schema.Reader
}

func NewConfigCache(ctx context.Context, s3Facade *awslib.AWSS3Facade, s3Bucket string, schemaReader *schema.Reader) *ConfigCache {
	cache := &ConfigCache{
		keyedMutex:   NewKeyedMutex(),
		s3Facade:     s3Facade,
		s3Bucket:     s3Bucket,
		schemaReader: schemaReader,
	}
	cache.initialize(ctx)
	return cache
}

func (c *ConfigCache) initialize(ctx context.Context) {
	serials := c.getAllRobotSerials()
	c.initializeUnsyncedKeyCache(ctx, serials)
	c.initializeUnsyncedListRemovalsCache(ctx, serials)
	if err := c.initializeTemplates(); err != nil {
		log.WithError(err).Error("Error initializing template cache")
	}
}

func fetchConfigFromS3(s3f *awslib.AWSS3Facade, s3Bucket string, serial *carbon.ValidSerial, schemaReader *schema.Reader) (*config_service.ConfigNode, error) {
	log.Debugf("Loading config for %s from S3", serial)
	configCacheReadsFromS3.Inc()
	readsFromS3.WithLabelValues(serial.String()).Inc()
	robotConfigFilename := path.Join(serial.Class().String(), serial.String(), constants.ROBOT_CONFIG_FILENAME)
	buf := &bytes.Buffer{}
	err := s3f.GetObject(s3Bucket, robotConfigFilename, buf)
	if err != nil {
		return nil, err
	}

	configSchema, err := schemaReader.GetConfigSchema(serial.Class())
	if err != nil {
		return nil, fmt.Errorf("failed to get %s config schema: %w", serial.Class(), err)
	}

	configBytes := buf.Bytes()
	root, err := config.UnmarshalConfigFromJson(configBytes, configSchema)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal robot config from json: %w", err)
	}

	return root, nil
}

func (c *ConfigCache) writeConfig(serial *carbon.ValidSerial, cachedConfig *writeSafeRobotConfig) error {
	// N.B. this function assumes you have already write locked the associated cache entry
	jsonData, err := config.MarshalConfigToJson(cachedConfig.Root)
	if err != nil {
		return fmt.Errorf("failed to marshal config to json: %w", err)
	}

	err = c.writeConfigToS3(serial, jsonData)
	if err != nil {
		return fmt.Errorf("failed to write config to S3: %w", err)
	}

	// rename the root node to the serial (default is it is named after the class)
	cachedConfig.Root.Name = serial.String()

	c.cache.Store(serial.String(), cachedConfig)
	return nil
}

func (c *ConfigCache) writeConfigToS3(serial *carbon.ValidSerial, content string) error {
	// N.B. this function assumes you have already locked the associated cache entry
	configCacheWritesToS3.Inc()
	writesToS3.WithLabelValues(serial.String()).Inc()
	key := path.Join(serial.Class().String(), serial.String(), constants.ROBOT_CONFIG_FILENAME)
	reader := strings.NewReader(content)
	err := c.s3Facade.PutObject(c.s3Bucket, key, reader)
	if err != nil {
		return fmt.Errorf("failed to write %s config to S3: %w", serial, err)
	}

	return nil
}

func (c *ConfigCache) CreateRobotConfigFromTemplate(ctx context.Context, serial, copyFromSerial *carbon.ValidSerial, ts int64) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	if copyFromSerial != nil {
		mu2 := c.keyedMutex.Get(copyFromSerial.String())
		mu2.RLock()
		defer mu.RUnlock()
	}

	cachedTemplate, err := c.getTemplate(serial.Class())
	if err != nil {
		return err
	}

	clone := newWriteSafeRobotConfig(proto.Clone(cachedTemplate.Root).(*config_service.ConfigNode))

	return c.createRobotConfig(ctx, clone, serial, copyFromSerial, ts)
}

func (c *ConfigCache) GetRobotConfigRootJSON(ctx context.Context, serial *carbon.ValidSerial) ([]byte, error) {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		return nil, fmt.Errorf("failed to get robot config: %w", err)
	}

	jsonData, err := config.MarshalConfigToJson(cachedConfig.Root)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal config to json: %w", err)
	}
	return []byte(jsonData), nil
}

/**
 * getRobotConfigRoot returns the config for the given serial. This function
 * handles writeSafeCacheItem creation and locking. It creates an empty cache
 * entry and locks it immediately to ensure that no other goroutines can
 * concurrently store the same cache item. If the config is not in the cache,
 * it attempts to load it from S3. If the config is found in the cache, the
 * newly created cache entry is discarded and the existing cache entry is
 * returned.
 **/
func (c *ConfigCache) getRobotConfigRoot(ctx context.Context, serial *carbon.ValidSerial) (*writeSafeRobotConfig, error) {
	cacheEntryRaw, exists := c.cache.LoadOrStore(serial.String(), newWriteSafeRobotConfig(nil))
	if cacheEntryRaw == nil {
		return nil, fmt.Errorf("cached config for %s is nil. Refresh RoSy's config cache entry for %s once a config has been created or added to S3", serial, serial)
	}

	cacheEntry, ok := cacheEntryRaw.(*writeSafeRobotConfig)
	if !ok {
		return nil, fmt.Errorf("failed to cast cache entry to *writeSafeRobotConfig")
	}

	if !exists {
		// not in cache, attempt to load from S3
		root, err := fetchConfigFromS3(c.s3Facade, c.s3Bucket, serial, c.schemaReader)
		if err != nil {
			log.Warnf("Failed to fetch config for %s, caching nil config", serial)
			c.cache.Store(serial.String(), nil)
			return nil, fmt.Errorf("failed to fetch config for %s: %w", serial, err)
		}

		// rename the root node to the serial (default is it is named after the class)
		root.Name = serial.String()
		cacheEntry.Root = root
		c.cache.Store(serial.String(), cacheEntry)
		return cacheEntry, nil
	}
	return cacheEntry, nil
}

func (c *ConfigCache) GetConfigNodeFromPath(ctx context.Context, serial *carbon.ValidSerial, path string) (*config_service.ConfigNode, error) {
	mu := c.keyedMutex.Get(serial.String())
	mu.RLock()
	defer mu.RUnlock()

	errorPrefix := fmt.Sprintf("unable to fetch config node from path: %s", path)
	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		return nil, fmt.Errorf("%s -- %v", errorPrefix, err)
	}

	return config.GetNodeFromPath(cachedConfig.Root, path)
}

func (c *ConfigCache) UpdateConfigNode(ctx context.Context, serial *carbon.ValidSerial, key string, value any, ts uint64, enforceSchemaChoices bool) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to set %s config value", key)
	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	configSchema, err := c.schemaReader.GetConfigSchema(serial.Class())
	if err != nil {
		return fmt.Errorf("failed to get %s config schema: %w", serial.Class(), err)
	}

	schemaNode, err := c.schemaReader.GetNodeSchemaFromPath(configSchema, serial.Class().String(), key)
	if err != nil {
		return fmt.Errorf("failed to get node schema for path %s: %w", key, err)
	}

	node, err := config.GetNodeFromPath(cachedConfig.Root, key)
	if err != nil {
		return err
	}

	configValue, ok := value.(*config_service.ConfigValue)
	if !ok {
		configValue, err = anyToConfigValue(value, ts, errorPrefix, node.Def.Type)
		if err != nil {
			return err
		}
	}
	// TODO:(smt) kind of a hack, to ensure, passed in ts is registered as the value ts.
	configValue.TimestampMs = ts

	_, err = config.UpdateConfigNodeValue(schemaNode, node, configValue, enforceSchemaChoices)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	if err := c.writeConfig(serial, cachedConfig); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	return nil
}

func anyToConfigValue(value any, ts uint64, errorPrefix string, nodeType config_service.ConfigType) (*config_service.ConfigValue, error) {
	result := &config_service.ConfigValue{
		TimestampMs: ts,
	}
	switch nodeType {
	case config_service.ConfigType_BOOL:
		val, ok := value.(bool)
		if !ok {
			return nil, fmt.Errorf("%s: expected bool value, got %T", errorPrefix, value)
		}
		result.Value = &config_service.ConfigValue_BoolVal{BoolVal: val}
	case config_service.ConfigType_INT:
		val, ok := schema.CastNumber[int64](value)
		if !ok {
			return nil, fmt.Errorf("%s: expected int value, got %T", errorPrefix, value)
		}
		result.Value = &config_service.ConfigValue_Int64Val{Int64Val: val}
	case config_service.ConfigType_UINT:
		val, ok := schema.CastNumber[uint64](value)
		if !ok {
			return nil, fmt.Errorf("%s: expected uint value, got %T", errorPrefix, value)
		}
		result.Value = &config_service.ConfigValue_Uint64Val{Uint64Val: val}
	case config_service.ConfigType_FLOAT:
		val, ok := schema.CastNumber[float64](value)
		if !ok {
			return nil, fmt.Errorf("%s: expected float value, got %T", errorPrefix, value)
		}
		result.Value = &config_service.ConfigValue_FloatVal{FloatVal: val}
	case config_service.ConfigType_STRING:
		val, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("%s: expected string value, got %T", errorPrefix, value)
		}
		result.Value = &config_service.ConfigValue_StringVal{StringVal: val}
	default:
		return nil, fmt.Errorf("%s: unsupported config type %s", errorPrefix, nodeType)
	}
	return result, nil
}

func (c *ConfigCache) FillTree(ctx context.Context, node *config_service.ConfigNode, serial *carbon.ValidSerial, key string) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to fill %s config node", serial)
	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	// TODO:(smt) imported and exported forms should return the same
	unsyncedKeys := c.getUnsyncedListRemovals(serial)
	unsyncedKeysCopy := make([]config.UnsyncedKey, len(unsyncedKeys.Contents))
	copy(unsyncedKeysCopy, unsyncedKeys.Contents)

	cachedConfig.Root, err = config.ReconcileTree(cachedConfig.Root, node, c.schemaReader, serial.Class(), serial.String(), key, unsyncedKeysCopy)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	if err := c.writeConfig(serial, cachedConfig); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, key, err)
	}

	return nil
}

func (c *ConfigCache) AddToList(ctx context.Context, serial *carbon.ValidSerial, path, name string, ts uint64) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to add %s config list key", serial)

	// validate name of list item
	if !listNodeNameRegExp.MatchString(name) {
		return fmt.Errorf("node name %q must only contain alphanumeric characters, underscores, hyphens, and periods", name)
	}

	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	parentNode, err := config.GetNodeFromPath(cachedConfig.Root, path)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	if err = config.AddToConfigNodeList(c.schemaReader, parentNode, serial.Class(), path, name, ts); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, path, err)
	}

	if err := c.writeConfig(serial, cachedConfig); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, path, err)
	}

	return nil
}

func (c *ConfigCache) RemoveFromList(ctx context.Context, serial *carbon.ValidSerial, configPath string, ts uint64) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	errorPrefix := fmt.Sprintf("failed to delete %s config list key", serial)
	segments := strings.Split(configPath, "/")
	parentPath := path.Join(segments[:len(segments)-1]...)

	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}

	parentNode, err := config.GetNodeFromPath(cachedConfig.Root, parentPath)
	if err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, configPath, err)
	}

	if err = config.RemoveFromConfigNodeList(ctx, parentNode, configPath, errorPrefix, ts); err != nil {
		return err
	}

	if err := c.writeConfig(serial, cachedConfig); err != nil {
		return fmt.Errorf("%s %s: %w", errorPrefix, configPath, err)
	}

	return nil
}

func (c *ConfigCache) SetConfig(ctx context.Context, serial *carbon.ValidSerial, jsondata []byte) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	schema, err := c.schemaReader.GetConfigSchema(serial.Class())
	if err != nil {
		return fmt.Errorf("failed to get %s config schema: %w", serial.Class(), err)
	}

	root, err := config.UnmarshalConfigFromJson(jsondata, schema)
	if err != nil {
		return fmt.Errorf("failed to unmarshal robot config from json: %w", err)
	}

	// This endpoint is unique and only used by portal while not all robots are
	// syncing via Robot Syncer. This is essentially an upsert endpoint that
	// allows the caller (only portal) to create or overwrite a config.
	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		log.Warnf("Failed to fetch config for %s, setting to provided config", serial)
	}

	cachedConfig.Root = root
	return c.writeConfig(serial, cachedConfig)
}

func (c *ConfigCache) createRobotConfig(ctx context.Context, templateCopy *writeSafeRobotConfig, serial, copyFromSerial *carbon.ValidSerial, ts int64) error {
	_, err := c.getRobotConfigRoot(ctx, serial)
	if err == nil {
		return fmt.Errorf("robot %s config already exists", serial.String())
	}

	if copyFromSerial == nil {
		if templateCopy == nil {
			return fmt.Errorf("copyFrom serial and provided template are both nil")
		}

		if err = c.writeConfig(serial, templateCopy); err != nil {
			return fmt.Errorf("failed to set config for %s to %s template: %w", serial.String(), serial.Class(), err)
		}
		return nil
	}

	cachedCopyConfig, err := c.getRobotConfigRoot(ctx, copyFromSerial)
	if err != nil {
		return fmt.Errorf("failed to get copyFrom config for %s: %w", copyFromSerial.String(), err)
	}

	clone := newWriteSafeRobotConfig(proto.Clone(cachedCopyConfig.Root).(*config_service.ConfigNode))

	if err = c.writeConfig(serial, clone); err != nil {
		return fmt.Errorf("failed to set config for %s: %w", serial.String(), err)
	}

	c.writeUnsyncedKeys(serial, []config.UnsyncedKey{})
	c.writeUnsyncedListRemovals(serial, []config.UnsyncedKey{})
	return nil
}

func (c *ConfigCache) RefreshCache(ctx context.Context, serial *carbon.ValidSerial) error {
	if serial == nil {
		return fmt.Errorf("cannot refresh a nil serial")
	}

	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	cachedConfig, err := c.getRobotConfigRoot(ctx, serial)
	if err != nil {
		// remove nil config from cache and re-fetch, creating new cache entry
		c.cache.Delete(serial.String())
		cachedConfig, err = c.getRobotConfigRoot(ctx, serial)
		if err != nil {
			return fmt.Errorf("failed to get config for %s: %w", serial, err)
		}
	} else {

		root, err := fetchConfigFromS3(c.s3Facade, c.s3Bucket, serial, c.schemaReader)
		if err != nil {
			log.Warnf("Failed to fetch config for %s, caching nil config", serial)
			c.cache.Store(serial.String(), nil)
			return fmt.Errorf("failed to fetch config for %s: %w", serial, err)
		}

		// rename the root node to the serial (default is it is named after the class)
		root.Name = serial.String()
		cachedConfig.Root = root
		c.cache.Store(serial.String(), cachedConfig)
	}

	err = c.refreshUnsyncedKeysCache(serial)
	if err != nil {
		log.WithError(err).Errorf("Failed to refresh unsynced keys for %s", serial)
	}

	err = c.refreshUnsyncedListRemovalsCache(serial)
	if err != nil {
		log.WithError(err).Errorf("Failed to refresh unsynced list removals for %s", serial)
	}

	return nil
}

func (c *ConfigCache) getAllRobotSerials() []*carbon.ValidSerial {
	serials := make([]*carbon.ValidSerial, 0)
	// visit every folder in RoSy S3 bucket -- return all serials with robot_config.json files
	for _, class := range carbon.SupportedRobotClasses() {
		folder := path.Join(class, "")

		// robotSerials will be of the form class/serial
		robotSerials, err := c.s3Facade.ListSubPrefixNames(
			awslib.ListSubPrefixNamesInput{
				BucketName: c.s3Bucket,
				Subfolder:  fmt.Sprintf("%s/", folder),
			},
		)
		if err != nil {
			log.WithError(err).Errorf("Failed to list objects in %s under", folder)
			continue
		}

		for _, classSerial := range robotSerials {
			inputSerial := filepath.Base(classSerial)
			class := filepath.Base(filepath.Dir(classSerial))
			if inputSerial == "" {
				log.Errorf("Invalid robot S3 prefix: %q", classSerial)
				continue
			}

			// confirm robot_config.json exists
			robotConfigFilename := path.Join(class, inputSerial, constants.ROBOT_CONFIG_FILENAME)
			configExists, err := c.s3Facade.ObjectExists(c.s3Bucket, robotConfigFilename)
			if !configExists || err != nil {
				log.WithError(err).Warnf("No robot config found for %s, skipping...", inputSerial)
				continue
			}

			serial, err := carbon.ParseSerialWithClass(inputSerial, class)
			if err != nil {
				log.WithError(err).Warnf("Unable to parse valid serial using serial %s and class %s, skipping...", inputSerial, class)
				continue
			}

			serials = append(serials, serial)
		}
	}

	return serials
}

func (c *ConfigCache) GetConfigVersion(ctx context.Context, serial *carbon.ValidSerial, timestamp time.Time) (*config_service.ConfigNode, error) {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	robotConfigFilename := path.Join(serial.Class().String(), serial.String(), constants.ROBOT_CONFIG_FILENAME)
	configExists, err := c.s3Facade.ObjectExists(c.s3Bucket, robotConfigFilename)
	if !configExists || err != nil {
		return nil, fmt.Errorf("unable to get config version %s %w", serial.String(), err)
	}

	// Get the versioned object from S3
	buf := &bytes.Buffer{}
	err = c.s3Facade.GetClosestObjectVersion(c.s3Bucket, robotConfigFilename, timestamp, buf)
	if err != nil {
		return nil, fmt.Errorf("failed to get version at timestamp %v for %s: %w", timestamp, serial.String(), err)
	}

	// Get the schema for this robot class
	configSchema, err := c.schemaReader.GetConfigSchema(serial.Class())
	if err != nil {
		return nil, fmt.Errorf("failed to get %s config schema: %w", serial.Class(), err)
	}

	// Unmarshal the config
	configBytes := buf.Bytes()
	root, err := config.UnmarshalConfigFromJson(configBytes, configSchema)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal robot config from json: %w", err)
	}

	return root, nil
}
