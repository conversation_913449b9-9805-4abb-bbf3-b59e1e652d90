package cache

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"path"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/iterable"
	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/utils"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/clients/portal"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

/**
 * Profiles are stored by id in S3 under the customer/{customerId}/profiles key prefix.
 * For example: customers/{customerId}/profiles/{profile_type}/{profileId}.json
 *
 * Carbon provided profiles are stored in S3 under "CarbonProvided".
 * For example: carbon_provided/profiles/{profile_type}/{profileId}.json
 * Customers cannot make changes to the carbon provided profiles. See
 * protected_profiles.go for more details.
 *
 * The cache is initialized with all profiles for all customers and carbon
 * provided profiles. Note that deleted profiles remain in S3 but are not
 * included in the cache.
 *
 * Intentionally, all exported function pass *rosy.Profile while all internal functions
 * use *writeSafeProfile. This is to ensure that the caller can modify the profile
 * contents without acquiring the lock. To achieve this we must always deepcopy profile
 * contents before returning it to the caller.
 *
 * Customer and Carbon Provided profiles marked deleted and not updated for over a year
 * are permanently removed from S3.
 **/

type writeSafeProfile struct {
	Contents *rosy.Profile
}

func newWriteSafeProfile(profile *rosy.Profile) *writeSafeProfile {
	return &writeSafeProfile{
		Contents: profile,
	}
}

type writeSafeProfileTypeMapping struct {
	Contents map[frontend.ProfileType][]uuid.UUID
}

func newWriteSafeProfileTypeMapping(map[frontend.ProfileType][]uuid.UUID) *writeSafeProfileTypeMapping {
	return &writeSafeProfileTypeMapping{
		Contents: map[frontend.ProfileType][]uuid.UUID{},
	}
}

type ProfileCache struct {
	keyedMutex *KeyedMutex
	// these caches included deleted and active profiles
	profileIdsByCustomerByType sync.Map // map[CustomerId uuid.UUID]*writeSafeProfileTypeMapping
	customerProfiles           sync.Map // map[ProfileId uuid.UUID]*writeSafeProfile
	carbonProvidedProfiles     sync.Map // map[ProfileId uuid.UUID]*writeSafeProfile

	portalClient *portal.Client

	s3Bucket string
	s3Facade *awslib.AWSS3Facade
}

func NewProfileCache(ctx context.Context, s3Facade *awslib.AWSS3Facade, s3Bucket string, portalClient *portal.Client) (*ProfileCache, error) {
	c := &ProfileCache{
		keyedMutex: NewKeyedMutex(),
		s3Facade:   s3Facade,
		s3Bucket:   s3Bucket,

		portalClient: portalClient,
	}

	err := c.initialize(ctx)
	if err != nil {
		return nil, err
	}
	return c, nil
}

func (c *ProfileCache) initialize(ctx context.Context) error {
	customerIds, err := utils.GetAllCustomerIds(c.s3Facade, c.s3Bucket)
	if err != nil {
		return err
	}

	// init customer profiles
	if err = iterable.Concurrently(ctx, customerIds, 20, func(customerId uuid.UUID) error {
		return c.RefreshCustomerProfileCache(customerId)
	}); err != nil {
		return err
	}

	// init Carbon Provided Profiles
	profiles, err := c.fetchCarbonProvidedProfilesFromS3(ctx)
	if err != nil {
		return err
	}

	for _, profile := range profiles {
		c.carbonProvidedProfiles.Store(profile.Id, newWriteSafeProfile(profile))
	}

	return nil
}

func (c *ProfileCache) SaveCustomerProfile(profile *rosy.Profile) error {
	mu := c.keyedMutex.Get(profile.Id.String())
	mu.Lock()
	defer mu.Unlock()

	return c.saveCustomerProfile(profile)
}

func (c *ProfileCache) saveCustomerProfile(profile *rosy.Profile) error {
	// Note that, intentionally, robots cannot make changes to Carbon Provided profiles
	if profile.UpdatedAt > time.Now().Add(24*time.Hour).UnixMilli() {
		log.Warnf("Profile %s dropped for customer %s due to updated time being more than 24 hours in the future", profile.Id, profile.CustomerId)
		return fmt.Errorf("profile %s updated time is more than 24 hours in the future", profile.Id)
	}

	if profile.Protected {
		return fmt.Errorf("invalid attempt to update protected profile %s", profile.Id)
	}

	if profile.CustomerId == uuid.Nil {
		return fmt.Errorf("invalid attempt to update profile %s with nil customer id", profile.Id)
	}

	_, ok := c.carbonProvidedProfiles.Load(profile.Id)
	if ok {
		return fmt.Errorf("invalid attempt to update Carbon Provided profile %s", profile.Id)
	}

	currentProfile, existing := c.getCustomerProfile(profile.Id)
	if !existing {
		currentProfile = newWriteSafeProfile(profile)
	}

	err := func() error {
		if currentProfile.Contents.CustomerId != profile.CustomerId {
			return fmt.Errorf("invalid attempt to update profile %s for customer %s, it belongs to customer %s", profile.Id, profile.CustomerId, currentProfile.Contents.CustomerId)
		}

		isStaleUpdate := existing && profile.UpdatedAt <= currentProfile.Contents.UpdatedAt
		if isStaleUpdate {
			log.Warnf("Profile %s update dropped for customer %s due to updated time being older than the current profile", profile.Id, profile.CustomerId)
			return nil
		}

		currentProfile.Contents = profile
		return c.writeCustomerProfile(currentProfile)
	}()
	if err != nil {
		return err
	}

	return c.updateCustomerProfilesByType(profile)
}

func (c *ProfileCache) updateCustomerProfilesByType(profile *rosy.Profile) error {
	// make necessary changes to profileIdsByCustomerByType cache
	rawCustomerProfileIds, ok := c.profileIdsByCustomerByType.LoadOrStore(profile.CustomerId, newWriteSafeProfileTypeMapping(
		map[frontend.ProfileType][]uuid.UUID{
			profile.ProfileType: {profile.Id},
		},
	))

	if !ok {
		return nil
	}

	customerProfileIdsByType, ok := rawCustomerProfileIds.(*writeSafeProfileTypeMapping)
	if !ok {
		return fmt.Errorf("failed to cast customer profile ids by type to writeSafeProfileTypeMapping")
	}

	profiles, ok := customerProfileIdsByType.Contents[profile.ProfileType]
	if !ok {
		customerProfileIdsByType.Contents[profile.ProfileType] = []uuid.UUID{profile.Id}
		c.profileIdsByCustomerByType.Store(profile.CustomerId, customerProfileIdsByType)
	} else {
		if !slices.Contains(profiles, profile.Id) {
			profiles = append(profiles, profile.Id)
			customerProfileIdsByType.Contents[profile.ProfileType] = profiles
			c.profileIdsByCustomerByType.Store(profile.CustomerId, customerProfileIdsByType)
		}
	}

	return nil
}

func generateProfileS3Path(customerId, profileId uuid.UUID, profileType string, carbonProvided bool) string {
	profileFilename := fmt.Sprintf("%s.json", profileId)

	// carbon provided path returns carbon_provided/profiles/{profile_type}/{profileId}.json
	if carbonProvided {
		return path.Join(constants.CARBON_PROVIDED_FOLDER_NAME, constants.PROFILES_KEY_PREFIX, profileType, profileFilename)
	}

	// customer path returns customers/{customerId}/profiles/{profile_type}/{profileId}.json
	return path.Join(constants.CUSTOMERS_KEY_PREFIX, customerId.String(), constants.PROFILES_KEY_PREFIX, profileType, profileFilename)
}

func (c *ProfileCache) writeCustomerProfile(profile *writeSafeProfile) error {
	// assumes the profile's cache lock is already held
	c.customerProfiles.Store(profile.Contents.Id, profile)

	jsonData, err := json.Marshal(profile.Contents)
	if err != nil {
		return fmt.Errorf("failed to marshal profile to json: %w", err)
	}
	data := bytes.NewReader(jsonData)
	err = c.writeCustomerProfileToS3(profile.Contents, data)
	if err != nil {
		return err
	}

	return nil
}

func (c *ProfileCache) writeCustomerProfileToS3(profile *rosy.Profile, content io.Reader) error {
	serials := c.portalClient.GetRobotSerialsByCustomerId(profile.CustomerId)
	for _, serial := range serials {
		writesToS3.WithLabelValues(serial).Inc()
	}
	profileCacheWritesToS3.Inc()
	key := generateProfileS3Path(profile.CustomerId, profile.Id, profile.ProfileType.String(), profile.Protected)
	err := c.s3Facade.PutObject(c.s3Bucket, key, content)
	if err != nil {
		return fmt.Errorf("failed to write profile %s to S3 for customer %s: %w", profile.Id, profile.CustomerId, err)
	}

	return nil
}

func (c *ProfileCache) fetchProfileFromS3(s3f *awslib.AWSS3Facade, s3Bucket, profileType string, customerId, profileId uuid.UUID, carbonProvided bool) (*rosy.Profile, error) {
	if carbonProvided {
		log.Debugf("Loading Carbon Provided profile %s", profileId)
	} else {
		log.Debugf("Loading profile %s for customer %s", profileId, customerId)
		serials := c.portalClient.GetRobotSerialsByCustomerId(customerId)
		for _, serial := range serials {
			readsFromS3.WithLabelValues(serial).Inc()
		}
	}

	profileCacheReadsFromS3.Inc()
	buf := &bytes.Buffer{}
	err := s3f.GetObject(s3Bucket, generateProfileS3Path(customerId, profileId, profileType, carbonProvided), buf)
	if err != nil {
		return nil, err
	}

	var profile *rosy.Profile
	err = json.Unmarshal(buf.Bytes(), &profile)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal profile from json: %w", err)
	}

	return profile, nil
}

func (c *ProfileCache) deleteProfileFromS3(profile *rosy.Profile) error {
	key := generateProfileS3Path(profile.CustomerId, profile.Id, profile.ProfileType.String(), profile.Protected)
	if err := c.s3Facade.DeleteObject(c.s3Bucket, key); err != nil {
		return fmt.Errorf("failed to delete profile %s from S3: %w", profile.Id, err)
	}
	log.Infof("Permanently deleted expired (protected=%v) profile %s", profile.Protected, profile.Id)
	return nil
}

func (c *ProfileCache) RefreshCustomerProfileCache(customerId uuid.UUID) error {
	mu := c.keyedMutex.Get(customerId.String())
	mu.Lock()
	defer mu.Unlock()

	profilesByType := map[frontend.ProfileType][]uuid.UUID{}
	totalProfiles := 0
	for profileTypeId, profileTypeName := range frontend.ProfileType_name {
		profileType := frontend.ProfileType(profileTypeId)
		profilesByType[profileType] = make([]uuid.UUID, 0)

		key := path.Join(constants.CUSTOMERS_KEY_PREFIX, customerId.String(), constants.PROFILES_KEY_PREFIX, profileTypeName)
		profileIds, err := c.fetchProfileIds(key)
		if err != nil {
			return err
		}

		for _, profileId := range profileIds {
			profile, err := c.fetchProfileFromS3(c.s3Facade, c.s3Bucket, profileTypeName, customerId, profileId, false)
			if err != nil {
				return err
			}

			// permanently remove profiles marked deleted and not updated for over a year
			if profile.Deleted && time.Since(time.UnixMilli(profile.UpdatedAt)) > 365*24*time.Hour {
				if err := c.permanentlyRemoveProfile(profile); err != nil {
					return err
				}
				continue
			}

			// update customer profile cache
			rawOldProfile, ok := c.customerProfiles.LoadOrStore(profile.Id, newWriteSafeProfile(profile))
			if ok {
				oldRWSafeProfile := rawOldProfile.(*writeSafeProfile)
				oldRWSafeProfile.Contents = profile
			}
			totalProfiles++
		}
		profilesByType[profileType] = profileIds

		// update profile ids by customer by type cache
		rawOldProfilesByType, ok := c.profileIdsByCustomerByType.LoadOrStore(customerId, newWriteSafeProfileTypeMapping(profilesByType))
		if ok {
			oldProfilesByType, ok := rawOldProfilesByType.(*writeSafeProfileTypeMapping)
			if !ok {
				return fmt.Errorf("failed to cast old profiles by type to writeSafeProfileTypeMapping")
			}

			oldProfilesByType.Contents = profilesByType
			c.profileIdsByCustomerByType.Store(customerId, oldProfilesByType)
		}
	}

	customerRobots := c.portalClient.GetRobotSerialsByCustomerId(customerId)
	for _, serial := range customerRobots {
		profileCount.WithLabelValues(serial).Set(float64(totalProfiles))
	}
	return nil
}

func (c *ProfileCache) fetchProfileIds(key string) ([]uuid.UUID, error) {
	fileNames, err := c.s3Facade.ListFileNames(
		awslib.ListSubPrefixNamesInput{
			BucketName: c.s3Bucket,
			Subfolder:  key,
		},
	)
	if err != nil {
		return make([]uuid.UUID, 0), err
	}

	return c.parseProfileIdsFromFileNames(fileNames), nil
}

func (c *ProfileCache) parseProfileIdsFromFileNames(fileNames []string) []uuid.UUID {
	profileIds := make([]uuid.UUID, 0)
	for _, fileName := range fileNames {
		if !strings.HasSuffix(fileName, ".json") {
			log.Debugf("Skipping non-json profile file %s", fileName)
			continue
		}

		profileIdString := strings.TrimSuffix(fileName, ".json")
		profileId, err := uuid.Parse(profileIdString)
		if err != nil {
			log.WithError(err).Errorf("Failed to parse profile id %s", profileIdString)
			continue
		}
		profileIds = append(profileIds, profileId)
	}

	return profileIds
}

func (c *ProfileCache) getCustomerProfile(profileId uuid.UUID) (*writeSafeProfile, bool) {
	rawProfile, ok := c.carbonProvidedProfiles.Load(profileId)
	if ok {
		profile := rawProfile.(*writeSafeProfile)
		return profile, true
	}

	rawProfile, ok = c.customerProfiles.Load(profileId)
	if ok {
		profile := rawProfile.(*writeSafeProfile)
		return profile, true
	}

	return nil, false
}

func (c *ProfileCache) GetCustomerProfile(profileId uuid.UUID) (*rosy.Profile, bool) {
	mu := c.keyedMutex.Get(profileId.String())
	mu.RLock()
	defer mu.RUnlock()

	profile, ok := c.getCustomerProfile(profileId)
	if ok {
		profileCopy, err := c.copyProfileContents(profile)
		if err != nil {
			return nil, false
		}
		return profileCopy, true
	}

	return nil, false
}

func (c *ProfileCache) GetCustomerProfiles(customerId uuid.UUID, profileType *frontend.ProfileType) ([]*rosy.Profile, error) {
	mu := c.keyedMutex.Get(customerId.String())
	mu.RLock()
	defer mu.RUnlock()

	// Note that this does not include carbon provided profiles inherited by every customer
	profiles, err := c.fetchProfilesForCustomer(customerId, profileType)
	if err != nil {
		return nil, err
	}

	// deep copy profile data so caller can read/manipulate without locking
	profilesCopy := make([]*rosy.Profile, 0)
	for _, profile := range profiles {
		profileCopy, err := c.copyProfileContents(profile)
		if err != nil {
			continue
		}
		if profileCopy != nil && !profileCopy.Deleted {
			profilesCopy = append(profilesCopy, profileCopy)
		}
	}

	return profilesCopy, nil
}

func (c *ProfileCache) fetchProfilesForCustomer(customerId uuid.UUID, profileType *frontend.ProfileType) ([]*writeSafeProfile, error) {
	profiles := make([]*writeSafeProfile, 0)
	customerRobots := c.portalClient.GetRobotSerialsByCustomerId(customerId)

	rawProfileIdsByType, ok := c.profileIdsByCustomerByType.Load(customerId)
	if !ok {
		return profiles, nil
	}

	profileIdsByType, ok := rawProfileIdsByType.(*writeSafeProfileTypeMapping)
	if !ok {
		return profiles, fmt.Errorf("failed to cast profile ids by type to writeSafeProfileTypeMapping")
	}

	if profileType != nil {
		profileIds, exists := profileIdsByType.Contents[*profileType]
		if !exists {
			return profiles, nil
		}
		log.Debugf("Fetching %d profiles of type %s for customer %s", len(profileIds), profileType.String(), customerId)
		for _, profileId := range profileIds {
			profile, ok := c.getCustomerProfile(profileId)
			if ok {
				profiles = append(profiles, profile)
			}
		}

		for _, serial := range customerRobots {
			profileCount.WithLabelValues(serial, profileType.String()).Set(float64(len(profileIds)))
		}
	} else {
		for profileType, profileIds := range profileIdsByType.Contents {
			var numProfilesByType float64 = 0
			log.Debugf("Fetching %d profiles of type %s for customer %s", len(profileIds), profileType.String(), customerId)
			for _, profileId := range profileIds {
				profile, ok := c.getCustomerProfile(profileId)
				if ok {
					profiles = append(profiles, profile)
					numProfilesByType++
				}
			}

			for _, serial := range customerRobots {
				profileCount.WithLabelValues(serial, profileType.String()).Set(numProfilesByType)
			}
		}
	}

	return profiles, nil
}

func (c *ProfileCache) GetProfileSyncData(customerId uuid.UUID) (*robot_syncer.GetProfileSyncDataResponse, error) {
	mu := c.keyedMutex.Get(customerId.String())
	mu.RLock()
	defer mu.RUnlock()

	// This function does not read or write from S3 as it is called by every robot
	// online every 1 minute. It is expected that the cache will be up to date.
	profiles, err := c.fetchProfilesForCustomer(customerId, nil)
	if err != nil {
		return nil, err
	}

	carbonProvidedProfiles, err := c.fetchCarbonProvidedProfiles()
	if err != nil {
		return nil, err
	}

	profiles = append(profiles, carbonProvidedProfiles...)
	profileSyncData := map[string]*frontend.ProfileSyncData{}
	for _, profile := range profiles {
		profileSyncData[profile.Contents.Id.String()] = &frontend.ProfileSyncData{
			ProfileType:     profile.Contents.ProfileType,
			LastUpdatedTsMs: profile.Contents.UpdatedAt,
			Deleted:         profile.Contents.Deleted,
			Protected:       profile.Contents.Protected,
		}
	}

	return &robot_syncer.GetProfileSyncDataResponse{
		Profiles: profileSyncData,
	}, nil
}

func (c *ProfileCache) DeleteCustomerProfile(profileId uuid.UUID) error {
	mu := c.keyedMutex.Get(profileId.String())
	mu.Lock()
	defer mu.Unlock()

	// Note that, intentionally, this does not delete Carbon Provided profiles
	profile, ok := c.getCustomerProfile(profileId)
	if !ok {
		return nil
	}

	if !profile.Contents.Deleted {
		profile.Contents.Deleted = true
		profile.Contents.UpdatedAt = time.Now().UnixMilli()
		if err := c.saveCustomerProfile(profile.Contents); err != nil {
			return fmt.Errorf("failed to set profile %s as deleted: %w", profileId, err)
		}
	}
	return nil
}

func (c *ProfileCache) FilterProfilesByType(profiles []*rosy.Profile, profileType frontend.ProfileType) []*rosy.Profile {
	filteredProfiles := make([]*rosy.Profile, 0)
	for _, profile := range profiles {
		if profile.ProfileType == profileType && !profile.Deleted {
			filteredProfiles = append(filteredProfiles, profile)
		}
	}
	return filteredProfiles
}

func listAllProfilesInSyncMap(m *sync.Map) ([]*writeSafeProfile, error) {
	profiles := make([]*writeSafeProfile, 0)
	m.Range(func(_, value interface{}) bool {
		profile := value.(*writeSafeProfile)
		profiles = append(profiles, profile)
		return true
	})
	return profiles, nil
}

func (c *ProfileCache) permanentlyRemoveProfile(profile *rosy.Profile) error {
	// assumes profile was never added to the profile caches
	if !profile.Deleted {
		return fmt.Errorf("cannot purge profile %s as it is not deleted", profile.Id)
	}

	if profile.UpdatedAt > time.Now().Add(-365*24*time.Hour).UnixMilli() {
		return fmt.Errorf("cannot purge profile %s as it was deleted less than a year ago", profile.Id)
	}

	if err := c.deleteProfileFromS3(profile); err != nil {
		return fmt.Errorf("failed to purge profile %s: %w", profile.Id, err)
	}

	return nil
}

func (c *ProfileCache) copyProfileContents(profile *writeSafeProfile) (*rosy.Profile, error) {
	if profile == nil {
		return nil, fmt.Errorf("profile is nil")
	}

	return &rosy.Profile{
		CustomerId:  profile.Contents.CustomerId,
		Id:          profile.Contents.Id,
		ProfileType: profile.Contents.ProfileType,
		Profile:     profile.Contents.Profile,
		Protected:   profile.Contents.Protected,
		UpdatedAt:   profile.Contents.UpdatedAt,
		Deleted:     profile.Contents.Deleted,
	}, nil
}

func (c *ProfileCache) DropCustomerProfiles(ctx context.Context) error {
	log.Warn("Drop Customer Profiles invoked")
	// Because this is a debug handler for testing env only, and there is no good way to lock each profile here.
	// this is just a sentinel lock to prevent concurrent profile drops.
	mu := c.keyedMutex.Get("DROP-CUSTOMER-PROFILES")
	mu.Lock()
	defer mu.Unlock()

	// mark all customer profiles as deleted (only used in testing environment)
	errorPrefix := fmt.Sprintf("failed to drop customer profiles")

	profiles, err := listAllProfilesInSyncMap(&c.customerProfiles)
	if err != nil {
		return fmt.Errorf("%s: %w", errorPrefix, err)
	}
	for _, profile := range profiles {
		err := func() error {
			if !profile.Contents.Deleted {
				log.Infof("Marking profile %s of type %s as deleted", profile.Contents.Id, profile.Contents.ProfileType.String())
				profile.Contents.Deleted = true
				profile.Contents.UpdatedAt = time.Now().UnixMilli()
				if err := c.writeCustomerProfile(profile); err != nil {
					return fmt.Errorf("%s: %w", errorPrefix, err)
				}
			}
			return nil
		}()

		if err != nil {
			return fmt.Errorf("%s for profile %s: %w", errorPrefix, profile.Contents.Id, err)
		}
	}
	return nil
}
