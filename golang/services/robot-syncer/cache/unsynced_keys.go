package cache

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"path"

	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/service/s3"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"

	awslib "github.com/carbonrobotics/cloud/golang/pkg/aws"
	"github.com/carbonrobotics/cloud/golang/pkg/iterable"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

/**
 * UnsyncedKeys are only used for metrics and auditing purposes. They are not
 * used by Robot Syncer as part of the sync process.
 *
 * UnsyncedKeys are maintained per robot classification, per serial. They live under the
 * the classification/<serial> key prefix (e.g. "slayer/slayer101/unsynced_config_keys.json").
 *
 * Intentionally, all exported function pass config.UnsyncedKey while all internal functions
 * use *writeSafeUnsyncedKeys. This is to ensure that the caller can modify the UnsyncedKey
 * contents without acquiring the lock. To achieve this we must always deepcopy UnsyncedKey
 * contents before returning it to the caller.
 **/

func (c *ConfigCache) initializeUnsyncedKeyCache(ctx context.Context, serials []*carbon.ValidSerial) {
	err := iterable.Concurrently(ctx, serials, 100, func(serial *carbon.ValidSerial) error {
		c.getUnsyncedKeys(serial)
		return nil
	})
	if err != nil {
		log.WithError(err).Error("errors initializing unsynced keys cache")
	}
}

type writeSafeUnsyncedKeys struct {
	Contents []config.UnsyncedKey
}

// N.B. Cache Items should ONLY be created in the getUnsyncedKeys and
// getUnsyncedListRemovals functions. This is to ensure that the cache item
// is always locked during and after creation. This is important because, if
// misused, multiple cache items can be created concurrently, leading to data
// loss as only one of the items will end up being inserted into the config
// cache and written to S3.
func newWriteSafeUnsyncedKeys() *writeSafeUnsyncedKeys {
	return &writeSafeUnsyncedKeys{
		Contents: make([]config.UnsyncedKey, 0),
	}
}

func (w *writeSafeUnsyncedKeys) reconcileUnsyncedKeys(entry config.UnsyncedKey) []config.UnsyncedKey {
	// Assumes that the unsynced keys are already locked
	var reconciledUnsyncedKeys []config.UnsyncedKey
	found := false
	for i, existingKey := range w.Contents {
		if existingKey.Key == entry.Key {
			found = true
			if existingKey.OldValue == entry.NewValue {
				// drop the unsynced key if it was reverted to the original value (as saved on robot)
				continue
			}

			if existingKey.Ts < entry.Ts {
				// keep existing old value, update new value and timestamp
				updatedEntry := w.Contents[i]
				updatedEntry.Ts = entry.Ts
				updatedEntry.NewValue = entry.NewValue
				reconciledUnsyncedKeys = append(reconciledUnsyncedKeys, updatedEntry)
				continue
			}
		}
		reconciledUnsyncedKeys = append(reconciledUnsyncedKeys, existingKey)
	}

	if !found {
		reconciledUnsyncedKeys = append(reconciledUnsyncedKeys, entry)
	}

	return reconciledUnsyncedKeys
}

func (c *ConfigCache) writeUnsyncedKeys(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) error {
	oldUnsyncedKeys := c.getUnsyncedKeys(serial)
	oldUnsyncedKeys.Contents = unsyncedKeys

	unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(float64(len(unsyncedKeys)))
	c.unsyncedKeys.Store(serial.String(), oldUnsyncedKeys)
	return c.writeUnsyncedKeysToS3(serial, oldUnsyncedKeys.Contents)
}

func (c *ConfigCache) writeUnsyncedKeysToS3(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) error {
	configCacheWritesToS3.Inc()
	writesToS3.WithLabelValues(serial.String()).Inc()
	jsonData, err := json.Marshal(unsyncedKeys)
	if err != nil {
		return fmt.Errorf("failed to marshal unsyncedKeys to json: %w", err)
	}
	data := bytes.NewReader(jsonData)

	key := path.Join(serial.Class().String(), serial.String(), constants.UNSYNCED_CONFIG_KEYS_FILENAME)
	err = c.s3Facade.PutObject(c.s3Bucket, key, data)
	if err != nil {
		return fmt.Errorf("failed to write %s unsyncedKeys to S3: %w", serial, err)
	}

	return nil
}

func (c *ConfigCache) fetchUnsyncedKeysFromS3(s3f *awslib.AWSS3Facade, s3Bucket string, serial *carbon.ValidSerial) ([]config.UnsyncedKey, error) {
	log.Debugf("Loading unsynced keys for %s", serial)
	configCacheReadsFromS3.Inc()
	readsFromS3.WithLabelValues(serial.String()).Inc()
	robotConfigFilename := path.Join(serial.Class().String(), serial.String(), constants.UNSYNCED_CONFIG_KEYS_FILENAME)
	buf := &bytes.Buffer{}
	err := s3f.GetObject(s3Bucket, robotConfigFilename, buf)
	if err != nil {
		return nil, err
	}

	var unsyncedKeys []config.UnsyncedKey
	err = json.Unmarshal(buf.Bytes(), &unsyncedKeys)
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			if aerr.Code() == s3.ErrCodeNoSuchKey {
				unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(0)
				return []config.UnsyncedKey{}, nil
			}
		}
		return nil, fmt.Errorf("failed to unmarshal unsynced keys from json: %w", err)
	}

	unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(float64(len(unsyncedKeys)))
	return unsyncedKeys, nil
}

func (c *ConfigCache) AddUnsyncedKeys(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey) {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	cachedUnsyncedKeys := c.getUnsyncedKeys(serial)

	for _, entry := range unsyncedKeys {
		cachedUnsyncedKeys.Contents = cachedUnsyncedKeys.reconcileUnsyncedKeys(entry)
	}

	unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(float64(len(cachedUnsyncedKeys.Contents)))
	c.unsyncedKeys.Store(serial.String(), cachedUnsyncedKeys)
	err := c.writeUnsyncedKeysToS3(serial, cachedUnsyncedKeys.Contents)
	if err != nil {
		log.WithError(err).Errorf("Failed to write unsynced keys for %s to S3", serial)
		return
	}
}

/**
 * getUnsyncedKeys returns the unsynced keys for the given serial. This function
 * handles writeSafeCacheItem creation and locking. It creates an empty cache
 * entry and locks it immediately to ensure that no other goroutines can
 * concurrently store the same cache item. If the unsynced keys are not in the cache,
 * it attempts to load them from S3. If the unsynced keys are found in the cache, the
 * newly created cache entry is discarded and the existing cache entry is
 * returned.
 **/
func (c *ConfigCache) getUnsyncedKeys(serial *carbon.ValidSerial) *writeSafeUnsyncedKeys {
	// immediately locked to ensure we don't have multiple goroutines trying to concurrently load the same config
	emptyCacheItem := newWriteSafeUnsyncedKeys()

	cacheEntryRaw, exists := c.unsyncedKeys.LoadOrStore(serial.String(), emptyCacheItem)
	if cacheEntryRaw == nil {
		c.unsyncedKeys.Delete(serial.String())
		return emptyCacheItem
	}

	unsyncedKeys, ok := cacheEntryRaw.(*writeSafeUnsyncedKeys)
	if !ok {
		unsyncedKeys = emptyCacheItem
	}

	if !exists {
		contents, err := c.fetchUnsyncedKeysFromS3(c.s3Facade, c.s3Bucket, serial)
		if err != nil {
			log.WithError(err).Warnf("Failed to load unsynced keys for %s, setting to []", serial)
			unsyncedKeys = emptyCacheItem
			c.writeUnsyncedKeysToS3(serial, unsyncedKeys.Contents)
		} else {
			unsyncedKeys.Contents = contents
		}
		c.unsyncedKeys.Store(serial.String(), unsyncedKeys)
		unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(float64(len(unsyncedKeys.Contents)))
		return unsyncedKeys
	}

	unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(float64(len(unsyncedKeys.Contents)))
	return unsyncedKeys
}

func (c *ConfigCache) GetUnsyncedKeys(serial *carbon.ValidSerial) []config.UnsyncedKey {
	mu := c.keyedMutex.Get(serial.String())
	mu.RLock()
	defer mu.RUnlock()

	unsyncedKeys := c.getUnsyncedKeys(serial)

	unsyncedKeysCopy := make([]config.UnsyncedKey, len(unsyncedKeys.Contents))
	copy(unsyncedKeysCopy, unsyncedKeys.Contents)
	return unsyncedKeysCopy
}

func (c *ConfigCache) refreshUnsyncedKeysCache(serial *carbon.ValidSerial) error {
	// ensure writes can't occur while we are refreshing
	cacheEntry := c.getUnsyncedKeys(serial)

	unsyncedKeys, err := c.fetchUnsyncedKeysFromS3(c.s3Facade, c.s3Bucket, serial)
	if err != nil {
		return err
	}

	cacheEntry.Contents = unsyncedKeys
	c.unsyncedKeys.Store(serial.String(), cacheEntry)
	unsyncedKeysPerRobot.WithLabelValues(serial.String()).Set(float64(len(unsyncedKeys)))
	return nil
}

func (c *ConfigCache) ClearUnsyncedKeys(serial *carbon.ValidSerial) error {
	mu := c.keyedMutex.Get(serial.String())
	mu.Lock()
	defer mu.Unlock()

	unsyncedKeys := c.getUnsyncedKeys(serial)
	if len(unsyncedKeys.Contents) != 0 {
		return c.writeUnsyncedKeys(serial, []config.UnsyncedKey{})
	}
	return nil
}
