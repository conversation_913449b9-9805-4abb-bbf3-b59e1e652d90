package grpc

import (
	"context"
	"fmt"
	"path"
	"slices"
	"strings"
	"time"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	cloudconfig "github.com/carbonrobotics/cloud/golang/pkg/carbon/configs"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"

	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

const (
	// robotMetadataKey should be the robot serial set by the config service.
	robotMetadataKey = "robot"
)

type ConfigCache interface {
	GetConfigNodeFromPath(ctx context.Context, serial *carbon.ValidSerial, path string) (*config_service.ConfigNode, error)
	UpdateConfigNode(ctx context.Context, serial *carbon.ValidSerial, key string, value any, ts uint64, enforceSchemaChoices bool) error
	FillTree(ctx context.Context, node *config_service.ConfigNode, serial *carbon.ValidSerial, key string) error
	AddToList(ctx context.Context, serial *carbon.ValidSerial, path, name string, ts uint64) error
	RemoveFromList(ctx context.Context, serial *carbon.ValidSerial, configPath string, ts uint64) error
	AddUnsyncedKeys(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)
	AddUnsyncedListRemovals(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)
	ReconcileUnsyncedListRemovals(serial *carbon.ValidSerial, absolutPath string) error
	ClearUnsyncedKeys(serial *carbon.ValidSerial) error

	GetTemplateConfigNodeFromPath(ctx context.Context, class carbon.Classification, path string) (*config_service.ConfigNode, error)
}

type StreamCache interface {
	AddStream(streamID string, serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer) *cache.StreamWithContext
	GetActiveStream(serial *carbon.ValidSerial) (*cache.StreamWithContext, bool)
	NotifyRobot(serial *carbon.ValidSerial, key string, ts uint64) (success bool)
	Delete(streamID string)
}

type ConfigService struct {
	config_service.UnimplementedConfigServiceServer // Embed the unimplemented server

	auditLogger *audit.Logger
	configCache ConfigCache //*cache.ConfigCache
	streamCache StreamCache //*cache.StreamCache
}

func NewConfigService(server *grpc.Server, auditLogger *audit.Logger, configCache *cache.ConfigCache, streamCache *cache.StreamCache) *ConfigService {
	s := &ConfigService{
		auditLogger: auditLogger,
		configCache: configCache,
		streamCache: streamCache,
	}
	config_service.RegisterConfigServiceServer(server, s)
	return s
}

/** START BOILERPLATE CODE **/
/**
 * For all handlers, Key contains a path prefixed with the robot class and serial
 * ex. slayers/slayer65/common/almanac/point_categories/broadleaf/enabled
 * In the case of a config template, the key is prefixed with the template name
 * ex. slayers/Template
 *
 * NOTE: GRPC handlers expect that requests about a config are coming from the
 * robot that owns the config. This is not currently enforced. While testing,
 * if you grpcurl an endpoint in RoSy, the robot will not be notified of the
 * change. Eventually we will add a check that verifies the robot serial in the
 * context matches the robot serial in the key.
 **/

func (service *ConfigService) Ping(ctx context.Context, req *config_service.PingRequest) (*config_service.PongResponse, error) {
	robot := GetRobotFromContext(ctx)
	log.Infof("Received ping with x=%d robot=%s", req.GetX(), robot)
	return &config_service.PongResponse{X: req.GetX()}, nil
}

func (service *ConfigService) SetValue(ctx context.Context, req *config_service.SetValueRequest) (*config_service.SetValueResponse, error) {
	robot := GetRobotFromContext(ctx)
	key := req.GetKey()
	value := req.GetValue()
	valueString := cloudconfig.ConfigValueToString(value)
	log.Debugf("Received SetValue with robot=%s, key=%s, value=%v", robot, key, value)

	configKey, err := validateConfigKey(key)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	if isTemplateKey(configKey) {
		return nil, status.Errorf(codes.InvalidArgument, "cannot modify a template via gRPC API -- use Ops Center")
	}

	if err := isRobotAllowed(configKey.ValidSerial, robot, "robots are only allowed to modify their own configs"); err != nil {
		return nil, err
	}

	// *Note* value.TimestampMs is never set on robot side for SetValue
	ts := uint64(time.Now().UnixMilli())
	if _, err := ConvertValidateTimestampMs(ts); err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	oldNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
	}
	oldValue := cloudconfig.ConfigValueToString(oldNode.Value)

	err = service.configCache.UpdateConfigNode(ctx, configKey.ValidSerial, configKey.Path, value, ts, false)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "%v", err)
	}

	if ok := service.streamCache.NotifyRobot(configKey.ValidSerial, configKey.Path, ts); !ok {
		service.configCache.AddUnsyncedKeys(
			configKey.ValidSerial,
			[]config.UnsyncedKey{
				{
					Key:      configKey.Path,
					Ts:       ts,
					NewValue: valueString,
					OldValue: oldValue,
				},
			},
		)
	}

	if err = service.auditLogger.LogEvent(&audit.AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     "SetValue",
		Namespace:   constants.ROSY_GRPC_NAMESPACE,
		RobotSerial: configKey.ValidSerial.String(),
		Timestamp:   int64(ts),
		UserID:      getUserIDFromContext(ctx, configKey.ValidSerial.String()),
		Key:         configKey.Path,
		NewValue:    valueString,
		OldValue:    oldValue,
	}); err != nil {
		log.WithError(err).Error("Failed to log event")
	}

	return &config_service.SetValueResponse{}, nil
}

func (service *ConfigService) GetTree(ctx context.Context, req *config_service.GetTreeRequest) (*config_service.GetTreeResponse, error) {
	robot := GetRobotFromContext(ctx)
	key := req.GetKey()
	log.Debugf("Received GetTree with robot=%s, key=%s", robot, key)

	configKey, err := validateConfigKey(key)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	var node *config_service.ConfigNode
	if isTemplateKey(configKey) {
		node, err = service.configCache.GetTemplateConfigNodeFromPath(ctx, configKey.ValidSerial.Class(), configKey.Path)
		if err != nil {
			return nil, status.Errorf(codes.NotFound, "%v", err)
		}
	} else {
		if err := isRobotAllowed(configKey.ValidSerial, robot, "robots are only allowed to get their own configs"); err != nil {
			return nil, err
		}
		node, err = service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
		if err != nil {
			return nil, status.Errorf(codes.NotFound, "%v", err)
		}
	}

	if node == nil {
		return nil, status.Errorf(codes.NotFound, "node not found")
	}

	return &config_service.GetTreeResponse{Node: node}, nil
}

func (service *ConfigService) SetTree(ctx context.Context, req *config_service.SetTreeRequest) (*config_service.SetTreeResponse, error) {
	robot := GetRobotFromContext(ctx)
	key := req.GetKey()
	node := req.GetNode()
	log.Debugf("Received SetTree with robot=%s, key=%s", robot, key)

	configKey, err := validateConfigKey(key)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	if isTemplateKey(configKey) {
		return nil, status.Errorf(codes.InvalidArgument, "cannot modify a template via gRPC API -- use Ops Center")
	}

	if err := isRobotAllowed(configKey.ValidSerial, robot, "robots are only allowed to modify their own configs"); err != nil {
		return nil, err
	}

	ts := node.GetValue().GetTimestampMs()
	if _, err := ConvertValidateTimestampMs(ts); err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	oldNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
	}

	// fetch old value of node before manipulating
	var oldValue string
	if node.Def.Type != config_service.ConfigType_NODE && node.Def.Type != config_service.ConfigType_LIST {
		oldValue = cloudconfig.ConfigValueToString(oldNode.Value)
	}

	// Do not set unsycned keys here as this is where we sync the keys
	if err = service.configCache.FillTree(ctx, node, configKey.ValidSerial, configKey.Path); err != nil {
		return nil, status.Errorf(codes.Internal, "%v", err)
	}

	// if the node is a leaf node, fetch the old value and log the event
	if node.Def.Type != config_service.ConfigType_NODE && node.Def.Type != config_service.ConfigType_LIST {
		postReconciliationNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
		}

		newValue := cloudconfig.ConfigValueToString(postReconciliationNode.Value)
		if err = service.auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "SetTree",
			Namespace:   constants.ROSY_GRPC_NAMESPACE,
			RobotSerial: configKey.ValidSerial.String(),
			Timestamp:   int64(ts),
			UserID:      getUserIDFromContext(ctx, configKey.ValidSerial.String()),
			Key:         configKey.Path,
			NewValue:    newValue,
			OldValue:    oldValue,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		// Only notify if reconciliation resulted in a value other than the
		// supplied value. This indicates that the robot sending the SetTree
		// request needs to re-fetch the value of this node.
		if postReconciliationNode.Value.Value != node.Value.Value {
			if ok := service.streamCache.NotifyRobot(configKey.ValidSerial, configKey.Path, ts); !ok {
				log.Debugf("Failed to notify robot %s of config change", configKey.ValidSerial)
				service.configCache.AddUnsyncedKeys(
					configKey.ValidSerial,
					[]config.UnsyncedKey{
						{
							Key: configKey.Path,
							Ts:  ts,
						},
					},
				)
			}
		}
	} else {
		if err = service.auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "SetTree",
			Namespace:   constants.ROSY_GRPC_NAMESPACE,
			RobotSerial: configKey.ValidSerial.String(),
			Timestamp:   int64(ts),
			UserID:      getUserIDFromContext(ctx, configKey.ValidSerial.String()),
			Key:         configKey.Path,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		if ok := service.streamCache.NotifyRobot(configKey.ValidSerial, configKey.Path, ts); !ok {
			log.Debugf("Failed to notify robot %s of config change", configKey.ValidSerial)
			service.configCache.AddUnsyncedKeys(
				configKey.ValidSerial,
				[]config.UnsyncedKey{
					{
						Key: configKey.Path,
						Ts:  ts,
					},
				},
			)
		} else {
			// successful sync means we can reconcile our unsynced list removals
			absolutPath := path.Join(configKey.ValidSerial.Class().String(), configKey.ValidSerial.String(), key)
			if err := service.configCache.ReconcileUnsyncedListRemovals(configKey.ValidSerial, absolutPath); err != nil {
				log.WithError(err).Errorf("Failed to reconcile unsynced list removals for %s", configKey.ValidSerial)
			}
		}
	}

	return &config_service.SetTreeResponse{}, nil
}

/**
 * NOTE this endpoint is only used by the bot CLI
 **/
func (service *ConfigService) GetLeaves(ctx context.Context, req *config_service.GetLeavesRequest) (*config_service.GetLeavesResponse, error) {
	robot := GetRobotFromContext(ctx)
	key := req.GetKey()
	log.Debugf("Received GetLeaves with robot=%s, key=%s", robot, key)

	configKey, err := validateConfigKey(key)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	var node *config_service.ConfigNode
	if isTemplateKey(configKey) {
		node, err = service.configCache.GetTemplateConfigNodeFromPath(ctx, configKey.ValidSerial.Class(), configKey.Path)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "%v", err)
		}
	} else {
		if err := isRobotAllowed(configKey.ValidSerial, robot, "robots are only allowed to get their own configs"); err != nil {
			return nil, err
		}
		node, err = service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "%v", err)
		}
	}

	return &config_service.GetLeavesResponse{Leaves: config.GetConfigLeaves(node, key)}, nil
}

func (service *ConfigService) AddToList(ctx context.Context, req *config_service.AddToListRequest) (*config_service.AddToListResponse, error) {
	robot := GetRobotFromContext(ctx)
	key := req.GetKey()
	name := req.GetName()
	log.Debugf("Received AddToList with robot=%s, key=%s, name=%s", robot, key, name)

	configKey, err := validateConfigKey(key)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	if isTemplateKey(configKey) {
		return nil, status.Errorf(codes.InvalidArgument, "cannot modify a template via gRPC API -- use Ops Center")
	}

	if err := isRobotAllowed(configKey.ValidSerial, robot, "robots are only allowed to modify their own configs"); err != nil {
		return nil, err
	}

	ts := uint64(time.Now().UnixMilli())
	oldNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
	}
	expectedChildren := append(oldNode.Children, &config_service.ConfigNode{Name: name})

	err = service.configCache.AddToList(ctx, configKey.ValidSerial, configKey.Path, name, ts)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	postReconciliationNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
	}

	// Only notify if reconciliation resulted in a list other than the old list
	// plus the new element. This indicates that the robot sending the AddToList
	// request needs to re-fetch the parent node's list.
	postReconciliationChildren := postReconciliationNode.Children
	if !nodeListsEqual(postReconciliationChildren, expectedChildren) {
		if ok := service.streamCache.NotifyRobot(configKey.ValidSerial, configKey.Path, ts); !ok {
			log.Debugf("Failed to notify robot %s of config change", configKey.ValidSerial)
			service.configCache.AddUnsyncedKeys(
				configKey.ValidSerial,
				[]config.UnsyncedKey{
					{
						Key:      configKey.Path,
						Ts:       ts,
						NewValue: name,
					},
				},
			)
		}
	}

	if err = service.auditLogger.LogEvent(&audit.AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     cache.AddToListOperation,
		Namespace:   constants.ROSY_GRPC_NAMESPACE,
		RobotSerial: configKey.ValidSerial.String(),
		Timestamp:   int64(ts),
		UserID:      getUserIDFromContext(ctx, configKey.ValidSerial.String()),
		Key:         configKey.Path,
		NewValue:    name,
	}); err != nil {
		log.WithError(err).Error("Failed to log event")
	}

	return &config_service.AddToListResponse{}, nil
}

func (service *ConfigService) RemoveFromList(ctx context.Context, req *config_service.RemoveFromListRequest) (*config_service.RemoveFromListResponse, error) {
	robot := GetRobotFromContext(ctx)
	key := req.GetKey()
	name := req.GetName()
	log.Debugf("Received RemoveFromList with robot=%s, key=%s", robot, key)

	configKey, err := validateConfigKey(key)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	if isTemplateKey(configKey) {
		return nil, status.Errorf(codes.InvalidArgument, "cannot modify a template via gRPC API -- use Ops Center")
	}

	if err := isRobotAllowed(configKey.ValidSerial, robot, "robots are only allowed to modify their own configs"); err != nil {
		return nil, err
	}

	ts := uint64(time.Now().UnixMilli())
	oldNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
	}

	expectedChildren := make([]*config_service.ConfigNode, 0)
	for _, child := range oldNode.Children {
		if child.Name != name {
			expectedChildren = append(expectedChildren, child)
		}
	}

	nodePath := path.Join(configKey.Path, name)
	if err = service.configCache.RemoveFromList(ctx, configKey.ValidSerial, nodePath, ts); err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	postReconciliationNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "node not found: %v", err)
	}

	// Only notify if reconciliation resulted in a list other than the old list
	// minus the new element. This indicates that the robot sending the RemoveFromList
	// request needs to re-fetch the parent node's list.
	postReconciliationChildren := postReconciliationNode.Children
	if !nodeListsEqual(postReconciliationChildren, expectedChildren) {
		segments := strings.Split(configKey.Path, "/")
		parentPath := path.Join(segments[:len(segments)-1]...)
		if ok := service.streamCache.NotifyRobot(configKey.ValidSerial, parentPath, ts); !ok {
			log.Debugf("Failed to notify robot %s of config change", configKey.ValidSerial)
			oldNode, err := service.configCache.GetConfigNodeFromPath(ctx, configKey.ValidSerial, configKey.Path)
			if err != nil {
				return nil, status.Errorf(codes.InvalidArgument, "%v", err)
			}

			service.configCache.AddUnsyncedListRemovals(
				configKey.ValidSerial,
				[]config.UnsyncedKey{
					{
						Key: nodePath,
						Ts:  ts,
					},
				})
			service.configCache.AddUnsyncedKeys(
				configKey.ValidSerial,
				[]config.UnsyncedKey{
					{
						Key:      configKey.Path,
						Ts:       ts,
						OldValue: oldNode.Name,
					},
				})
		}
	}

	if err = service.auditLogger.LogEvent(&audit.AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     cache.RemoveFromListOperation,
		Namespace:   constants.ROSY_GRPC_NAMESPACE,
		RobotSerial: configKey.ValidSerial.String(),
		Timestamp:   int64(ts),
		UserID:      getUserIDFromContext(ctx, configKey.ValidSerial.String()),
		Key:         configKey.Path,
		OldValue:    name,
	}); err != nil {
		log.WithError(err).Error("Failed to log event")
	}

	return &config_service.RemoveFromListResponse{}, nil
}

func (service *ConfigService) UpgradeCloudConfig(ctx context.Context, req *config_service.UpgradeCloudConfigRequest) (*config_service.UpgradeCloudConfigResponse, error) {
	_ = GetRobotFromContext(ctx)
	// left unimplemented as it is not used
	return nil, status.Errorf(codes.Unimplemented, "not implemented by Robot Syncer")
}

/** END BOILERPLATE CODE **/

type ConfigKey struct {
	Path        string
	ValidSerial *carbon.ValidSerial
}

func validateConfigKey(key string) (*ConfigKey, error) {
	errorPrefix := fmt.Sprintf("invalid key: %s", key)
	if len(key) == 0 {
		return nil, fmt.Errorf("%s -- key must not be empty", errorPrefix)
	}

	if key[0] == '/' || key[len(key)-1] == '/' {
		return nil, fmt.Errorf("%s -- key must not start or end with /", errorPrefix)
	}

	segments := strings.Split(key, "/")
	if len(segments) < 2 {
		return nil, fmt.Errorf("%s -- too few segments. Must start with class/serial", errorPrefix)
	}

	// confirm none of the segments are empty
	for _, segment := range segments {
		if len(segment) == 0 {
			return nil, fmt.Errorf("%s -- empty segment found", errorPrefix)
		}
	}

	classification := segments[0]
	serial := segments[1]
	if classification == carbon.ClassReapers.String() && strings.HasPrefix(serial, carbon.MODULE_VALIDATION_STATION_SERIAL_PREFIX) {
		// special case for module validation stations
		classification = carbon.ClassModuleValidationStations.String()
	}

	validSerial, err := carbon.ParseSerialWithClass(serial, classification)
	if err != nil {
		return nil, fmt.Errorf("invalid classification/serial %w", err)
	}

	configPath := path.Join(segments[2:]...)

	return &ConfigKey{
		ValidSerial: validSerial,
		Path:        configPath,
	}, nil
}

func isTemplateKey(configKey *ConfigKey) bool {
	if slices.Contains(carbon.TemplateSerials(), configKey.ValidSerial.String()) || configKey.ValidSerial.String() == "Template" {
		return true
	}

	return false
}

func isRobotAllowed(serial *carbon.ValidSerial, robotSerial, errorFormat string, errorArgs ...any) error {
	if robotSerial == "" {
		// TODO:(smt) no serial provided, Allow for now, until all robots support serial value.
		return nil
	}
	if robotSerial != serial.String() {
		return status.Errorf(codes.PermissionDenied, errorFormat, errorArgs...)
	}
	return nil
}

func getUserIDFromContext(ctx context.Context, serial string) string {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return serial
	}

	userID, ok := md["user_id"]
	if !ok || len(userID) == 0 {
		// TODO SOFTWARE-1985
		// If the user_id is not present in the context, we should retrieve
		// it using the bearer token from the context
		return serial
	}

	return userID[0]
}

func nodeListsEqual(a, b []*config_service.ConfigNode) bool {
	if len(a) != len(b) {
		return false
	}

	for _, child1 := range a {
		foundMatch := false
		for _, child2 := range b {
			if child1.Name == child2.Name {
				foundMatch = true
				break
			}
		}

		if !foundMatch {
			return false
		}
	}

	return true
}

func ConvertValidateTimestampMs(ts uint64) (time.Time, error) {
	tm := time.UnixMilli(int64(ts))
	if time.Since(tm) < 0 {
		return time.Time{}, fmt.Errorf("future timestamps are invalid")
	}
	return tm, nil
}

func GetRobotFromContext(ctx context.Context) string {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if robotArr := md.Get(robotMetadataKey); len(robotArr) > 0 {
			return robotArr[0]
		}
	}
	return ""
}
