package grpc

import (
	"fmt"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
)

type ConfigNotificationService struct {
	config_service.UnimplementedConfigNotificationServiceServer

	streamCache StreamCache //*cache.StreamCache
	configCache ConfigCache //*cache.ConfigCache
}

func NewConfigNotificationService(server *grpc.Server, streamCache *cache.StreamCache, configCache *cache.ConfigCache) *ConfigNotificationService {
	s := &ConfigNotificationService{
		configCache: configCache,
		streamCache: streamCache,
	}
	config_service.RegisterConfigNotificationServiceServer(server, s)
	return s
}

func (s *ConfigNotificationService) Subscribe(req *config_service.SubscriptionRequest, stream config_service.ConfigNotificationService_SubscribeServer) error {
	robot := GetRobotFromContext(stream.Context())
	keys := req.GetKeys()
	log.Infof("Attempt to subscribe robot=%s, (%d) keys=%v", robot, len(keys), keys)

	if len(keys) == 0 {
		log.Errorf("Attempt to subscribe with no keys")
		return status.Error(codes.InvalidArgument, "no keys provided")
	}
	if len(keys) != 1 {
		log.Errorf("Attempt to subscribe to multiple keys %v", keys)
		return status.Error(codes.InvalidArgument, "only one key is allowed per subscription")
	}

	key := keys[0]
	configKey, err := validateConfigKey(key)
	if err != nil {
		log.WithError(err).Errorf("Attempt to subscribe to invalid key %s", key)
		return status.Error(codes.InvalidArgument, fmt.Sprintf("invalid key %v", err))
	}

	if isTemplateKey(configKey) {
		log.Errorf("Attempt to subscribe to template key %s", key)
		return status.Error(codes.InvalidArgument, "cannot subscribe to a template")
	}

	if configKey.Path != "" {
		log.Errorf("Attempt to subscribe to non-root key %s", key)
		return status.Error(codes.InvalidArgument, "can only subscribe to robot config root key of the form <class>/<serial>")
	}

	if err := isRobotAllowed(configKey.ValidSerial, robot, "robot only allowed to subscribe to its own config"); err != nil {
		return err
	}

	s.handleSubscriptionStream(configKey.ValidSerial, stream)
	return nil
}

func (s *ConfigNotificationService) handleSubscriptionStream(serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer) {
	/**
	 * Immediately after subscribing, the client robot's config service
	 * also makes 1 or more unary calls to Robot Syncer. It first calls GetTree
	 * and uses the result to reconcile its local config. If there were changes
	 * in the local tree that were more recent than the remote tree, it calls
	 * SetTree for each leaf or list with changes, which updates the config
	 * stored in Robot Syncer.
	 *
	 * The combination of these calls means that the robot config and cloud
	 * config are in sync. At that point, we can remove all unsynced keys
	 * from our cache. Note that, while the robot is online, the stream
	 * resets every 15 minutes by the robot.
	 **/
	streamID := uuid.NewString()

	streamWithContext := s.streamCache.AddStream(streamID, serial, stream)
	connectedStreamsPerRobot.WithLabelValues(serial.String()).Inc()
	robotStreamsTotal.Inc()
	defer func() {
		s.streamCache.Delete(streamID)
		connectedStreamsPerRobot.WithLabelValues(serial.String()).Dec()
		robotStreamsTotal.Dec()
	}()

	if err := s.configCache.ClearUnsyncedKeys(serial); err != nil {
		log.WithError(err).Errorf("Failed to clear unsynced keys for %s", serial)
	}
	streamWithContext.Run(stream.Context())
}
