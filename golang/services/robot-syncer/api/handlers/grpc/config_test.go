package grpc

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"
)

func TestValidateConfigKey(t *testing.T) {
	t.<PERSON>()
	tests := []struct {
		name    string
		key     string
		class   string
		path    string
		serial  string
		wantErr bool
	}{
		{
			name:    "empty key",
			key:     "",
			class:   "",
			serial:  "",
			path:    "",
			wantErr: true,
		},
		{
			name:    "key with leading slash",
			key:     "/slayers/slayer65",
			class:   "",
			serial:  "",
			path:    "",
			wantErr: true,
		},
		{
			name:    "key with trailing slash",
			key:     "slayers/slayer65/",
			class:   "",
			serial:  "",
			path:    "",
			wantErr: true,
		},
		{
			name:    "key with leading and trailing slash",
			key:     "/slayers/slayer65/",
			class:   "",
			serial:  "",
			path:    "",
			wantErr: true,
		},
		{
			name:    "key with consecutive slashes",
			key:     "slayers//slayer65",
			class:   "",
			serial:  "",
			path:    "",
			wantErr: true,
		},
		{
			name:    "valid key",
			key:     "slayers/slayer65",
			class:   "slayers",
			serial:  "slayer65",
			path:    "",
			wantErr: false,
		},
		{
			name:    "valid longer key",
			key:     "slayers/slayer65/common/almanac/point_categories/broadleaf/enabled",
			class:   "slayers",
			serial:  "slayer65",
			path:    "common/almanac/point_categories/broadleaf/enabled",
			wantErr: false,
		},
		{
			name:    "mvs key",
			key:     "reapers/mvs1",
			class:   "module_validation_stations",
			serial:  "mvs1",
			path:    "",
			wantErr: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			configKey, err := validateConfigKey(test.key)
			if test.wantErr {
				if err == nil {
					t.Fatal("expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			assert.Equal(t, configKey.ValidSerial.Class().String(), test.class)
			assert.Equal(t, configKey.ValidSerial.String(), test.serial)
			assert.Equal(t, configKey.Path, test.path)
		})
	}
}

func TestNodeListsEqual(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name      string
		list1     []*config_service.ConfigNode
		list2     []*config_service.ConfigNode
		wantEqual bool
	}{
		{
			name:      "both empty",
			list1:     []*config_service.ConfigNode{},
			list2:     []*config_service.ConfigNode{},
			wantEqual: true,
		},
		{
			name:  "one empty",
			list1: []*config_service.ConfigNode{},
			list2: []*config_service.ConfigNode{{
				Name: "test",
			}},
			wantEqual: false,
		},
		{
			name: "both have one element",
			list1: []*config_service.ConfigNode{{
				Name: "test",
			}},
			list2: []*config_service.ConfigNode{{
				Name: "test",
			}},
			wantEqual: true,
		},
		{
			name: "both have different elements",
			list1: []*config_service.ConfigNode{{
				Name: "test",
			}},
			list2: []*config_service.ConfigNode{{
				Name: "test2",
			}},
			wantEqual: false,
		},
		{
			name: "equal elements out of order",
			list1: []*config_service.ConfigNode{{
				Name: "test",
			}, {
				Name: "test2",
			}},
			list2: []*config_service.ConfigNode{{
				Name: "test2",
			}, {
				Name: "test",
			}},
			wantEqual: true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, nodeListsEqual(test.list1, test.list2), test.wantEqual)
		})
	}
}

func TestConvertValidateTimestampMs(t *testing.T) {
	testTime := time.Now()
	futureTimestamp := testTime.Add(time.Hour)
	pastTimestamp := testTime.Add(-time.Hour)
	tests := []struct {
		name              string
		ts                uint64
		expectedTimestamp time.Time
		expectError       bool
	}{
		{
			"happy path",
			uint64(testTime.UnixMilli()),
			testTime,
			false,
		},
		{
			"future timestamp",
			uint64(futureTimestamp.UnixMilli()),
			futureTimestamp,
			true,
		},
		{
			"past timestamp",
			uint64(pastTimestamp.UnixMilli()),
			pastTimestamp,
			false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			tm, err := ConvertValidateTimestampMs(test.ts)
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedTimestamp.UnixMilli(), tm.UnixMilli(), "timestamp mismatch")
			}
		})
	}
}
