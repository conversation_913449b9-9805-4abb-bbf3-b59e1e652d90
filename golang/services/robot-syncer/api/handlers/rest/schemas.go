package rest

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/schema"
)

func GetSchemaHandler(schemaReader *schema.Reader) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		rawClass := ctx.Param("class")
		errorPrefix := fmt.Sprintf("failed to get schema for %q", rawClass)

		class, err := carbon.ParseRobotClass(rawClass)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "")
			return
		}

		schemaNode, err := schemaReader.GetConfigSchema(class)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "")
		}

		ReturnProtoJSON(ctx, schemaNode, errorPrefix)
	}
}

func RegisterSchemasRoutes(router *gin.RouterGroup, schemaReader *schema.Reader) {
	configs := router.Group("/schemas")
	{
		configs.GET("/:class", GetSchemaHandler(schemaReader))
	}
}
