package rest

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	cloudconfig "github.com/carbonrobotics/cloud/golang/pkg/carbon/configs"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

func GetTemplateHandler(configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		class := ctx.Param("class")
		errorPrefix := fmt.Sprintf("failed to get template for %s", class)

		robotClass, err := carbon.ParseRobotClass(class)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "")
			return
		}

		jsonData, err := configCache.GetTemplateJSON(robotClass)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "Failed to marshal template")
			return
		}

		ctx.Data(http.StatusOK, "application/json", jsonData)
	}
}

func SetTemplateValueHandler(auditLogger *audit.Logger, configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		class := ctx.Param("class")
		errorPrefix := fmt.Sprintf("failed to update %s template", class)
		input := struct {
			Class     string `url:"class"`
			Path      string `url:"path"`
			Timestamp int64  `json:"timestamp"`
			UserID    string `json:"userId"`
			Value     any    `json:"value"`
		}{}
		if err := ctx.Bind(&input); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		robotClass, err := carbon.ParseRobotClass(class)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		oldNode, err := configCache.GetTemplateConfigNodeFromPath(ctx, robotClass, input.Path)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to get path from template")
			return
		}

		if oldNode.Def.Type == config_service.ConfigType_LIST {
			// value is used as name to add to list
			name, ok := input.Value.(string)
			if !ok {
				ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "invalid name of list node")
				return
			}

			if err := configCache.AddToTemplateList(ctx, robotClass, input.Path, name, uint64(input.Timestamp)); err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, err.Error())
				return
			}

			if err = auditLogger.LogEvent(&audit.AuditLogMessage{
				Level:       strings.ToUpper(log.InfoLevel.String()),
				Message:     "AddToTemplateList",
				Namespace:   constants.ROSY_SERVICE_NAMESPACE,
				RobotSerial: robotClass.String(),
				Timestamp:   input.Timestamp,
				UserID:      input.UserID,
				Key:         input.Path,
				NewValue:    name,
			}); err != nil {
				log.WithError(err).Error("Failed to log event")
			}

			ctx.JSON(http.StatusOK, gin.H{})
			return
		}

		if err := configCache.UpdateTemplateNode(ctx, robotClass, input.Path, input.Value, uint64(input.Timestamp)); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "update failed")
			return
		}

		oldValue := cloudconfig.ConfigValueToString(oldNode.Value)
		newValue := cloudconfig.AnyToString(input.Value)
		if err = auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "UpdateTemplateNode",
			Namespace:   constants.ROSY_SERVICE_NAMESPACE,
			RobotSerial: robotClass.String(),
			Timestamp:   input.Timestamp,
			UserID:      input.UserID,
			Key:         input.Path,
			NewValue:    newValue,
			OldValue:    oldValue,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func RemoveFromTemplateList(auditLogger *audit.Logger, configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		class := ctx.Param("class")
		errorPrefix := fmt.Sprintf("failed to remove from list for %s", class)
		input := struct {
			Class     string `url:"class"`
			Path      string `json:"path"`
			Timestamp int64  `json:"timestamp"`
			UserID    string `json:"userId"`
		}{}
		if err := ctx.Bind(&input); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		robotClass, err := carbon.ParseRobotClass(class)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		if err := configCache.RemoveFromTemplateList(ctx, robotClass, input.Path, uint64(input.Timestamp)); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "remove failed")
			return
		}

		if err = auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "RemoveFromTemplateList",
			Namespace:   constants.ROSY_SERVICE_NAMESPACE,
			RobotSerial: robotClass.String(),
			Timestamp:   input.Timestamp,
			UserID:      input.UserID,
			Key:         input.Path,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func RefreshTemplateCache(auditLogger *audit.Logger, configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		class := ctx.Param("class")
		errorPrefix := fmt.Sprintf("failed to refresh config template cache for class %s", class)
		input := struct {
			UserID string `json:"userId"`
		}{}
		if err := ctx.Bind(&input); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		robotClass, err := carbon.ParseRobotClass(class)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		if err := configCache.RefreshTemplateCache(ctx, robotClass); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to invalidate cache")
			return
		}

		if err := auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "RefreshTemplateCache",
			Namespace:   constants.ROSY_SERVICE_NAMESPACE,
			RobotSerial: robotClass.String(),
			Timestamp:   time.Now().UnixMilli(),
			UserID:      input.UserID,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func RegisterTemplatesRoutes(router *gin.RouterGroup, auditLogger *audit.Logger, configCache ConfigCache) {
	configs := router.Group("/templates")
	{
		configs.GET("/:class", GetTemplateHandler(configCache))
		configs.POST("/:class", SetTemplateValueHandler(auditLogger, configCache))
		configs.DELETE("/:class", RemoveFromTemplateList(auditLogger, configCache))
		configs.DELETE("/:class/cache", RefreshTemplateCache(auditLogger, configCache))
	}
}
