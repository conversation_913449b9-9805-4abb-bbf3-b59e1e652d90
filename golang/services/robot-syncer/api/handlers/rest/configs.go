package rest

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"path"
	"slices"
	"strings"
	"time"

	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/crgo/config/config"
	"github.com/carbonrobotics/protos/golang/generated/proto/config_service"

	cloudconfig "github.com/carbonrobotics/cloud/golang/pkg/carbon/configs"
	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

type ConfigCache interface {
	SetConfig(ctx context.Context, serial *carbon.ValidSerial, jsondata []byte) error
	GetRobotConfigRootJSON(ctx context.Context, serial *carbon.ValidSerial) ([]byte, error)
	GetConfigNodeFromPath(ctx context.Context, serial *carbon.ValidSerial, path string) (*config_service.ConfigNode, error)
	UpdateConfigNode(ctx context.Context, serial *carbon.ValidSerial, key string, value any, ts uint64, enforceSchemaChoices bool) error
	FillTree(ctx context.Context, node *config_service.ConfigNode, serial *carbon.ValidSerial, key string) error
	AddToList(ctx context.Context, serial *carbon.ValidSerial, path, name string, ts uint64) error
	RemoveFromList(ctx context.Context, serial *carbon.ValidSerial, configPath string, ts uint64) error
	AddUnsyncedKeys(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)
	AddUnsyncedListRemovals(serial *carbon.ValidSerial, unsyncedKeys []config.UnsyncedKey)
	ReconcileUnsyncedListRemovals(serial *carbon.ValidSerial, absolutPath string) error
	ClearUnsyncedKeys(serial *carbon.ValidSerial) error
	RefreshCache(ctx context.Context, serial *carbon.ValidSerial) error
	GetConfigVersion(ctx context.Context, serial *carbon.ValidSerial, timestamp time.Time) (*config_service.ConfigNode, error)
	RestoreConfigToVersion(ctx context.Context, params cache.RestoreConfigToVersionParameters) ([]*robot_syncer.EditRecord, error)
	BulkEditConfig(ctx context.Context, params cache.BulkEditConfigParameters) ([]*robot_syncer.EditRecord, error)
	GetUnsyncedKeys(serial *carbon.ValidSerial) []config.UnsyncedKey

	CreateRobotConfigFromTemplate(ctx context.Context, serial, copyFromSerial *carbon.ValidSerial, ts int64) error
	GetTemplateConfigNodeFromPath(ctx context.Context, class carbon.Classification, path string) (*config_service.ConfigNode, error)
	GetTemplateJSON(class carbon.Classification) ([]byte, error)
	AddToTemplateList(ctx context.Context, class carbon.Classification, path, name string, ts uint64) error
	UpdateTemplateNode(ctx context.Context, class carbon.Classification, key string, value any, ts uint64) error
	RemoveFromTemplateList(ctx context.Context, class carbon.Classification, configPath string, ts uint64) error
	RefreshTemplateCache(ctx context.Context, class carbon.Classification) error
}

type ProfileCache interface {
	GetCustomerProfile(profileId uuid.UUID) (*rosy.Profile, bool)
	GetCustomerProfiles(customerId uuid.UUID, profileType *frontend.ProfileType) ([]*rosy.Profile, error)
	GetProfileSyncData(customerId uuid.UUID) (*robot_syncer.GetProfileSyncDataResponse, error)
	SaveCustomerProfile(profile *rosy.Profile) error
	DeleteCustomerProfile(profileId uuid.UUID) error
	GetCarbonProvidedProfile(profileId uuid.UUID) (*rosy.Profile, bool)
}

type StreamCache interface {
	AddStream(streamID string, serial *carbon.ValidSerial, stream config_service.ConfigNotificationService_SubscribeServer) *cache.StreamWithContext
	GetActiveStream(serial *carbon.ValidSerial) (*cache.StreamWithContext, bool)
	NotifyRobot(serial *carbon.ValidSerial, key string, ts uint64) (success bool)
	Delete(streamID string)
}

func GetConfigHandler(configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")
		errorPrefix := fmt.Sprintf("failed to get config for %s", serial)

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serial")
			return
		}

		jsonData, err := configCache.GetRobotConfigRootJSON(ctx, validSerial)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to marshal config")
			return
		}

		ctx.Data(http.StatusOK, "application/json", jsonData)
	}
}

func SetConfigValueHandler(auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")
		errorPrefix := fmt.Sprintf("failed to update %s config", serial)
		input := struct {
			Serial    string `url:"serial"`
			Path      string `url:"path"`
			Timestamp int64  `json:"timestamp"`
			UserID    string `json:"userId"`
			Value     any    `json:"value"`
		}{}
		if err := ctx.Bind(&input); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		if input.Serial != serial {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "serial mismatch")
		}

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serial")
			return
		}

		oldNode, err := configCache.GetConfigNodeFromPath(ctx, validSerial, input.Path)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "path not found")
			return
		}

		if oldNode.Def.Type == config_service.ConfigType_LIST {
			// value is used as name to add to list
			name := cloudconfig.AnyToString(input.Value)
			if err := addToConfigList(ctx, auditLogger, configCache, streamCache, validSerial, uint64(input.Timestamp), input.Path, name, input.UserID); err != nil {
				ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "add to list failed")
				return
			}

			ctx.JSON(http.StatusOK, gin.H{})
			return
		}

		oldValue := cloudconfig.ConfigValueToString(oldNode.Value)
		if err := updateConfigValue(ctx, auditLogger, configCache, streamCache, validSerial, uint64(input.Timestamp), input.Path, oldValue, input.UserID, input.Value); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "update failed")
			return
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func updateConfigValue(ctx *gin.Context, auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache, serial *carbon.ValidSerial, timestamp uint64, key, oldValue, userID string, value any) error {
	if err := configCache.UpdateConfigNode(ctx, serial, key, value, timestamp, true); err != nil {
		return err
	}

	if err := auditLogger.LogEvent(&audit.AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     cache.UpdateConfigNodeOperation,
		Namespace:   constants.ROSY_SERVICE_NAMESPACE,
		RobotSerial: serial.String(),
		Timestamp:   int64(timestamp),
		UserID:      userID,
		Key:         key,
		NewValue:    cloudconfig.AnyToString(value),
		OldValue:    oldValue,
	}); err != nil {
		log.WithError(err).Error("Failed to log event")
	}

	if ok := streamCache.NotifyRobot(serial, key, timestamp); !ok {
		log.Debugf("Failed to notify robot %s of config change", serial)
		configCache.AddUnsyncedKeys(
			serial,
			[]config.UnsyncedKey{
				{
					Key:      key,
					Ts:       timestamp,
					NewValue: cloudconfig.AnyToString(value),
					OldValue: oldValue,
				},
			},
		)
	}
	return nil
}

func addToConfigList(ctx *gin.Context, auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache, serial *carbon.ValidSerial, timestamp uint64, key, name, userID string) error {
	if err := configCache.AddToList(ctx, serial, key, name, timestamp); err != nil {
		return err
	}

	if err := auditLogger.LogEvent(&audit.AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     cache.AddToListOperation,
		Namespace:   constants.ROSY_SERVICE_NAMESPACE,
		RobotSerial: serial.String(),
		Timestamp:   int64(timestamp),
		UserID:      userID,
		Key:         key,
		NewValue:    name,
	}); err != nil {
		log.WithError(err).Error("Failed to log event")
	}

	if ok := streamCache.NotifyRobot(serial, key, timestamp); !ok {
		log.Debugf("Failed to notify robot %s of config change", serial)
		configCache.AddUnsyncedKeys(
			serial,
			[]config.UnsyncedKey{
				{
					Key:      key,
					Ts:       timestamp,
					NewValue: name,
				},
			},
		)
	}
	return nil
}

func RemoveFromConfigList(auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")
		errorPrefix := fmt.Sprintf("failed to remove from list for %s", serial)
		input := struct {
			Serial    string `url:"serial"`
			Path      string `json:"path"`
			Timestamp int64  `json:"timestamp"`
			UserID    string `json:"userId"`
		}{}
		if err := ctx.Bind(&input); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		if input.Serial != serial {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "serial mismatch")
		}

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serial")
			return
		}

		// save the updated config
		if err := removeFromConfigList(ctx, auditLogger, configCache, streamCache, validSerial, uint64(input.Timestamp), input.Path, input.UserID); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "write failed")
			return
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func removeFromConfigList(ctx *gin.Context, auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache, serial *carbon.ValidSerial, timestamp uint64, key, userID string) error {
	if err := configCache.RemoveFromList(ctx, serial, key, timestamp); err != nil {
		return err
	}

	if err := auditLogger.LogEvent(&audit.AuditLogMessage{
		Level:       strings.ToUpper(log.InfoLevel.String()),
		Message:     cache.RemoveFromListOperation,
		Namespace:   constants.ROSY_SERVICE_NAMESPACE,
		RobotSerial: serial.String(),
		Timestamp:   int64(timestamp),
		UserID:      userID,
		Key:         key,
	}); err != nil {
		log.WithError(err).Error("Failed to log event")
	}

	segments := strings.Split(key, "/")
	parentPath := path.Join(segments[:len(segments)-1]...)

	if ok := streamCache.NotifyRobot(serial, parentPath, timestamp); !ok {
		log.Debugf("Failed to notify robot %s of config change", serial)
		configCache.AddUnsyncedKeys(
			serial,
			[]config.UnsyncedKey{
				{
					Key: parentPath,
					Ts:  timestamp,
				},
			},
		)
		configCache.AddUnsyncedListRemovals(
			serial,
			[]config.UnsyncedKey{
				{
					Key: key,
					Ts:  timestamp,
				},
			},
		)
	}
	return nil
}

// TODO remove this endpoint after Phase 2 of the migration to Robot Syncer
func SetConfigHandler(auditLogger *audit.Logger, configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid serial")
			return
		}

		// unmarshal configNode from request body
		body := ctx.Request.Body
		defer body.Close()

		bodyBytes, err := io.ReadAll(body)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "failed to read request body")
			return
		}

		if err := configCache.SetConfig(ctx, validSerial, bodyBytes); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to set config")
			return
		}

		if err := auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.WarnLevel.String()),
			Message:     "SetConfig",
			Namespace:   constants.ROSY_SERVICE_NAMESPACE,
			RobotSerial: validSerial.String(),
			Timestamp:   time.Now().UnixMilli(),
			UserID:      constants.ROSY_SERVICE_NAMESPACE,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		// if this ends up staying, we need to add a robot notification here

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func CreateConfigHandler(auditLogger *audit.Logger, configCache ConfigCache) gin.HandlerFunc {
	errorPrefix := "failed to create robot config"
	return func(ctx *gin.Context) {
		input := struct {
			CopyFrom  string `json:"copyFrom"`
			Serial    string `json:"serial"`
			Timestamp int64  `json:"timestamp"`
			UserID    string `json:"userId"`
		}{}
		err := ctx.Bind(&input)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		serial, err := carbon.ParseSerial(input.Serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, fmt.Sprintf("invalid serial: %s", input.Serial))
			return
		}

		// either template or validCopyFromSerial gets populated depending on
		// whether we are copying from a template or another robot's config
		var validCopyFromSerial *carbon.ValidSerial
		if input.CopyFrom == "" {
			log.Infof("Empty copyFrom serial, using class %s template", serial.Class())
		} else {
			if slices.Contains(carbon.TemplateSerials(), input.CopyFrom) {
				templateName := carbon.GetTemplateSerialByClass(serial.Class())
				if templateName.String() != input.CopyFrom {
					ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, fmt.Sprintf("mismatched template name %s for class %s (%s)", input.CopyFrom, serial.Class(), templateName))
					return
				}
			} else {
				validCopyFromSerial, err = carbon.ParseSerialWithClass(input.CopyFrom, serial.Class().String())
				if err != nil {
					ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid copyFrom serial")
					return
				}
			}
		}

		if err = configCache.CreateRobotConfigFromTemplate(ctx, serial, validCopyFromSerial, input.Timestamp); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to create new robot config")
			return
		}

		if err := auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "CreateRobotConfig",
			Namespace:   constants.ROSY_SERVICE_NAMESPACE,
			RobotSerial: serial.String(),
			Timestamp:   input.Timestamp,
			UserID:      input.UserID,
			OldValue:    input.CopyFrom,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func RefreshCache(auditLogger *audit.Logger, configCache ConfigCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		serial := ctx.Param("serial")
		errorPrefix := fmt.Sprintf("failed to refresh config cache for %s", serial)
		input := struct {
			UserID string `json:"userId"`
		}{}
		if err := ctx.Bind(&input); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "invalid serial")
			return
		}

		if err := configCache.RefreshCache(ctx, validSerial); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to invalidate cache")
			return
		}

		if err := auditLogger.LogEvent(&audit.AuditLogMessage{
			Level:       strings.ToUpper(log.InfoLevel.String()),
			Message:     "RefreshCache",
			Namespace:   constants.ROSY_SERVICE_NAMESPACE,
			RobotSerial: validSerial.String(),
			Timestamp:   time.Now().UnixMilli(),
			UserID:      input.UserID,
		}); err != nil {
			log.WithError(err).Error("Failed to log event")
		}

		ctx.JSON(http.StatusOK, gin.H{})
	}
}

func RegisterRobotConfigRoutes(
	router *gin.RouterGroup,
	auditLogger *audit.Logger,
	configCache ConfigCache,
	streamCache StreamCache,
) {
	configs := router.Group("/configs")
	{
		configs.POST("/", CreateConfigHandler(auditLogger, configCache))
		configs.GET("/:serial", GetConfigHandler(configCache))
		configs.PUT("/:serial", SetConfigHandler(auditLogger, configCache))
		configs.POST("/:serial", SetConfigValueHandler(auditLogger, configCache, streamCache))
		configs.DELETE("/:serial/cache", RefreshCache(auditLogger, configCache))
		configs.DELETE("/:serial", RemoveFromConfigList(auditLogger, configCache, streamCache))
		configs.POST("/bulk_update", BulkUpdateConfigsHandler(auditLogger, configCache, streamCache))
		configs.POST("/restore_to_version", RestoreConfigToVersionHandler(auditLogger, configCache, streamCache))
	}
}
