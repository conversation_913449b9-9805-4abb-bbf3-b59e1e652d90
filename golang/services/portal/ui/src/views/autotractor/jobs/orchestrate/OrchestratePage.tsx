import { But<PERSON>, CircularProgress, Typography } from "@mui/material";
import { capitalize } from "portal/utils/strings";
import { DetailsPanel } from "./DetailsPanel";
import { FullViewportPage } from "portal/components/Page";
import { Header } from "portal/components/header/Header";
import { Job, ObjectiveAssignment, State } from "protos/rtc/jobs";
import { Navigate, useParams } from "react-router-dom";
import { OrchestrationMap } from "./OrchestrationMap";
import { Path } from "portal/utils/routing";
import { SelectionPanel } from "./SelectionPanel";
import {
  setAssignmentsByTractorSerial,
  setFarm,
  setJob,
} from "common/state/jobExplorer";
import { skipToken } from "@reduxjs/toolkit/query";
import {
  useDeviceLocations,
  useTractorLocations,
} from "common/hooks/useLiveRtcLocation";
import { useDispatch } from "react-redux";
import { useGetFarmQuery } from "common/state/portalApi/farm";
import {
  useGetJobQuery,
  useListAssignmentsQuery,
} from "common/state/rtcJobsApi";
import { useJobExplorer } from "portal/state/store";
import { useListRobotsQuery } from "common/state/rtcLocatorApi";
import { useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import React, { FC, useEffect, useMemo, useState } from "react";
import TaskIcon from "@mui/icons-material/FormatListBulleted";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";

export const OrchestratePage: FC = () => {
  const { t } = useTranslation();
  const { jobId } = useParams();
  const {
    data: job,
    isLoading,
    isSuccess,
    isError,
  } = useQueryPopups(
    useGetJobQuery(jobId ? { id: jobId } : skipToken, {
      pollingInterval: 5000,
      refetchOnMountOrArgChange: 5,
    })
  );
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setJob(job));
  }, [job, dispatch]);

  const { data: assignments } = useQueryPopups(
    useListAssignmentsQuery({
      objectiveIds: job?.objectives.map((o) => `${o.id}`) ?? [],
    })
  );

  useEffect(() => {
    const assignmentsByTractorId: Record<string, ObjectiveAssignment[]> = {};

    for (const assignment of assignments?.assignments ?? []) {
      const currentList = assignmentsByTractorId[assignment.robotSerial];
      if (currentList) {
        assignmentsByTractorId[assignment.robotSerial]?.push(assignment);
      } else {
        assignmentsByTractorId[assignment.robotSerial] = [assignment];
      }
    }

    dispatch(setAssignmentsByTractorSerial(assignmentsByTractorId));
  }, [assignments, dispatch]);

  const { data: availableTractors } = useQueryPopups(
    useListRobotsQuery(
      job
        ? {
            serials: job.robotWhitelist?.entries.map((r) => r.robotSerial),
          }
        : skipToken
    )
  );

  const tractorSerials = useMemo(
    () =>
      availableTractors ? availableTractors.robots.map((r) => r.serial) : [],
    [availableTractors]
  );

  useTractorLocations(tractorSerials);

  const { data: farm } = useQueryPopups(
    useGetFarmQuery(job ? job.farmId : skipToken)
  );
  useEffect(() => {
    dispatch(setFarm(farm));
  }, [farm, dispatch]);

  // todo: re-evaluate if we move to specifying co-located zones
  const field = farm?.zones.find((z) => z.id?.id === job?.fieldId);
  const centerPivotId = field?.contents?.field?.centerPivot?.endpointDeviceId;

  useDeviceLocations(centerPivotId ? [centerPivotId] : []);

  if (job?.state === State.PENDING) {
    return <Navigate to={`${Path.JOBS}/${job.id}`} />;
  }

  if (isError) {
    return <Navigate to={`${Path.JOBS}`} />;
  }
  return (
    <>
      <Header
        title={capitalize(t("models.jobs.job_one"))}
        pageTitle={`${
          job?.name ?? t("utils.descriptors.loading")
        } - ${capitalize(t("models.jobs.job_one"))}`}
        parentLink={Path.JOBS}
      />
      <FullViewportPage>
        <div className="flex flex-col h-full">
          {isLoading && <CircularProgress variant="indeterminate" />}
          {isSuccess && <OrchestrateView job={job} />}
        </div>
      </FullViewportPage>
    </>
  );
};

interface OrchestrateViewProps {
  job: Job;
}
const OrchestrateView: FC<OrchestrateViewProps> = ({ job }) => {
  const { showDetails } = useJobExplorer();
  const [showAllTasks, setShowAllTasks] = useState<boolean>(false);

  return (
    <FullViewportPage>
      <div className="flex flex-col w-full h-full pt-20 pb-10">
        <div className="flex gap-4">
          <Typography variant="h3">{job.name}</Typography>
          <Button
            variant="contained"
            onClick={() => setShowAllTasks(!showAllTasks)}
            className="flex my-auto"
            startIcon={
              showAllTasks ? <VisibilityOffIcon /> : <VisibilityIcon />
            }
          >
            <TaskIcon />
          </Button>
        </div>
        <div className="flex w-full h-full overflow-y-hidden relative">
          <div className="flex w-1/5 h-full">
            <SelectionPanel />
          </div>
          <div className="flex w-full h-full">
            <OrchestrationMap job={job} showAllTasks={showAllTasks} />
          </div>
          {showDetails && (
            <div className="flex w-full h-full max-w-1/4 absolute right-0">
              <DetailsPanel />
            </div>
          )}
        </div>
      </div>
    </FullViewportPage>
  );
};
