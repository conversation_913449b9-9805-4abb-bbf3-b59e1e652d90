import { Chip, Skeleton, Tooltip } from "@mui/material";
import { classes } from "portal/theme/theme";
import { DateTime } from "luxon";
import { formatCompactDate } from "portal/utils/dates";
import { t } from "i18next";
import { ThumbnailImage, ThumbnailImageItem } from "./ThumbnailImage";
import { userFacingDate } from "portal/utils/categoryCollectionProfile";
import { useTranslation } from "react-i18next";
import React, {
  FC,
  FunctionComponent,
  PropsWithChildren,
  useCallback,
  useMemo,
  useRef,
} from "react";

type ImageGridItem = ThumbnailImageItem & { capturedAt: number };
export type OptionalImageGridItem = ImageGridItem | undefined;
interface ImageGridProps extends PropsWithChildren {
  loadingItemCount: number;
  loading?: boolean;
  dimensions: {
    width: number;
    height: number;
  };
  thumbnailImages: OptionalImageGridItem[];
  onImagesLoaded?: (loadedCount: number) => void;
  groupByDate?: boolean;
}
export const ImageGrid: FunctionComponent<ImageGridProps> = ({
  loadingItemCount,
  loading,
  thumbnailImages,
  dimensions,
  onImagesLoaded,
  groupByDate = true,
}) => {
  const { i18n } = useTranslation();
  const imagesLoaded = useRef<Set<string>>(new Set());

  const onImageLoaded = useCallback(
    (id: string): void => {
      imagesLoaded.current.add(id);
      onImagesLoaded?.(imagesLoaded.current.size);
    },
    [onImagesLoaded]
  );

  const dateGroupedThumbnails = useMemo(() => {
    const dateGrouped: {
      [group: string]: ImageGridItem[];
    } = {};
    const dateSortedThumbnails = thumbnailImages.sort(
      (a, b) => (b?.capturedAt ?? 0) - (a?.capturedAt ?? 0)
    );
    for (const thumbnail of dateSortedThumbnails) {
      if (!thumbnail?.capturedAt) {
        continue;
      }
      const groupKey: string = String(
        DateTime.fromMillis(thumbnail.capturedAt).startOf("day").toMillis()
      );
      dateGrouped[groupKey] = dateGrouped[groupKey] || [];
      dateGrouped[groupKey].push(thumbnail);
    }
    return dateGrouped;
  }, [thumbnailImages]);

  return groupByDate ? (
    <>
      {Object.entries(dateGroupedThumbnails).map(([date, thumbnails]) => {
        const latestDate = Number(thumbnails[0]?.capturedAt);
        const earliestDate = Number(thumbnails.at(-1)?.capturedAt);
        const chipLabel = formatCompactDate(t, i18n, earliestDate);
        return (
          <React.Fragment key={date}>
            {chipLabel && (
              <Tooltip
                title={`${userFacingDate(
                  i18n,
                  earliestDate
                )} - ${userFacingDate(i18n, latestDate)}`}
              >
                <Chip
                  label={chipLabel}
                  className="font-normal bg-gray-600 p-1 my-2"
                  size="small"
                />
              </Tooltip>
            )}
            <GridWrapper>
              <Thumbnails
                thumbnails={thumbnails}
                dimensions={dimensions}
                onImageLoaded={onImageLoaded}
              />
            </GridWrapper>
          </React.Fragment>
        );
      })}
      {loading && (
        <GridWrapper className="pt-2">
          <LoadingThumbnails count={loadingItemCount} dimensions={dimensions} />
        </GridWrapper>
      )}
    </>
  ) : (
    <GridWrapper>
      <Thumbnails
        thumbnails={thumbnailImages}
        dimensions={dimensions}
        onImageLoaded={onImageLoaded}
      />
      {loading && (
        <LoadingThumbnails count={loadingItemCount} dimensions={dimensions} />
      )}
    </GridWrapper>
  );
};
interface ThumbnailsProps {
  thumbnails: OptionalImageGridItem[];
  dimensions: {
    width: number;
    height: number;
  };
  onImageLoaded?: (id: string) => void;
}
export const Thumbnails: FC<ThumbnailsProps> = ({
  thumbnails,
  dimensions,
  onImageLoaded,
}) => {
  return thumbnails.map((thumbnail, index) => (
    <div className="relative" style={dimensions} key={thumbnail?.id ?? index}>
      <ThumbnailImage
        image={thumbnail}
        dimensions={dimensions}
        onImageLoaded={onImageLoaded}
      />
    </div>
  ));
};

interface LoadingThumbnailsProps {
  count: number;
  dimensions: {
    width: number;
    height: number;
  };
}
const LoadingThumbnails: FC<LoadingThumbnailsProps> = ({
  count,
  dimensions: { width, height },
}) => {
  return Array.from({ length: count })
    .fill(undefined)
    .map((_, index) => (
      <Skeleton
        key={`loading-${index}`}
        variant="rectangular"
        width={width}
        height={height}
      />
    ));
};

const GridWrapper: FC<PropsWithChildren & { className?: string }> = ({
  children,
  className,
}) => (
  <div className={classes("flex flex-wrap gap-1 justify-start", className)}>
    {children}
  </div>
);
