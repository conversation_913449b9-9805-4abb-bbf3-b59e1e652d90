import {
  DEFAULT_AVERAGE_PPCM,
  PaginationParameters,
  thumbnailCropPlacementFromPoint,
} from "portal/utils/categoryCollectionProfile";
import { IconButton, Skeleton, Tooltip } from "@mui/material";
import { PointLabelOverlay } from "./imageOverlays/PointLabelOverlay";
import { PredictionPoint } from "protos/veselka/prediction_point";
import {
  processImageResults,
  ResultsMetadata,
} from "../images/processImageResults";
import { RadioChipOption } from "../RadioChips";
import { RemoveImageOverlay } from "./imageOverlays/RemoveImageOverlay";
import { theme } from "portal/theme/theme";
import { ThumbnailBaseInfo } from "./ImageCategorizationOptions";
import { ThumbnailImage } from "../images/ThumbnailImage";
import { useCategorizedImages } from "./useCategorizedImages";
import { useInfiniteScroll } from "../images/useInfiniteScroll";
import { useLazyListPredictionPointsQuery } from "portal/state/portalApi";
import { useLazyPopups } from "portal/utils/hooks/useApiPopups";
import { useTranslation } from "react-i18next";
import { useUserPreferences } from "portal/state/store";
import MoreIcon from "@mui/icons-material/Add";
import React, {
  FunctionComponent,
  PropsWithChildren,
  useCallback,
  useEffect,
  useState,
} from "react";

const DEFAULT_PAGINATION: PaginationParameters = { page: 1, pageSize: 25 };

const initialResultsState: ResultsMetadata<PredictionPoint> = {
  data: {
    ids: new Set(),
    data: [],
  },
  areAllResultsLoaded: false,
  loadableImagesCount: 0,
};

interface Props {
  category: RadioChipOption;
  thumbnailIds: string[];
  dimensions: { width: number; height: number };
  onRemove?: (thumbnail: ThumbnailBaseInfo) => void;
}
export const CategorizedImages: FunctionComponent<Props> = ({
  category,
  thumbnailIds,
  dimensions,
  onRemove,
}) => {
  const { t } = useTranslation();
  const userPreferences = useUserPreferences();

  const [pagination, setPagination] =
    useState<PaginationParameters>(DEFAULT_PAGINATION);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [resultsState, setResultsState] =
    useState<ResultsMetadata<PredictionPoint>>(initialResultsState);
  const { combineThumbnails } = useCategorizedImages();

  const [getPredictionPoints] = useLazyPopups(
    useLazyListPredictionPointsQuery()
  );

  const fetchChipPoints = useCallback(
    async (
      { page: currentPage, pageSize: currentPageSize }: PaginationParameters,
      thumbnailIds: string[]
    ) => {
      const offset = (currentPage - 1) * currentPageSize;
      const pageOfThumbnailIds = thumbnailIds.slice(
        offset,
        offset + currentPageSize + 1
      );
      if (pageOfThumbnailIds.length === 0) {
        return;
      }
      setIsLoading(true);
      const { data: results, isError: fetchError } = await getPredictionPoints({
        page: 1, // This is always 1 because we're doing FE pagination via chunking the id parameters
        pageSize: currentPageSize + 1, // fetching one more than we need to be able to check if we need a subsequent request
        ids: pageOfThumbnailIds,
      });

      const sortedResults = results
        ? [...results.data].sort(
            (a, b) =>
              pageOfThumbnailIds.indexOf(a.id) -
              pageOfThumbnailIds.indexOf(b.id)
          )
        : undefined;

      if (fetchError) {
        setIsError(true);
      } else {
        const pageOfResults = sortedResults?.slice(0, currentPageSize) ?? [];
        setResultsState((previous) =>
          processImageResults(previous, {
            pagination: results?.pagination
              ? {
                  ...results.pagination,
                  totalRecords: offset + results.pagination.totalRecords, // provide the total previously loaded results plus the current total records which includes the +1 request that indicates whether there is more
                }
              : undefined,
            data: pageOfResults,
          })
        );
      }
      setIsLoading(false);
    },
    [getPredictionPoints]
  );

  const loadMore = useCallback(() => {
    if (!isLoading && !resultsState.areAllResultsLoaded && !isError) {
      const newPagination = { ...pagination, page: pagination.page + 1 };
      fetchChipPoints(newPagination, thumbnailIds);
      setPagination(newPagination);
    }
  }, [
    isLoading,
    resultsState.areAllResultsLoaded,
    isError,
    pagination,
    thumbnailIds,
    fetchChipPoints,
  ]);

  useEffect(() => {
    setResultsState(initialResultsState);
    setPagination(DEFAULT_PAGINATION);
    fetchChipPoints(DEFAULT_PAGINATION, thumbnailIds);
  }, [thumbnailIds, fetchChipPoints]);

  const allThumbnails = combineThumbnails(
    isLoading,
    pagination,
    resultsState.data.data,
    thumbnailIds
  );

  const loadMoreRef = useInfiniteScroll<HTMLDivElement>(isLoading, loadMore);

  return (
    <div className="flex flex-col">
      <p
        className="px-1 py-0.5 m-0 w-fit text-xs hidden md:block"
        style={{
          backgroundColor: category.color?.bg ?? theme.colors.carbon.gray.light,
          color: category.color?.text ?? theme.colors.white,
        }}
      >
        {category.name.toLocaleUpperCase()}
      </p>
      <div
        className="overflow-x-auto flex gap-1 whitespace-nowrap border-1 border-solid p-1"
        style={{
          borderColor: category.color?.bg ?? theme.colors.carbon.gray.light,
          minHeight: `${dimensions.height + 12}px`,
        }}
      >
        {allThumbnails.map(({ thumbnail, state }, i) => {
          if (state.loading) {
            // show loading spinner when it's loading
            return (
              <ImageWrapper dimensions={dimensions} key={`loading-${i}`}>
                <Skeleton
                  variant="rectangular"
                  width={dimensions.width}
                  height={dimensions.height}
                />
              </ImageWrapper>
            );
          }

          // show the saved or unsaved thumbnail (or a broken icon if image is undefined)
          let renderedThumbnail;
          if (thumbnail) {
            const { paddingFactor } = thumbnailCropPlacementFromPoint({
              x: thumbnail.x,
              y: thumbnail.y,
              radius: thumbnail.radius,
            });
            renderedThumbnail = {
              ...thumbnail,
              renderOverlay: () => (
                <>
                  {userPreferences.categoryCollection.showLabels && (
                    <PointLabelOverlay
                      padding={dimensions.width * paddingFactor}
                      radiusCm={
                        thumbnail.radius /
                        (thumbnail.ppcm > 0
                          ? thumbnail.ppcm
                          : DEFAULT_AVERAGE_PPCM)
                      }
                      dimensions={dimensions}
                    />
                  )}
                  {onRemove && (
                    <RemoveImageOverlay
                      dimensions={dimensions}
                      onClick={() => {
                        onRemove(thumbnail);
                      }}
                    />
                  )}
                </>
              ),
            };
          }

          return (
            <ImageWrapper
              dimensions={dimensions}
              key={renderedThumbnail?.id ?? `broken-${i}`}
            >
              <ThumbnailImage
                className="cursor-auto"
                image={renderedThumbnail}
                dimensions={dimensions}
              />
            </ImageWrapper>
          );
        })}
        {thumbnailIds.length > 0 && !resultsState.areAllResultsLoaded && (
          <div className="flex items-center" ref={loadMoreRef}>
            <Tooltip title={t("utils.lists.loadMore")}>
              <span>
                <IconButton
                  className="text-carbon-orange"
                  size="small"
                  onClick={loadMore}
                  aria-label={t("utils.lists.loadMore")}
                >
                  <MoreIcon className="text-inherit" />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        )}
      </div>
    </div>
  );
};

interface ImageWrapperProps extends PropsWithChildren {
  dimensions: { width: number; height: number };
}
const ImageWrapper: FunctionComponent<ImageWrapperProps> = ({
  dimensions,
  children,
}) => (
  <div className="relative grow-0 shrink-0" style={dimensions}>
    {children}
  </div>
);
