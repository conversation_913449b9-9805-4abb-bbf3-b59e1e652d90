import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  capitalize,
  Card,
  IconButton,
  Tab,
  Typography,
} from "@mui/material";
import {
  BLUE_LOADING_BUTTON,
  CHIP_COLORS,
  RED_BUTTON,
  theme,
} from "portal/theme/theme";
import { buildPermission } from "portal/utils/auth";
import { CategorizedImages } from "./CategorizedImages";
import { Category } from "protos/category_profile/category_profile";
import {
  categoryCollectionProfileImageToggle,
  createCategoryOptions,
  expandedCollectionHasProfile,
  imagesToCategoryIds,
} from "portal/utils/categoryCollectionProfile";
import { ConfirmationDialog } from "../ConfirmationDialog";
import { darken } from "@mui/material/styles";
import { ExpandedCategoryCollectionRequest } from "protos/portal/category_profile";
import { FilterBar } from "../filters/FilterBar";
import { findWhere } from "portal/utils/arrays";
import { ImageSizeSlider } from "../images/ImageSizeSlider";
import { isUndefined } from "portal/utils/identity";
import { Link, useNavigate } from "react-router-dom";
import { Loading } from "../Loading";
import { LoadingButton, TabContext, TabList, TabPanel } from "@mui/lab";
import { mergeDeep } from "common/utils/objects";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import {
  PredictionPointFilter,
  PredictionPointFilterChipSummary,
  PredictionPointFilters,
} from "./filters/PredictionPointFilters";
import { PreviewResultsSession } from "./session/PreviewResultsSession";
import { RadioChipOption, RadioChips } from "../RadioChips";
import {
  ScrollableImageCategorizationOptions,
  ThumbnailBaseInfo,
} from "./ImageCategorizationOptions";
import { SessionStatus } from "protos/veselka/prediction_point";
import { SetCategoryCollectionProfile } from "./SetCategoryCollectionProfile";
import { ShowLabelsToggle } from "./ShowLabelsToggle";
import { skipToken } from "@reduxjs/toolkit/query";
import { titleCase } from "portal/utils/strings";
import {
  updateCategoryCollectionShowLabels,
  updateCategoryCollectionThumbnailSize,
} from "portal/state/userPreferences";
import { useAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import {
  useDeleteCustomerCategoryCollectionProfileMutation,
  useDeleteGlobalCategoryCollectionProfileMutation,
  useGetCustomerCategoryCollectionProfileQuery,
  useGetGlobalCategoryCollectionProfileQuery,
  useGetRobotQuery,
  useListCustomerCategoryCollectionProfilesQuery,
  useListGlobalCategoryCollectionProfilesQuery,
  useSetCustomerCategoryCollectionProfileMutation,
  useSetGlobalCategoryCollectionProfileMutation,
} from "portal/state/portalApi";
import { useDispatch } from "react-redux";
import {
  useMutationPopups,
  useQueryPopups,
} from "portal/utils/hooks/useApiPopups";
import { usePreviewResults } from "./usePreviewResults";
import { useResizeObserver } from "portal/utils/hooks/useResizeObserver";
import { useTranslation } from "react-i18next";
import { useUserPreferences } from "portal/state/store";
import DeleteIcon from "@mui/icons-material/DeleteOutlined";
import EditIcon from "@mui/icons-material/ModeEditOutlined";
import ParentIcon from "@mui/icons-material/ArrowBackOutlined";
import React, {
  FunctionComponent,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import SaveIcon from "@mui/icons-material/SaveOutlined";
import SortIcon from "@mui/icons-material/Sort";

enum ImageViewTabs {
  ALL = "All",
  CATEGORIZED = "Categorized",
}
export const PREVIEW_RESULTS_QUERYABLE_AMOUNT = 1000; // amount of chips processed that we feel good about showing the customer

interface Props {
  adminEditing?: boolean;
  parentLink?: string;
  uuid: string;
  serial?: string;
  useGetQuery:
    | typeof useGetGlobalCategoryCollectionProfileQuery
    | typeof useGetCustomerCategoryCollectionProfileQuery;
  useSetMutation:
    | typeof useSetGlobalCategoryCollectionProfileMutation
    | typeof useSetCustomerCategoryCollectionProfileMutation;
  useDeleteMutation:
    | typeof useDeleteGlobalCategoryCollectionProfileMutation
    | typeof useDeleteCustomerCategoryCollectionProfileMutation;
}

const MIN_THUMBNAIL_SIZE = 50;
const MAX_THUMBNAIL_SIZE = 400;

export const CategoryCollectionProfile: FunctionComponent<Props> = ({
  adminEditing = false,
  uuid,
  serial,
  parentLink,
  useGetQuery,
  useSetMutation,
  useDeleteMutation,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userPreferences = useUserPreferences();
  const thumbnailDivRef = useRef<HTMLDivElement | null>(null);
  const { width: thumbnailMaxWidth } = useResizeObserver(thumbnailDivRef);
  const largestPossibleThumbnailSize = thumbnailMaxWidth || Infinity;
  const thumbnailSize = Math.min(
    userPreferences.categoryCollection.thumbnailSize,
    largestPossibleThumbnailSize
  );
  const dimensions = {
    width: thumbnailSize,
    height: thumbnailSize,
  };

  const { data: summary } = useQueryPopups(
    useGetRobotQuery(serial ? { serial } : skipToken),
    { errorVariant: "warning" }
  );

  const {
    data: customerCategoryCollectionProfiles,
    isSuccess: hasCustomerOptionsLoaded,
  } = useQueryPopups(
    useListCustomerCategoryCollectionProfilesQuery(
      serial ? { serial } : skipToken
    )
  );

  const {
    data: globalCategoryCollectionProfiles,
    isSuccess: hasGlobalOptionsLoaded,
  } = useQueryPopups(useListGlobalCategoryCollectionProfilesQuery({ serial }));

  const crossProfileCategoryOptions = useMemo(() => {
    const availableProfiles = [
      ...(customerCategoryCollectionProfiles ?? []),
      ...(globalCategoryCollectionProfiles ?? []),
    ];
    return createCategoryOptions(t, availableProfiles);
  }, [customerCategoryCollectionProfiles, globalCategoryCollectionProfiles, t]);

  // local changes
  const [imageTab, setImageTab] = useState(ImageViewTabs.ALL);
  const [filters, setFilters] = useState<PredictionPointFilter>({
    crops: [],
    robots: [],
    capturedAt: undefined,
    diameterUserUnits: undefined,
    sessionId: undefined,
    categoryIds: [],
  });

  const [
    updatedCategoryCollectionProfile,
    setUpdatedCategoryCollectionProfile,
  ] = useState<ExpandedCategoryCollectionRequest | undefined>();

  const resetUnsavedChanges = useCallback(() => {
    setUpdatedCategoryCollectionProfile(undefined);
  }, []);

  // remote data
  const {
    data: freezedCategoryCollectionProfile,
    isFetching,
    isError,
    error,
  } = useQueryPopups(useGetQuery({ uuid, serial }));

  // mutations
  const [updateCategoryCollectionProfile, { isLoading: isSaving }] =
    useMutationPopups(useSetMutation(), {
      success: capitalize(
        t("components.categoryCollectionProfile.actions.savedLong", {
          subject: titleCase(
            t("models.categoryCollectionProfiles.categoryCollectionProfile_one")
          ),
        })
      ),
    });

  const [deleteCategoryCollectionProfile] = useMutationPopups(
    useDeleteMutation(),
    {
      success: capitalize(
        t("utils.actions.deletedLong", {
          subject: t(
            "models.categoryCollectionProfiles.categoryCollectionProfile_one"
          ),
        })
      ),
    }
  );

  // confirmation dialog
  const [confirmDelete, setConfirmDelete] = useState<boolean>(false);
  const [activeCategoryId, setActiveCategoryId] = useState<
    string | undefined
  >();

  useEffect(() => {
    if (
      !activeCategoryId &&
      freezedCategoryCollectionProfile &&
      freezedCategoryCollectionProfile.categories.length > 0 &&
      freezedCategoryCollectionProfile.categories[0]?.profile
    ) {
      setActiveCategoryId(
        freezedCategoryCollectionProfile.categories[0].profile.id
      );
    }
  }, [activeCategoryId, freezedCategoryCollectionProfile]);

  // unfreezed state
  const unfreezedCategoryCollectionProfile = useMemo<
    ExpandedCategoryCollectionRequest | undefined
  >(() => {
    if (!freezedCategoryCollectionProfile) {
      return;
    }
    const request: ExpandedCategoryCollectionRequest = {
      profile: freezedCategoryCollectionProfile.profile?.profile,
      categories: [],
    };
    for (const {
      profile: category,
    } of freezedCategoryCollectionProfile.categories) {
      if (!category) {
        continue;
      }
      request.categories.push(Category.fromPartial(category));
    }

    return ExpandedCategoryCollectionRequest.fromPartial(request);
  }, [freezedCategoryCollectionProfile]);

  // visible state
  const categoryCollectionProfile =
    updatedCategoryCollectionProfile ?? unfreezedCategoryCollectionProfile;
  const isActive =
    !isUndefined(categoryCollectionProfile?.profile?.id) &&
    categoryCollectionProfile.profile.id ===
      summary?.robot?.health?.fieldConfig?.activeCategoryCollectionId;

  const categoryOptions = useMemo(
    () =>
      (categoryCollectionProfile?.categories ?? [])
        .map(({ id, name }, index) => ({
          id,
          name,
          color: CHIP_COLORS[index % CHIP_COLORS.length],
        }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [categoryCollectionProfile?.categories]
  );

  const activeCategory = useMemo(
    () => categoryOptions.find(({ id }) => id === activeCategoryId),
    [activeCategoryId, categoryOptions]
  );

  const categorizationMap = useMemo(
    () => imagesToCategoryIds(categoryCollectionProfile),
    [categoryCollectionProfile]
  );

  const createCategoryRadioChipLabel = useCallback(
    ({ id, name }: RadioChipOption): string => {
      if (!categoryCollectionProfile) {
        return "";
      }
      const match = findWhere(categoryCollectionProfile.categories, {
        id,
      });
      if (!match) {
        return name;
      }
      const { chipIds } = match;
      const count = chipIds.length > 0 ? ` (${chipIds.length})` : "";
      return capitalize(`${name}${count}`);
    },
    [categoryCollectionProfile]
  );

  const onSessionStatusChange = useCallback(
    (sessionId: string, status?: SessionStatus) => {
      if (
        status?.countOfResults &&
        status.countOfResults >= PREVIEW_RESULTS_QUERYABLE_AMOUNT &&
        filters.sessionId !== sessionId
      ) {
        setFilters((prev) => ({ ...prev, sessionId }));
      }
    },
    [filters.sessionId]
  );

  const {
    testResults,
    reset: resetTestResults,
    canStartSession,
    isLoading: isTestResultsLoading,
    session,
  } = usePreviewResults(
    serial,
    categoryCollectionProfile,
    onSessionStatusChange
  );
  const resetSession = useCallback(() => {
    resetTestResults();
    setFilters((prev) => ({
      ...prev,
      sessionId: undefined,
      categoryIds: [],
    }));
  }, [resetTestResults]);

  const onImageToggle = useCallback(
    (thumbnail: ThumbnailBaseInfo) => {
      if (!categoryCollectionProfile) {
        return;
      }
      const { id } = thumbnail;
      const updated: ExpandedCategoryCollectionRequest =
        categoryCollectionProfileImageToggle(
          categoryCollectionProfile,
          activeCategoryId,
          id
        );

      setUpdatedCategoryCollectionProfile(updated);
    },
    [activeCategoryId, categoryCollectionProfile]
  );
  const canRead = useAuthorizationRequired(
    adminEditing
      ? [
          buildPermission(
            PermissionAction.read,
            PermissionResource.plant_category_profiles,
            PermissionDomain.templates
          ),
        ]
      : [
          buildPermission(
            PermissionAction.read,
            PermissionResource.plant_category_profiles,
            PermissionDomain.all
          ),
          buildPermission(
            PermissionAction.read,
            PermissionResource.plant_category_profiles,
            PermissionDomain.customer
          ),
        ]
  );
  const canUpdate =
    useAuthorizationRequired(
      adminEditing
        ? [
            buildPermission(
              PermissionAction.update,
              PermissionResource.plant_category_profiles,
              PermissionDomain.templates
            ),
          ]
        : [
            buildPermission(
              PermissionAction.update,
              PermissionResource.plant_category_profiles,
              PermissionDomain.all
            ),
            buildPermission(
              PermissionAction.update,
              PermissionResource.plant_category_profiles,
              PermissionDomain.customer
            ),
          ]
    ) &&
    (!categoryCollectionProfile?.profile?.protected || adminEditing);

  // temporary guard to specifically allow admin-like folks on customer profiles see and run test results
  const canSeePreviewResults =
    useAuthorizationRequired([
      buildPermission(
        PermissionAction.update,
        PermissionResource.plant_category_profiles,
        PermissionDomain.all
      ),
    ]) && !adminEditing;

  if (!canRead) {
    return;
  }

  if (!expandedCollectionHasProfile(categoryCollectionProfile)) {
    return <Loading failed={isError} error={error} />;
  }

  const EditButton: FunctionComponent = () => (
    <SetCategoryCollectionProfile
      triggerButton={(onClick) => (
        <IconButton
          onClick={onClick}
          className="text-lighten-400 hover:text-white"
        >
          <EditIcon />
        </IconButton>
      )}
      hotkey={{ sequence: "ec", key: "EDIT_CATEGORY_COLLECTION_PROFILE" }}
      title={t("utils.actions.editLong", {
        subject: t(
          "models.categoryCollectionProfiles.categoryCollectionProfile_one"
        ),
      })}
      okText={t("utils.actions.next")}
      initialValue={{
        ...categoryCollectionProfile,
      }}
      areOptionsLoading={
        !(serial && hasCustomerOptionsLoaded) || !hasGlobalOptionsLoaded
      }
      categoryOptions={crossProfileCategoryOptions}
      onSubmit={(newProfile) => {
        setUpdatedCategoryCollectionProfile(newProfile);
      }}
    />
  );

  return (
    <div className="flex flex-col min-h-full">
      {canUpdate && adminEditing && (
        <Alert severity="warning" className="mb-8">
          {t("components.categoryCollectionProfile.warnings.admin")}
        </Alert>
      )}
      {isActive && (
        <Alert severity="warning" className="mb-8">
          {t("components.categoryCollectionProfile.warnings.production")}
        </Alert>
      )}
      {updatedCategoryCollectionProfile &&
        updatedCategoryCollectionProfile !==
          unfreezedCategoryCollectionProfile && (
          <Alert severity="warning" className="mb-8">
            {t("components.categoryCollectionProfile.warnings.unsavedChanges")}
          </Alert>
        )}
      {categoryCollectionProfile.profile.protected && !adminEditing && (
        <Alert severity="warning" className="mb-8">
          {t("components.categoryCollectionProfile.warnings.protected")}
        </Alert>
      )}
      {/* Header */}
      <div className="flex flex-wrap flex-col md:flex-row items-start md:items-center justify-center md:justify-between md:mb-8 w-full gap-2 md:gap-4">
        {/* Left Controls */}
        <div className="flex items-center justify-start flex-none">
          {/* Breadcrumb */}
          {parentLink && (
            <IconButton component={Link} to={parentLink} className="text-white">
              <ParentIcon />
            </IconButton>
          )}
        </div>

        {/* Title */}
        <div className="flex flex-auto">
          <Typography variant="h4">
            {categoryCollectionProfile.profile.name}
          </Typography>
          {canUpdate && <EditButton />}
        </div>

        {/* Right Controls */}
        <div className="flex items-center gap-4 flex-none">
          {canUpdate && (
            <Button
              {...RED_BUTTON}
              onClick={() => setConfirmDelete(true)}
              startIcon={<DeleteIcon />}
            >
              {t("utils.actions.delete")}
            </Button>
          )}
          {confirmDelete && (
            <ConfirmationDialog
              title={t("utils.actions.deleteLong", {
                subject: t(
                  "models.categoryCollectionProfiles.categoryCollectionProfile_one"
                ),
              })}
              description={
                isActive ? (
                  <Alert severity="warning" className="mb-8">
                    {t(
                      "components.ConfirmationDialog.delete.descriptionActive",
                      {
                        subject: t(
                          "models.categoryCollectionProfiles.categoryCollectionProfile_one"
                        ),
                      }
                    )}
                  </Alert>
                ) : (
                  capitalize(
                    t("components.ConfirmationDialog.delete.description", {
                      subject: t(
                        "models.categoryCollectionProfiles.categoryCollectionProfile_one"
                      ),
                    })
                  )
                )
              }
              destructive
              yesText={t("utils.actions.delete", {
                subject: categoryCollectionProfile.profile.name,
              })}
              yesDisabled={isActive}
              onClose={() => setConfirmDelete(false)}
              onYes={async () => {
                const result = await deleteCategoryCollectionProfile({
                  uuid: categoryCollectionProfile.profile.id,
                  serial,
                });
                if (parentLink && !result.error) {
                  navigate(parentLink);
                }
              }}
            />
          )}
          {canUpdate && (
            <>
              <Button
                variant="text"
                color="info"
                classes={{ disabled: "text-lighten-200" }}
                disabled={!updatedCategoryCollectionProfile}
                onClick={() => {
                  resetUnsavedChanges();
                }}
              >
                {t("utils.actions.cancel")}
              </Button>
              <LoadingButton
                {...BLUE_LOADING_BUTTON}
                loading={isSaving}
                onClick={async () => {
                  setUpdatedCategoryCollectionProfile(
                    categoryCollectionProfile
                  );
                  const result = await updateCategoryCollectionProfile({
                    serial,
                    expandedCategoryCollection: categoryCollectionProfile,
                  });
                  if (!result.error) {
                    resetUnsavedChanges();
                  }
                }}
                disabled={!updatedCategoryCollectionProfile}
                startIcon={<SaveIcon />}
              >
                {t("utils.actions.save")}
              </LoadingButton>
            </>
          )}
        </div>
      </div>

      {/* Main View */}
      {/* Note: overflow-visible is required for making the options sticky */}
      <Card className="p-4 mt-4 overflow-visible">
        <TabContext value={imageTab}>
          <div className="flex flex-col md:flex-row justify-center md:justify-between items-start md:items-end">
            <TabList
              onChange={(_: React.SyntheticEvent, newValue: ImageViewTabs) => {
                setImageTab(newValue);
              }}
              className="text-white"
            >
              <Tab
                label={t(
                  "components.categoryCollectionProfile.images.allImages"
                )}
                className="text-inherit"
                value={ImageViewTabs.ALL}
              />
              <Tab
                label={t(
                  "components.categoryCollectionProfile.images.categorized"
                )}
                className="text-inherit"
                value={ImageViewTabs.CATEGORIZED}
              />
            </TabList>
            <div
              className="flex gap-3 lg:gap-2 items-center px-2 rounded-md w-full md:w-auto justify-center flex-wrap"
              style={{
                backgroundColor: darken(theme.colors.carbon.gray.medium, 0.2),
              }}
            >
              <ImageSizeSlider
                className="w-36 text-white"
                min={MIN_THUMBNAIL_SIZE}
                max={Math.min(largestPossibleThumbnailSize, MAX_THUMBNAIL_SIZE)}
                value={thumbnailSize}
                onChange={(size) => {
                  dispatch(updateCategoryCollectionThumbnailSize(size));
                }}
              />
              <ShowLabelsToggle
                showLabels={userPreferences.categoryCollection.showLabels}
                onClick={() => {
                  dispatch(
                    updateCategoryCollectionShowLabels(
                      !userPreferences.categoryCollection.showLabels
                    )
                  );
                }}
              />
            </div>
          </div>
          {/* Note: overflow-visible is required for making the options sticky */}
          <TabPanel value={ImageViewTabs.ALL} className="pt-2 overflow-visible">
            <div className="sticky top-0 bg-inherit z-10">
              <div
                className="flex items-center py-2"
                style={{ backgroundColor: theme.colors.carbon.gray.medium }}
              >
                <RadioChips
                  isLoading={isFetching}
                  options={categoryOptions}
                  activeId={activeCategoryId}
                  setActiveId={setActiveCategoryId}
                  createLabel={createCategoryRadioChipLabel}
                />
                {canUpdate && (
                  <div className="pl-2">
                    <EditButton />
                  </div>
                )}
              </div>
            </div>
            {activeCategory && (
              <div className="pb-4">
                <CategorizedImages
                  category={activeCategory}
                  thumbnailIds={
                    categoryCollectionProfile.categories.find(
                      ({ id }) => id === activeCategoryId
                    )?.chipIds ?? []
                  }
                  dimensions={dimensions}
                  onRemove={canUpdate ? onImageToggle : undefined}
                />
              </div>
            )}
            <div className="flex flex-col gap-2">
              <FilterBar
                collapsedSummary={
                  <PredictionPointFilterChipSummary filters={filters} />
                }
              >
                <PredictionPointFilters
                  filters={filters}
                  onChange={setFilters}
                  serial={serial}
                />
              </FilterBar>
              {canSeePreviewResults && (
                <PreviewResultsSession
                  onStart={() => {
                    resetSession();
                    testResults();
                  }}
                  onCancel={() => {
                    resetSession();
                  }}
                  sessionId={filters.sessionId}
                  loading={isTestResultsLoading}
                  disabled={!canStartSession}
                  progress={
                    session?.status
                      ? {
                          processed: session.status.countOfResults,
                          total: session.status.expectedCount,
                          error: session.error,
                        }
                      : undefined
                  }
                  sessionFilters={filters}
                  onSessionFiltersChange={(sessionFilters) =>
                    setFilters((prev) => mergeDeep(prev, sessionFilters))
                  }
                  categoryOptions={categoryOptions}
                />
              )}
            </div>
            <div className="flex justify-end flex-wrap gap-6 mt-2">
              <div className="my-1 lg:my-2 flex gap-2 items-center justify-start">
                <SortIcon className="text-sm" />
                <Typography className="text-sm text-gray-200 whitespace-nowrap">
                  {t("components.categoryCollectionProfile.images.sortedBy", {
                    sortBy: t(
                      "components.categoryCollectionProfile.images.sortBy.latest"
                    ),
                  })}
                </Typography>
              </div>
            </div>

            <div ref={thumbnailDivRef}>
              <ScrollableImageCategorizationOptions
                serial={serial}
                isSaving={isSaving}
                filters={filters}
                dimensions={dimensions}
                categoryOptions={categoryOptions}
                categorizationMap={categorizationMap}
                ai={{
                  processing: Boolean(
                    session?.id
                      ? (session.status?.countOfResults ?? 0) <
                          PREVIEW_RESULTS_QUERYABLE_AMOUNT
                      : isTestResultsLoading
                  ),
                  stale: Boolean(session?.stale),
                }}
                onImageClick={canUpdate ? onImageToggle : undefined}
              />
            </div>
          </TabPanel>
          <TabPanel value={ImageViewTabs.CATEGORIZED} className="pt-2">
            <div className="flex flex-col gap-4">
              {categoryOptions.map((categoryOption) => (
                <CategorizedImages
                  key={categoryOption.id}
                  category={categoryOption}
                  thumbnailIds={
                    categoryCollectionProfile.categories.find(
                      ({ id }) => id === categoryOption.id
                    )?.chipIds ?? []
                  }
                  dimensions={dimensions}
                  onRemove={canUpdate ? onImageToggle : undefined}
                />
              ))}
            </div>
          </TabPanel>
        </TabContext>
      </Card>
    </div>
  );
};
