import { buildPermission } from "portal/utils/auth";
import { Button, SvgIcon } from "@mui/material";
import {
  CategorizationMap,
  DEFAULT_AVERAGE_PPCM,
  diameterUnitsToRadiusPixels,
  getDiameterFilterUnits,
  PaginationParameters,
  thumbnailCropPlacementFromPoint,
} from "portal/utils/categoryCollectionProfile";
import { ChipOverlay } from "./imageOverlays/ChipOverlay";
import { ImageGrid, OptionalImageGridItem } from "../images/ImageGrid";
import { ImageProcessingOverlay } from "./imageOverlays/ImageProcessingOverlay";
import { isAbort } from "portal/utils/errors";
import { isNil } from "portal/utils/identity";
import { LoadMoreImagesButton } from "./LoadMoreImagesButton";
import { MetadataOverlay } from "./imageOverlays/MetadataOverlay";
import { NumericalRangeValue } from "../filters/filter.types";
import {
  PermissionAction,
  PermissionDomain,
  PermissionResource,
} from "protos/portal/auth";
import { PointLabelOverlay } from "./imageOverlays/PointLabelOverlay";
import { PolaroidOverlay } from "./imageOverlays/PolaroidOverlay";
import { PredictionPoint } from "protos/veselka/prediction_point";
import { PredictionPointFilter } from "./filters/PredictionPointFilters";
import {
  processImageResults,
  ResultsMetadata,
} from "../images/processImageResults";
import { RadioChipOption } from "../RadioChips";
import { skipToken } from "@reduxjs/toolkit/query";
import { theme } from "portal/theme/theme";
import { ThumbnailImageItem } from "../images/ThumbnailImage";
import { useAuthorizationRequired } from "../auth/WithAuthorizationRequired";
import { useGetRobotFilterOptions } from "./useGetRobotFilterOptions";
import {
  useGetRobotQuery,
  useLazyListPredictionPointsQuery,
} from "portal/state/portalApi";
import { useInfiniteScroll } from "../images/useInfiniteScroll";
import { useLazyPopups, useQueryPopups } from "portal/utils/hooks/useApiPopups";
import { usePageRef } from "../Page";
import { useSelf, useUserPreferences } from "portal/state/store";
import { useTranslation } from "react-i18next";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ModelIcon from "portal/images/icons/model.svg?react";
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import TimeIcon from "@mui/icons-material/AccessTime";

export type ThumbnailBaseInfo = Pick<
  ThumbnailImageItem,
  "id" | "x" | "y" | "width" | "height" | "url"
> & {
  radius: number;
  ppcm: number;
};

interface ImageCategorizationOptionsProps {
  serial?: string;
  isSaving: boolean;
  ai?: {
    processing: boolean;
    stale: boolean;
  };
  filters: PredictionPointFilter;
  categoryOptions: RadioChipOption[];
  categorizationMap: CategorizationMap;
  dimensions: { width: number; height: number };
  onImageClick?: (thumbnail: ThumbnailBaseInfo) => void;
}
const DEFAULT_PAGINATION: PaginationParameters = { page: 1, pageSize: 50 };

const initialResultsState: ResultsMetadata<PredictionPoint> = {
  data: {
    ids: new Set(),
    data: [],
  },
  areAllResultsLoaded: false,
  loadableImagesCount: 0,
  averagePpcm: DEFAULT_AVERAGE_PPCM,
};

export const ImageCategorizationOptions: FC<
  ImageCategorizationOptionsProps
> = ({
  serial,
  isSaving,
  ai,
  filters,
  categoryOptions,
  categorizationMap,
  dimensions,
  onImageClick,
}) => {
  const { t } = useTranslation();
  const userPreferences = useUserPreferences();
  const { measurementSystem } = useSelf();

  const [imagesRendered, setImagesRendered] = useState(0);
  const [pagination, setPagination] =
    useState<PaginationParameters>(DEFAULT_PAGINATION);
  const [resultsState, setResultsState] =
    useState<ResultsMetadata<PredictionPoint>>(initialResultsState);

  // robot
  const {
    data: robotSummary,
    isLoading: isRobotLoading,
    error: robotError,
  } = useQueryPopups(useGetRobotQuery(isNil(serial) ? skipToken : { serial }), {
    errorVariant: "warning",
  });
  const robotCustomerId = robotSummary?.customer?.db?.id;

  // robots
  const {
    robots: availableRobots,
    isLoading: isRobotsLoading,
    error: robotsError,
  } = useGetRobotFilterOptions(robotCustomerId);
  const customerScopedRobotOptions = serial ? availableRobots : undefined;
  const isCustomerRobotScopingReady = isNil(serial)
    ? true
    : !isRobotLoading && !isRobotsLoading && !robotError && !robotsError;

  const [getPredictionPoints] = useLazyPopups(
    useLazyListPredictionPointsQuery()
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const canSeeChipMetadata = useAuthorizationRequired([
    buildPermission(
      PermissionAction.update,
      PermissionResource.plant_category_profiles,
      PermissionDomain.templates
    ),
  ]);

  const diameterFilterUnits = getDiameterFilterUnits(measurementSystem);
  const previousFetchResultsRef = useRef<ReturnType<
    typeof getPredictionPoints
  > | null>(null);

  const fetchResults = useCallback(
    async (
      pagination: PaginationParameters,
      {
        crops,
        robots,
        capturedAt,
        diameterUserUnits,
        sessionId,
        categoryIds,
      }: PredictionPointFilter,
      averagePpcm?: number
    ): Promise<void> => {
      // Cancel the previous request if it's still active so the previous filters' results don't get added to current results
      previousFetchResultsRef.current?.abort();
      setIsLoading(true);

      let radiusPx: NumericalRangeValue | undefined;
      if (
        diameterUserUnits !== undefined &&
        (diameterUserUnits[0] !== undefined ||
          diameterUserUnits[1] !== undefined)
      ) {
        const [minRadiusPx, maxRadiusPx] = diameterUserUnits.map((d) =>
          d === undefined
            ? undefined
            : Math.floor(
                diameterUnitsToRadiusPixels(
                  d,
                  averagePpcm ?? DEFAULT_AVERAGE_PPCM,
                  diameterFilterUnits
                )
              )
        );
        radiusPx = [minRadiusPx, maxRadiusPx];
      }

      try {
        const query = getPredictionPoints({
          ...pagination,
          crops: crops.map((c) => c.id),
          robots: (robots.length > 0
            ? robots
            : customerScopedRobotOptions ?? []
          ).map((r) => r.id),
          radius: radiusPx,
          capturedAt: capturedAt
            ? [
                capturedAt[0]?.toISO() ?? undefined,
                capturedAt[1]?.toISO() ?? undefined,
              ]
            : undefined,
          sessionId,
          categoryIds: categoryIds.map((c) => c.id),
        });
        previousFetchResultsRef.current = query;
        const predictionPointResults = await query.unwrap();
        setResultsState((previous) =>
          processImageResults(previous, predictionPointResults)
        );
        setPagination((previous) => ({
          ...previous,
          page: previous.page + 1,
        }));
      } catch (error) {
        if (isAbort(error)) {
          // Request was cancelled — ignore error
          return;
        }
        setIsError(true);
      }
      setIsLoading(false);
    },
    [customerScopedRobotOptions, diameterFilterUnits, getPredictionPoints]
  );

  // Automatically fetch more results when infinite scrolling
  const loadMore = useCallback(() => {
    if (
      !isLoading &&
      isCustomerRobotScopingReady &&
      !resultsState.areAllResultsLoaded &&
      !isError &&
      imagesRendered >= resultsState.loadableImagesCount
    ) {
      fetchResults(pagination, filters, resultsState.averagePpcm);
    }
  }, [
    isLoading,
    isCustomerRobotScopingReady,
    resultsState.areAllResultsLoaded,
    resultsState.loadableImagesCount,
    resultsState.averagePpcm,
    isError,
    imagesRendered,
    fetchResults,
    pagination,
    filters,
  ]);

  const loadMoreRef = useInfiniteScroll<HTMLDivElement>(
    isLoading || imagesRendered < resultsState.loadableImagesCount,
    loadMore
  );

  // Reset results and refetch when filters change or when save completes
  useEffect(() => {
    if (!isSaving && isCustomerRobotScopingReady) {
      setResultsState(initialResultsState);
      setPagination(DEFAULT_PAGINATION);
      fetchResults(
        DEFAULT_PAGINATION,
        filters,
        initialResultsState.averagePpcm
      );
    }
  }, [fetchResults, filters, isCustomerRobotScopingReady, isSaving]);

  const thumbnailImages: OptionalImageGridItem[] = useMemo(
    () =>
      resultsState.data.data.map((data) => {
        const { id, x, y, radius, image, categoryId } = data;
        if (!image) {
          return;
        }
        const thumbnailCropPlacement = thumbnailCropPlacementFromPoint({
          x,
          y,
          radius,
        });
        let category: RadioChipOption | undefined;
        let predictedCategory: RadioChipOption | undefined;
        for (const option of categoryOptions) {
          if (option.id === categorizationMap[id]) {
            category = option;
          }
          if (option.id === categoryId) {
            predictedCategory = option;
          }
        }
        const baseThumbnail = {
          id,
          url: image.url,
          x: thumbnailCropPlacement.x,
          y: thumbnailCropPlacement.y,
          width: thumbnailCropPlacement.width,
          height: thumbnailCropPlacement.height,
          radius,
          ppcm: image.ppcm,
          capturedAt: image.capturedAt,
        };

        const staleStyle = {
          bg: theme.colors.carbon.gray.light,
          text: theme.colors.black,
        };
        const predictionChipIconProps = {
          className: "w-3 h-3",
          style: {
            color: ai?.stale ? staleStyle.text : predictedCategory?.color?.text,
          },
        };

        return {
          ...baseThumbnail,
          onClick: () => {
            onImageClick?.(baseThumbnail);
          },
          renderOverlay: () => {
            const showLabels = userPreferences.categoryCollection.showLabels;
            const radiusCm = image.ppcm > 0 ? radius / image.ppcm : undefined;

            return (
              <>
                {ai?.processing && (
                  <ImageProcessingOverlay image={baseThumbnail} />
                )}
                {showLabels && (
                  <PointLabelOverlay
                    padding={
                      dimensions.width * thumbnailCropPlacement.paddingFactor
                    }
                    radiusCm={category ? undefined : radiusCm}
                    dimensions={dimensions}
                  />
                )}
                {predictedCategory && !category && (
                  <ChipOverlay
                    name={predictedCategory.name}
                    color={ai?.stale ? staleStyle : predictedCategory.color}
                    icon={
                      ai?.stale ? (
                        <TimeIcon {...predictionChipIconProps} />
                      ) : (
                        <SvgIcon {...predictionChipIconProps}>
                          <ModelIcon />
                        </SvgIcon>
                      )
                    }
                  />
                )}
                {category && (
                  <PolaroidOverlay
                    dimensions={dimensions}
                    radiusCm={showLabels ? radiusCm : undefined}
                    name={category.name}
                    color={category.color}
                    position="bottom"
                  />
                )}
                {canSeeChipMetadata && (
                  <MetadataOverlay dimensions={dimensions} data={data} />
                )}
              </>
            );
          },
        };
      }),
    [
      ai,
      canSeeChipMetadata,
      categorizationMap,
      categoryOptions,
      dimensions,
      onImageClick,
      resultsState.data.data,
      userPreferences.categoryCollection.showLabels,
    ]
  );

  return (
    <>
      <ImageGrid
        loading={isLoading}
        loadingItemCount={pagination.pageSize}
        dimensions={dimensions}
        thumbnailImages={thumbnailImages}
        onImagesLoaded={setImagesRendered}
      />
      {!isLoading && resultsState.data.data.length === 0 ? (
        <p>{t("utils.lists.noResults")}</p>
      ) : (
        <div className="flex justify-center" ref={loadMoreRef}>
          <LoadMoreImagesButton
            isInitialRequestLoading={isLoading}
            canLoadMore={Boolean(!resultsState.areAllResultsLoaded)}
            disabled={!isCustomerRobotScopingReady}
            onClick={() => fetchResults(pagination, filters)}
          />
        </div>
      )}
    </>
  );
};

export const ScrollableImageCategorizationOptions: FC<
  ImageCategorizationOptionsProps
> = ({
  serial,
  isSaving,
  ai,
  filters,
  dimensions,
  categoryOptions,
  categorizationMap,
  onImageClick,
}) => {
  const pageRef = usePageRef();
  const { t } = useTranslation();
  const imageOptionsRef = useRef<HTMLDivElement>(null);
  const [showScrollToTop, setShowScrollToTop] = useState(false);

  const onScroll = useCallback(() => {
    const imagesElement = imageOptionsRef.current;
    if (!imagesElement || !pageRef?.current) {
      return;
    }

    const pageRect = pageRef.current.getBoundingClientRect();
    const imagesRect = imagesElement.getBoundingClientRect();

    const scrollPosRelativeToImages = pageRect.top - imagesRect.top;
    const SCROLL_BUFFER_PX = 100;
    setShowScrollToTop(scrollPosRelativeToImages > SCROLL_BUFFER_PX);
  }, [pageRef]);

  useEffect(() => {
    const el = pageRef?.current;
    if (!el) {
      return;
    }
    el.addEventListener("scroll", onScroll);
    return () => {
      el.removeEventListener("scroll", onScroll);
    };
  }, [onScroll, pageRef]);

  const handleScrollToTop = (): void => {
    const imagesElement = imageOptionsRef.current;
    if (!imagesElement || !pageRef?.current) {
      return;
    }

    const { top } = pageRef.current.getBoundingClientRect();
    const { scrollHeight } = pageRef.current;
    pageRef.current.scrollTo({
      top,
      behavior: scrollHeight > 20_000 ? "auto" : "smooth",
    });
  };

  return (
    <>
      <div ref={imageOptionsRef}>
        <ImageCategorizationOptions
          serial={serial}
          isSaving={isSaving}
          filters={filters}
          dimensions={dimensions}
          categoryOptions={categoryOptions}
          categorizationMap={categorizationMap}
          onImageClick={onImageClick}
          ai={ai}
        />
      </div>
      {showScrollToTop && (
        <div className="flex justify-end">
          <Button
            className="fixed bottom-4 bg-white p-2 rounded shadow"
            onClick={handleScrollToTop}
          >
            <ArrowUpwardIcon className="pr-2" />
            {t("components.categoryCollectionProfile.images.scrollToTop")}
          </Button>
        </div>
      )}
    </>
  );
};
