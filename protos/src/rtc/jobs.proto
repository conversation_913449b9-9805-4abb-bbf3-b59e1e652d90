syntax = "proto3";

package carbon.rtc;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/rtc";

import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";

import "geo/geo.proto";

// Unified State for all objects
enum State {
  STATE_UNSPECIFIED = 0;
  PENDING = 1;
  READY = 2;
  IN_PROGRESS = 3;
  COMPLETED = 4;
  CANCELLED = 5;
  PAUSED = 6;
  FAILED = 7;
  ACKNOWLEDGED = 8;
  NEW = 9;
}

// Objective API
message Objective {
  uint64 id = 1;
  string name = 2;
  string description = 3;
  int32 progress_percent = 4; // (0-100)
  int32 priority = 5;         // for ordering
  enum ObjectiveType {
    OBJECTIVE_TYPE_UNSPECIFIED = 0;
    LASER_WEED_ROW = 1;
  }
  ObjectiveType type = 6;

  // dynamic/json to define, per objective info
  //  this is nice and flexible so for example laser weeding can be a row (start
  //  -> stop) but also be more.
  google.protobuf.Struct data = 7;

  ObjectiveAssignment assignment = 8;
  uint64 job_id = 9;
}

message LaserWeedRowObjectiveData {
  uint32 row_num = 1;
  carbon.geo.AbLine ab_line = 2;
}

message ObjectiveList { repeated Objective objectives = 1; }

message ListObjectivesResponse {
  string page_token = 1;
  repeated Objective objectives = 2;
}

message GetNextActiveObjectiveRequest { uint64 objective_id = 1; }

message GetNextActiveObjectiveResponse { Objective objective = 2; }

// Objective Assignments API
message ObjectiveAssignment {
  uint64 id = 1;
  uint64 objective_id = 2;
  string robot_serial = 3;
}

message ObjectiveAssignmentList {
  repeated ObjectiveAssignment assignments = 1;
}

message ListObjectiveAssignmentsResponse {
  string page_token = 1;
  repeated ObjectiveAssignment assignments = 2;
}

// Robot Whitelist API
message RobotWhitelistEntry { string robot_serial = 1; }

message RobotWhitelist { repeated RobotWhitelistEntry entries = 1; }

// Task API
message Task {
  uint64 id = 1;
  string name = 2;
  google.protobuf.Timestamp started_at = 3;
  google.protobuf.Timestamp ended_at = 4;
  google.protobuf.Duration expected_duration = 5;
  string status_info = 6;
  repeated TractorState expected_tractor_state = 7;
  State state = 8;
  int32 priority = 9;
  uint64 objective_id = 16;
  geo.Point start_location = 18;
  double start_heading = 19;
  geo.Point end_location = 20;
  double end_heading = 21;
  bool manually_assisted = 23;
  google.protobuf.Timestamp progress_updated_at = 24;
  geo.Point progress_location = 25;
  double progress_heading = 26;

  oneof task_details {
    // core structural task types
    SequenceTask sequence = 10;
    ManualTask manual = 11;
    // tasks with specific automations available
    GoToAndFaceTask go_to_and_face = 12;
    FollowPathTask follow_path = 13;
    SetTractorStateTask tractor_state = 14;
    LaserWeedTask laser_weed = 15;
    StopAutonomyTask stop_autonomy = 17;
    GoToReversiblePathTask go_to_reversible_path = 22;
  }
}

// A `StopAutonomyTask` is to signal completion, allowing the robot to exit
// autonomy mode and do nothing.
message StopAutonomyTask {}

message LaserWeedTask {
  // start as a path / row
  carbon.geo.LineString path = 1;
  bool path_is_reversible = 2;
  bool weeding_enabled = 3;
  bool thinning_enabled = 4;
  SpatialPathTolerance tolerances = 6;
}

// A `SequenceTask` requires completing all of its child tasks, in order, with
// no overlap in time.
message SequenceTask {
  repeated Task items = 1;
  bool atomic = 2;
}

// A `ManualTask` can only be completed by a human and will always require an
// intervention request.
message ManualTask { string instructions = 1; }

// A `GoToAndFaceTask` requires that the robot move to a particular position
// and face in a particular direction. For example, this might be used to line
// up at the start of a row.
message GoToAndFaceTask {
  carbon.geo.Point point = 1;
  double heading = 2;
}

// A `GoToReversiblePathTask` requires that the robot move to one end of a path,
// facing the other end of the first path segment. This is useful for lining
// up at either end of a row (whichever is most convenient), or the start of a
// more complex path like a ground prep pass.
message GoToReversiblePathTask {
  carbon.geo.LineString path = 1;
  SpatialPathTolerance tolerances = 2;
}

message FollowPathTask {
  carbon.geo.LineString path = 1;
  SpeedSetting speed = 2;
  bool stop_on_completion = 3;
}
message SpeedSetting {
  oneof speed {
    double constant_mph = 1;
    google.protobuf.Empty remote_operator_controlled = 2;
    google.protobuf.Empty implement_controlled = 3;
  }
}

message SetTractorStateTask { repeated TractorState state = 1; }
message TractorState {
  enum Gear {
    GEAR_UNSPECIFIED = 0;
    PARK = 1;
    REVERSE = 2;
    NEUTRAL = 3;
    FORWARD = 4;
    POWERZERO = 5;
  }
  oneof state {
    Gear gear = 1;
    HitchState hitch = 2;
  }
}
message HitchState {
  enum HitchCommand {
    HITCH_COMMAND_UNSPECIFIED = 0;
    RAISED = 1;
    LOWERED = 2;
  }
  oneof state {
    HitchCommand command = 1;
    // Target position, between 0.0 (fully lowered) and 1.0 (fully raised).
    double position = 2;
  }
}

message TaskList { repeated Task tasks = 1; }

message ListTasksResponse {
  string page_token = 1;
  repeated Task tasks = 2;
}

// SpatialPathTolerance is a set of tolerance values used to evaluate geo
// spatial criteria based on location and/or heading.
message SpatialPathTolerance {
  float heading = 1;
  float crosstrack = 2;
  float distance = 3;
  float continuous_crosstrack = 4;
}

// Jobs API
message Job {
  uint64 id = 1;
  string name = 2;
  google.protobuf.Timestamp started_at = 3;
  google.protobuf.Timestamp ended_at = 4;
  repeated Objective objectives = 5;
  State state = 6;
  enum JobType {
    JOB_TYPE_UNSPECIFIED = 0;
    LASER_WEED = 1;
  }
  JobType type = 7;
  optional uint64 work_order_id = 8;
  // Farm ID per Portal, typically a 12-character alphanumeric string.
  string farm_id = 9;
  string field_id = 10;
  // Customer ID per Portal, typically a UUID.
  string customer_id = 11;
  int32 priority = 12;
  RobotWhitelist robot_whitelist = 13;
}

message JobList { repeated Job jobs = 1; }

message ListJobsResponse {
  string page_token = 1;
  repeated Job jobs = 2;
}

// Work Orders APIs
message WorkOrder {
  uint64 id = 1;
  string name = 2;
  google.protobuf.Timestamp scheduled_at = 3;
  int32 duration_minutes = 4;
  repeated Job jobs = 5;
  State state = 6;
}

message WorkOrderList { repeated WorkOrder work_orders = 1; }

message ListWorkOrdersResponse {
  string page_token = 1;
  repeated WorkOrder work_orders = 2;
}

// Intervention APIs
message Intervention {
  uint64 id = 1;
  uint64 task_id = 2;
  string qualification = 3;
  string description = 4;
  State state = 5;
  string robot_serial = 6;
  uint64 job_id = 7;
  enum InterventionCause {
    INTERVENTION_CAUSE_UNSPECIFIED = 0;
    SENSOR_TRIGGERED = 1;
    SAFETY_DRIVER_ACTION = 2;
    TRACTOR_REQUEST = 3;
  }
  InterventionCause cause = 8;
  int32 priority = 9;
  InterventionAssignment assignment = 10;
}

message InterventionList { repeated Intervention intervention = 1; }

message InterventionAssignment {
  string user_id = 1;
  google.protobuf.Timestamp assigned_at = 2;
}

message ListInterventionsRequest {
  int32 page_size = 1;
  string page_token = 2;
  string robot_serials = 3;
  string assiged_user_ids = 4;
  bool assigned = 5;
  string task_ids = 6;
  string job_ids = 7;
  string causes = 8;
  string states = 9;
}
message ListInterventionsResponse {
  string page_token = 1;
  repeated Intervention interventions = 2;
}

message CreateInterventionRequest { Intervention intervention = 1; }

message CreateInterventionResponse { Intervention intervention = 1; }

message GetActiveTaskRequest {
  geo.Point current_location = 2;
  // TODO:(smt) what other state should we provide?
}

message GetActiveTaskResponse { Task task = 1; }

message GetTaskRequest { uint64 task_id = 1; }

message GetTaskResponse { Task task = 1; }

message UpdateTaskRequest {
  uint64 task_id = 1;
  optional State state = 2;
  optional google.protobuf.Timestamp started_at = 3;
  optional geo.Point start_location = 4;
  optional double start_heading = 5;
  optional google.protobuf.Timestamp ended_at = 6;
  optional geo.Point end_location = 7;
  optional double end_heading = 8;
  optional string status_info = 9;
  optional google.protobuf.Timestamp progress_updated_at = 10;
  optional geo.Point progress_location = 11;
  optional double progress_heading = 12;
}

message UpdateTaskResponse { Task task = 1; }

message StartManualTaskRequest {
  geo.Point start_location = 1;
  double start_heading = 2;
}

message StopManualTaskRequest {
  geo.Point end_location = 1;
  double end_heading = 2;
}

service JobService {
  // Robot API
  rpc CreateIntervention(CreateInterventionRequest)
      returns (CreateInterventionResponse);
  rpc GetActiveTask(GetActiveTaskRequest) returns (GetActiveTaskResponse);
  rpc GetTask(GetTaskRequest) returns (GetTaskResponse);
  rpc GetNextActiveObjective(GetNextActiveObjectiveRequest)
      returns (GetNextActiveObjectiveResponse);
  rpc UpdateTask(UpdateTaskRequest) returns (UpdateTaskResponse);
}
