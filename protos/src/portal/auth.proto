syntax = "proto3";

package carbon.portal.auth;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/portal";

enum UserDisplayRole {
  unknown_role = 0;
  robot_role = 1;
  operator_basic = 2;
  farm_manager = 3;
  carbon_tech = 4;
  carbon_basic = 5;
  operator_advanced = 6;
}

enum PermissionAction {
  unknown_action = 0;
  read = 1;
  update = 2;
}

enum PermissionResource {
  unknown = 0;
  alarms_customer = 1;
  alarms_internal = 2;
  almanacs = 3;
  operator_settings = 4;
  banding = 5;
  banding_basic = 6;
  banding_advanced = 7;
  calibration = 8;
  cameras = 9;
  capture = 10;
  chat = 11;
  configs = 12;
  crops = 13;
  models_basic = 14;
  models_pinned = 15;
  models_nickname = 16;
  thresholds = 17;
  diagnostics = 18;
  guides = 19;
  jobs = 20;
  captcha = 21;
  lasers_row = 22;
  lasers_basic = 23;
  lasers_disable = 24;
  lasers_enable = 25;
  lasers_advanced = 26;
  lasers = 27;
  feeds = 28;
  models_advanced = 29;
  hardware = 30;
  quick_tune = 31;
  robot_ui = 32;
  thinning = 33;
  thinning_basic = 34;
  thinning_advanced = 35;
  software_basic = 36;
  software_advanced = 37;
  velocity_estimators = 38;
  vizualization_basic = 39;
  vizualization_advanced = 40;
  metrics = 41;
  mode = 42;
  jobs_select = 43;
  portal_settings = 44;
  portal_labs = 45 [ deprecated = true ];
  shortcuts_internal = 46;
  admin_alarms = 47;
  admin_cloud = 48;
  customers = 49;
  users = 50;
  users_invite = 51;
  users_permissions = 52;
  reports = 53;
  robots = 54;
  robots_assign = 55;
  robots_status = 56;
  robots_health = 57;
  veselka = 58;
  metrics_internal = 59;
  metrics_customer = 60;
  crops_basic = 61;
  crops_advanced = 62;
  discriminators = 63;
  operator_map = 64;
  uploads = 65;
  farms = 66;
  images = 67;
  autotractor_jobs = 68;
  plant_category_profiles = 69;
  portal_globals = 70;
  autotractor_drive = 71;
}

enum PermissionDomain {
  unknown_domain = 0;
  none = 1;
  self = 2;
  robot = 3;
  customer = 4;
  all = 5;
  templates = 6;
}
