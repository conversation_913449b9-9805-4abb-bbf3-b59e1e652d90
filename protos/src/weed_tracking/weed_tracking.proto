syntax = "proto3";

package weed_tracking;
option go_package = "github.com/carbonrobotics/protos/golang/generated/proto/weed_tracking";

message Empty {}

message Trajectory {
  option deprecated = true;
  uint32 id = 1;
  uint32 tracker_id = 2;
  uint32 status = 3;
  bool is_weed = 4;
  double x_mm = 5;
  double y_mm = 6;
  double z_mm = 7;
  bool intersected_with_nonshootable = 8;
  string nonshootable_type_string = 9;
  bool deduplicated_across_tracker = 10;
}
message Target {
  option deprecated = true;
  uint32 scanner_id = 1;
  uint32 trajectory_id = 2;
  uint32 next_trajectory_id = 3;
  float starting_pos_y = 4;
  float ending_pos_y = 5;
}

message Bounds {
  option deprecated = true;
  string tracker_id = 1;
  float max_pos_mm_y = 2;
}

message TrackingStatusReply {
  option deprecated = true;
  repeated Target targets = 1;
  repeated Trajectory trajectories = 2;
  repeated Bounds bounds = 3;
}

// Deepweed positions

message GetDetectionsRequest {
  string cam_id = 1;
  int64 timestamp_ms = 2;
  int32 max_x = 3;
}

message Detection {
  float x = 1;
  float y = 2;
  float size = 3;
  string clz = 4;
  bool is_weed = 5;
  bool out_of_band = 6;
  uint32 id = 7;
  float score = 8;
  float weed_score = 9;
  float crop_score = 10;
  repeated float embedding = 11;
  float plant_score = 12;
}

message Detections {
  repeated Detection detections = 1;
  int64 timestamp_ms = 2;
}

message Band {
  double start_x_px = 1;
  double end_x_px = 2;
}

message Bands {
  repeated Band band = 1;
  bool banding_enabled = 2;
  bool row_has_bands_defined = 3;
}

message GetDetectionsResponse {
  Detections detections = 1;
  Bands bands = 2;
}

message GetTrajectoryMetadataRequest {}

message TrackedItemMetadata {
  uint32 detection_id = 1;
  int64 timestamp_ms = 2;
}

message TrajectoryMetadata {
  uint32 trajectory_id = 1;
  string cam_id = 2;
  repeated TrackedItemMetadata tracked_item_metadata = 3;
  string band_status = 4;
}

message GetTrajectoryMetadataResponse {
  repeated TrajectoryMetadata metadata = 1;
}

message PingRequest { uint32 x = 1; }

message PongReply { uint32 x = 1; }

enum InvalidScoreReason {
  NONE = 0;
  ANOTHER_LASER_SHOOTING = 1;
  SCANNER_POSITION_INVALID = 2;
  WEED_ALREADY_KILLED = 3;
  WEED_SHOT_BY_ANOTHER_LASER = 4;
  WEED_OUT_OF_BAND = 5;
  NOT_A_WEED = 6;
  SCORE_NEGATIVE = 7;
  INTERSECTED_WITH_NONSHOOTABLE = 8;
  EXTERMINATION_FAILURES_EXCEEDED_MAX = 9;
  DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN = 10;
}

message TrajectoryScore {
  uint64 score = 1;
  double score_tilt = 2;
  double score_pan = 3;
  double score_busy = 4;
  double score_in_range = 5;
  double score_importance = 6;
  double score_size = 7;
}

enum KillStatus {
  STATUS_NOT_SHOT = 0;
  STATUS_BEING_SHOT = 1;
  STATUS_SHOT = 2;
  STATUS_PARTIALLY_SHOT = 3;
  STATUS_P2P_NOT_FOUND = 4;
  STATUS_ERROR = 5;
  STATUS_P2P_MISSING_CONTEXT = 6;
}

enum DuplicateStatus {
  UNIQUE = 0;
  PRIMARY = 1;
  DUPLICATE = 2;
}

enum ThinningState {
  THINNING_UNSET = 0;
  THINNING_MARKED_FOR_THINNING = 1;
  THINNING_KEPT = 2;
  THINNING_IGNORED = 3;
}

enum TargetableState {
  TARGET_NOT_IN_SCHEDULER = 0;
  TARGET_SCORED = 1;
  TARGET_INTERSECTS_NON_SHOOTABLE = 2;
  TARGET_TOO_MANY_FAILURES = 3;
  TARGET_DOO_TOO_LOW = 4;
  TARGET_IGNORED_FROM_ALMANAC = 5;
  TARGET_OUT_OF_BAND = 6;
  TARGET_AVOID_FROM_ALMANAC = 7;
}

message PerScannerScore {
  uint32 scanner_id = 1;
  int32 score = 2;
}

message ScoreState {
  TargetableState target_state = 1;
  repeated PerScannerScore scores = 2;
}

message Pos2D {
  double x = 1;
  double y = 2;
}

message KillBox {
  uint32 scanner_id = 1;
  Pos2D top_left = 2;
  Pos2D bottom_right = 3;
}
message Thresholds {
  float weeding = 1 [ deprecated = true ];
  float thinning = 2 [ deprecated = true ];
  float banding = 3 [ deprecated = true ];
  bool passed_weeding = 4;
  bool passed_thinning = 5;
  bool passed_banding = 6;
}

message Decisions {
  bool weeding_weed = 1;
  bool weeding_crop = 2;
  bool thinning_weed = 3;
  bool thinning_crop = 4;
  bool keepable_crop = 5;
  bool banding_crop = 6;
}

enum Classification {
  CLASS_UNDECIDED = 0; // class not yet decided
  CLASS_WEED = 1;
  CLASS_CROP = 2;
  CLASS_BOTH = 3;
  CLASS_UNKNOWN = 4;
}

message TrajectorySnapshot {
  uint32 id = 1;
  KillStatus kill_status = 2;
  bool is_weed = 3 [ deprecated = true ]; // deprecated 2.1
  double x_mm = 4;
  double y_mm = 5;
  double z_mm = 6;
  oneof score_details { // Deprecated 1.16
    TrajectoryScore score = 7;
    InvalidScoreReason invalid_score = 8;
  }
  double radius_mm = 9;
  string category = 10;
  uint64 shoot_time_requested_ms = 11;
  uint64 shoot_time_actual_ms = 12;
  bool marked_for_thinning = 13;
  uint32 tracker_id = 14;
  DuplicateStatus duplicate_status = 15;
  uint32 duplicate_trajectory_id = 16;
  repeated uint32 assigned_lasers = 17;
  bool out_of_band = 18;                   // deprecated 1.16 use score state
  bool intersected_with_nonshootable = 19; // deprecated 1.16
  map<string, double> detection_classes = 20;
  double confidence = 21;
  ThinningState thinning_state = 22;
  double global_pos = 23;
  ScoreState score_state = 24;
  float doo = 25;
  uint32 distance_perspectives_count = 26;
  int32 size_category_index =
      27; // as returned from almanac, 0 for small, 1 for medium, 2 for large
  bool speculative_allowed = 28;
  bool protected_by_traj = 29; // crop protect / reverse crop protect
  Thresholds thresholds = 30;
  // new fields
  Decisions decisions = 31;
  double crop_score = 32;
  double weed_score = 33;
  double plant_score = 34;
  uint32 num_detections_used_for_decision = 35;
  Classification classification = 36;
  map<string, float> embedding_distances = 37;
  uint32 speculative_shoot_time_actual_ms = 38;
  SnapshotMetadata snapshot_metadata = 39;
}

message SnapshotMetadata {
  string pcam_id = 1;
  int64 timestamp_ms = 2;
  float center_x_px = 3;
  float center_y_px = 4;
}

message BandDefinition {
  float offset_mm = 1;
  float width_mm = 2;
  int32 id = 3;
}

// Avoid nested messages
message CLDAlgorithmSnapshot {
  uint64 timestamp_ms = 1;
  repeated float graph_points_x = 2;
  repeated float graph_points_y = 3;
  repeated float minimas_x = 4;
  repeated float minimas_y = 5;
  repeated float lines = 6;
}

message DiagnosticsSnapshot {
  uint64 timestamp_ms = 1;
  repeated TrajectorySnapshot trajectories = 2;
  repeated BandDefinition bands = 3;
  repeated KillBox kill_boxes = 4;
  optional CLDAlgorithmSnapshot banding_algorithm_snapshot = 5;
}

message RecordDiagnosticsRequest {
  uint32 ttl_sec = 1;
  float crop_images_per_sec = 2;
  float weed_images_per_sec = 3;
}

enum RecordingStatus {
  NOT_RECORDING = 0;
  RECORDING_STARTED = 1;
  RECORDING_FINISHED = 2;
  RECORDING_FAILED = 3;
}

message GetRecordingStatusResponse { RecordingStatus status = 1; }

message StartSavingCropLineDetectionReplayRequest {
  string filename = 1;
  uint32 ttl_ms = 2;
}

message RecordAimbotInputRequest {
  string name = 1;
  uint32 ttl_ms = 2;
  optional bool rotary_ticks = 3;
  optional bool deepweed = 4;
  optional bool lane_heights = 5;
}

enum ConclusionType {
  NOT_WEEDING = 0;
  OUT_OF_BAND = 1;
  INTERSECTS_WITH_NON_SHOOTABLE = 2;
  OUT_OF_RANGE = 3;
  UNIMPORTANT = 4;
  NOT_SHOT = 5;
  PARTIALLY_SHOT = 6;
  SHOT = 7;
  P2P_NOT_FOUND = 8;
  ERROR = 9;
  FLICKER = 10;
  MARKED_FOR_THINNING = 11;
  NOT_TARGETED = 12;
  P2P_MISSING_CONTEXT = 13;
};

message ConclusionCount {
  ConclusionType type = 1;
  uint32 count = 2;
}

message ConclusionCounter { repeated ConclusionCount counts = 1; }

message BandDefinitions {
  repeated BandDefinition bands = 1;
  bool banding_enabled = 2;
  bool row_has_bands_defined = 3;
}

enum PlantCaptchaStatus {
  NOT_STARTED = 0;
  CAPTCHA_STARTED = 1;
  CAPTCHA_FINISHED = 2;
  CAPTCHA_FAILED = 3;
  CAPTCHA_CANCELLED = 4;
}

message PlantCaptchaStatusResponse {
  PlantCaptchaStatus status = 1;
  int32 total_images = 2;
  int32 images_taken = 3;
  int32 metadata_taken = 4;
  map<string, int32> exemplar_counts = 5;
}

message Embedding { repeated float elements = 1; }

enum PlantCaptchaUserPrediction {
  WEED = 0;
  CROP = 1;
  UNKNOWN = 2;
  OTHER = 3;
  IGNORE = 4; // Only used in initial state
  VOLUNTEER = 5;
  BENEFICIAL = 6;
  DEBRIS = 7;
}

message WeedClasses { map<string, float> classes = 1; }

message PlantCaptchaItemMetadata {
  float confidence = 1;
  int32 x_px = 2;
  int32 y_px = 3;
  double x_mm = 4;
  double y_mm = 5;
  double z_mm = 6;
  float size_mm = 7;
  map<string, float> categories = 8;
  float doo = 9;
  bool is_weed = 10;
  bool intersected_with_nonshootable = 11;
  repeated float confidence_history = 12;
  bool is_in_band = 13;
  string id = 14;
  float size_px = 15;
  uint32 shoot_time_ms = 16;
  repeated float weed_confidence_history = 17;
  repeated float crop_confidence_history = 18;
  int32 size_category_index =
      19; // as returned from almanac, 0 for small, 1 for medium, 2 for large
  map<string, float> weed_categories = 20;
  repeated Embedding embedding_history = 21;
  PlantCaptchaUserPrediction initial_label = 22;
  repeated float plant_confidence_history = 23;
  repeated WeedClasses weed_classes_history = 24;
  repeated float size_mm_history = 25;
  Decisions decisions = 26;
  uint32 num_detections_used_for_decision = 27;
  map<string, float> embedding_distances = 28;
}

message GetTargetingEnabledRequest {}
message GetTargetingEnabledResponse { bool enabled = 1; }
message GetBootedRequest {}
message GetBootedResponse { bool booted = 1; }

service WeedTrackingService {
  rpc Ping(PingRequest) returns (PongReply) {}
  rpc GetDetections(GetDetectionsRequest) returns (GetDetectionsResponse);
  rpc GetTrajectoryMetadata(GetTrajectoryMetadataRequest)
      returns (GetTrajectoryMetadataResponse);
  rpc UpdateBands(Empty) returns (Empty);
  rpc GetBooted(GetBootedRequest) returns (GetBootedResponse) {}

  // weeding diagnostics
  rpc GetCurrentTrajectories(Empty) returns (DiagnosticsSnapshot);
  rpc StartSavingCropLineDetectionReplay(
      StartSavingCropLineDetectionReplayRequest) returns (Empty);
  rpc StartRecordingDiagnostics(RecordDiagnosticsRequest) returns (Empty);
  rpc GetDiagnosticsRecordingStatus(Empty) returns (GetRecordingStatusResponse);
  rpc RemoveRecordingsDirectory(Empty) returns (Empty);
  rpc StartRecordingAimbotInputs(RecordAimbotInputRequest) returns (Empty);
  rpc GetConclusionCounter(Empty) returns (ConclusionCounter);
  rpc GetBands(Empty) returns (BandDefinitions);
  rpc StartPlantCaptcha(Empty) returns (Empty);
  rpc GetPlantCaptchaStatus(Empty) returns (PlantCaptchaStatusResponse);
  rpc RemovePlantCaptchaDirectory(Empty) returns (Empty);
  rpc CancelPlantCaptcha(Empty) returns (Empty);
  rpc GetTargetingEnabled(GetTargetingEnabledRequest)
      returns (GetTargetingEnabledResponse);
}
