// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: frontend/weeding_diagnostics.proto

package frontend

import (
	aimbot "github.com/carbonrobotics/protos/golang/generated/proto/aimbot"
	cv "github.com/carbonrobotics/protos/golang/generated/proto/cv"
	recorder "github.com/carbonrobotics/protos/golang/generated/proto/recorder"
	thinning "github.com/carbonrobotics/protos/golang/generated/proto/thinning"
	util "github.com/carbonrobotics/protos/golang/generated/proto/util"
	weed_tracking "github.com/carbonrobotics/protos/golang/generated/proto/weed_tracking"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadState int32

const (
	UploadState_NONE        UploadState = 0
	UploadState_IN_PROGRESS UploadState = 1
	UploadState_DONE        UploadState = 2
)

// Enum value maps for UploadState.
var (
	UploadState_name = map[int32]string{
		0: "NONE",
		1: "IN_PROGRESS",
		2: "DONE",
	}
	UploadState_value = map[string]int32{
		"NONE":        0,
		"IN_PROGRESS": 1,
		"DONE":        2,
	}
)

func (x UploadState) Enum() *UploadState {
	p := new(UploadState)
	*p = x
	return p
}

func (x UploadState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadState) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_weeding_diagnostics_proto_enumTypes[0].Descriptor()
}

func (UploadState) Type() protoreflect.EnumType {
	return &file_frontend_weeding_diagnostics_proto_enumTypes[0]
}

func (x UploadState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadState.Descriptor instead.
func (UploadState) EnumDescriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{0}
}

type RecordWeedingDiagnosticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TtlSec           uint32  `protobuf:"varint,2,opt,name=ttl_sec,json=ttlSec,proto3" json:"ttl_sec,omitempty"`
	CropImagesPerSec float32 `protobuf:"fixed32,3,opt,name=crop_images_per_sec,json=cropImagesPerSec,proto3" json:"crop_images_per_sec,omitempty"`
	WeedImagesPerSec float32 `protobuf:"fixed32,4,opt,name=weed_images_per_sec,json=weedImagesPerSec,proto3" json:"weed_images_per_sec,omitempty"`
}

func (x *RecordWeedingDiagnosticsRequest) Reset() {
	*x = RecordWeedingDiagnosticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordWeedingDiagnosticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordWeedingDiagnosticsRequest) ProtoMessage() {}

func (x *RecordWeedingDiagnosticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordWeedingDiagnosticsRequest.ProtoReflect.Descriptor instead.
func (*RecordWeedingDiagnosticsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{0}
}

func (x *RecordWeedingDiagnosticsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecordWeedingDiagnosticsRequest) GetTtlSec() uint32 {
	if x != nil {
		return x.TtlSec
	}
	return 0
}

func (x *RecordWeedingDiagnosticsRequest) GetCropImagesPerSec() float32 {
	if x != nil {
		return x.CropImagesPerSec
	}
	return 0
}

func (x *RecordWeedingDiagnosticsRequest) GetWeedImagesPerSec() float32 {
	if x != nil {
		return x.WeedImagesPerSec
	}
	return 0
}

type GetCurrentTrajectoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId uint32 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
}

func (x *GetCurrentTrajectoriesRequest) Reset() {
	*x = GetCurrentTrajectoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrentTrajectoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentTrajectoriesRequest) ProtoMessage() {}

func (x *GetCurrentTrajectoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentTrajectoriesRequest.ProtoReflect.Descriptor instead.
func (*GetCurrentTrajectoriesRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{1}
}

func (x *GetCurrentTrajectoriesRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

type GetRecordingsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRecordingsListRequest) Reset() {
	*x = GetRecordingsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordingsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordingsListRequest) ProtoMessage() {}

func (x *GetRecordingsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordingsListRequest.ProtoReflect.Descriptor instead.
func (*GetRecordingsListRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{2}
}

type GetRecordingsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name []string `protobuf:"bytes,1,rep,name=name,proto3" json:"name,omitempty"`
}

func (x *GetRecordingsListResponse) Reset() {
	*x = GetRecordingsListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordingsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordingsListResponse) ProtoMessage() {}

func (x *GetRecordingsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordingsListResponse.ProtoReflect.Descriptor instead.
func (*GetRecordingsListResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{3}
}

func (x *GetRecordingsListResponse) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

type GetSnapshotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName  string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId          uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	SnapshotNumber uint32 `protobuf:"varint,3,opt,name=snapshot_number,json=snapshotNumber,proto3" json:"snapshot_number,omitempty"`
}

func (x *GetSnapshotRequest) Reset() {
	*x = GetSnapshotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSnapshotRequest) ProtoMessage() {}

func (x *GetSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSnapshotRequest.ProtoReflect.Descriptor instead.
func (*GetSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{4}
}

func (x *GetSnapshotRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetSnapshotRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetSnapshotRequest) GetSnapshotNumber() uint32 {
	if x != nil {
		return x.SnapshotNumber
	}
	return 0
}

type GetSnapshotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snapshot *weed_tracking.DiagnosticsSnapshot `protobuf:"bytes,1,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
}

func (x *GetSnapshotResponse) Reset() {
	*x = GetSnapshotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSnapshotResponse) ProtoMessage() {}

func (x *GetSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSnapshotResponse.ProtoReflect.Descriptor instead.
func (*GetSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{5}
}

func (x *GetSnapshotResponse) GetSnapshot() *weed_tracking.DiagnosticsSnapshot {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

type OpenRecordingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
}

func (x *OpenRecordingRequest) Reset() {
	*x = OpenRecordingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenRecordingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRecordingRequest) ProtoMessage() {}

func (x *OpenRecordingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRecordingRequest.ProtoReflect.Descriptor instead.
func (*OpenRecordingRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{6}
}

func (x *OpenRecordingRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

type TrajectoriesWithImages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *TrajectoriesWithImages) Reset() {
	*x = TrajectoriesWithImages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrajectoriesWithImages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrajectoriesWithImages) ProtoMessage() {}

func (x *TrajectoriesWithImages) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrajectoriesWithImages.ProtoReflect.Descriptor instead.
func (*TrajectoriesWithImages) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{7}
}

func (x *TrajectoriesWithImages) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type PredictImages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
}

func (x *PredictImages) Reset() {
	*x = PredictImages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictImages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictImages) ProtoMessage() {}

func (x *PredictImages) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictImages.ProtoReflect.Descriptor instead.
func (*PredictImages) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{8}
}

func (x *PredictImages) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

type PredictImagesPerCam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Images map[int32]*PredictImages `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PredictImagesPerCam) Reset() {
	*x = PredictImagesPerCam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictImagesPerCam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictImagesPerCam) ProtoMessage() {}

func (x *PredictImagesPerCam) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictImagesPerCam.ProtoReflect.Descriptor instead.
func (*PredictImagesPerCam) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{9}
}

func (x *PredictImagesPerCam) GetImages() map[int32]*PredictImages {
	if x != nil {
		return x.Images
	}
	return nil
}

type OpenRecordingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumSnapshotsPerRow     map[uint32]uint32                  `protobuf:"bytes,1,rep,name=num_snapshots_per_row,json=numSnapshotsPerRow,proto3" json:"num_snapshots_per_row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	RecordingData          *StaticRecordingData               `protobuf:"bytes,2,opt,name=recording_data,json=recordingData,proto3" json:"recording_data,omitempty"`
	TrajectoryImagesPerRow map[uint32]*TrajectoriesWithImages `protobuf:"bytes,3,rep,name=trajectory_images_per_row,json=trajectoryImagesPerRow,proto3" json:"trajectory_images_per_row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PredictImagesPerRow    map[uint32]*PredictImagesPerCam    `protobuf:"bytes,4,rep,name=predict_images_per_row,json=predictImagesPerRow,proto3" json:"predict_images_per_row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *OpenRecordingResponse) Reset() {
	*x = OpenRecordingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenRecordingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRecordingResponse) ProtoMessage() {}

func (x *OpenRecordingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRecordingResponse.ProtoReflect.Descriptor instead.
func (*OpenRecordingResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{10}
}

func (x *OpenRecordingResponse) GetNumSnapshotsPerRow() map[uint32]uint32 {
	if x != nil {
		return x.NumSnapshotsPerRow
	}
	return nil
}

func (x *OpenRecordingResponse) GetRecordingData() *StaticRecordingData {
	if x != nil {
		return x.RecordingData
	}
	return nil
}

func (x *OpenRecordingResponse) GetTrajectoryImagesPerRow() map[uint32]*TrajectoriesWithImages {
	if x != nil {
		return x.TrajectoryImagesPerRow
	}
	return nil
}

func (x *OpenRecordingResponse) GetPredictImagesPerRow() map[uint32]*PredictImagesPerCam {
	if x != nil {
		return x.PredictImagesPerRow
	}
	return nil
}

type DeleteRecordingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
}

func (x *DeleteRecordingRequest) Reset() {
	*x = DeleteRecordingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRecordingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRecordingRequest) ProtoMessage() {}

func (x *DeleteRecordingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRecordingRequest.ProtoReflect.Descriptor instead.
func (*DeleteRecordingRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteRecordingRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

type ConfigNodeSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values     map[string]string              `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ChildNodes map[string]*ConfigNodeSnapshot `protobuf:"bytes,2,rep,name=child_nodes,json=childNodes,proto3" json:"child_nodes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ConfigNodeSnapshot) Reset() {
	*x = ConfigNodeSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigNodeSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigNodeSnapshot) ProtoMessage() {}

func (x *ConfigNodeSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigNodeSnapshot.ProtoReflect.Descriptor instead.
func (*ConfigNodeSnapshot) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{12}
}

func (x *ConfigNodeSnapshot) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *ConfigNodeSnapshot) GetChildNodes() map[string]*ConfigNodeSnapshot {
	if x != nil {
		return x.ChildNodes
	}
	return nil
}

type CameraDimensions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width  uint32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height uint32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *CameraDimensions) Reset() {
	*x = CameraDimensions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraDimensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraDimensions) ProtoMessage() {}

func (x *CameraDimensions) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraDimensions.ProtoReflect.Descriptor instead.
func (*CameraDimensions) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{13}
}

func (x *CameraDimensions) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *CameraDimensions) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type RowCameras struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cams map[string]*CameraDimensions `protobuf:"bytes,1,rep,name=cams,proto3" json:"cams,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RowCameras) Reset() {
	*x = RowCameras{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RowCameras) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RowCameras) ProtoMessage() {}

func (x *RowCameras) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RowCameras.ProtoReflect.Descriptor instead.
func (*RowCameras) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{14}
}

func (x *RowCameras) GetCams() map[string]*CameraDimensions {
	if x != nil {
		return x.Cams
	}
	return nil
}

type StaticRecordingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LasersEnabled             map[string]bool                          `protobuf:"bytes,1,rep,name=lasers_enabled,json=lasersEnabled,proto3" json:"lasers_enabled,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	RootConfig                *ConfigNodeSnapshot                      `protobuf:"bytes,2,opt,name=root_config,json=rootConfig,proto3" json:"root_config,omitempty"`
	RowsRecorded              []int32                                  `protobuf:"varint,3,rep,packed,name=rows_recorded,json=rowsRecorded,proto3" json:"rows_recorded,omitempty"`
	RowDimensions             map[uint32]*aimbot.GetDimensionsResponse `protobuf:"bytes,4,rep,name=row_dimensions,json=rowDimensions,proto3" json:"row_dimensions,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CropSafetyRadiusMmPerRow  map[uint32]float32                       `protobuf:"bytes,5,rep,name=crop_safety_radius_mm_per_row,json=cropSafetyRadiusMmPerRow,proto3" json:"crop_safety_radius_mm_per_row,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	ThinningConfig            *thinning.ConfigDefinition               `protobuf:"bytes,6,opt,name=thinning_config,json=thinningConfig,proto3" json:"thinning_config,omitempty"`
	RecordingTimestamp        int64                                    `protobuf:"varint,7,opt,name=recording_timestamp,json=recordingTimestamp,proto3" json:"recording_timestamp,omitempty"`
	RowCameras                map[uint32]*RowCameras                   `protobuf:"bytes,8,rep,name=row_cameras,json=rowCameras,proto3" json:"row_cameras,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WeedPointThreshold        float32                                  `protobuf:"fixed32,9,opt,name=weed_point_threshold,json=weedPointThreshold,proto3" json:"weed_point_threshold,omitempty"`
	CropPointThreshold        float32                                  `protobuf:"fixed32,10,opt,name=crop_point_threshold,json=cropPointThreshold,proto3" json:"crop_point_threshold,omitempty"`
	WheelDiameterBackLeftIn   float32                                  `protobuf:"fixed32,11,opt,name=wheel_diameter_back_left_in,json=wheelDiameterBackLeftIn,proto3" json:"wheel_diameter_back_left_in,omitempty"`
	WheelDiameterBackRightIn  float32                                  `protobuf:"fixed32,12,opt,name=wheel_diameter_back_right_in,json=wheelDiameterBackRightIn,proto3" json:"wheel_diameter_back_right_in,omitempty"`
	WheelDiameterFrontLeftIn  float32                                  `protobuf:"fixed32,13,opt,name=wheel_diameter_front_left_in,json=wheelDiameterFrontLeftIn,proto3" json:"wheel_diameter_front_left_in,omitempty"`
	WheelDiameterFrontRightIn float32                                  `protobuf:"fixed32,14,opt,name=wheel_diameter_front_right_in,json=wheelDiameterFrontRightIn,proto3" json:"wheel_diameter_front_right_in,omitempty"`
}

func (x *StaticRecordingData) Reset() {
	*x = StaticRecordingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaticRecordingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticRecordingData) ProtoMessage() {}

func (x *StaticRecordingData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticRecordingData.ProtoReflect.Descriptor instead.
func (*StaticRecordingData) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{15}
}

func (x *StaticRecordingData) GetLasersEnabled() map[string]bool {
	if x != nil {
		return x.LasersEnabled
	}
	return nil
}

func (x *StaticRecordingData) GetRootConfig() *ConfigNodeSnapshot {
	if x != nil {
		return x.RootConfig
	}
	return nil
}

func (x *StaticRecordingData) GetRowsRecorded() []int32 {
	if x != nil {
		return x.RowsRecorded
	}
	return nil
}

func (x *StaticRecordingData) GetRowDimensions() map[uint32]*aimbot.GetDimensionsResponse {
	if x != nil {
		return x.RowDimensions
	}
	return nil
}

func (x *StaticRecordingData) GetCropSafetyRadiusMmPerRow() map[uint32]float32 {
	if x != nil {
		return x.CropSafetyRadiusMmPerRow
	}
	return nil
}

func (x *StaticRecordingData) GetThinningConfig() *thinning.ConfigDefinition {
	if x != nil {
		return x.ThinningConfig
	}
	return nil
}

func (x *StaticRecordingData) GetRecordingTimestamp() int64 {
	if x != nil {
		return x.RecordingTimestamp
	}
	return 0
}

func (x *StaticRecordingData) GetRowCameras() map[uint32]*RowCameras {
	if x != nil {
		return x.RowCameras
	}
	return nil
}

func (x *StaticRecordingData) GetWeedPointThreshold() float32 {
	if x != nil {
		return x.WeedPointThreshold
	}
	return 0
}

func (x *StaticRecordingData) GetCropPointThreshold() float32 {
	if x != nil {
		return x.CropPointThreshold
	}
	return 0
}

func (x *StaticRecordingData) GetWheelDiameterBackLeftIn() float32 {
	if x != nil {
		return x.WheelDiameterBackLeftIn
	}
	return 0
}

func (x *StaticRecordingData) GetWheelDiameterBackRightIn() float32 {
	if x != nil {
		return x.WheelDiameterBackRightIn
	}
	return 0
}

func (x *StaticRecordingData) GetWheelDiameterFrontLeftIn() float32 {
	if x != nil {
		return x.WheelDiameterFrontLeftIn
	}
	return 0
}

func (x *StaticRecordingData) GetWheelDiameterFrontRightIn() float32 {
	if x != nil {
		return x.WheelDiameterFrontRightIn
	}
	return 0
}

type GetTrajectoryDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	TrajectoryId  uint32 `protobuf:"varint,3,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
}

func (x *GetTrajectoryDataRequest) Reset() {
	*x = GetTrajectoryDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrajectoryDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrajectoryDataRequest) ProtoMessage() {}

func (x *GetTrajectoryDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrajectoryDataRequest.ProtoReflect.Descriptor instead.
func (*GetTrajectoryDataRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{16}
}

func (x *GetTrajectoryDataRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetTrajectoryDataRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetTrajectoryDataRequest) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

type LastSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trajectory                *weed_tracking.TrajectorySnapshot `protobuf:"bytes,1,opt,name=trajectory,proto3" json:"trajectory,omitempty"`
	DiagnosticsSnapshotNumber uint32                            `protobuf:"varint,2,opt,name=diagnostics_snapshot_number,json=diagnosticsSnapshotNumber,proto3" json:"diagnostics_snapshot_number,omitempty"`
}

func (x *LastSnapshot) Reset() {
	*x = LastSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LastSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LastSnapshot) ProtoMessage() {}

func (x *LastSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LastSnapshot.ProtoReflect.Descriptor instead.
func (*LastSnapshot) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{17}
}

func (x *LastSnapshot) GetTrajectory() *weed_tracking.TrajectorySnapshot {
	if x != nil {
		return x.Trajectory
	}
	return nil
}

func (x *LastSnapshot) GetDiagnosticsSnapshotNumber() uint32 {
	if x != nil {
		return x.DiagnosticsSnapshotNumber
	}
	return 0
}

type ExtraTrajectoryField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *ExtraTrajectoryField) Reset() {
	*x = ExtraTrajectoryField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtraTrajectoryField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraTrajectoryField) ProtoMessage() {}

func (x *ExtraTrajectoryField) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraTrajectoryField.ProtoReflect.Descriptor instead.
func (*ExtraTrajectoryField) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{18}
}

func (x *ExtraTrajectoryField) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ExtraTrajectoryField) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ExtraTrajectoryField) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type TargetImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Timestamp   uint64 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	P2PPredictX uint32 `protobuf:"varint,3,opt,name=p2p_predict_x,json=p2pPredictX,proto3" json:"p2p_predict_x,omitempty"`
	P2PPredictY uint32 `protobuf:"varint,4,opt,name=p2p_predict_y,json=p2pPredictY,proto3" json:"p2p_predict_y,omitempty"`
}

func (x *TargetImage) Reset() {
	*x = TargetImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetImage) ProtoMessage() {}

func (x *TargetImage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetImage.ProtoReflect.Descriptor instead.
func (*TargetImage) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{19}
}

func (x *TargetImage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TargetImage) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *TargetImage) GetP2PPredictX() uint32 {
	if x != nil {
		return x.P2PPredictX
	}
	return 0
}

func (x *TargetImage) GetP2PPredictY() uint32 {
	if x != nil {
		return x.P2PPredictY
	}
	return 0
}

type P2PPredictImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	CenterXPx int32           `protobuf:"varint,2,opt,name=center_x_px,json=centerXPx,proto3" json:"center_x_px,omitempty"`
	CenterYPx int32           `protobuf:"varint,3,opt,name=center_y_px,json=centerYPx,proto3" json:"center_y_px,omitempty"`
	RadiusPx  int32           `protobuf:"varint,4,opt,name=radius_px,json=radiusPx,proto3" json:"radius_px,omitempty"`
	Ts        *util.Timestamp `protobuf:"bytes,5,opt,name=ts,proto3" json:"ts,omitempty"`
	PcamId    string          `protobuf:"bytes,6,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
}

func (x *P2PPredictImage) Reset() {
	*x = P2PPredictImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2PPredictImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2PPredictImage) ProtoMessage() {}

func (x *P2PPredictImage) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2PPredictImage.ProtoReflect.Descriptor instead.
func (*P2PPredictImage) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{20}
}

func (x *P2PPredictImage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *P2PPredictImage) GetCenterXPx() int32 {
	if x != nil {
		return x.CenterXPx
	}
	return 0
}

func (x *P2PPredictImage) GetCenterYPx() int32 {
	if x != nil {
		return x.CenterYPx
	}
	return 0
}

func (x *P2PPredictImage) GetRadiusPx() int32 {
	if x != nil {
		return x.RadiusPx
	}
	return 0
}

func (x *P2PPredictImage) GetTs() *util.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *P2PPredictImage) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

type TrajectoryPredictImageMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CenterXPx int32           `protobuf:"varint,1,opt,name=center_x_px,json=centerXPx,proto3" json:"center_x_px,omitempty"`
	CenterYPx int32           `protobuf:"varint,2,opt,name=center_y_px,json=centerYPx,proto3" json:"center_y_px,omitempty"`
	RadiusPx  int32           `protobuf:"varint,3,opt,name=radius_px,json=radiusPx,proto3" json:"radius_px,omitempty"`
	Ts        *util.Timestamp `protobuf:"bytes,4,opt,name=ts,proto3" json:"ts,omitempty"`
	PcamId    string          `protobuf:"bytes,5,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
}

func (x *TrajectoryPredictImageMetadata) Reset() {
	*x = TrajectoryPredictImageMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrajectoryPredictImageMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrajectoryPredictImageMetadata) ProtoMessage() {}

func (x *TrajectoryPredictImageMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrajectoryPredictImageMetadata.ProtoReflect.Descriptor instead.
func (*TrajectoryPredictImageMetadata) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{21}
}

func (x *TrajectoryPredictImageMetadata) GetCenterXPx() int32 {
	if x != nil {
		return x.CenterXPx
	}
	return 0
}

func (x *TrajectoryPredictImageMetadata) GetCenterYPx() int32 {
	if x != nil {
		return x.CenterYPx
	}
	return 0
}

func (x *TrajectoryPredictImageMetadata) GetRadiusPx() int32 {
	if x != nil {
		return x.RadiusPx
	}
	return 0
}

func (x *TrajectoryPredictImageMetadata) GetTs() *util.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *TrajectoryPredictImageMetadata) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

type TrajectoryData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PredictImage     *TrajectoryPredictImageMetadata `protobuf:"bytes,1,opt,name=predict_image,json=predictImage,proto3" json:"predict_image,omitempty"`
	TargetImages     []*TargetImage                  `protobuf:"bytes,2,rep,name=target_images,json=targetImages,proto3" json:"target_images,omitempty"`
	LastSnapshot     *LastSnapshot                   `protobuf:"bytes,3,opt,name=last_snapshot,json=lastSnapshot,proto3" json:"last_snapshot,omitempty"`
	ExtraFields      []*ExtraTrajectoryField         `protobuf:"bytes,4,rep,name=extra_fields,json=extraFields,proto3" json:"extra_fields,omitempty"`
	CrosshairX       uint32                          `protobuf:"varint,5,opt,name=crosshair_x,json=crosshairX,proto3" json:"crosshair_x,omitempty"`
	CrosshairY       uint32                          `protobuf:"varint,6,opt,name=crosshair_y,json=crosshairY,proto3" json:"crosshair_y,omitempty"`
	P2PPredictImages []*P2PPredictImage              `protobuf:"bytes,7,rep,name=p2p_predict_images,json=p2pPredictImages,proto3" json:"p2p_predict_images,omitempty"`
}

func (x *TrajectoryData) Reset() {
	*x = TrajectoryData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrajectoryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrajectoryData) ProtoMessage() {}

func (x *TrajectoryData) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrajectoryData.ProtoReflect.Descriptor instead.
func (*TrajectoryData) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{22}
}

func (x *TrajectoryData) GetPredictImage() *TrajectoryPredictImageMetadata {
	if x != nil {
		return x.PredictImage
	}
	return nil
}

func (x *TrajectoryData) GetTargetImages() []*TargetImage {
	if x != nil {
		return x.TargetImages
	}
	return nil
}

func (x *TrajectoryData) GetLastSnapshot() *LastSnapshot {
	if x != nil {
		return x.LastSnapshot
	}
	return nil
}

func (x *TrajectoryData) GetExtraFields() []*ExtraTrajectoryField {
	if x != nil {
		return x.ExtraFields
	}
	return nil
}

func (x *TrajectoryData) GetCrosshairX() uint32 {
	if x != nil {
		return x.CrosshairX
	}
	return 0
}

func (x *TrajectoryData) GetCrosshairY() uint32 {
	if x != nil {
		return x.CrosshairY
	}
	return 0
}

func (x *TrajectoryData) GetP2PPredictImages() []*P2PPredictImage {
	if x != nil {
		return x.P2PPredictImages
	}
	return nil
}

type GetTrajectoryPredictImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	TrajectoryId  uint32 `protobuf:"varint,3,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
}

func (x *GetTrajectoryPredictImageRequest) Reset() {
	*x = GetTrajectoryPredictImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrajectoryPredictImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrajectoryPredictImageRequest) ProtoMessage() {}

func (x *GetTrajectoryPredictImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrajectoryPredictImageRequest.ProtoReflect.Descriptor instead.
func (*GetTrajectoryPredictImageRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{23}
}

func (x *GetTrajectoryPredictImageRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetTrajectoryPredictImageRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetTrajectoryPredictImageRequest) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

type GetTrajectoryTargetImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	TrajectoryId  uint32 `protobuf:"varint,3,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
	ImageName     string `protobuf:"bytes,4,opt,name=image_name,json=imageName,proto3" json:"image_name,omitempty"`
}

func (x *GetTrajectoryTargetImageRequest) Reset() {
	*x = GetTrajectoryTargetImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrajectoryTargetImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrajectoryTargetImageRequest) ProtoMessage() {}

func (x *GetTrajectoryTargetImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrajectoryTargetImageRequest.ProtoReflect.Descriptor instead.
func (*GetTrajectoryTargetImageRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{24}
}

func (x *GetTrajectoryTargetImageRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetTrajectoryTargetImageRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetTrajectoryTargetImageRequest) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

func (x *GetTrajectoryTargetImageRequest) GetImageName() string {
	if x != nil {
		return x.ImageName
	}
	return ""
}

type ImageChunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageChunk []byte `protobuf:"bytes,1,opt,name=image_chunk,json=imageChunk,proto3" json:"image_chunk,omitempty"`
}

func (x *ImageChunk) Reset() {
	*x = ImageChunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageChunk) ProtoMessage() {}

func (x *ImageChunk) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageChunk.ProtoReflect.Descriptor instead.
func (*ImageChunk) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{25}
}

func (x *ImageChunk) GetImageChunk() []byte {
	if x != nil {
		return x.ImageChunk
	}
	return nil
}

type GetPredictImageMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	ImageName     string `protobuf:"bytes,3,opt,name=image_name,json=imageName,proto3" json:"image_name,omitempty"`
}

func (x *GetPredictImageMetadataRequest) Reset() {
	*x = GetPredictImageMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictImageMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictImageMetadataRequest) ProtoMessage() {}

func (x *GetPredictImageMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictImageMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetPredictImageMetadataRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{26}
}

func (x *GetPredictImageMetadataRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetPredictImageMetadataRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetPredictImageMetadataRequest) GetImageName() string {
	if x != nil {
		return x.ImageName
	}
	return ""
}

type GetPredictImageMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts                           *util.Timestamp    `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Annotations                  *Annotations       `protobuf:"bytes,2,opt,name=annotations,proto3" json:"annotations,omitempty"`
	DeepweedOutput               *cv.DeepweedOutput `protobuf:"bytes,3,opt,name=deepweed_output,json=deepweedOutput,proto3" json:"deepweed_output,omitempty"`
	DeepweedOutputBelowThreshold *cv.DeepweedOutput `protobuf:"bytes,4,opt,name=deepweed_output_below_threshold,json=deepweedOutputBelowThreshold,proto3" json:"deepweed_output_below_threshold,omitempty"`
}

func (x *GetPredictImageMetadataResponse) Reset() {
	*x = GetPredictImageMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictImageMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictImageMetadataResponse) ProtoMessage() {}

func (x *GetPredictImageMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictImageMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetPredictImageMetadataResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{27}
}

func (x *GetPredictImageMetadataResponse) GetTs() *util.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetPredictImageMetadataResponse) GetAnnotations() *Annotations {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *GetPredictImageMetadataResponse) GetDeepweedOutput() *cv.DeepweedOutput {
	if x != nil {
		return x.DeepweedOutput
	}
	return nil
}

func (x *GetPredictImageMetadataResponse) GetDeepweedOutputBelowThreshold() *cv.DeepweedOutput {
	if x != nil {
		return x.DeepweedOutputBelowThreshold
	}
	return nil
}

type GetPredictImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	ImageName     string `protobuf:"bytes,3,opt,name=image_name,json=imageName,proto3" json:"image_name,omitempty"`
}

func (x *GetPredictImageRequest) Reset() {
	*x = GetPredictImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPredictImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPredictImageRequest) ProtoMessage() {}

func (x *GetPredictImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPredictImageRequest.ProtoReflect.Descriptor instead.
func (*GetPredictImageRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{28}
}

func (x *GetPredictImageRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetPredictImageRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *GetPredictImageRequest) GetImageName() string {
	if x != nil {
		return x.ImageName
	}
	return ""
}

type StartUploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
}

func (x *StartUploadRequest) Reset() {
	*x = StartUploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartUploadRequest) ProtoMessage() {}

func (x *StartUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartUploadRequest.ProtoReflect.Descriptor instead.
func (*StartUploadRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{29}
}

func (x *StartUploadRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

type GetNextUploadStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string          `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	Ts            *util.Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *GetNextUploadStateRequest) Reset() {
	*x = GetNextUploadStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextUploadStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextUploadStateRequest) ProtoMessage() {}

func (x *GetNextUploadStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextUploadStateRequest.ProtoReflect.Descriptor instead.
func (*GetNextUploadStateRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{30}
}

func (x *GetNextUploadStateRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetNextUploadStateRequest) GetTs() *util.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type GetNextUploadStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadState     UploadState     `protobuf:"varint,1,opt,name=upload_state,json=uploadState,proto3,enum=carbon.frontend.weeding_diagnostics.UploadState" json:"upload_state,omitempty"`
	Ts              *util.Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`
	PercentUploaded uint32          `protobuf:"varint,3,opt,name=percent_uploaded,json=percentUploaded,proto3" json:"percent_uploaded,omitempty"`
}

func (x *GetNextUploadStateResponse) Reset() {
	*x = GetNextUploadStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextUploadStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextUploadStateResponse) ProtoMessage() {}

func (x *GetNextUploadStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextUploadStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextUploadStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{31}
}

func (x *GetNextUploadStateResponse) GetUploadState() UploadState {
	if x != nil {
		return x.UploadState
	}
	return UploadState_NONE
}

func (x *GetNextUploadStateResponse) GetTs() *util.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextUploadStateResponse) GetPercentUploaded() uint32 {
	if x != nil {
		return x.PercentUploaded
	}
	return 0
}

type GetDeepweedPredictionsCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	Row           uint32 `protobuf:"varint,2,opt,name=row,proto3" json:"row,omitempty"`
	CamId         uint32 `protobuf:"varint,3,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
}

func (x *GetDeepweedPredictionsCountRequest) Reset() {
	*x = GetDeepweedPredictionsCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedPredictionsCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedPredictionsCountRequest) ProtoMessage() {}

func (x *GetDeepweedPredictionsCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedPredictionsCountRequest.ProtoReflect.Descriptor instead.
func (*GetDeepweedPredictionsCountRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{32}
}

func (x *GetDeepweedPredictionsCountRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetDeepweedPredictionsCountRequest) GetRow() uint32 {
	if x != nil {
		return x.Row
	}
	return 0
}

func (x *GetDeepweedPredictionsCountRequest) GetCamId() uint32 {
	if x != nil {
		return x.CamId
	}
	return 0
}

type GetDeepweedPredictionsCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetDeepweedPredictionsCountResponse) Reset() {
	*x = GetDeepweedPredictionsCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedPredictionsCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedPredictionsCountResponse) ProtoMessage() {}

func (x *GetDeepweedPredictionsCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedPredictionsCountResponse.ProtoReflect.Descriptor instead.
func (*GetDeepweedPredictionsCountResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{33}
}

func (x *GetDeepweedPredictionsCountResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetDeepweedPredictionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	Row           uint32 `protobuf:"varint,2,opt,name=row,proto3" json:"row,omitempty"`
	CamId         uint32 `protobuf:"varint,3,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	Idx           uint32 `protobuf:"varint,4,opt,name=idx,proto3" json:"idx,omitempty"`
}

func (x *GetDeepweedPredictionsRequest) Reset() {
	*x = GetDeepweedPredictionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedPredictionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedPredictionsRequest) ProtoMessage() {}

func (x *GetDeepweedPredictionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedPredictionsRequest.ProtoReflect.Descriptor instead.
func (*GetDeepweedPredictionsRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{34}
}

func (x *GetDeepweedPredictionsRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetDeepweedPredictionsRequest) GetRow() uint32 {
	if x != nil {
		return x.Row
	}
	return 0
}

func (x *GetDeepweedPredictionsRequest) GetCamId() uint32 {
	if x != nil {
		return x.CamId
	}
	return 0
}

func (x *GetDeepweedPredictionsRequest) GetIdx() uint32 {
	if x != nil {
		return x.Idx
	}
	return 0
}

type GetDeepweedPredictionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Predictions *recorder.DeepweedPredictionRecord `protobuf:"bytes,1,opt,name=predictions,proto3" json:"predictions,omitempty"`
}

func (x *GetDeepweedPredictionsResponse) Reset() {
	*x = GetDeepweedPredictionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeepweedPredictionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeepweedPredictionsResponse) ProtoMessage() {}

func (x *GetDeepweedPredictionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeepweedPredictionsResponse.ProtoReflect.Descriptor instead.
func (*GetDeepweedPredictionsResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{35}
}

func (x *GetDeepweedPredictionsResponse) GetPredictions() *recorder.DeepweedPredictionRecord {
	if x != nil {
		return x.Predictions
	}
	return nil
}

type FindTrajectoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
	TrajectoryId  uint32 `protobuf:"varint,3,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
}

func (x *FindTrajectoryRequest) Reset() {
	*x = FindTrajectoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindTrajectoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindTrajectoryRequest) ProtoMessage() {}

func (x *FindTrajectoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindTrajectoryRequest.ProtoReflect.Descriptor instead.
func (*FindTrajectoryRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{36}
}

func (x *FindTrajectoryRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *FindTrajectoryRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

func (x *FindTrajectoryRequest) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

type FindTrajectoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SnapshotId uint32                            `protobuf:"varint,1,opt,name=snapshot_id,json=snapshotId,proto3" json:"snapshot_id,omitempty"`
	Trajectory *weed_tracking.TrajectorySnapshot `protobuf:"bytes,2,opt,name=trajectory,proto3" json:"trajectory,omitempty"`
}

func (x *FindTrajectoryResponse) Reset() {
	*x = FindTrajectoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindTrajectoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindTrajectoryResponse) ProtoMessage() {}

func (x *FindTrajectoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindTrajectoryResponse.ProtoReflect.Descriptor instead.
func (*FindTrajectoryResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{37}
}

func (x *FindTrajectoryResponse) GetSnapshotId() uint32 {
	if x != nil {
		return x.SnapshotId
	}
	return 0
}

func (x *FindTrajectoryResponse) GetTrajectory() *weed_tracking.TrajectorySnapshot {
	if x != nil {
		return x.Trajectory
	}
	return nil
}

type GetRotaryTicksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordingName string `protobuf:"bytes,1,opt,name=recording_name,json=recordingName,proto3" json:"recording_name,omitempty"`
	RowId         uint32 `protobuf:"varint,2,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
}

func (x *GetRotaryTicksRequest) Reset() {
	*x = GetRotaryTicksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRotaryTicksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRotaryTicksRequest) ProtoMessage() {}

func (x *GetRotaryTicksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRotaryTicksRequest.ProtoReflect.Descriptor instead.
func (*GetRotaryTicksRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{38}
}

func (x *GetRotaryTicksRequest) GetRecordingName() string {
	if x != nil {
		return x.RecordingName
	}
	return ""
}

func (x *GetRotaryTicksRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

type GetRotaryTicksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records                []*recorder.RotaryTicksRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	WheelEncoderResolution uint32                        `protobuf:"varint,2,opt,name=wheel_encoder_resolution,json=wheelEncoderResolution,proto3" json:"wheel_encoder_resolution,omitempty"`
}

func (x *GetRotaryTicksResponse) Reset() {
	*x = GetRotaryTicksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRotaryTicksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRotaryTicksResponse) ProtoMessage() {}

func (x *GetRotaryTicksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRotaryTicksResponse.ProtoReflect.Descriptor instead.
func (*GetRotaryTicksResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{39}
}

func (x *GetRotaryTicksResponse) GetRecords() []*recorder.RotaryTicksRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *GetRotaryTicksResponse) GetWheelEncoderResolution() uint32 {
	if x != nil {
		return x.WheelEncoderResolution
	}
	return 0
}

type SnapshotPredictImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowId uint32 `protobuf:"varint,1,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
}

func (x *SnapshotPredictImagesRequest) Reset() {
	*x = SnapshotPredictImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotPredictImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotPredictImagesRequest) ProtoMessage() {}

func (x *SnapshotPredictImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotPredictImagesRequest.ProtoReflect.Descriptor instead.
func (*SnapshotPredictImagesRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{40}
}

func (x *SnapshotPredictImagesRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

type PcamSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PcamId      string `protobuf:"bytes,1,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *PcamSnapshot) Reset() {
	*x = PcamSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PcamSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PcamSnapshot) ProtoMessage() {}

func (x *PcamSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PcamSnapshot.ProtoReflect.Descriptor instead.
func (*PcamSnapshot) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{41}
}

func (x *PcamSnapshot) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

func (x *PcamSnapshot) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type SnapshotPredictImagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snapshots []*PcamSnapshot `protobuf:"bytes,1,rep,name=snapshots,proto3" json:"snapshots,omitempty"`
}

func (x *SnapshotPredictImagesResponse) Reset() {
	*x = SnapshotPredictImagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotPredictImagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotPredictImagesResponse) ProtoMessage() {}

func (x *SnapshotPredictImagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotPredictImagesResponse.ProtoReflect.Descriptor instead.
func (*SnapshotPredictImagesResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{42}
}

func (x *SnapshotPredictImagesResponse) GetSnapshots() []*PcamSnapshot {
	if x != nil {
		return x.Snapshots
	}
	return nil
}

type GetChipForPredictImageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PcamId      string `protobuf:"bytes,1,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	CenterXPx   int32  `protobuf:"varint,3,opt,name=center_x_px,json=centerXPx,proto3" json:"center_x_px,omitempty"`
	CenterYPx   int32  `protobuf:"varint,4,opt,name=center_y_px,json=centerYPx,proto3" json:"center_y_px,omitempty"`
	RowId       uint32 `protobuf:"varint,5,opt,name=row_id,json=rowId,proto3" json:"row_id,omitempty"`
}

func (x *GetChipForPredictImageRequest) Reset() {
	*x = GetChipForPredictImageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChipForPredictImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChipForPredictImageRequest) ProtoMessage() {}

func (x *GetChipForPredictImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChipForPredictImageRequest.ProtoReflect.Descriptor instead.
func (*GetChipForPredictImageRequest) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{43}
}

func (x *GetChipForPredictImageRequest) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

func (x *GetChipForPredictImageRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetChipForPredictImageRequest) GetCenterXPx() int32 {
	if x != nil {
		return x.CenterXPx
	}
	return 0
}

func (x *GetChipForPredictImageRequest) GetCenterYPx() int32 {
	if x != nil {
		return x.CenterYPx
	}
	return 0
}

func (x *GetChipForPredictImageRequest) GetRowId() uint32 {
	if x != nil {
		return x.RowId
	}
	return 0
}

type GetChipForPredictImageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChipImage []byte `protobuf:"bytes,1,opt,name=chip_image,json=chipImage,proto3" json:"chip_image,omitempty"`
}

func (x *GetChipForPredictImageResponse) Reset() {
	*x = GetChipForPredictImageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_weeding_diagnostics_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChipForPredictImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChipForPredictImageResponse) ProtoMessage() {}

func (x *GetChipForPredictImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_weeding_diagnostics_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChipForPredictImageResponse.ProtoReflect.Descriptor instead.
func (*GetChipForPredictImageResponse) Descriptor() ([]byte, []int) {
	return file_frontend_weeding_diagnostics_proto_rawDescGZIP(), []int{44}
}

func (x *GetChipForPredictImageResponse) GetChipImage() []byte {
	if x != nil {
		return x.ChipImage
	}
	return nil
}

var File_frontend_weeding_diagnostics_proto protoreflect.FileDescriptor

var file_frontend_weeding_diagnostics_proto_rawDesc = []byte{
	0x0a, 0x22, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x23, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x0f, 0x75, 0x74, 0x69, 0x6c, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x72, 0x73, 0x2f, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x2f, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x63, 0x76, 0x2f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2f, 0x63, 0x76, 0x5f, 0x72, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x74, 0x68, 0x69,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x01,
	0x0a, 0x1f, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x74, 0x6c, 0x5f, 0x73, 0x65, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x74, 0x74, 0x6c, 0x53, 0x65, 0x63, 0x12, 0x2d,
	0x0a, 0x13, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x65,
	0x72, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x63, 0x72, 0x6f,
	0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x12, 0x2d, 0x0a,
	0x13, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72,
	0x5f, 0x73, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x77, 0x65, 0x65, 0x64,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x22, 0x36, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72,
	0x6f, 0x77, 0x49, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x2f, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e,
	0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x55,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x08, 0x73, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x22, 0x3d, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2a, 0x0a, 0x16, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x22, 0x25, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xe2, 0x01, 0x0a, 0x13, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x43, 0x61, 0x6d, 0x12,
	0x5c, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x43, 0x61, 0x6d, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x6d, 0x0a,
	0x0b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x48,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf2, 0x06, 0x0a,
	0x15, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x6e, 0x75, 0x6d, 0x5f, 0x73,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x4f, 0x70, 0x65,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4e, 0x75, 0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x50,
	0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x6e, 0x75, 0x6d, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x12, 0x5f,
	0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x91, 0x01, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x74, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72,
	0x52, 0x6f, 0x77, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x77, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65,
	0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x70, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x1a, 0x45,
	0x0a, 0x17, 0x4e, 0x75, 0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x50, 0x65,
	0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x86, 0x01, 0x0a, 0x1b, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x51, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x54, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x80,
	0x01, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4e, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x50, 0x65, 0x72, 0x43, 0x61, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x3f, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x8e, 0x03, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x5b, 0x0a, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x0b, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73,
	0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x76, 0x0a, 0x0f, 0x43,
	0x68, 0x69, 0x6c, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x4d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x40, 0x0a, 0x10, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x69, 0x6d,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0xcb, 0x01, 0x0a, 0x0a, 0x52, 0x6f, 0x77, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x73, 0x12, 0x4d, 0x0a, 0x04, 0x63, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x63,
	0x61, 0x6d, 0x73, 0x1a, 0x6e, 0x0a, 0x09, 0x43, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x4b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xc2, 0x0b, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x72, 0x0a, 0x0e, 0x6c,
	0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4c, 0x61,
	0x73, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0d, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x58, 0x0a, 0x0b, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x0a, 0x72,
	0x6f, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f, 0x77,
	0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0c, 0x72, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x64, 0x12, 0x72,
	0x0a, 0x0e, 0x72, 0x6f, 0x77, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x52, 0x6f, 0x77, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0d, 0x72, 0x6f, 0x77, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x97, 0x01, 0x0a, 0x1d, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x6d, 0x6d, 0x5f, 0x70, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x6f, 0x70, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x52,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x4d, 0x6d, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x18, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x52, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x4d, 0x6d, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x12, 0x4a, 0x0a, 0x0f,
	0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x74,
	0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2f, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x69, 0x0a, 0x0b, 0x72, 0x6f, 0x77,
	0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x72, 0x6f, 0x77, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x12, 0x77, 0x65, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x12, 0x63, 0x72, 0x6f, 0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x3c, 0x0a, 0x1b, 0x77, 0x68, 0x65, 0x65,
	0x6c, 0x5f, 0x64, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x77,
	0x68, 0x65, 0x65, 0x6c, 0x44, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42, 0x61, 0x63, 0x6b,
	0x4c, 0x65, 0x66, 0x74, 0x49, 0x6e, 0x12, 0x3e, 0x0a, 0x1c, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f,
	0x64, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18, 0x77, 0x68,
	0x65, 0x65, 0x6c, 0x44, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42, 0x61, 0x63, 0x6b, 0x52,
	0x69, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x12, 0x3e, 0x0a, 0x1c, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f,
	0x64, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x6c,
	0x65, 0x66, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18, 0x77, 0x68,
	0x65, 0x65, 0x6c, 0x44, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6e, 0x74,
	0x4c, 0x65, 0x66, 0x74, 0x49, 0x6e, 0x12, 0x40, 0x0a, 0x1d, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f,
	0x64, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x19, 0x77,
	0x68, 0x65, 0x65, 0x6c, 0x44, 0x69, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6e,
	0x74, 0x52, 0x69, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x1a, 0x40, 0x0a, 0x12, 0x4c, 0x61, 0x73, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5f, 0x0a, 0x12, 0x52, 0x6f,
	0x77, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4b, 0x0a, 0x1d, 0x43,
	0x72, 0x6f, 0x70, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4d,
	0x6d, 0x50, 0x65, 0x72, 0x52, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6e, 0x0a, 0x0f, 0x52, 0x6f, 0x77, 0x43,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x45, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x52, 0x6f, 0x77, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7d, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0x91, 0x01, 0x0a, 0x0c, 0x4c, 0x61, 0x73, 0x74,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x41, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x6a,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52,
	0x0a, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3e, 0x0a, 0x1b, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x19, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x58, 0x0a, 0x14, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x87, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x32, 0x70, 0x5f, 0x70,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x70, 0x32, 0x70, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x58, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x32, 0x70, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x70, 0x32, 0x70, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x59, 0x22,
	0xc3, 0x01, 0x0a, 0x0f, 0x50, 0x32, 0x50, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x5f, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x58, 0x50, 0x78, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x5f, 0x79, 0x5f, 0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x59, 0x50, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x5f, 0x70, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x50, 0x78, 0x12, 0x26, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x70, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70,
	0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0xbe, 0x01, 0x0a, 0x1e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x5f, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x58, 0x50, 0x78, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x5f, 0x79, 0x5f, 0x70, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x59, 0x50, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x5f, 0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x50, 0x78, 0x12, 0x26, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0xad, 0x04, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6a, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x68, 0x0a, 0x0d, 0x70, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x55, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x12, 0x5c, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x72, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x78, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72,
	0x58, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x68, 0x61, 0x69,
	0x72, 0x59, 0x12, 0x62, 0x0a, 0x12, 0x70, 0x32, 0x70, 0x5f, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x2e, 0x50, 0x32, 0x50, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x10, 0x70, 0x32, 0x70, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54, 0x72,
	0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0xa3,
	0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2d, 0x0a, 0x0a, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x22, 0x7d, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f,
	0x77, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xca, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x4b,
	0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x64,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x0e, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x67, 0x0a, 0x1f, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x62, 0x65, 0x6c, 0x6f, 0x77, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x76, 0x2e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x52, 0x1c, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x42, 0x65, 0x6c, 0x6f, 0x77, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22,
	0x75, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3b, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x6a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x22,
	0xc4, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53,
	0x0a, 0x0c, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x22, 0x74, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65,
	0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x72, 0x6f, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x23,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x72, 0x6f, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x64, 0x78, 0x22, 0x66, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x7a, 0x0a, 0x15, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x22, 0x7c, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a,
	0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x22,
	0x55, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x35, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x77, 0x68, 0x65, 0x65,
	0x6c, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x77, 0x68, 0x65, 0x65,
	0x6c, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x1c, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x0c, 0x50, 0x63, 0x61,
	0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x63, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x61, 0x6d,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x70, 0x0a, 0x1d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x50, 0x63, 0x61, 0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x09, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x22, 0xb2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43,
	0x68, 0x69, 0x70, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x63, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x61, 0x6d,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f,
	0x78, 0x5f, 0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x58, 0x50, 0x78, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f,
	0x79, 0x5f, 0x70, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x59, 0x50, 0x78, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x6f, 0x77, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x09, 0x63, 0x68, 0x69, 0x70, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x2a, 0x32, 0x0a,
	0x0b, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x4f, 0x4e, 0x45, 0x10,
	0x02, 0x32, 0xa4, 0x15, 0x0a, 0x19, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x74, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x44, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x80, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01,
	0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x39, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x53, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x0f, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3b, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62,
	0x6f, 0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x87, 0x01,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x3d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x95, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x45, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x30, 0x01, 0x12,
	0x93, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x44, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x30, 0x01, 0x12, 0xa4, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x30, 0x01,
	0x12, 0x5a, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x37, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x95, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x3e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78,
	0x74, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb0, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70,
	0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x65, 0x70,
	0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x0e,
	0x46, 0x69, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3a,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x3a, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x9e, 0x01, 0x0a, 0x15, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x41, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70,
	0x46, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x42, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69, 0x70, 0x46, 0x6f, 0x72,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x69,
	0x70, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_weeding_diagnostics_proto_rawDescOnce sync.Once
	file_frontend_weeding_diagnostics_proto_rawDescData = file_frontend_weeding_diagnostics_proto_rawDesc
)

func file_frontend_weeding_diagnostics_proto_rawDescGZIP() []byte {
	file_frontend_weeding_diagnostics_proto_rawDescOnce.Do(func() {
		file_frontend_weeding_diagnostics_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_weeding_diagnostics_proto_rawDescData)
	})
	return file_frontend_weeding_diagnostics_proto_rawDescData
}

var file_frontend_weeding_diagnostics_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_frontend_weeding_diagnostics_proto_msgTypes = make([]protoimpl.MessageInfo, 56)
var file_frontend_weeding_diagnostics_proto_goTypes = []interface{}{
	(UploadState)(0),                            // 0: carbon.frontend.weeding_diagnostics.UploadState
	(*RecordWeedingDiagnosticsRequest)(nil),     // 1: carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest
	(*GetCurrentTrajectoriesRequest)(nil),       // 2: carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest
	(*GetRecordingsListRequest)(nil),            // 3: carbon.frontend.weeding_diagnostics.GetRecordingsListRequest
	(*GetRecordingsListResponse)(nil),           // 4: carbon.frontend.weeding_diagnostics.GetRecordingsListResponse
	(*GetSnapshotRequest)(nil),                  // 5: carbon.frontend.weeding_diagnostics.GetSnapshotRequest
	(*GetSnapshotResponse)(nil),                 // 6: carbon.frontend.weeding_diagnostics.GetSnapshotResponse
	(*OpenRecordingRequest)(nil),                // 7: carbon.frontend.weeding_diagnostics.OpenRecordingRequest
	(*TrajectoriesWithImages)(nil),              // 8: carbon.frontend.weeding_diagnostics.TrajectoriesWithImages
	(*PredictImages)(nil),                       // 9: carbon.frontend.weeding_diagnostics.PredictImages
	(*PredictImagesPerCam)(nil),                 // 10: carbon.frontend.weeding_diagnostics.PredictImagesPerCam
	(*OpenRecordingResponse)(nil),               // 11: carbon.frontend.weeding_diagnostics.OpenRecordingResponse
	(*DeleteRecordingRequest)(nil),              // 12: carbon.frontend.weeding_diagnostics.DeleteRecordingRequest
	(*ConfigNodeSnapshot)(nil),                  // 13: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	(*CameraDimensions)(nil),                    // 14: carbon.frontend.weeding_diagnostics.CameraDimensions
	(*RowCameras)(nil),                          // 15: carbon.frontend.weeding_diagnostics.RowCameras
	(*StaticRecordingData)(nil),                 // 16: carbon.frontend.weeding_diagnostics.StaticRecordingData
	(*GetTrajectoryDataRequest)(nil),            // 17: carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest
	(*LastSnapshot)(nil),                        // 18: carbon.frontend.weeding_diagnostics.LastSnapshot
	(*ExtraTrajectoryField)(nil),                // 19: carbon.frontend.weeding_diagnostics.ExtraTrajectoryField
	(*TargetImage)(nil),                         // 20: carbon.frontend.weeding_diagnostics.TargetImage
	(*P2PPredictImage)(nil),                     // 21: carbon.frontend.weeding_diagnostics.P2PPredictImage
	(*TrajectoryPredictImageMetadata)(nil),      // 22: carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata
	(*TrajectoryData)(nil),                      // 23: carbon.frontend.weeding_diagnostics.TrajectoryData
	(*GetTrajectoryPredictImageRequest)(nil),    // 24: carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest
	(*GetTrajectoryTargetImageRequest)(nil),     // 25: carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest
	(*ImageChunk)(nil),                          // 26: carbon.frontend.weeding_diagnostics.ImageChunk
	(*GetPredictImageMetadataRequest)(nil),      // 27: carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest
	(*GetPredictImageMetadataResponse)(nil),     // 28: carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse
	(*GetPredictImageRequest)(nil),              // 29: carbon.frontend.weeding_diagnostics.GetPredictImageRequest
	(*StartUploadRequest)(nil),                  // 30: carbon.frontend.weeding_diagnostics.StartUploadRequest
	(*GetNextUploadStateRequest)(nil),           // 31: carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest
	(*GetNextUploadStateResponse)(nil),          // 32: carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse
	(*GetDeepweedPredictionsCountRequest)(nil),  // 33: carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest
	(*GetDeepweedPredictionsCountResponse)(nil), // 34: carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse
	(*GetDeepweedPredictionsRequest)(nil),       // 35: carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest
	(*GetDeepweedPredictionsResponse)(nil),      // 36: carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse
	(*FindTrajectoryRequest)(nil),               // 37: carbon.frontend.weeding_diagnostics.FindTrajectoryRequest
	(*FindTrajectoryResponse)(nil),              // 38: carbon.frontend.weeding_diagnostics.FindTrajectoryResponse
	(*GetRotaryTicksRequest)(nil),               // 39: carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest
	(*GetRotaryTicksResponse)(nil),              // 40: carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse
	(*SnapshotPredictImagesRequest)(nil),        // 41: carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest
	(*PcamSnapshot)(nil),                        // 42: carbon.frontend.weeding_diagnostics.PcamSnapshot
	(*SnapshotPredictImagesResponse)(nil),       // 43: carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse
	(*GetChipForPredictImageRequest)(nil),       // 44: carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest
	(*GetChipForPredictImageResponse)(nil),      // 45: carbon.frontend.weeding_diagnostics.GetChipForPredictImageResponse
	nil,                                         // 46: carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry
	nil,                                         // 47: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry
	nil,                                         // 48: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry
	nil,                                         // 49: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry
	nil,                                         // 50: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry
	nil,                                         // 51: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry
	nil,                                         // 52: carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry
	nil,                                         // 53: carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry
	nil,                                         // 54: carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry
	nil,                                         // 55: carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry
	nil,                                         // 56: carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry
	(*weed_tracking.DiagnosticsSnapshot)(nil),   // 57: weed_tracking.DiagnosticsSnapshot
	(*thinning.ConfigDefinition)(nil),           // 58: carbon.thinning.ConfigDefinition
	(*weed_tracking.TrajectorySnapshot)(nil),    // 59: weed_tracking.TrajectorySnapshot
	(*util.Timestamp)(nil),                      // 60: carbon.util.Timestamp
	(*Annotations)(nil),                         // 61: carbon.frontend.image_stream.Annotations
	(*cv.DeepweedOutput)(nil),                   // 62: cv.runtime.proto.DeepweedOutput
	(*recorder.DeepweedPredictionRecord)(nil),   // 63: recorder.DeepweedPredictionRecord
	(*recorder.RotaryTicksRecord)(nil),          // 64: recorder.RotaryTicksRecord
	(*aimbot.GetDimensionsResponse)(nil),        // 65: aimbot.GetDimensionsResponse
	(*util.Empty)(nil),                          // 66: carbon.util.Empty
}
var file_frontend_weeding_diagnostics_proto_depIdxs = []int32{
	57, // 0: carbon.frontend.weeding_diagnostics.GetSnapshotResponse.snapshot:type_name -> weed_tracking.DiagnosticsSnapshot
	46, // 1: carbon.frontend.weeding_diagnostics.PredictImagesPerCam.images:type_name -> carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry
	47, // 2: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.num_snapshots_per_row:type_name -> carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry
	16, // 3: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.recording_data:type_name -> carbon.frontend.weeding_diagnostics.StaticRecordingData
	48, // 4: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.trajectory_images_per_row:type_name -> carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry
	49, // 5: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.predict_images_per_row:type_name -> carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry
	50, // 6: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.values:type_name -> carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry
	51, // 7: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.child_nodes:type_name -> carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry
	52, // 8: carbon.frontend.weeding_diagnostics.RowCameras.cams:type_name -> carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry
	53, // 9: carbon.frontend.weeding_diagnostics.StaticRecordingData.lasers_enabled:type_name -> carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry
	13, // 10: carbon.frontend.weeding_diagnostics.StaticRecordingData.root_config:type_name -> carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	54, // 11: carbon.frontend.weeding_diagnostics.StaticRecordingData.row_dimensions:type_name -> carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry
	55, // 12: carbon.frontend.weeding_diagnostics.StaticRecordingData.crop_safety_radius_mm_per_row:type_name -> carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry
	58, // 13: carbon.frontend.weeding_diagnostics.StaticRecordingData.thinning_config:type_name -> carbon.thinning.ConfigDefinition
	56, // 14: carbon.frontend.weeding_diagnostics.StaticRecordingData.row_cameras:type_name -> carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry
	59, // 15: carbon.frontend.weeding_diagnostics.LastSnapshot.trajectory:type_name -> weed_tracking.TrajectorySnapshot
	60, // 16: carbon.frontend.weeding_diagnostics.P2PPredictImage.ts:type_name -> carbon.util.Timestamp
	60, // 17: carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata.ts:type_name -> carbon.util.Timestamp
	22, // 18: carbon.frontend.weeding_diagnostics.TrajectoryData.predict_image:type_name -> carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata
	20, // 19: carbon.frontend.weeding_diagnostics.TrajectoryData.target_images:type_name -> carbon.frontend.weeding_diagnostics.TargetImage
	18, // 20: carbon.frontend.weeding_diagnostics.TrajectoryData.last_snapshot:type_name -> carbon.frontend.weeding_diagnostics.LastSnapshot
	19, // 21: carbon.frontend.weeding_diagnostics.TrajectoryData.extra_fields:type_name -> carbon.frontend.weeding_diagnostics.ExtraTrajectoryField
	21, // 22: carbon.frontend.weeding_diagnostics.TrajectoryData.p2p_predict_images:type_name -> carbon.frontend.weeding_diagnostics.P2PPredictImage
	60, // 23: carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.ts:type_name -> carbon.util.Timestamp
	61, // 24: carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.annotations:type_name -> carbon.frontend.image_stream.Annotations
	62, // 25: carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.deepweed_output:type_name -> cv.runtime.proto.DeepweedOutput
	62, // 26: carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse.deepweed_output_below_threshold:type_name -> cv.runtime.proto.DeepweedOutput
	60, // 27: carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest.ts:type_name -> carbon.util.Timestamp
	0,  // 28: carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse.upload_state:type_name -> carbon.frontend.weeding_diagnostics.UploadState
	60, // 29: carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse.ts:type_name -> carbon.util.Timestamp
	63, // 30: carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse.predictions:type_name -> recorder.DeepweedPredictionRecord
	59, // 31: carbon.frontend.weeding_diagnostics.FindTrajectoryResponse.trajectory:type_name -> weed_tracking.TrajectorySnapshot
	64, // 32: carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse.records:type_name -> recorder.RotaryTicksRecord
	42, // 33: carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse.snapshots:type_name -> carbon.frontend.weeding_diagnostics.PcamSnapshot
	9,  // 34: carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry.value:type_name -> carbon.frontend.weeding_diagnostics.PredictImages
	8,  // 35: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry.value:type_name -> carbon.frontend.weeding_diagnostics.TrajectoriesWithImages
	10, // 36: carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry.value:type_name -> carbon.frontend.weeding_diagnostics.PredictImagesPerCam
	13, // 37: carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry.value:type_name -> carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot
	14, // 38: carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry.value:type_name -> carbon.frontend.weeding_diagnostics.CameraDimensions
	65, // 39: carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry.value:type_name -> aimbot.GetDimensionsResponse
	15, // 40: carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry.value:type_name -> carbon.frontend.weeding_diagnostics.RowCameras
	1,  // 41: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.RecordWeedingDiagnostics:input_type -> carbon.frontend.weeding_diagnostics.RecordWeedingDiagnosticsRequest
	2,  // 42: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetCurrentTrajectories:input_type -> carbon.frontend.weeding_diagnostics.GetCurrentTrajectoriesRequest
	3,  // 43: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetRecordingsList:input_type -> carbon.frontend.weeding_diagnostics.GetRecordingsListRequest
	7,  // 44: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.OpenRecording:input_type -> carbon.frontend.weeding_diagnostics.OpenRecordingRequest
	5,  // 45: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetSnapshot:input_type -> carbon.frontend.weeding_diagnostics.GetSnapshotRequest
	12, // 46: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.DeleteRecording:input_type -> carbon.frontend.weeding_diagnostics.DeleteRecordingRequest
	17, // 47: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryData:input_type -> carbon.frontend.weeding_diagnostics.GetTrajectoryDataRequest
	24, // 48: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryPredictImage:input_type -> carbon.frontend.weeding_diagnostics.GetTrajectoryPredictImageRequest
	25, // 49: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryTargetImage:input_type -> carbon.frontend.weeding_diagnostics.GetTrajectoryTargetImageRequest
	27, // 50: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetPredictImageMetadata:input_type -> carbon.frontend.weeding_diagnostics.GetPredictImageMetadataRequest
	29, // 51: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetPredictImage:input_type -> carbon.frontend.weeding_diagnostics.GetPredictImageRequest
	30, // 52: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.StartUpload:input_type -> carbon.frontend.weeding_diagnostics.StartUploadRequest
	31, // 53: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetNextUploadState:input_type -> carbon.frontend.weeding_diagnostics.GetNextUploadStateRequest
	33, // 54: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetDeepweedPredictionsCount:input_type -> carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountRequest
	35, // 55: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetDeepweedPredictions:input_type -> carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsRequest
	37, // 56: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.FindTrajectory:input_type -> carbon.frontend.weeding_diagnostics.FindTrajectoryRequest
	39, // 57: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetRotaryTicks:input_type -> carbon.frontend.weeding_diagnostics.GetRotaryTicksRequest
	41, // 58: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.SnapshotPredictImages:input_type -> carbon.frontend.weeding_diagnostics.SnapshotPredictImagesRequest
	44, // 59: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetChipForPredictImage:input_type -> carbon.frontend.weeding_diagnostics.GetChipForPredictImageRequest
	66, // 60: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.RecordWeedingDiagnostics:output_type -> carbon.util.Empty
	57, // 61: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetCurrentTrajectories:output_type -> weed_tracking.DiagnosticsSnapshot
	4,  // 62: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetRecordingsList:output_type -> carbon.frontend.weeding_diagnostics.GetRecordingsListResponse
	11, // 63: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.OpenRecording:output_type -> carbon.frontend.weeding_diagnostics.OpenRecordingResponse
	6,  // 64: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetSnapshot:output_type -> carbon.frontend.weeding_diagnostics.GetSnapshotResponse
	66, // 65: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.DeleteRecording:output_type -> carbon.util.Empty
	23, // 66: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryData:output_type -> carbon.frontend.weeding_diagnostics.TrajectoryData
	26, // 67: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryPredictImage:output_type -> carbon.frontend.weeding_diagnostics.ImageChunk
	26, // 68: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetTrajectoryTargetImage:output_type -> carbon.frontend.weeding_diagnostics.ImageChunk
	28, // 69: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetPredictImageMetadata:output_type -> carbon.frontend.weeding_diagnostics.GetPredictImageMetadataResponse
	26, // 70: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetPredictImage:output_type -> carbon.frontend.weeding_diagnostics.ImageChunk
	66, // 71: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.StartUpload:output_type -> carbon.util.Empty
	32, // 72: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetNextUploadState:output_type -> carbon.frontend.weeding_diagnostics.GetNextUploadStateResponse
	34, // 73: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetDeepweedPredictionsCount:output_type -> carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsCountResponse
	36, // 74: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetDeepweedPredictions:output_type -> carbon.frontend.weeding_diagnostics.GetDeepweedPredictionsResponse
	38, // 75: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.FindTrajectory:output_type -> carbon.frontend.weeding_diagnostics.FindTrajectoryResponse
	40, // 76: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetRotaryTicks:output_type -> carbon.frontend.weeding_diagnostics.GetRotaryTicksResponse
	43, // 77: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.SnapshotPredictImages:output_type -> carbon.frontend.weeding_diagnostics.SnapshotPredictImagesResponse
	45, // 78: carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService.GetChipForPredictImage:output_type -> carbon.frontend.weeding_diagnostics.GetChipForPredictImageResponse
	60, // [60:79] is the sub-list for method output_type
	41, // [41:60] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_frontend_weeding_diagnostics_proto_init() }
func file_frontend_weeding_diagnostics_proto_init() {
	if File_frontend_weeding_diagnostics_proto != nil {
		return
	}
	file_frontend_image_stream_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_weeding_diagnostics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordWeedingDiagnosticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrentTrajectoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordingsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordingsListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSnapshotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSnapshotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenRecordingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrajectoriesWithImages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictImages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictImagesPerCam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenRecordingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRecordingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigNodeSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraDimensions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RowCameras); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaticRecordingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrajectoryDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LastSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtraTrajectoryField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2PPredictImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrajectoryPredictImageMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrajectoryData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrajectoryPredictImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrajectoryTargetImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageChunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictImageMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictImageMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPredictImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartUploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextUploadStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextUploadStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedPredictionsCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedPredictionsCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedPredictionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDeepweedPredictionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindTrajectoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindTrajectoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRotaryTicksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRotaryTicksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotPredictImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PcamSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotPredictImagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChipForPredictImageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_weeding_diagnostics_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChipForPredictImageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_weeding_diagnostics_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   56,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_weeding_diagnostics_proto_goTypes,
		DependencyIndexes: file_frontend_weeding_diagnostics_proto_depIdxs,
		EnumInfos:         file_frontend_weeding_diagnostics_proto_enumTypes,
		MessageInfos:      file_frontend_weeding_diagnostics_proto_msgTypes,
	}.Build()
	File_frontend_weeding_diagnostics_proto = out.File
	file_frontend_weeding_diagnostics_proto_rawDesc = nil
	file_frontend_weeding_diagnostics_proto_goTypes = nil
	file_frontend_weeding_diagnostics_proto_depIdxs = nil
}
