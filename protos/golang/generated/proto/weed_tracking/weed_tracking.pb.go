// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: weed_tracking/weed_tracking.proto

package weed_tracking

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InvalidScoreReason int32

const (
	InvalidScoreReason_NONE                                    InvalidScoreReason = 0
	InvalidScoreReason_ANOTHER_LASER_SHOOTING                  InvalidScoreReason = 1
	InvalidScoreReason_SCANNER_POSITION_INVALID                InvalidScoreReason = 2
	InvalidScoreReason_WEED_ALREADY_KILLED                     InvalidScoreReason = 3
	InvalidScoreReason_WEED_SHOT_BY_ANOTHER_LASER              InvalidScoreReason = 4
	InvalidScoreReason_WEED_OUT_OF_BAND                        InvalidScoreReason = 5
	InvalidScoreReason_NOT_A_WEED                              InvalidScoreReason = 6
	InvalidScoreReason_SCORE_NEGATIVE                          InvalidScoreReason = 7
	InvalidScoreReason_INTERSECTED_WITH_NONSHOOTABLE           InvalidScoreReason = 8
	InvalidScoreReason_EXTERMINATION_FAILURES_EXCEEDED_MAX     InvalidScoreReason = 9
	InvalidScoreReason_DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN InvalidScoreReason = 10
)

// Enum value maps for InvalidScoreReason.
var (
	InvalidScoreReason_name = map[int32]string{
		0:  "NONE",
		1:  "ANOTHER_LASER_SHOOTING",
		2:  "SCANNER_POSITION_INVALID",
		3:  "WEED_ALREADY_KILLED",
		4:  "WEED_SHOT_BY_ANOTHER_LASER",
		5:  "WEED_OUT_OF_BAND",
		6:  "NOT_A_WEED",
		7:  "SCORE_NEGATIVE",
		8:  "INTERSECTED_WITH_NONSHOOTABLE",
		9:  "EXTERMINATION_FAILURES_EXCEEDED_MAX",
		10: "DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN",
	}
	InvalidScoreReason_value = map[string]int32{
		"NONE":                                    0,
		"ANOTHER_LASER_SHOOTING":                  1,
		"SCANNER_POSITION_INVALID":                2,
		"WEED_ALREADY_KILLED":                     3,
		"WEED_SHOT_BY_ANOTHER_LASER":              4,
		"WEED_OUT_OF_BAND":                        5,
		"NOT_A_WEED":                              6,
		"SCORE_NEGATIVE":                          7,
		"INTERSECTED_WITH_NONSHOOTABLE":           8,
		"EXTERMINATION_FAILURES_EXCEEDED_MAX":     9,
		"DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN": 10,
	}
)

func (x InvalidScoreReason) Enum() *InvalidScoreReason {
	p := new(InvalidScoreReason)
	*p = x
	return p
}

func (x InvalidScoreReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvalidScoreReason) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[0].Descriptor()
}

func (InvalidScoreReason) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[0]
}

func (x InvalidScoreReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvalidScoreReason.Descriptor instead.
func (InvalidScoreReason) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{0}
}

type KillStatus int32

const (
	KillStatus_STATUS_NOT_SHOT            KillStatus = 0
	KillStatus_STATUS_BEING_SHOT          KillStatus = 1
	KillStatus_STATUS_SHOT                KillStatus = 2
	KillStatus_STATUS_PARTIALLY_SHOT      KillStatus = 3
	KillStatus_STATUS_P2P_NOT_FOUND       KillStatus = 4
	KillStatus_STATUS_ERROR               KillStatus = 5
	KillStatus_STATUS_P2P_MISSING_CONTEXT KillStatus = 6
)

// Enum value maps for KillStatus.
var (
	KillStatus_name = map[int32]string{
		0: "STATUS_NOT_SHOT",
		1: "STATUS_BEING_SHOT",
		2: "STATUS_SHOT",
		3: "STATUS_PARTIALLY_SHOT",
		4: "STATUS_P2P_NOT_FOUND",
		5: "STATUS_ERROR",
		6: "STATUS_P2P_MISSING_CONTEXT",
	}
	KillStatus_value = map[string]int32{
		"STATUS_NOT_SHOT":            0,
		"STATUS_BEING_SHOT":          1,
		"STATUS_SHOT":                2,
		"STATUS_PARTIALLY_SHOT":      3,
		"STATUS_P2P_NOT_FOUND":       4,
		"STATUS_ERROR":               5,
		"STATUS_P2P_MISSING_CONTEXT": 6,
	}
)

func (x KillStatus) Enum() *KillStatus {
	p := new(KillStatus)
	*p = x
	return p
}

func (x KillStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KillStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[1].Descriptor()
}

func (KillStatus) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[1]
}

func (x KillStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KillStatus.Descriptor instead.
func (KillStatus) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{1}
}

type DuplicateStatus int32

const (
	DuplicateStatus_UNIQUE    DuplicateStatus = 0
	DuplicateStatus_PRIMARY   DuplicateStatus = 1
	DuplicateStatus_DUPLICATE DuplicateStatus = 2
)

// Enum value maps for DuplicateStatus.
var (
	DuplicateStatus_name = map[int32]string{
		0: "UNIQUE",
		1: "PRIMARY",
		2: "DUPLICATE",
	}
	DuplicateStatus_value = map[string]int32{
		"UNIQUE":    0,
		"PRIMARY":   1,
		"DUPLICATE": 2,
	}
)

func (x DuplicateStatus) Enum() *DuplicateStatus {
	p := new(DuplicateStatus)
	*p = x
	return p
}

func (x DuplicateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DuplicateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[2].Descriptor()
}

func (DuplicateStatus) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[2]
}

func (x DuplicateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DuplicateStatus.Descriptor instead.
func (DuplicateStatus) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{2}
}

type ThinningState int32

const (
	ThinningState_THINNING_UNSET               ThinningState = 0
	ThinningState_THINNING_MARKED_FOR_THINNING ThinningState = 1
	ThinningState_THINNING_KEPT                ThinningState = 2
	ThinningState_THINNING_IGNORED             ThinningState = 3
)

// Enum value maps for ThinningState.
var (
	ThinningState_name = map[int32]string{
		0: "THINNING_UNSET",
		1: "THINNING_MARKED_FOR_THINNING",
		2: "THINNING_KEPT",
		3: "THINNING_IGNORED",
	}
	ThinningState_value = map[string]int32{
		"THINNING_UNSET":               0,
		"THINNING_MARKED_FOR_THINNING": 1,
		"THINNING_KEPT":                2,
		"THINNING_IGNORED":             3,
	}
)

func (x ThinningState) Enum() *ThinningState {
	p := new(ThinningState)
	*p = x
	return p
}

func (x ThinningState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ThinningState) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[3].Descriptor()
}

func (ThinningState) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[3]
}

func (x ThinningState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ThinningState.Descriptor instead.
func (ThinningState) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{3}
}

type TargetableState int32

const (
	TargetableState_TARGET_NOT_IN_SCHEDULER         TargetableState = 0
	TargetableState_TARGET_SCORED                   TargetableState = 1
	TargetableState_TARGET_INTERSECTS_NON_SHOOTABLE TargetableState = 2
	TargetableState_TARGET_TOO_MANY_FAILURES        TargetableState = 3
	TargetableState_TARGET_DOO_TOO_LOW              TargetableState = 4
	TargetableState_TARGET_IGNORED_FROM_ALMANAC     TargetableState = 5
	TargetableState_TARGET_OUT_OF_BAND              TargetableState = 6
	TargetableState_TARGET_AVOID_FROM_ALMANAC       TargetableState = 7
)

// Enum value maps for TargetableState.
var (
	TargetableState_name = map[int32]string{
		0: "TARGET_NOT_IN_SCHEDULER",
		1: "TARGET_SCORED",
		2: "TARGET_INTERSECTS_NON_SHOOTABLE",
		3: "TARGET_TOO_MANY_FAILURES",
		4: "TARGET_DOO_TOO_LOW",
		5: "TARGET_IGNORED_FROM_ALMANAC",
		6: "TARGET_OUT_OF_BAND",
		7: "TARGET_AVOID_FROM_ALMANAC",
	}
	TargetableState_value = map[string]int32{
		"TARGET_NOT_IN_SCHEDULER":         0,
		"TARGET_SCORED":                   1,
		"TARGET_INTERSECTS_NON_SHOOTABLE": 2,
		"TARGET_TOO_MANY_FAILURES":        3,
		"TARGET_DOO_TOO_LOW":              4,
		"TARGET_IGNORED_FROM_ALMANAC":     5,
		"TARGET_OUT_OF_BAND":              6,
		"TARGET_AVOID_FROM_ALMANAC":       7,
	}
)

func (x TargetableState) Enum() *TargetableState {
	p := new(TargetableState)
	*p = x
	return p
}

func (x TargetableState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetableState) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[4].Descriptor()
}

func (TargetableState) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[4]
}

func (x TargetableState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetableState.Descriptor instead.
func (TargetableState) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{4}
}

type Classification int32

const (
	Classification_CLASS_UNDECIDED Classification = 0 // class not yet decided
	Classification_CLASS_WEED      Classification = 1
	Classification_CLASS_CROP      Classification = 2
	Classification_CLASS_BOTH      Classification = 3
	Classification_CLASS_UNKNOWN   Classification = 4
)

// Enum value maps for Classification.
var (
	Classification_name = map[int32]string{
		0: "CLASS_UNDECIDED",
		1: "CLASS_WEED",
		2: "CLASS_CROP",
		3: "CLASS_BOTH",
		4: "CLASS_UNKNOWN",
	}
	Classification_value = map[string]int32{
		"CLASS_UNDECIDED": 0,
		"CLASS_WEED":      1,
		"CLASS_CROP":      2,
		"CLASS_BOTH":      3,
		"CLASS_UNKNOWN":   4,
	}
)

func (x Classification) Enum() *Classification {
	p := new(Classification)
	*p = x
	return p
}

func (x Classification) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Classification) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[5].Descriptor()
}

func (Classification) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[5]
}

func (x Classification) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Classification.Descriptor instead.
func (Classification) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{5}
}

type RecordingStatus int32

const (
	RecordingStatus_NOT_RECORDING      RecordingStatus = 0
	RecordingStatus_RECORDING_STARTED  RecordingStatus = 1
	RecordingStatus_RECORDING_FINISHED RecordingStatus = 2
	RecordingStatus_RECORDING_FAILED   RecordingStatus = 3
)

// Enum value maps for RecordingStatus.
var (
	RecordingStatus_name = map[int32]string{
		0: "NOT_RECORDING",
		1: "RECORDING_STARTED",
		2: "RECORDING_FINISHED",
		3: "RECORDING_FAILED",
	}
	RecordingStatus_value = map[string]int32{
		"NOT_RECORDING":      0,
		"RECORDING_STARTED":  1,
		"RECORDING_FINISHED": 2,
		"RECORDING_FAILED":   3,
	}
)

func (x RecordingStatus) Enum() *RecordingStatus {
	p := new(RecordingStatus)
	*p = x
	return p
}

func (x RecordingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecordingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[6].Descriptor()
}

func (RecordingStatus) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[6]
}

func (x RecordingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecordingStatus.Descriptor instead.
func (RecordingStatus) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{6}
}

type ConclusionType int32

const (
	ConclusionType_NOT_WEEDING                   ConclusionType = 0
	ConclusionType_OUT_OF_BAND                   ConclusionType = 1
	ConclusionType_INTERSECTS_WITH_NON_SHOOTABLE ConclusionType = 2
	ConclusionType_OUT_OF_RANGE                  ConclusionType = 3
	ConclusionType_UNIMPORTANT                   ConclusionType = 4
	ConclusionType_NOT_SHOT                      ConclusionType = 5
	ConclusionType_PARTIALLY_SHOT                ConclusionType = 6
	ConclusionType_SHOT                          ConclusionType = 7
	ConclusionType_P2P_NOT_FOUND                 ConclusionType = 8
	ConclusionType_ERROR                         ConclusionType = 9
	ConclusionType_FLICKER                       ConclusionType = 10
	ConclusionType_MARKED_FOR_THINNING           ConclusionType = 11
	ConclusionType_NOT_TARGETED                  ConclusionType = 12
	ConclusionType_P2P_MISSING_CONTEXT           ConclusionType = 13
)

// Enum value maps for ConclusionType.
var (
	ConclusionType_name = map[int32]string{
		0:  "NOT_WEEDING",
		1:  "OUT_OF_BAND",
		2:  "INTERSECTS_WITH_NON_SHOOTABLE",
		3:  "OUT_OF_RANGE",
		4:  "UNIMPORTANT",
		5:  "NOT_SHOT",
		6:  "PARTIALLY_SHOT",
		7:  "SHOT",
		8:  "P2P_NOT_FOUND",
		9:  "ERROR",
		10: "FLICKER",
		11: "MARKED_FOR_THINNING",
		12: "NOT_TARGETED",
		13: "P2P_MISSING_CONTEXT",
	}
	ConclusionType_value = map[string]int32{
		"NOT_WEEDING":                   0,
		"OUT_OF_BAND":                   1,
		"INTERSECTS_WITH_NON_SHOOTABLE": 2,
		"OUT_OF_RANGE":                  3,
		"UNIMPORTANT":                   4,
		"NOT_SHOT":                      5,
		"PARTIALLY_SHOT":                6,
		"SHOT":                          7,
		"P2P_NOT_FOUND":                 8,
		"ERROR":                         9,
		"FLICKER":                       10,
		"MARKED_FOR_THINNING":           11,
		"NOT_TARGETED":                  12,
		"P2P_MISSING_CONTEXT":           13,
	}
)

func (x ConclusionType) Enum() *ConclusionType {
	p := new(ConclusionType)
	*p = x
	return p
}

func (x ConclusionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConclusionType) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[7].Descriptor()
}

func (ConclusionType) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[7]
}

func (x ConclusionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConclusionType.Descriptor instead.
func (ConclusionType) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{7}
}

type PlantCaptchaStatus int32

const (
	PlantCaptchaStatus_NOT_STARTED       PlantCaptchaStatus = 0
	PlantCaptchaStatus_CAPTCHA_STARTED   PlantCaptchaStatus = 1
	PlantCaptchaStatus_CAPTCHA_FINISHED  PlantCaptchaStatus = 2
	PlantCaptchaStatus_CAPTCHA_FAILED    PlantCaptchaStatus = 3
	PlantCaptchaStatus_CAPTCHA_CANCELLED PlantCaptchaStatus = 4
)

// Enum value maps for PlantCaptchaStatus.
var (
	PlantCaptchaStatus_name = map[int32]string{
		0: "NOT_STARTED",
		1: "CAPTCHA_STARTED",
		2: "CAPTCHA_FINISHED",
		3: "CAPTCHA_FAILED",
		4: "CAPTCHA_CANCELLED",
	}
	PlantCaptchaStatus_value = map[string]int32{
		"NOT_STARTED":       0,
		"CAPTCHA_STARTED":   1,
		"CAPTCHA_FINISHED":  2,
		"CAPTCHA_FAILED":    3,
		"CAPTCHA_CANCELLED": 4,
	}
)

func (x PlantCaptchaStatus) Enum() *PlantCaptchaStatus {
	p := new(PlantCaptchaStatus)
	*p = x
	return p
}

func (x PlantCaptchaStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlantCaptchaStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[8].Descriptor()
}

func (PlantCaptchaStatus) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[8]
}

func (x PlantCaptchaStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlantCaptchaStatus.Descriptor instead.
func (PlantCaptchaStatus) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{8}
}

type PlantCaptchaUserPrediction int32

const (
	PlantCaptchaUserPrediction_WEED       PlantCaptchaUserPrediction = 0
	PlantCaptchaUserPrediction_CROP       PlantCaptchaUserPrediction = 1
	PlantCaptchaUserPrediction_UNKNOWN    PlantCaptchaUserPrediction = 2
	PlantCaptchaUserPrediction_OTHER      PlantCaptchaUserPrediction = 3
	PlantCaptchaUserPrediction_IGNORE     PlantCaptchaUserPrediction = 4 // Only used in initial state
	PlantCaptchaUserPrediction_VOLUNTEER  PlantCaptchaUserPrediction = 5
	PlantCaptchaUserPrediction_BENEFICIAL PlantCaptchaUserPrediction = 6
	PlantCaptchaUserPrediction_DEBRIS     PlantCaptchaUserPrediction = 7
)

// Enum value maps for PlantCaptchaUserPrediction.
var (
	PlantCaptchaUserPrediction_name = map[int32]string{
		0: "WEED",
		1: "CROP",
		2: "UNKNOWN",
		3: "OTHER",
		4: "IGNORE",
		5: "VOLUNTEER",
		6: "BENEFICIAL",
		7: "DEBRIS",
	}
	PlantCaptchaUserPrediction_value = map[string]int32{
		"WEED":       0,
		"CROP":       1,
		"UNKNOWN":    2,
		"OTHER":      3,
		"IGNORE":     4,
		"VOLUNTEER":  5,
		"BENEFICIAL": 6,
		"DEBRIS":     7,
	}
)

func (x PlantCaptchaUserPrediction) Enum() *PlantCaptchaUserPrediction {
	p := new(PlantCaptchaUserPrediction)
	*p = x
	return p
}

func (x PlantCaptchaUserPrediction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlantCaptchaUserPrediction) Descriptor() protoreflect.EnumDescriptor {
	return file_weed_tracking_weed_tracking_proto_enumTypes[9].Descriptor()
}

func (PlantCaptchaUserPrediction) Type() protoreflect.EnumType {
	return &file_weed_tracking_weed_tracking_proto_enumTypes[9]
}

func (x PlantCaptchaUserPrediction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlantCaptchaUserPrediction.Descriptor instead.
func (PlantCaptchaUserPrediction) EnumDescriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{9}
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Do not use.
type Trajectory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                          uint32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TrackerId                   uint32  `protobuf:"varint,2,opt,name=tracker_id,json=trackerId,proto3" json:"tracker_id,omitempty"`
	Status                      uint32  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	IsWeed                      bool    `protobuf:"varint,4,opt,name=is_weed,json=isWeed,proto3" json:"is_weed,omitempty"`
	XMm                         float64 `protobuf:"fixed64,5,opt,name=x_mm,json=xMm,proto3" json:"x_mm,omitempty"`
	YMm                         float64 `protobuf:"fixed64,6,opt,name=y_mm,json=yMm,proto3" json:"y_mm,omitempty"`
	ZMm                         float64 `protobuf:"fixed64,7,opt,name=z_mm,json=zMm,proto3" json:"z_mm,omitempty"`
	IntersectedWithNonshootable bool    `protobuf:"varint,8,opt,name=intersected_with_nonshootable,json=intersectedWithNonshootable,proto3" json:"intersected_with_nonshootable,omitempty"`
	NonshootableTypeString      string  `protobuf:"bytes,9,opt,name=nonshootable_type_string,json=nonshootableTypeString,proto3" json:"nonshootable_type_string,omitempty"`
	DeduplicatedAcrossTracker   bool    `protobuf:"varint,10,opt,name=deduplicated_across_tracker,json=deduplicatedAcrossTracker,proto3" json:"deduplicated_across_tracker,omitempty"`
}

func (x *Trajectory) Reset() {
	*x = Trajectory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trajectory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trajectory) ProtoMessage() {}

func (x *Trajectory) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trajectory.ProtoReflect.Descriptor instead.
func (*Trajectory) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{1}
}

func (x *Trajectory) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Trajectory) GetTrackerId() uint32 {
	if x != nil {
		return x.TrackerId
	}
	return 0
}

func (x *Trajectory) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Trajectory) GetIsWeed() bool {
	if x != nil {
		return x.IsWeed
	}
	return false
}

func (x *Trajectory) GetXMm() float64 {
	if x != nil {
		return x.XMm
	}
	return 0
}

func (x *Trajectory) GetYMm() float64 {
	if x != nil {
		return x.YMm
	}
	return 0
}

func (x *Trajectory) GetZMm() float64 {
	if x != nil {
		return x.ZMm
	}
	return 0
}

func (x *Trajectory) GetIntersectedWithNonshootable() bool {
	if x != nil {
		return x.IntersectedWithNonshootable
	}
	return false
}

func (x *Trajectory) GetNonshootableTypeString() string {
	if x != nil {
		return x.NonshootableTypeString
	}
	return ""
}

func (x *Trajectory) GetDeduplicatedAcrossTracker() bool {
	if x != nil {
		return x.DeduplicatedAcrossTracker
	}
	return false
}

// Deprecated: Do not use.
type Target struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId        uint32  `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	TrajectoryId     uint32  `protobuf:"varint,2,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
	NextTrajectoryId uint32  `protobuf:"varint,3,opt,name=next_trajectory_id,json=nextTrajectoryId,proto3" json:"next_trajectory_id,omitempty"`
	StartingPosY     float32 `protobuf:"fixed32,4,opt,name=starting_pos_y,json=startingPosY,proto3" json:"starting_pos_y,omitempty"`
	EndingPosY       float32 `protobuf:"fixed32,5,opt,name=ending_pos_y,json=endingPosY,proto3" json:"ending_pos_y,omitempty"`
}

func (x *Target) Reset() {
	*x = Target{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Target) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Target) ProtoMessage() {}

func (x *Target) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Target.ProtoReflect.Descriptor instead.
func (*Target) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{2}
}

func (x *Target) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *Target) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

func (x *Target) GetNextTrajectoryId() uint32 {
	if x != nil {
		return x.NextTrajectoryId
	}
	return 0
}

func (x *Target) GetStartingPosY() float32 {
	if x != nil {
		return x.StartingPosY
	}
	return 0
}

func (x *Target) GetEndingPosY() float32 {
	if x != nil {
		return x.EndingPosY
	}
	return 0
}

// Deprecated: Do not use.
type Bounds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackerId string  `protobuf:"bytes,1,opt,name=tracker_id,json=trackerId,proto3" json:"tracker_id,omitempty"`
	MaxPosMmY float32 `protobuf:"fixed32,2,opt,name=max_pos_mm_y,json=maxPosMmY,proto3" json:"max_pos_mm_y,omitempty"`
}

func (x *Bounds) Reset() {
	*x = Bounds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bounds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bounds) ProtoMessage() {}

func (x *Bounds) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bounds.ProtoReflect.Descriptor instead.
func (*Bounds) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{3}
}

func (x *Bounds) GetTrackerId() string {
	if x != nil {
		return x.TrackerId
	}
	return ""
}

func (x *Bounds) GetMaxPosMmY() float32 {
	if x != nil {
		return x.MaxPosMmY
	}
	return 0
}

// Deprecated: Do not use.
type TrackingStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Targets      []*Target     `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty"`
	Trajectories []*Trajectory `protobuf:"bytes,2,rep,name=trajectories,proto3" json:"trajectories,omitempty"`
	Bounds       []*Bounds     `protobuf:"bytes,3,rep,name=bounds,proto3" json:"bounds,omitempty"`
}

func (x *TrackingStatusReply) Reset() {
	*x = TrackingStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackingStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingStatusReply) ProtoMessage() {}

func (x *TrackingStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingStatusReply.ProtoReflect.Descriptor instead.
func (*TrackingStatusReply) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{4}
}

func (x *TrackingStatusReply) GetTargets() []*Target {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *TrackingStatusReply) GetTrajectories() []*Trajectory {
	if x != nil {
		return x.Trajectories
	}
	return nil
}

func (x *TrackingStatusReply) GetBounds() []*Bounds {
	if x != nil {
		return x.Bounds
	}
	return nil
}

type GetDetectionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CamId       string `protobuf:"bytes,1,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	MaxX        int32  `protobuf:"varint,3,opt,name=max_x,json=maxX,proto3" json:"max_x,omitempty"`
}

func (x *GetDetectionsRequest) Reset() {
	*x = GetDetectionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetectionsRequest) ProtoMessage() {}

func (x *GetDetectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetectionsRequest.ProtoReflect.Descriptor instead.
func (*GetDetectionsRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{5}
}

func (x *GetDetectionsRequest) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *GetDetectionsRequest) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *GetDetectionsRequest) GetMaxX() int32 {
	if x != nil {
		return x.MaxX
	}
	return 0
}

type Detection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X          float32   `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y          float32   `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	Size       float32   `protobuf:"fixed32,3,opt,name=size,proto3" json:"size,omitempty"`
	Clz        string    `protobuf:"bytes,4,opt,name=clz,proto3" json:"clz,omitempty"`
	IsWeed     bool      `protobuf:"varint,5,opt,name=is_weed,json=isWeed,proto3" json:"is_weed,omitempty"`
	OutOfBand  bool      `protobuf:"varint,6,opt,name=out_of_band,json=outOfBand,proto3" json:"out_of_band,omitempty"`
	Id         uint32    `protobuf:"varint,7,opt,name=id,proto3" json:"id,omitempty"`
	Score      float32   `protobuf:"fixed32,8,opt,name=score,proto3" json:"score,omitempty"`
	WeedScore  float32   `protobuf:"fixed32,9,opt,name=weed_score,json=weedScore,proto3" json:"weed_score,omitempty"`
	CropScore  float32   `protobuf:"fixed32,10,opt,name=crop_score,json=cropScore,proto3" json:"crop_score,omitempty"`
	Embedding  []float32 `protobuf:"fixed32,11,rep,packed,name=embedding,proto3" json:"embedding,omitempty"`
	PlantScore float32   `protobuf:"fixed32,12,opt,name=plant_score,json=plantScore,proto3" json:"plant_score,omitempty"`
}

func (x *Detection) Reset() {
	*x = Detection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detection) ProtoMessage() {}

func (x *Detection) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detection.ProtoReflect.Descriptor instead.
func (*Detection) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{6}
}

func (x *Detection) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Detection) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *Detection) GetSize() float32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Detection) GetClz() string {
	if x != nil {
		return x.Clz
	}
	return ""
}

func (x *Detection) GetIsWeed() bool {
	if x != nil {
		return x.IsWeed
	}
	return false
}

func (x *Detection) GetOutOfBand() bool {
	if x != nil {
		return x.OutOfBand
	}
	return false
}

func (x *Detection) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Detection) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Detection) GetWeedScore() float32 {
	if x != nil {
		return x.WeedScore
	}
	return 0
}

func (x *Detection) GetCropScore() float32 {
	if x != nil {
		return x.CropScore
	}
	return 0
}

func (x *Detection) GetEmbedding() []float32 {
	if x != nil {
		return x.Embedding
	}
	return nil
}

func (x *Detection) GetPlantScore() float32 {
	if x != nil {
		return x.PlantScore
	}
	return 0
}

type Detections struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detections  []*Detection `protobuf:"bytes,1,rep,name=detections,proto3" json:"detections,omitempty"`
	TimestampMs int64        `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *Detections) Reset() {
	*x = Detections{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detections) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detections) ProtoMessage() {}

func (x *Detections) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detections.ProtoReflect.Descriptor instead.
func (*Detections) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{7}
}

func (x *Detections) GetDetections() []*Detection {
	if x != nil {
		return x.Detections
	}
	return nil
}

func (x *Detections) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type Band struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartXPx float64 `protobuf:"fixed64,1,opt,name=start_x_px,json=startXPx,proto3" json:"start_x_px,omitempty"`
	EndXPx   float64 `protobuf:"fixed64,2,opt,name=end_x_px,json=endXPx,proto3" json:"end_x_px,omitempty"`
}

func (x *Band) Reset() {
	*x = Band{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Band) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Band) ProtoMessage() {}

func (x *Band) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Band.ProtoReflect.Descriptor instead.
func (*Band) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{8}
}

func (x *Band) GetStartXPx() float64 {
	if x != nil {
		return x.StartXPx
	}
	return 0
}

func (x *Band) GetEndXPx() float64 {
	if x != nil {
		return x.EndXPx
	}
	return 0
}

type Bands struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Band               []*Band `protobuf:"bytes,1,rep,name=band,proto3" json:"band,omitempty"`
	BandingEnabled     bool    `protobuf:"varint,2,opt,name=banding_enabled,json=bandingEnabled,proto3" json:"banding_enabled,omitempty"`
	RowHasBandsDefined bool    `protobuf:"varint,3,opt,name=row_has_bands_defined,json=rowHasBandsDefined,proto3" json:"row_has_bands_defined,omitempty"`
}

func (x *Bands) Reset() {
	*x = Bands{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bands) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bands) ProtoMessage() {}

func (x *Bands) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bands.ProtoReflect.Descriptor instead.
func (*Bands) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{9}
}

func (x *Bands) GetBand() []*Band {
	if x != nil {
		return x.Band
	}
	return nil
}

func (x *Bands) GetBandingEnabled() bool {
	if x != nil {
		return x.BandingEnabled
	}
	return false
}

func (x *Bands) GetRowHasBandsDefined() bool {
	if x != nil {
		return x.RowHasBandsDefined
	}
	return false
}

type GetDetectionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detections *Detections `protobuf:"bytes,1,opt,name=detections,proto3" json:"detections,omitempty"`
	Bands      *Bands      `protobuf:"bytes,2,opt,name=bands,proto3" json:"bands,omitempty"`
}

func (x *GetDetectionsResponse) Reset() {
	*x = GetDetectionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetectionsResponse) ProtoMessage() {}

func (x *GetDetectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetectionsResponse.ProtoReflect.Descriptor instead.
func (*GetDetectionsResponse) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{10}
}

func (x *GetDetectionsResponse) GetDetections() *Detections {
	if x != nil {
		return x.Detections
	}
	return nil
}

func (x *GetDetectionsResponse) GetBands() *Bands {
	if x != nil {
		return x.Bands
	}
	return nil
}

type GetTrajectoryMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTrajectoryMetadataRequest) Reset() {
	*x = GetTrajectoryMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrajectoryMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrajectoryMetadataRequest) ProtoMessage() {}

func (x *GetTrajectoryMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrajectoryMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetTrajectoryMetadataRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{11}
}

type TrackedItemMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetectionId uint32 `protobuf:"varint,1,opt,name=detection_id,json=detectionId,proto3" json:"detection_id,omitempty"`
	TimestampMs int64  `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (x *TrackedItemMetadata) Reset() {
	*x = TrackedItemMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackedItemMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackedItemMetadata) ProtoMessage() {}

func (x *TrackedItemMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackedItemMetadata.ProtoReflect.Descriptor instead.
func (*TrackedItemMetadata) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{12}
}

func (x *TrackedItemMetadata) GetDetectionId() uint32 {
	if x != nil {
		return x.DetectionId
	}
	return 0
}

func (x *TrackedItemMetadata) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

type TrajectoryMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrajectoryId        uint32                 `protobuf:"varint,1,opt,name=trajectory_id,json=trajectoryId,proto3" json:"trajectory_id,omitempty"`
	CamId               string                 `protobuf:"bytes,2,opt,name=cam_id,json=camId,proto3" json:"cam_id,omitempty"`
	TrackedItemMetadata []*TrackedItemMetadata `protobuf:"bytes,3,rep,name=tracked_item_metadata,json=trackedItemMetadata,proto3" json:"tracked_item_metadata,omitempty"`
	BandStatus          string                 `protobuf:"bytes,4,opt,name=band_status,json=bandStatus,proto3" json:"band_status,omitempty"`
}

func (x *TrajectoryMetadata) Reset() {
	*x = TrajectoryMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrajectoryMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrajectoryMetadata) ProtoMessage() {}

func (x *TrajectoryMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrajectoryMetadata.ProtoReflect.Descriptor instead.
func (*TrajectoryMetadata) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{13}
}

func (x *TrajectoryMetadata) GetTrajectoryId() uint32 {
	if x != nil {
		return x.TrajectoryId
	}
	return 0
}

func (x *TrajectoryMetadata) GetCamId() string {
	if x != nil {
		return x.CamId
	}
	return ""
}

func (x *TrajectoryMetadata) GetTrackedItemMetadata() []*TrackedItemMetadata {
	if x != nil {
		return x.TrackedItemMetadata
	}
	return nil
}

func (x *TrajectoryMetadata) GetBandStatus() string {
	if x != nil {
		return x.BandStatus
	}
	return ""
}

type GetTrajectoryMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metadata []*TrajectoryMetadata `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *GetTrajectoryMetadataResponse) Reset() {
	*x = GetTrajectoryMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTrajectoryMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrajectoryMetadataResponse) ProtoMessage() {}

func (x *GetTrajectoryMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrajectoryMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetTrajectoryMetadataResponse) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{14}
}

func (x *GetTrajectoryMetadataResponse) GetMetadata() []*TrajectoryMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type PingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{15}
}

func (x *PingRequest) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type PongReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X uint32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"`
}

func (x *PongReply) Reset() {
	*x = PongReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PongReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PongReply) ProtoMessage() {}

func (x *PongReply) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PongReply.ProtoReflect.Descriptor instead.
func (*PongReply) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{16}
}

func (x *PongReply) GetX() uint32 {
	if x != nil {
		return x.X
	}
	return 0
}

type TrajectoryScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score           uint64  `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	ScoreTilt       float64 `protobuf:"fixed64,2,opt,name=score_tilt,json=scoreTilt,proto3" json:"score_tilt,omitempty"`
	ScorePan        float64 `protobuf:"fixed64,3,opt,name=score_pan,json=scorePan,proto3" json:"score_pan,omitempty"`
	ScoreBusy       float64 `protobuf:"fixed64,4,opt,name=score_busy,json=scoreBusy,proto3" json:"score_busy,omitempty"`
	ScoreInRange    float64 `protobuf:"fixed64,5,opt,name=score_in_range,json=scoreInRange,proto3" json:"score_in_range,omitempty"`
	ScoreImportance float64 `protobuf:"fixed64,6,opt,name=score_importance,json=scoreImportance,proto3" json:"score_importance,omitempty"`
	ScoreSize       float64 `protobuf:"fixed64,7,opt,name=score_size,json=scoreSize,proto3" json:"score_size,omitempty"`
}

func (x *TrajectoryScore) Reset() {
	*x = TrajectoryScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrajectoryScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrajectoryScore) ProtoMessage() {}

func (x *TrajectoryScore) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrajectoryScore.ProtoReflect.Descriptor instead.
func (*TrajectoryScore) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{17}
}

func (x *TrajectoryScore) GetScore() uint64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TrajectoryScore) GetScoreTilt() float64 {
	if x != nil {
		return x.ScoreTilt
	}
	return 0
}

func (x *TrajectoryScore) GetScorePan() float64 {
	if x != nil {
		return x.ScorePan
	}
	return 0
}

func (x *TrajectoryScore) GetScoreBusy() float64 {
	if x != nil {
		return x.ScoreBusy
	}
	return 0
}

func (x *TrajectoryScore) GetScoreInRange() float64 {
	if x != nil {
		return x.ScoreInRange
	}
	return 0
}

func (x *TrajectoryScore) GetScoreImportance() float64 {
	if x != nil {
		return x.ScoreImportance
	}
	return 0
}

func (x *TrajectoryScore) GetScoreSize() float64 {
	if x != nil {
		return x.ScoreSize
	}
	return 0
}

type PerScannerScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	Score     int32  `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *PerScannerScore) Reset() {
	*x = PerScannerScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PerScannerScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerScannerScore) ProtoMessage() {}

func (x *PerScannerScore) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerScannerScore.ProtoReflect.Descriptor instead.
func (*PerScannerScore) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{18}
}

func (x *PerScannerScore) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *PerScannerScore) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type ScoreState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetState TargetableState    `protobuf:"varint,1,opt,name=target_state,json=targetState,proto3,enum=weed_tracking.TargetableState" json:"target_state,omitempty"`
	Scores      []*PerScannerScore `protobuf:"bytes,2,rep,name=scores,proto3" json:"scores,omitempty"`
}

func (x *ScoreState) Reset() {
	*x = ScoreState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScoreState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreState) ProtoMessage() {}

func (x *ScoreState) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreState.ProtoReflect.Descriptor instead.
func (*ScoreState) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{19}
}

func (x *ScoreState) GetTargetState() TargetableState {
	if x != nil {
		return x.TargetState
	}
	return TargetableState_TARGET_NOT_IN_SCHEDULER
}

func (x *ScoreState) GetScores() []*PerScannerScore {
	if x != nil {
		return x.Scores
	}
	return nil
}

type Pos2D struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float64 `protobuf:"fixed64,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float64 `protobuf:"fixed64,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *Pos2D) Reset() {
	*x = Pos2D{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pos2D) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pos2D) ProtoMessage() {}

func (x *Pos2D) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pos2D.ProtoReflect.Descriptor instead.
func (*Pos2D) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{20}
}

func (x *Pos2D) GetX() float64 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Pos2D) GetY() float64 {
	if x != nil {
		return x.Y
	}
	return 0
}

type KillBox struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScannerId   uint32 `protobuf:"varint,1,opt,name=scanner_id,json=scannerId,proto3" json:"scanner_id,omitempty"`
	TopLeft     *Pos2D `protobuf:"bytes,2,opt,name=top_left,json=topLeft,proto3" json:"top_left,omitempty"`
	BottomRight *Pos2D `protobuf:"bytes,3,opt,name=bottom_right,json=bottomRight,proto3" json:"bottom_right,omitempty"`
}

func (x *KillBox) Reset() {
	*x = KillBox{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KillBox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillBox) ProtoMessage() {}

func (x *KillBox) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillBox.ProtoReflect.Descriptor instead.
func (*KillBox) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{21}
}

func (x *KillBox) GetScannerId() uint32 {
	if x != nil {
		return x.ScannerId
	}
	return 0
}

func (x *KillBox) GetTopLeft() *Pos2D {
	if x != nil {
		return x.TopLeft
	}
	return nil
}

func (x *KillBox) GetBottomRight() *Pos2D {
	if x != nil {
		return x.BottomRight
	}
	return nil
}

type Thresholds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Do not use.
	Weeding float32 `protobuf:"fixed32,1,opt,name=weeding,proto3" json:"weeding,omitempty"`
	// Deprecated: Do not use.
	Thinning float32 `protobuf:"fixed32,2,opt,name=thinning,proto3" json:"thinning,omitempty"`
	// Deprecated: Do not use.
	Banding        float32 `protobuf:"fixed32,3,opt,name=banding,proto3" json:"banding,omitempty"`
	PassedWeeding  bool    `protobuf:"varint,4,opt,name=passed_weeding,json=passedWeeding,proto3" json:"passed_weeding,omitempty"`
	PassedThinning bool    `protobuf:"varint,5,opt,name=passed_thinning,json=passedThinning,proto3" json:"passed_thinning,omitempty"`
	PassedBanding  bool    `protobuf:"varint,6,opt,name=passed_banding,json=passedBanding,proto3" json:"passed_banding,omitempty"`
}

func (x *Thresholds) Reset() {
	*x = Thresholds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Thresholds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Thresholds) ProtoMessage() {}

func (x *Thresholds) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Thresholds.ProtoReflect.Descriptor instead.
func (*Thresholds) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{22}
}

// Deprecated: Do not use.
func (x *Thresholds) GetWeeding() float32 {
	if x != nil {
		return x.Weeding
	}
	return 0
}

// Deprecated: Do not use.
func (x *Thresholds) GetThinning() float32 {
	if x != nil {
		return x.Thinning
	}
	return 0
}

// Deprecated: Do not use.
func (x *Thresholds) GetBanding() float32 {
	if x != nil {
		return x.Banding
	}
	return 0
}

func (x *Thresholds) GetPassedWeeding() bool {
	if x != nil {
		return x.PassedWeeding
	}
	return false
}

func (x *Thresholds) GetPassedThinning() bool {
	if x != nil {
		return x.PassedThinning
	}
	return false
}

func (x *Thresholds) GetPassedBanding() bool {
	if x != nil {
		return x.PassedBanding
	}
	return false
}

type Decisions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeedingWeed  bool `protobuf:"varint,1,opt,name=weeding_weed,json=weedingWeed,proto3" json:"weeding_weed,omitempty"`
	WeedingCrop  bool `protobuf:"varint,2,opt,name=weeding_crop,json=weedingCrop,proto3" json:"weeding_crop,omitempty"`
	ThinningWeed bool `protobuf:"varint,3,opt,name=thinning_weed,json=thinningWeed,proto3" json:"thinning_weed,omitempty"`
	ThinningCrop bool `protobuf:"varint,4,opt,name=thinning_crop,json=thinningCrop,proto3" json:"thinning_crop,omitempty"`
	KeepableCrop bool `protobuf:"varint,5,opt,name=keepable_crop,json=keepableCrop,proto3" json:"keepable_crop,omitempty"`
	BandingCrop  bool `protobuf:"varint,6,opt,name=banding_crop,json=bandingCrop,proto3" json:"banding_crop,omitempty"`
}

func (x *Decisions) Reset() {
	*x = Decisions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Decisions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Decisions) ProtoMessage() {}

func (x *Decisions) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Decisions.ProtoReflect.Descriptor instead.
func (*Decisions) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{23}
}

func (x *Decisions) GetWeedingWeed() bool {
	if x != nil {
		return x.WeedingWeed
	}
	return false
}

func (x *Decisions) GetWeedingCrop() bool {
	if x != nil {
		return x.WeedingCrop
	}
	return false
}

func (x *Decisions) GetThinningWeed() bool {
	if x != nil {
		return x.ThinningWeed
	}
	return false
}

func (x *Decisions) GetThinningCrop() bool {
	if x != nil {
		return x.ThinningCrop
	}
	return false
}

func (x *Decisions) GetKeepableCrop() bool {
	if x != nil {
		return x.KeepableCrop
	}
	return false
}

func (x *Decisions) GetBandingCrop() bool {
	if x != nil {
		return x.BandingCrop
	}
	return false
}

type TrajectorySnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	KillStatus KillStatus `protobuf:"varint,2,opt,name=kill_status,json=killStatus,proto3,enum=weed_tracking.KillStatus" json:"kill_status,omitempty"`
	// Deprecated: Do not use.
	IsWeed bool    `protobuf:"varint,3,opt,name=is_weed,json=isWeed,proto3" json:"is_weed,omitempty"` // deprecated 2.1
	XMm    float64 `protobuf:"fixed64,4,opt,name=x_mm,json=xMm,proto3" json:"x_mm,omitempty"`
	YMm    float64 `protobuf:"fixed64,5,opt,name=y_mm,json=yMm,proto3" json:"y_mm,omitempty"`
	ZMm    float64 `protobuf:"fixed64,6,opt,name=z_mm,json=zMm,proto3" json:"z_mm,omitempty"`
	// Types that are assignable to ScoreDetails:
	//	*TrajectorySnapshot_Score
	//	*TrajectorySnapshot_InvalidScore
	ScoreDetails                isTrajectorySnapshot_ScoreDetails `protobuf_oneof:"score_details"`
	RadiusMm                    float64                           `protobuf:"fixed64,9,opt,name=radius_mm,json=radiusMm,proto3" json:"radius_mm,omitempty"`
	Category                    string                            `protobuf:"bytes,10,opt,name=category,proto3" json:"category,omitempty"`
	ShootTimeRequestedMs        uint64                            `protobuf:"varint,11,opt,name=shoot_time_requested_ms,json=shootTimeRequestedMs,proto3" json:"shoot_time_requested_ms,omitempty"`
	ShootTimeActualMs           uint64                            `protobuf:"varint,12,opt,name=shoot_time_actual_ms,json=shootTimeActualMs,proto3" json:"shoot_time_actual_ms,omitempty"`
	MarkedForThinning           bool                              `protobuf:"varint,13,opt,name=marked_for_thinning,json=markedForThinning,proto3" json:"marked_for_thinning,omitempty"`
	TrackerId                   uint32                            `protobuf:"varint,14,opt,name=tracker_id,json=trackerId,proto3" json:"tracker_id,omitempty"`
	DuplicateStatus             DuplicateStatus                   `protobuf:"varint,15,opt,name=duplicate_status,json=duplicateStatus,proto3,enum=weed_tracking.DuplicateStatus" json:"duplicate_status,omitempty"`
	DuplicateTrajectoryId       uint32                            `protobuf:"varint,16,opt,name=duplicate_trajectory_id,json=duplicateTrajectoryId,proto3" json:"duplicate_trajectory_id,omitempty"`
	AssignedLasers              []uint32                          `protobuf:"varint,17,rep,packed,name=assigned_lasers,json=assignedLasers,proto3" json:"assigned_lasers,omitempty"`
	OutOfBand                   bool                              `protobuf:"varint,18,opt,name=out_of_band,json=outOfBand,proto3" json:"out_of_band,omitempty"`                                                       // deprecated 1.16 use score state
	IntersectedWithNonshootable bool                              `protobuf:"varint,19,opt,name=intersected_with_nonshootable,json=intersectedWithNonshootable,proto3" json:"intersected_with_nonshootable,omitempty"` // deprecated 1.16
	DetectionClasses            map[string]float64                `protobuf:"bytes,20,rep,name=detection_classes,json=detectionClasses,proto3" json:"detection_classes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	Confidence                  float64                           `protobuf:"fixed64,21,opt,name=confidence,proto3" json:"confidence,omitempty"`
	ThinningState               ThinningState                     `protobuf:"varint,22,opt,name=thinning_state,json=thinningState,proto3,enum=weed_tracking.ThinningState" json:"thinning_state,omitempty"`
	GlobalPos                   float64                           `protobuf:"fixed64,23,opt,name=global_pos,json=globalPos,proto3" json:"global_pos,omitempty"`
	ScoreState                  *ScoreState                       `protobuf:"bytes,24,opt,name=score_state,json=scoreState,proto3" json:"score_state,omitempty"`
	Doo                         float32                           `protobuf:"fixed32,25,opt,name=doo,proto3" json:"doo,omitempty"`
	DistancePerspectivesCount   uint32                            `protobuf:"varint,26,opt,name=distance_perspectives_count,json=distancePerspectivesCount,proto3" json:"distance_perspectives_count,omitempty"`
	SizeCategoryIndex           int32                             `protobuf:"varint,27,opt,name=size_category_index,json=sizeCategoryIndex,proto3" json:"size_category_index,omitempty"` // as returned from almanac, 0 for small, 1 for medium, 2 for large
	SpeculativeAllowed          bool                              `protobuf:"varint,28,opt,name=speculative_allowed,json=speculativeAllowed,proto3" json:"speculative_allowed,omitempty"`
	ProtectedByTraj             bool                              `protobuf:"varint,29,opt,name=protected_by_traj,json=protectedByTraj,proto3" json:"protected_by_traj,omitempty"` // crop protect / reverse crop protect
	Thresholds                  *Thresholds                       `protobuf:"bytes,30,opt,name=thresholds,proto3" json:"thresholds,omitempty"`
	// new fields
	Decisions                    *Decisions         `protobuf:"bytes,31,opt,name=decisions,proto3" json:"decisions,omitempty"`
	CropScore                    float64            `protobuf:"fixed64,32,opt,name=crop_score,json=cropScore,proto3" json:"crop_score,omitempty"`
	WeedScore                    float64            `protobuf:"fixed64,33,opt,name=weed_score,json=weedScore,proto3" json:"weed_score,omitempty"`
	PlantScore                   float64            `protobuf:"fixed64,34,opt,name=plant_score,json=plantScore,proto3" json:"plant_score,omitempty"`
	NumDetectionsUsedForDecision uint32             `protobuf:"varint,35,opt,name=num_detections_used_for_decision,json=numDetectionsUsedForDecision,proto3" json:"num_detections_used_for_decision,omitempty"`
	Classification               Classification     `protobuf:"varint,36,opt,name=classification,proto3,enum=weed_tracking.Classification" json:"classification,omitempty"`
	EmbeddingDistances           map[string]float32 `protobuf:"bytes,37,rep,name=embedding_distances,json=embeddingDistances,proto3" json:"embedding_distances,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	SpeculativeShootTimeActualMs uint32             `protobuf:"varint,38,opt,name=speculative_shoot_time_actual_ms,json=speculativeShootTimeActualMs,proto3" json:"speculative_shoot_time_actual_ms,omitempty"`
	SnapshotMetadata             *SnapshotMetadata  `protobuf:"bytes,39,opt,name=snapshot_metadata,json=snapshotMetadata,proto3" json:"snapshot_metadata,omitempty"`
}

func (x *TrajectorySnapshot) Reset() {
	*x = TrajectorySnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrajectorySnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrajectorySnapshot) ProtoMessage() {}

func (x *TrajectorySnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrajectorySnapshot.ProtoReflect.Descriptor instead.
func (*TrajectorySnapshot) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{24}
}

func (x *TrajectorySnapshot) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TrajectorySnapshot) GetKillStatus() KillStatus {
	if x != nil {
		return x.KillStatus
	}
	return KillStatus_STATUS_NOT_SHOT
}

// Deprecated: Do not use.
func (x *TrajectorySnapshot) GetIsWeed() bool {
	if x != nil {
		return x.IsWeed
	}
	return false
}

func (x *TrajectorySnapshot) GetXMm() float64 {
	if x != nil {
		return x.XMm
	}
	return 0
}

func (x *TrajectorySnapshot) GetYMm() float64 {
	if x != nil {
		return x.YMm
	}
	return 0
}

func (x *TrajectorySnapshot) GetZMm() float64 {
	if x != nil {
		return x.ZMm
	}
	return 0
}

func (m *TrajectorySnapshot) GetScoreDetails() isTrajectorySnapshot_ScoreDetails {
	if m != nil {
		return m.ScoreDetails
	}
	return nil
}

func (x *TrajectorySnapshot) GetScore() *TrajectoryScore {
	if x, ok := x.GetScoreDetails().(*TrajectorySnapshot_Score); ok {
		return x.Score
	}
	return nil
}

func (x *TrajectorySnapshot) GetInvalidScore() InvalidScoreReason {
	if x, ok := x.GetScoreDetails().(*TrajectorySnapshot_InvalidScore); ok {
		return x.InvalidScore
	}
	return InvalidScoreReason_NONE
}

func (x *TrajectorySnapshot) GetRadiusMm() float64 {
	if x != nil {
		return x.RadiusMm
	}
	return 0
}

func (x *TrajectorySnapshot) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *TrajectorySnapshot) GetShootTimeRequestedMs() uint64 {
	if x != nil {
		return x.ShootTimeRequestedMs
	}
	return 0
}

func (x *TrajectorySnapshot) GetShootTimeActualMs() uint64 {
	if x != nil {
		return x.ShootTimeActualMs
	}
	return 0
}

func (x *TrajectorySnapshot) GetMarkedForThinning() bool {
	if x != nil {
		return x.MarkedForThinning
	}
	return false
}

func (x *TrajectorySnapshot) GetTrackerId() uint32 {
	if x != nil {
		return x.TrackerId
	}
	return 0
}

func (x *TrajectorySnapshot) GetDuplicateStatus() DuplicateStatus {
	if x != nil {
		return x.DuplicateStatus
	}
	return DuplicateStatus_UNIQUE
}

func (x *TrajectorySnapshot) GetDuplicateTrajectoryId() uint32 {
	if x != nil {
		return x.DuplicateTrajectoryId
	}
	return 0
}

func (x *TrajectorySnapshot) GetAssignedLasers() []uint32 {
	if x != nil {
		return x.AssignedLasers
	}
	return nil
}

func (x *TrajectorySnapshot) GetOutOfBand() bool {
	if x != nil {
		return x.OutOfBand
	}
	return false
}

func (x *TrajectorySnapshot) GetIntersectedWithNonshootable() bool {
	if x != nil {
		return x.IntersectedWithNonshootable
	}
	return false
}

func (x *TrajectorySnapshot) GetDetectionClasses() map[string]float64 {
	if x != nil {
		return x.DetectionClasses
	}
	return nil
}

func (x *TrajectorySnapshot) GetConfidence() float64 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *TrajectorySnapshot) GetThinningState() ThinningState {
	if x != nil {
		return x.ThinningState
	}
	return ThinningState_THINNING_UNSET
}

func (x *TrajectorySnapshot) GetGlobalPos() float64 {
	if x != nil {
		return x.GlobalPos
	}
	return 0
}

func (x *TrajectorySnapshot) GetScoreState() *ScoreState {
	if x != nil {
		return x.ScoreState
	}
	return nil
}

func (x *TrajectorySnapshot) GetDoo() float32 {
	if x != nil {
		return x.Doo
	}
	return 0
}

func (x *TrajectorySnapshot) GetDistancePerspectivesCount() uint32 {
	if x != nil {
		return x.DistancePerspectivesCount
	}
	return 0
}

func (x *TrajectorySnapshot) GetSizeCategoryIndex() int32 {
	if x != nil {
		return x.SizeCategoryIndex
	}
	return 0
}

func (x *TrajectorySnapshot) GetSpeculativeAllowed() bool {
	if x != nil {
		return x.SpeculativeAllowed
	}
	return false
}

func (x *TrajectorySnapshot) GetProtectedByTraj() bool {
	if x != nil {
		return x.ProtectedByTraj
	}
	return false
}

func (x *TrajectorySnapshot) GetThresholds() *Thresholds {
	if x != nil {
		return x.Thresholds
	}
	return nil
}

func (x *TrajectorySnapshot) GetDecisions() *Decisions {
	if x != nil {
		return x.Decisions
	}
	return nil
}

func (x *TrajectorySnapshot) GetCropScore() float64 {
	if x != nil {
		return x.CropScore
	}
	return 0
}

func (x *TrajectorySnapshot) GetWeedScore() float64 {
	if x != nil {
		return x.WeedScore
	}
	return 0
}

func (x *TrajectorySnapshot) GetPlantScore() float64 {
	if x != nil {
		return x.PlantScore
	}
	return 0
}

func (x *TrajectorySnapshot) GetNumDetectionsUsedForDecision() uint32 {
	if x != nil {
		return x.NumDetectionsUsedForDecision
	}
	return 0
}

func (x *TrajectorySnapshot) GetClassification() Classification {
	if x != nil {
		return x.Classification
	}
	return Classification_CLASS_UNDECIDED
}

func (x *TrajectorySnapshot) GetEmbeddingDistances() map[string]float32 {
	if x != nil {
		return x.EmbeddingDistances
	}
	return nil
}

func (x *TrajectorySnapshot) GetSpeculativeShootTimeActualMs() uint32 {
	if x != nil {
		return x.SpeculativeShootTimeActualMs
	}
	return 0
}

func (x *TrajectorySnapshot) GetSnapshotMetadata() *SnapshotMetadata {
	if x != nil {
		return x.SnapshotMetadata
	}
	return nil
}

type isTrajectorySnapshot_ScoreDetails interface {
	isTrajectorySnapshot_ScoreDetails()
}

type TrajectorySnapshot_Score struct {
	Score *TrajectoryScore `protobuf:"bytes,7,opt,name=score,proto3,oneof"`
}

type TrajectorySnapshot_InvalidScore struct {
	InvalidScore InvalidScoreReason `protobuf:"varint,8,opt,name=invalid_score,json=invalidScore,proto3,enum=weed_tracking.InvalidScoreReason,oneof"`
}

func (*TrajectorySnapshot_Score) isTrajectorySnapshot_ScoreDetails() {}

func (*TrajectorySnapshot_InvalidScore) isTrajectorySnapshot_ScoreDetails() {}

type SnapshotMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PcamId      string  `protobuf:"bytes,1,opt,name=pcam_id,json=pcamId,proto3" json:"pcam_id,omitempty"`
	TimestampMs int64   `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	CenterXPx   float32 `protobuf:"fixed32,3,opt,name=center_x_px,json=centerXPx,proto3" json:"center_x_px,omitempty"`
	CenterYPx   float32 `protobuf:"fixed32,4,opt,name=center_y_px,json=centerYPx,proto3" json:"center_y_px,omitempty"`
}

func (x *SnapshotMetadata) Reset() {
	*x = SnapshotMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotMetadata) ProtoMessage() {}

func (x *SnapshotMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotMetadata.ProtoReflect.Descriptor instead.
func (*SnapshotMetadata) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{25}
}

func (x *SnapshotMetadata) GetPcamId() string {
	if x != nil {
		return x.PcamId
	}
	return ""
}

func (x *SnapshotMetadata) GetTimestampMs() int64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *SnapshotMetadata) GetCenterXPx() float32 {
	if x != nil {
		return x.CenterXPx
	}
	return 0
}

func (x *SnapshotMetadata) GetCenterYPx() float32 {
	if x != nil {
		return x.CenterYPx
	}
	return 0
}

type BandDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OffsetMm float32 `protobuf:"fixed32,1,opt,name=offset_mm,json=offsetMm,proto3" json:"offset_mm,omitempty"`
	WidthMm  float32 `protobuf:"fixed32,2,opt,name=width_mm,json=widthMm,proto3" json:"width_mm,omitempty"`
	Id       int32   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *BandDefinition) Reset() {
	*x = BandDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BandDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BandDefinition) ProtoMessage() {}

func (x *BandDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BandDefinition.ProtoReflect.Descriptor instead.
func (*BandDefinition) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{26}
}

func (x *BandDefinition) GetOffsetMm() float32 {
	if x != nil {
		return x.OffsetMm
	}
	return 0
}

func (x *BandDefinition) GetWidthMm() float32 {
	if x != nil {
		return x.WidthMm
	}
	return 0
}

func (x *BandDefinition) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Avoid nested messages
type CLDAlgorithmSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs  uint64    `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	GraphPointsX []float32 `protobuf:"fixed32,2,rep,packed,name=graph_points_x,json=graphPointsX,proto3" json:"graph_points_x,omitempty"`
	GraphPointsY []float32 `protobuf:"fixed32,3,rep,packed,name=graph_points_y,json=graphPointsY,proto3" json:"graph_points_y,omitempty"`
	MinimasX     []float32 `protobuf:"fixed32,4,rep,packed,name=minimas_x,json=minimasX,proto3" json:"minimas_x,omitempty"`
	MinimasY     []float32 `protobuf:"fixed32,5,rep,packed,name=minimas_y,json=minimasY,proto3" json:"minimas_y,omitempty"`
	Lines        []float32 `protobuf:"fixed32,6,rep,packed,name=lines,proto3" json:"lines,omitempty"`
}

func (x *CLDAlgorithmSnapshot) Reset() {
	*x = CLDAlgorithmSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CLDAlgorithmSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CLDAlgorithmSnapshot) ProtoMessage() {}

func (x *CLDAlgorithmSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CLDAlgorithmSnapshot.ProtoReflect.Descriptor instead.
func (*CLDAlgorithmSnapshot) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{27}
}

func (x *CLDAlgorithmSnapshot) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *CLDAlgorithmSnapshot) GetGraphPointsX() []float32 {
	if x != nil {
		return x.GraphPointsX
	}
	return nil
}

func (x *CLDAlgorithmSnapshot) GetGraphPointsY() []float32 {
	if x != nil {
		return x.GraphPointsY
	}
	return nil
}

func (x *CLDAlgorithmSnapshot) GetMinimasX() []float32 {
	if x != nil {
		return x.MinimasX
	}
	return nil
}

func (x *CLDAlgorithmSnapshot) GetMinimasY() []float32 {
	if x != nil {
		return x.MinimasY
	}
	return nil
}

func (x *CLDAlgorithmSnapshot) GetLines() []float32 {
	if x != nil {
		return x.Lines
	}
	return nil
}

type DiagnosticsSnapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimestampMs              uint64                `protobuf:"varint,1,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
	Trajectories             []*TrajectorySnapshot `protobuf:"bytes,2,rep,name=trajectories,proto3" json:"trajectories,omitempty"`
	Bands                    []*BandDefinition     `protobuf:"bytes,3,rep,name=bands,proto3" json:"bands,omitempty"`
	KillBoxes                []*KillBox            `protobuf:"bytes,4,rep,name=kill_boxes,json=killBoxes,proto3" json:"kill_boxes,omitempty"`
	BandingAlgorithmSnapshot *CLDAlgorithmSnapshot `protobuf:"bytes,5,opt,name=banding_algorithm_snapshot,json=bandingAlgorithmSnapshot,proto3,oneof" json:"banding_algorithm_snapshot,omitempty"`
}

func (x *DiagnosticsSnapshot) Reset() {
	*x = DiagnosticsSnapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiagnosticsSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiagnosticsSnapshot) ProtoMessage() {}

func (x *DiagnosticsSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiagnosticsSnapshot.ProtoReflect.Descriptor instead.
func (*DiagnosticsSnapshot) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{28}
}

func (x *DiagnosticsSnapshot) GetTimestampMs() uint64 {
	if x != nil {
		return x.TimestampMs
	}
	return 0
}

func (x *DiagnosticsSnapshot) GetTrajectories() []*TrajectorySnapshot {
	if x != nil {
		return x.Trajectories
	}
	return nil
}

func (x *DiagnosticsSnapshot) GetBands() []*BandDefinition {
	if x != nil {
		return x.Bands
	}
	return nil
}

func (x *DiagnosticsSnapshot) GetKillBoxes() []*KillBox {
	if x != nil {
		return x.KillBoxes
	}
	return nil
}

func (x *DiagnosticsSnapshot) GetBandingAlgorithmSnapshot() *CLDAlgorithmSnapshot {
	if x != nil {
		return x.BandingAlgorithmSnapshot
	}
	return nil
}

type RecordDiagnosticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TtlSec           uint32  `protobuf:"varint,1,opt,name=ttl_sec,json=ttlSec,proto3" json:"ttl_sec,omitempty"`
	CropImagesPerSec float32 `protobuf:"fixed32,2,opt,name=crop_images_per_sec,json=cropImagesPerSec,proto3" json:"crop_images_per_sec,omitempty"`
	WeedImagesPerSec float32 `protobuf:"fixed32,3,opt,name=weed_images_per_sec,json=weedImagesPerSec,proto3" json:"weed_images_per_sec,omitempty"`
}

func (x *RecordDiagnosticsRequest) Reset() {
	*x = RecordDiagnosticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordDiagnosticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordDiagnosticsRequest) ProtoMessage() {}

func (x *RecordDiagnosticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordDiagnosticsRequest.ProtoReflect.Descriptor instead.
func (*RecordDiagnosticsRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{29}
}

func (x *RecordDiagnosticsRequest) GetTtlSec() uint32 {
	if x != nil {
		return x.TtlSec
	}
	return 0
}

func (x *RecordDiagnosticsRequest) GetCropImagesPerSec() float32 {
	if x != nil {
		return x.CropImagesPerSec
	}
	return 0
}

func (x *RecordDiagnosticsRequest) GetWeedImagesPerSec() float32 {
	if x != nil {
		return x.WeedImagesPerSec
	}
	return 0
}

type GetRecordingStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status RecordingStatus `protobuf:"varint,1,opt,name=status,proto3,enum=weed_tracking.RecordingStatus" json:"status,omitempty"`
}

func (x *GetRecordingStatusResponse) Reset() {
	*x = GetRecordingStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordingStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordingStatusResponse) ProtoMessage() {}

func (x *GetRecordingStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordingStatusResponse.ProtoReflect.Descriptor instead.
func (*GetRecordingStatusResponse) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{30}
}

func (x *GetRecordingStatusResponse) GetStatus() RecordingStatus {
	if x != nil {
		return x.Status
	}
	return RecordingStatus_NOT_RECORDING
}

type StartSavingCropLineDetectionReplayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	TtlMs    uint32 `protobuf:"varint,2,opt,name=ttl_ms,json=ttlMs,proto3" json:"ttl_ms,omitempty"`
}

func (x *StartSavingCropLineDetectionReplayRequest) Reset() {
	*x = StartSavingCropLineDetectionReplayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartSavingCropLineDetectionReplayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartSavingCropLineDetectionReplayRequest) ProtoMessage() {}

func (x *StartSavingCropLineDetectionReplayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartSavingCropLineDetectionReplayRequest.ProtoReflect.Descriptor instead.
func (*StartSavingCropLineDetectionReplayRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{31}
}

func (x *StartSavingCropLineDetectionReplayRequest) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *StartSavingCropLineDetectionReplayRequest) GetTtlMs() uint32 {
	if x != nil {
		return x.TtlMs
	}
	return 0
}

type RecordAimbotInputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TtlMs       uint32 `protobuf:"varint,2,opt,name=ttl_ms,json=ttlMs,proto3" json:"ttl_ms,omitempty"`
	RotaryTicks *bool  `protobuf:"varint,3,opt,name=rotary_ticks,json=rotaryTicks,proto3,oneof" json:"rotary_ticks,omitempty"`
	Deepweed    *bool  `protobuf:"varint,4,opt,name=deepweed,proto3,oneof" json:"deepweed,omitempty"`
	LaneHeights *bool  `protobuf:"varint,5,opt,name=lane_heights,json=laneHeights,proto3,oneof" json:"lane_heights,omitempty"`
}

func (x *RecordAimbotInputRequest) Reset() {
	*x = RecordAimbotInputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordAimbotInputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordAimbotInputRequest) ProtoMessage() {}

func (x *RecordAimbotInputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordAimbotInputRequest.ProtoReflect.Descriptor instead.
func (*RecordAimbotInputRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{32}
}

func (x *RecordAimbotInputRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecordAimbotInputRequest) GetTtlMs() uint32 {
	if x != nil {
		return x.TtlMs
	}
	return 0
}

func (x *RecordAimbotInputRequest) GetRotaryTicks() bool {
	if x != nil && x.RotaryTicks != nil {
		return *x.RotaryTicks
	}
	return false
}

func (x *RecordAimbotInputRequest) GetDeepweed() bool {
	if x != nil && x.Deepweed != nil {
		return *x.Deepweed
	}
	return false
}

func (x *RecordAimbotInputRequest) GetLaneHeights() bool {
	if x != nil && x.LaneHeights != nil {
		return *x.LaneHeights
	}
	return false
}

type ConclusionCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  ConclusionType `protobuf:"varint,1,opt,name=type,proto3,enum=weed_tracking.ConclusionType" json:"type,omitempty"`
	Count uint32         `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ConclusionCount) Reset() {
	*x = ConclusionCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConclusionCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConclusionCount) ProtoMessage() {}

func (x *ConclusionCount) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConclusionCount.ProtoReflect.Descriptor instead.
func (*ConclusionCount) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{33}
}

func (x *ConclusionCount) GetType() ConclusionType {
	if x != nil {
		return x.Type
	}
	return ConclusionType_NOT_WEEDING
}

func (x *ConclusionCount) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ConclusionCounter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Counts []*ConclusionCount `protobuf:"bytes,1,rep,name=counts,proto3" json:"counts,omitempty"`
}

func (x *ConclusionCounter) Reset() {
	*x = ConclusionCounter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConclusionCounter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConclusionCounter) ProtoMessage() {}

func (x *ConclusionCounter) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConclusionCounter.ProtoReflect.Descriptor instead.
func (*ConclusionCounter) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{34}
}

func (x *ConclusionCounter) GetCounts() []*ConclusionCount {
	if x != nil {
		return x.Counts
	}
	return nil
}

type BandDefinitions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bands              []*BandDefinition `protobuf:"bytes,1,rep,name=bands,proto3" json:"bands,omitempty"`
	BandingEnabled     bool              `protobuf:"varint,2,opt,name=banding_enabled,json=bandingEnabled,proto3" json:"banding_enabled,omitempty"`
	RowHasBandsDefined bool              `protobuf:"varint,3,opt,name=row_has_bands_defined,json=rowHasBandsDefined,proto3" json:"row_has_bands_defined,omitempty"`
}

func (x *BandDefinitions) Reset() {
	*x = BandDefinitions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BandDefinitions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BandDefinitions) ProtoMessage() {}

func (x *BandDefinitions) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BandDefinitions.ProtoReflect.Descriptor instead.
func (*BandDefinitions) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{35}
}

func (x *BandDefinitions) GetBands() []*BandDefinition {
	if x != nil {
		return x.Bands
	}
	return nil
}

func (x *BandDefinitions) GetBandingEnabled() bool {
	if x != nil {
		return x.BandingEnabled
	}
	return false
}

func (x *BandDefinitions) GetRowHasBandsDefined() bool {
	if x != nil {
		return x.RowHasBandsDefined
	}
	return false
}

type PlantCaptchaStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         PlantCaptchaStatus `protobuf:"varint,1,opt,name=status,proto3,enum=weed_tracking.PlantCaptchaStatus" json:"status,omitempty"`
	TotalImages    int32              `protobuf:"varint,2,opt,name=total_images,json=totalImages,proto3" json:"total_images,omitempty"`
	ImagesTaken    int32              `protobuf:"varint,3,opt,name=images_taken,json=imagesTaken,proto3" json:"images_taken,omitempty"`
	MetadataTaken  int32              `protobuf:"varint,4,opt,name=metadata_taken,json=metadataTaken,proto3" json:"metadata_taken,omitempty"`
	ExemplarCounts map[string]int32   `protobuf:"bytes,5,rep,name=exemplar_counts,json=exemplarCounts,proto3" json:"exemplar_counts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *PlantCaptchaStatusResponse) Reset() {
	*x = PlantCaptchaStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaStatusResponse) ProtoMessage() {}

func (x *PlantCaptchaStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaStatusResponse.ProtoReflect.Descriptor instead.
func (*PlantCaptchaStatusResponse) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{36}
}

func (x *PlantCaptchaStatusResponse) GetStatus() PlantCaptchaStatus {
	if x != nil {
		return x.Status
	}
	return PlantCaptchaStatus_NOT_STARTED
}

func (x *PlantCaptchaStatusResponse) GetTotalImages() int32 {
	if x != nil {
		return x.TotalImages
	}
	return 0
}

func (x *PlantCaptchaStatusResponse) GetImagesTaken() int32 {
	if x != nil {
		return x.ImagesTaken
	}
	return 0
}

func (x *PlantCaptchaStatusResponse) GetMetadataTaken() int32 {
	if x != nil {
		return x.MetadataTaken
	}
	return 0
}

func (x *PlantCaptchaStatusResponse) GetExemplarCounts() map[string]int32 {
	if x != nil {
		return x.ExemplarCounts
	}
	return nil
}

type Embedding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Elements []float32 `protobuf:"fixed32,1,rep,packed,name=elements,proto3" json:"elements,omitempty"`
}

func (x *Embedding) Reset() {
	*x = Embedding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Embedding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Embedding) ProtoMessage() {}

func (x *Embedding) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Embedding.ProtoReflect.Descriptor instead.
func (*Embedding) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{37}
}

func (x *Embedding) GetElements() []float32 {
	if x != nil {
		return x.Elements
	}
	return nil
}

type WeedClasses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Classes map[string]float32 `protobuf:"bytes,1,rep,name=classes,proto3" json:"classes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *WeedClasses) Reset() {
	*x = WeedClasses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeedClasses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeedClasses) ProtoMessage() {}

func (x *WeedClasses) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeedClasses.ProtoReflect.Descriptor instead.
func (*WeedClasses) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{38}
}

func (x *WeedClasses) GetClasses() map[string]float32 {
	if x != nil {
		return x.Classes
	}
	return nil
}

type PlantCaptchaItemMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Confidence                   float32                    `protobuf:"fixed32,1,opt,name=confidence,proto3" json:"confidence,omitempty"`
	XPx                          int32                      `protobuf:"varint,2,opt,name=x_px,json=xPx,proto3" json:"x_px,omitempty"`
	YPx                          int32                      `protobuf:"varint,3,opt,name=y_px,json=yPx,proto3" json:"y_px,omitempty"`
	XMm                          float64                    `protobuf:"fixed64,4,opt,name=x_mm,json=xMm,proto3" json:"x_mm,omitempty"`
	YMm                          float64                    `protobuf:"fixed64,5,opt,name=y_mm,json=yMm,proto3" json:"y_mm,omitempty"`
	ZMm                          float64                    `protobuf:"fixed64,6,opt,name=z_mm,json=zMm,proto3" json:"z_mm,omitempty"`
	SizeMm                       float32                    `protobuf:"fixed32,7,opt,name=size_mm,json=sizeMm,proto3" json:"size_mm,omitempty"`
	Categories                   map[string]float32         `protobuf:"bytes,8,rep,name=categories,proto3" json:"categories,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	Doo                          float32                    `protobuf:"fixed32,9,opt,name=doo,proto3" json:"doo,omitempty"`
	IsWeed                       bool                       `protobuf:"varint,10,opt,name=is_weed,json=isWeed,proto3" json:"is_weed,omitempty"`
	IntersectedWithNonshootable  bool                       `protobuf:"varint,11,opt,name=intersected_with_nonshootable,json=intersectedWithNonshootable,proto3" json:"intersected_with_nonshootable,omitempty"`
	ConfidenceHistory            []float32                  `protobuf:"fixed32,12,rep,packed,name=confidence_history,json=confidenceHistory,proto3" json:"confidence_history,omitempty"`
	IsInBand                     bool                       `protobuf:"varint,13,opt,name=is_in_band,json=isInBand,proto3" json:"is_in_band,omitempty"`
	Id                           string                     `protobuf:"bytes,14,opt,name=id,proto3" json:"id,omitempty"`
	SizePx                       float32                    `protobuf:"fixed32,15,opt,name=size_px,json=sizePx,proto3" json:"size_px,omitempty"`
	ShootTimeMs                  uint32                     `protobuf:"varint,16,opt,name=shoot_time_ms,json=shootTimeMs,proto3" json:"shoot_time_ms,omitempty"`
	WeedConfidenceHistory        []float32                  `protobuf:"fixed32,17,rep,packed,name=weed_confidence_history,json=weedConfidenceHistory,proto3" json:"weed_confidence_history,omitempty"`
	CropConfidenceHistory        []float32                  `protobuf:"fixed32,18,rep,packed,name=crop_confidence_history,json=cropConfidenceHistory,proto3" json:"crop_confidence_history,omitempty"`
	SizeCategoryIndex            int32                      `protobuf:"varint,19,opt,name=size_category_index,json=sizeCategoryIndex,proto3" json:"size_category_index,omitempty"` // as returned from almanac, 0 for small, 1 for medium, 2 for large
	WeedCategories               map[string]float32         `protobuf:"bytes,20,rep,name=weed_categories,json=weedCategories,proto3" json:"weed_categories,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	EmbeddingHistory             []*Embedding               `protobuf:"bytes,21,rep,name=embedding_history,json=embeddingHistory,proto3" json:"embedding_history,omitempty"`
	InitialLabel                 PlantCaptchaUserPrediction `protobuf:"varint,22,opt,name=initial_label,json=initialLabel,proto3,enum=weed_tracking.PlantCaptchaUserPrediction" json:"initial_label,omitempty"`
	PlantConfidenceHistory       []float32                  `protobuf:"fixed32,23,rep,packed,name=plant_confidence_history,json=plantConfidenceHistory,proto3" json:"plant_confidence_history,omitempty"`
	WeedClassesHistory           []*WeedClasses             `protobuf:"bytes,24,rep,name=weed_classes_history,json=weedClassesHistory,proto3" json:"weed_classes_history,omitempty"`
	SizeMmHistory                []float32                  `protobuf:"fixed32,25,rep,packed,name=size_mm_history,json=sizeMmHistory,proto3" json:"size_mm_history,omitempty"`
	Decisions                    *Decisions                 `protobuf:"bytes,26,opt,name=decisions,proto3" json:"decisions,omitempty"`
	NumDetectionsUsedForDecision uint32                     `protobuf:"varint,27,opt,name=num_detections_used_for_decision,json=numDetectionsUsedForDecision,proto3" json:"num_detections_used_for_decision,omitempty"`
	EmbeddingDistances           map[string]float32         `protobuf:"bytes,28,rep,name=embedding_distances,json=embeddingDistances,proto3" json:"embedding_distances,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *PlantCaptchaItemMetadata) Reset() {
	*x = PlantCaptchaItemMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlantCaptchaItemMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlantCaptchaItemMetadata) ProtoMessage() {}

func (x *PlantCaptchaItemMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlantCaptchaItemMetadata.ProtoReflect.Descriptor instead.
func (*PlantCaptchaItemMetadata) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{39}
}

func (x *PlantCaptchaItemMetadata) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetXPx() int32 {
	if x != nil {
		return x.XPx
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetYPx() int32 {
	if x != nil {
		return x.YPx
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetXMm() float64 {
	if x != nil {
		return x.XMm
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetYMm() float64 {
	if x != nil {
		return x.YMm
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetZMm() float64 {
	if x != nil {
		return x.ZMm
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetSizeMm() float32 {
	if x != nil {
		return x.SizeMm
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetCategories() map[string]float32 {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetDoo() float32 {
	if x != nil {
		return x.Doo
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetIsWeed() bool {
	if x != nil {
		return x.IsWeed
	}
	return false
}

func (x *PlantCaptchaItemMetadata) GetIntersectedWithNonshootable() bool {
	if x != nil {
		return x.IntersectedWithNonshootable
	}
	return false
}

func (x *PlantCaptchaItemMetadata) GetConfidenceHistory() []float32 {
	if x != nil {
		return x.ConfidenceHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetIsInBand() bool {
	if x != nil {
		return x.IsInBand
	}
	return false
}

func (x *PlantCaptchaItemMetadata) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PlantCaptchaItemMetadata) GetSizePx() float32 {
	if x != nil {
		return x.SizePx
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetShootTimeMs() uint32 {
	if x != nil {
		return x.ShootTimeMs
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetWeedConfidenceHistory() []float32 {
	if x != nil {
		return x.WeedConfidenceHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetCropConfidenceHistory() []float32 {
	if x != nil {
		return x.CropConfidenceHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetSizeCategoryIndex() int32 {
	if x != nil {
		return x.SizeCategoryIndex
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetWeedCategories() map[string]float32 {
	if x != nil {
		return x.WeedCategories
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetEmbeddingHistory() []*Embedding {
	if x != nil {
		return x.EmbeddingHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetInitialLabel() PlantCaptchaUserPrediction {
	if x != nil {
		return x.InitialLabel
	}
	return PlantCaptchaUserPrediction_WEED
}

func (x *PlantCaptchaItemMetadata) GetPlantConfidenceHistory() []float32 {
	if x != nil {
		return x.PlantConfidenceHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetWeedClassesHistory() []*WeedClasses {
	if x != nil {
		return x.WeedClassesHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetSizeMmHistory() []float32 {
	if x != nil {
		return x.SizeMmHistory
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetDecisions() *Decisions {
	if x != nil {
		return x.Decisions
	}
	return nil
}

func (x *PlantCaptchaItemMetadata) GetNumDetectionsUsedForDecision() uint32 {
	if x != nil {
		return x.NumDetectionsUsedForDecision
	}
	return 0
}

func (x *PlantCaptchaItemMetadata) GetEmbeddingDistances() map[string]float32 {
	if x != nil {
		return x.EmbeddingDistances
	}
	return nil
}

type GetTargetingEnabledRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTargetingEnabledRequest) Reset() {
	*x = GetTargetingEnabledRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetingEnabledRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetingEnabledRequest) ProtoMessage() {}

func (x *GetTargetingEnabledRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetingEnabledRequest.ProtoReflect.Descriptor instead.
func (*GetTargetingEnabledRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{40}
}

type GetTargetingEnabledResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *GetTargetingEnabledResponse) Reset() {
	*x = GetTargetingEnabledResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetingEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetingEnabledResponse) ProtoMessage() {}

func (x *GetTargetingEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetingEnabledResponse.ProtoReflect.Descriptor instead.
func (*GetTargetingEnabledResponse) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{41}
}

func (x *GetTargetingEnabledResponse) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type GetBootedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetBootedRequest) Reset() {
	*x = GetBootedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBootedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBootedRequest) ProtoMessage() {}

func (x *GetBootedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBootedRequest.ProtoReflect.Descriptor instead.
func (*GetBootedRequest) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{42}
}

type GetBootedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Booted bool `protobuf:"varint,1,opt,name=booted,proto3" json:"booted,omitempty"`
}

func (x *GetBootedResponse) Reset() {
	*x = GetBootedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_weed_tracking_weed_tracking_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBootedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBootedResponse) ProtoMessage() {}

func (x *GetBootedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_weed_tracking_weed_tracking_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBootedResponse.ProtoReflect.Descriptor instead.
func (*GetBootedResponse) Descriptor() ([]byte, []int) {
	return file_weed_tracking_weed_tracking_proto_rawDescGZIP(), []int{43}
}

func (x *GetBootedResponse) GetBooted() bool {
	if x != nil {
		return x.Booted
	}
	return false
}

var File_weed_tracking_weed_tracking_proto protoreflect.FileDescriptor

var file_weed_tracking_weed_tracking_proto_rawDesc = []byte{
	0x0a, 0x21, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xe7, 0x02, 0x0a, 0x0a,
	0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x12, 0x11, 0x0a, 0x04, 0x78, 0x5f,
	0x6d, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x78, 0x4d, 0x6d, 0x12, 0x11, 0x0a,
	0x04, 0x79, 0x5f, 0x6d, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x79, 0x4d, 0x6d,
	0x12, 0x11, 0x0a, 0x04, 0x7a, 0x5f, 0x6d, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x7a, 0x4d, 0x6d, 0x12, 0x42, 0x0a, 0x1d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6e, 0x6f, 0x6e, 0x73, 0x68, 0x6f, 0x6f, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x73, 0x65, 0x63, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x4e, 0x6f, 0x6e, 0x73, 0x68,
	0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x6e, 0x6f, 0x6e, 0x73, 0x68,
	0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6e, 0x6f, 0x6e, 0x73, 0x68,
	0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x3e, 0x0a, 0x1b, 0x64, 0x65, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x64, 0x65, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x3a, 0x02, 0x18, 0x01, 0x22, 0xc6, 0x01, 0x0a, 0x06, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x6e, 0x65, 0x78, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x6f, 0x73, 0x5f, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x73, 0x59, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x6f, 0x73, 0x5f, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x73, 0x59, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x4c,
	0x0a, 0x06, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x70,
	0x6f, 0x73, 0x5f, 0x6d, 0x6d, 0x5f, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x6d,
	0x61, 0x78, 0x50, 0x6f, 0x73, 0x4d, 0x6d, 0x59, 0x3a, 0x02, 0x18, 0x01, 0x22, 0xb8, 0x01, 0x0a,
	0x13, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x2f, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x07, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6a,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x52, 0x06, 0x62, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x65, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x13, 0x0a, 0x05, 0x6d, 0x61, 0x78,
	0x5f, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x61, 0x78, 0x58, 0x22, 0xa9,
	0x02, 0x0a, 0x09, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x63, 0x6c, 0x7a, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6c, 0x7a, 0x12, 0x17,
	0x0a, 0x07, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x5f, 0x6f,
	0x66, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6f, 0x75,
	0x74, 0x4f, 0x66, 0x42, 0x61, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x09, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61,
	0x6e, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a,
	0x70, 0x6c, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x69, 0x0a, 0x0a, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x38, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4d, 0x73, 0x22, 0x3e, 0x0a, 0x04, 0x42, 0x61, 0x6e, 0x64, 0x12, 0x1c, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x58, 0x50, 0x78, 0x12, 0x18, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x65,
	0x6e, 0x64, 0x58, 0x50, 0x78, 0x22, 0x8c, 0x01, 0x0a, 0x05, 0x42, 0x61, 0x6e, 0x64, 0x73, 0x12,
	0x27, 0x0a, 0x04, 0x62, 0x61, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61,
	0x6e, 0x64, 0x52, 0x04, 0x62, 0x61, 0x6e, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x6f, 0x77, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x62, 0x61, 0x6e,
	0x64, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x12, 0x72, 0x6f, 0x77, 0x48, 0x61, 0x73, 0x42, 0x61, 0x6e, 0x64, 0x73, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x65, 0x64, 0x22, 0x7e, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x62, 0x61, 0x6e, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x73, 0x52, 0x05, 0x62,
	0x61, 0x6e, 0x64, 0x73, 0x22, 0x1e, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x5b, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d,
	0x73, 0x22, 0xc9, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6a,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x13, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x61, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5e, 0x0a,
	0x1d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x1b, 0x0a,
	0x0b, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0c, 0x0a, 0x01,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0x19, 0x0a, 0x09, 0x50, 0x6f,
	0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x01, 0x78, 0x22, 0xf2, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x69, 0x6c, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x50, 0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x75, 0x73, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x29, 0x0a, 0x10, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x46, 0x0a, 0x0f, 0x50, 0x65,
	0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x0a, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x41, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x22, 0x23, 0x0a, 0x05,
	0x50, 0x6f, 0x73, 0x32, 0x44, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x01,
	0x79, 0x22, 0x92, 0x01, 0x0a, 0x07, 0x4b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x78, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x08,
	0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50,
	0x6f, 0x73, 0x32, 0x44, 0x52, 0x07, 0x74, 0x6f, 0x70, 0x4c, 0x65, 0x66, 0x74, 0x12, 0x37, 0x0a,
	0x0c, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x73, 0x32, 0x44, 0x52, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f,
	0x6d, 0x52, 0x69, 0x67, 0x68, 0x74, 0x22, 0xdf, 0x01, 0x0a, 0x0a, 0x54, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x07, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x65,
	0x64, 0x57, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73,
	0x65, 0x64, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x65,
	0x64, 0x42, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0xe3, 0x01, 0x0a, 0x09, 0x44, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x77, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x65, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x23, 0x0a, 0x0d,
	0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x57, 0x65, 0x65,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x72,
	0x6f, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6b,
	0x65, 0x65, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x72, 0x6f, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x72, 0x6f, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x22, 0xbd,
	0x10, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x4b, 0x69, 0x6c, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x6b, 0x69, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1b, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x12, 0x11,
	0x0a, 0x04, 0x78, 0x5f, 0x6d, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x78, 0x4d,
	0x6d, 0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x6d, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x79, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x7a, 0x5f, 0x6d, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x03, 0x7a, 0x4d, 0x6d, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x48, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x5f, 0x6d, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x72, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x4d, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x35, 0x0a, 0x17, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x14, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x73, 0x68, 0x6f,
	0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x6d,
	0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x4d, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x64, 0x46,
	0x6f, 0x72, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x10, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0f, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x18,
	0x11, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x4c,
	0x61, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x66, 0x5f,
	0x62, 0x61, 0x6e, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x4f,
	0x66, 0x42, 0x61, 0x6e, 0x64, 0x12, 0x42, 0x0a, 0x1d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6e, 0x6f, 0x6e, 0x73, 0x68, 0x6f,
	0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74, 0x68, 0x4e, 0x6f, 0x6e,
	0x73, 0x68, 0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x64, 0x0a, 0x11, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x14,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x64,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x43, 0x0a, 0x0e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x70,
	0x6f, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x50, 0x6f, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x64, 0x6f, 0x6f, 0x18, 0x19, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x64, 0x6f,
	0x6f, 0x12, 0x3e, 0x0a, 0x1b, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x50, 0x65, 0x72, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x73, 0x69, 0x7a, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x2f, 0x0a, 0x13, 0x73, 0x70, 0x65, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12,
	0x73, 0x70, 0x65, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6a, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x79, 0x54, 0x72, 0x61, 0x6a, 0x12, 0x39,
	0x0a, 0x0a, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x52, 0x0a, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x09, 0x64, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x09, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x63, 0x72, 0x6f, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x77, 0x65, 0x65, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x46, 0x0a, 0x20, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1c, 0x6e, 0x75, 0x6d, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x73, 0x65, 0x64, 0x46, 0x6f, 0x72,
	0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0e, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x6a, 0x0a, 0x13, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x2e,
	0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x20, 0x73,
	0x70, 0x65, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x6f, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1c, 0x73, 0x70, 0x65, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x53, 0x68, 0x6f, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x4d, 0x73, 0x12, 0x4c, 0x0a, 0x11, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x10, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x43, 0x0a, 0x15, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x45, 0x0a, 0x17, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0f, 0x0a,
	0x0d, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x8e,
	0x01, 0x0a, 0x10, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x63, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12,
	0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x58, 0x50, 0x78, 0x12,
	0x1e, 0x0a, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x79, 0x5f, 0x70, 0x78, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x59, 0x50, 0x78, 0x22,
	0x58, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x4d, 0x6d, 0x12, 0x19,
	0x0a, 0x08, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x6d, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x07, 0x77, 0x69, 0x64, 0x74, 0x68, 0x4d, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd5, 0x01, 0x0a, 0x14, 0x43, 0x4c,
	0x44, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x78, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0c, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x58, 0x12, 0x24, 0x0a, 0x0e, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x79, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x59, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x61, 0x73, 0x5f, 0x78, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x61, 0x73, 0x58, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x61, 0x73, 0x5f, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x02, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x61, 0x73, 0x59, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6e, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x02, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65,
	0x73, 0x22, 0xf2, 0x02, 0x0a, 0x13, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x73, 0x12, 0x45, 0x0a, 0x0c,
	0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x0a, 0x6b, 0x69, 0x6c, 0x6c,
	0x5f, 0x62, 0x6f, 0x78, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x4b, 0x69, 0x6c,
	0x6c, 0x42, 0x6f, 0x78, 0x52, 0x09, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x78, 0x65, 0x73, 0x12,
	0x66, 0x0a, 0x1a, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72,
	0x69, 0x74, 0x68, 0x6d, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x4c, 0x44, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x48, 0x00, 0x52, 0x18, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x62, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x5f, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x74, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x74, 0x74, 0x6c, 0x53, 0x65, 0x63, 0x12, 0x2d, 0x0a, 0x13,
	0x63, 0x72, 0x6f, 0x70, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x73, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x63, 0x72, 0x6f, 0x70, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x12, 0x2d, 0x0a, 0x13, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73,
	0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x77, 0x65, 0x65, 0x64, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x73, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x22, 0x54, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x5e, 0x0a, 0x29, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x43,
	0x72, 0x6f, 0x70, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x74, 0x6c,
	0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x74, 0x6c, 0x4d, 0x73,
	0x22, 0xe5, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41, 0x69, 0x6d, 0x62, 0x6f,
	0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x74, 0x6c, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x74, 0x6c, 0x4d, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x72, 0x6f, 0x74, 0x61,
	0x72, 0x79, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x0b, 0x72, 0x6f, 0x74, 0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x01, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x65, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x72, 0x6f,
	0x74, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64,
	0x65, 0x65, 0x70, 0x77, 0x65, 0x65, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x61, 0x6e, 0x65,
	0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x73, 0x22, 0x5a, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4b, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x06, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x22, 0xa2, 0x01, 0x0a, 0x0f, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x05, 0x62, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x6f, 0x77, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x62,
	0x61, 0x6e, 0x64, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x72, 0x6f, 0x77, 0x48, 0x61, 0x73, 0x42, 0x61, 0x6e, 0x64, 0x73, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x22, 0xef, 0x02, 0x0a, 0x1a, 0x50, 0x6c, 0x61, 0x6e, 0x74,
	0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x74, 0x61,
	0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x54, 0x61, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x74, 0x61, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x54, 0x61, 0x6b, 0x65, 0x6e, 0x12, 0x66, 0x0a,
	0x0f, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74,
	0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x45, 0x78, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a, 0x41, 0x0a, 0x13, 0x45, 0x78, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x27, 0x0a, 0x09, 0x45, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x22, 0x8c, 0x01, 0x0a, 0x0b, 0x57, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65,
	0x73, 0x12, 0x41, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x2e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x65, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x9c, 0x0c, 0x0a, 0x18, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x11, 0x0a,
	0x04, 0x78, 0x5f, 0x70, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x78, 0x50, 0x78,
	0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x79, 0x50, 0x78, 0x12, 0x11, 0x0a, 0x04, 0x78, 0x5f, 0x6d, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x78, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x79, 0x5f, 0x6d, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x79, 0x4d, 0x6d, 0x12, 0x11, 0x0a, 0x04, 0x7a, 0x5f, 0x6d,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x7a, 0x4d, 0x6d, 0x12, 0x17, 0x0a, 0x07,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x73,
	0x69, 0x7a, 0x65, 0x4d, 0x6d, 0x12, 0x57, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x6f, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x64, 0x6f, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x69, 0x73, 0x57, 0x65, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x1d, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6e, 0x6f,
	0x6e, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73, 0x65, 0x63, 0x74, 0x65, 0x64, 0x57, 0x69, 0x74,
	0x68, 0x4e, 0x6f, 0x6e, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x02, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x49, 0x6e, 0x42, 0x61, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x69,
	0x7a, 0x65, 0x5f, 0x70, 0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x73, 0x69, 0x7a,
	0x65, 0x50, 0x78, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x6d, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x68, 0x6f, 0x6f,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x18, 0x11, 0x20, 0x03, 0x28, 0x02, 0x52, 0x15, 0x77, 0x65, 0x65, 0x64, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x36, 0x0a, 0x17, 0x63, 0x72, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x12, 0x20, 0x03, 0x28, 0x02,
	0x52, 0x15, 0x63, 0x72, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x69, 0x7a, 0x65, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x73, 0x69, 0x7a, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x64, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74, 0x65,
	0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x57, 0x65, 0x65, 0x64, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x77,
	0x65, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x45, 0x0a,
	0x11, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x10, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x4e, 0x0a, 0x0d, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e,
	0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0x38, 0x0a, 0x18, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x18, 0x17, 0x20, 0x03, 0x28, 0x02, 0x52, 0x16, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x4c,
	0x0a, 0x14, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x5f, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x57, 0x65, 0x65,
	0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x52, 0x12, 0x77, 0x65, 0x65, 0x64, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x0f,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x6d, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x19, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x73, 0x69, 0x7a, 0x65, 0x4d, 0x6d, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x36, 0x0a, 0x09, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x09, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x20,
	0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x75,
	0x73, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1c, 0x6e, 0x75, 0x6d, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x55, 0x73, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x70, 0x0a, 0x13, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x74,
	0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x12, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x57, 0x65, 0x65, 0x64, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x45, 0x0a, 0x17, 0x45, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x1c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x37, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2b, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x62, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x62, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x2a, 0xc4, 0x02, 0x0a, 0x12, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x08,
	0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x4e, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x48, 0x4f, 0x4f, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x43, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x57, 0x45, 0x45, 0x44, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x57,
	0x45, 0x45, 0x44, 0x5f, 0x53, 0x48, 0x4f, 0x54, 0x5f, 0x42, 0x59, 0x5f, 0x41, 0x4e, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x57,
	0x45, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x10,
	0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10,
	0x06, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x4e, 0x45, 0x47, 0x41, 0x54,
	0x49, 0x56, 0x45, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x45,
	0x43, 0x54, 0x45, 0x44, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x4e, 0x4f, 0x4e, 0x53, 0x48, 0x4f,
	0x4f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x58, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x10,
	0x09, 0x12, 0x2b, 0x0a, 0x27, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f,
	0x4f, 0x56, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x55, 0x4e, 0x49, 0x54, 0x49,
	0x45, 0x53, 0x5f, 0x42, 0x45, 0x4c, 0x4f, 0x57, 0x5f, 0x4d, 0x49, 0x4e, 0x10, 0x0a, 0x2a, 0xb0,
	0x01, 0x0a, 0x0a, 0x4b, 0x69, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x13, 0x0a,
	0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x48, 0x4f, 0x54,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x45, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x53,
	0x48, 0x4f, 0x54, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x50, 0x32, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x04, 0x12,
	0x10, 0x0a, 0x0c, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x32, 0x50, 0x5f,
	0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x06, 0x2a, 0x39, 0x0a, 0x0f, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e, 0x49, 0x51, 0x55, 0x45, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x6e, 0x0a, 0x0d,
	0x54, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a,
	0x0e, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x54, 0x10,
	0x00, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41,
	0x52, 0x4b, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x5f,
	0x4b, 0x45, 0x50, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49,
	0x4e, 0x47, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xf4, 0x01, 0x0a,
	0x0f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x17, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49,
	0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x52, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x44, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x53, 0x45, 0x43, 0x54, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x4f, 0x54, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x53, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x44, 0x4f,
	0x4f, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x54,
	0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x44, 0x5f, 0x46, 0x52,
	0x4f, 0x4d, 0x5f, 0x41, 0x4c, 0x4d, 0x41, 0x4e, 0x41, 0x43, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12,
	0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x42, 0x41,
	0x4e, 0x44, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x41,
	0x56, 0x4f, 0x49, 0x44, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x41, 0x4c, 0x4d, 0x41, 0x4e, 0x41,
	0x43, 0x10, 0x07, 0x2a, 0x68, 0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55,
	0x4e, 0x44, 0x45, 0x43, 0x49, 0x44, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x48, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x04, 0x2a, 0x69, 0x0a,
	0x0f, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45,
	0x43, 0x4f, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x93, 0x02, 0x0a, 0x0e, 0x43, 0x6f, 0x6e,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x4e,
	0x4f, 0x54, 0x5f, 0x57, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b,
	0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x21, 0x0a,
	0x1d, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x45, 0x43, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x4f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02,
	0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x41, 0x4e,
	0x54, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x48, 0x4f, 0x54, 0x10,
	0x05, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x53,
	0x48, 0x4f, 0x54, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x07, 0x12,
	0x11, 0x0a, 0x0d, 0x50, 0x32, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x08, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x09, 0x12, 0x0b, 0x0a,
	0x07, 0x46, 0x4c, 0x49, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41,
	0x52, 0x4b, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0x0b, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45,
	0x54, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x32, 0x50, 0x5f, 0x4d, 0x49, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0d, 0x2a, 0x7b,
	0x0a, 0x12, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52,
	0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41,
	0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41,
	0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f,
	0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x7f, 0x0a, 0x1a, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x45, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x52, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x10,
	0x04, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x4f, 0x4c, 0x55, 0x4e, 0x54, 0x45, 0x45, 0x52, 0x10, 0x05,
	0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x06,
	0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x42, 0x52, 0x49, 0x53, 0x10, 0x07, 0x32, 0x88, 0x0c, 0x0a,
	0x13, 0x57, 0x65, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x77, 0x65, 0x65,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x72, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61,
	0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6a, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x61,
	0x6e, 0x64, 0x73, 0x12, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x50, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x6f, 0x6f, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x52, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x6a, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x14, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x22, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x74, 0x0a, 0x22, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x38, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x6e, 0x65, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5a, 0x0a, 0x19, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x27, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x60, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x29,
	0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x19, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x5b, 0x0a, 0x1a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73,
	0x12, 0x27, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41, 0x69, 0x6d, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x4e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x20, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f,
	0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12,
	0x40, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x14, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x1e, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x3f, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x77,
	0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x58, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x29, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x1b,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x14, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x40, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x14, 0x2e,
	0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6c, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x29, 0x2e, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x77, 0x65,
	0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x61,
	0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x77, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_weed_tracking_weed_tracking_proto_rawDescOnce sync.Once
	file_weed_tracking_weed_tracking_proto_rawDescData = file_weed_tracking_weed_tracking_proto_rawDesc
)

func file_weed_tracking_weed_tracking_proto_rawDescGZIP() []byte {
	file_weed_tracking_weed_tracking_proto_rawDescOnce.Do(func() {
		file_weed_tracking_weed_tracking_proto_rawDescData = protoimpl.X.CompressGZIP(file_weed_tracking_weed_tracking_proto_rawDescData)
	})
	return file_weed_tracking_weed_tracking_proto_rawDescData
}

var file_weed_tracking_weed_tracking_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_weed_tracking_weed_tracking_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_weed_tracking_weed_tracking_proto_goTypes = []interface{}{
	(InvalidScoreReason)(0),                           // 0: weed_tracking.InvalidScoreReason
	(KillStatus)(0),                                   // 1: weed_tracking.KillStatus
	(DuplicateStatus)(0),                              // 2: weed_tracking.DuplicateStatus
	(ThinningState)(0),                                // 3: weed_tracking.ThinningState
	(TargetableState)(0),                              // 4: weed_tracking.TargetableState
	(Classification)(0),                               // 5: weed_tracking.Classification
	(RecordingStatus)(0),                              // 6: weed_tracking.RecordingStatus
	(ConclusionType)(0),                               // 7: weed_tracking.ConclusionType
	(PlantCaptchaStatus)(0),                           // 8: weed_tracking.PlantCaptchaStatus
	(PlantCaptchaUserPrediction)(0),                   // 9: weed_tracking.PlantCaptchaUserPrediction
	(*Empty)(nil),                                     // 10: weed_tracking.Empty
	(*Trajectory)(nil),                                // 11: weed_tracking.Trajectory
	(*Target)(nil),                                    // 12: weed_tracking.Target
	(*Bounds)(nil),                                    // 13: weed_tracking.Bounds
	(*TrackingStatusReply)(nil),                       // 14: weed_tracking.TrackingStatusReply
	(*GetDetectionsRequest)(nil),                      // 15: weed_tracking.GetDetectionsRequest
	(*Detection)(nil),                                 // 16: weed_tracking.Detection
	(*Detections)(nil),                                // 17: weed_tracking.Detections
	(*Band)(nil),                                      // 18: weed_tracking.Band
	(*Bands)(nil),                                     // 19: weed_tracking.Bands
	(*GetDetectionsResponse)(nil),                     // 20: weed_tracking.GetDetectionsResponse
	(*GetTrajectoryMetadataRequest)(nil),              // 21: weed_tracking.GetTrajectoryMetadataRequest
	(*TrackedItemMetadata)(nil),                       // 22: weed_tracking.TrackedItemMetadata
	(*TrajectoryMetadata)(nil),                        // 23: weed_tracking.TrajectoryMetadata
	(*GetTrajectoryMetadataResponse)(nil),             // 24: weed_tracking.GetTrajectoryMetadataResponse
	(*PingRequest)(nil),                               // 25: weed_tracking.PingRequest
	(*PongReply)(nil),                                 // 26: weed_tracking.PongReply
	(*TrajectoryScore)(nil),                           // 27: weed_tracking.TrajectoryScore
	(*PerScannerScore)(nil),                           // 28: weed_tracking.PerScannerScore
	(*ScoreState)(nil),                                // 29: weed_tracking.ScoreState
	(*Pos2D)(nil),                                     // 30: weed_tracking.Pos2D
	(*KillBox)(nil),                                   // 31: weed_tracking.KillBox
	(*Thresholds)(nil),                                // 32: weed_tracking.Thresholds
	(*Decisions)(nil),                                 // 33: weed_tracking.Decisions
	(*TrajectorySnapshot)(nil),                        // 34: weed_tracking.TrajectorySnapshot
	(*SnapshotMetadata)(nil),                          // 35: weed_tracking.SnapshotMetadata
	(*BandDefinition)(nil),                            // 36: weed_tracking.BandDefinition
	(*CLDAlgorithmSnapshot)(nil),                      // 37: weed_tracking.CLDAlgorithmSnapshot
	(*DiagnosticsSnapshot)(nil),                       // 38: weed_tracking.DiagnosticsSnapshot
	(*RecordDiagnosticsRequest)(nil),                  // 39: weed_tracking.RecordDiagnosticsRequest
	(*GetRecordingStatusResponse)(nil),                // 40: weed_tracking.GetRecordingStatusResponse
	(*StartSavingCropLineDetectionReplayRequest)(nil), // 41: weed_tracking.StartSavingCropLineDetectionReplayRequest
	(*RecordAimbotInputRequest)(nil),                  // 42: weed_tracking.RecordAimbotInputRequest
	(*ConclusionCount)(nil),                           // 43: weed_tracking.ConclusionCount
	(*ConclusionCounter)(nil),                         // 44: weed_tracking.ConclusionCounter
	(*BandDefinitions)(nil),                           // 45: weed_tracking.BandDefinitions
	(*PlantCaptchaStatusResponse)(nil),                // 46: weed_tracking.PlantCaptchaStatusResponse
	(*Embedding)(nil),                                 // 47: weed_tracking.Embedding
	(*WeedClasses)(nil),                               // 48: weed_tracking.WeedClasses
	(*PlantCaptchaItemMetadata)(nil),                  // 49: weed_tracking.PlantCaptchaItemMetadata
	(*GetTargetingEnabledRequest)(nil),                // 50: weed_tracking.GetTargetingEnabledRequest
	(*GetTargetingEnabledResponse)(nil),               // 51: weed_tracking.GetTargetingEnabledResponse
	(*GetBootedRequest)(nil),                          // 52: weed_tracking.GetBootedRequest
	(*GetBootedResponse)(nil),                         // 53: weed_tracking.GetBootedResponse
	nil,                                               // 54: weed_tracking.TrajectorySnapshot.DetectionClassesEntry
	nil,                                               // 55: weed_tracking.TrajectorySnapshot.EmbeddingDistancesEntry
	nil,                                               // 56: weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry
	nil,                                               // 57: weed_tracking.WeedClasses.ClassesEntry
	nil,                                               // 58: weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry
	nil,                                               // 59: weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry
	nil,                                               // 60: weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry
}
var file_weed_tracking_weed_tracking_proto_depIdxs = []int32{
	12, // 0: weed_tracking.TrackingStatusReply.targets:type_name -> weed_tracking.Target
	11, // 1: weed_tracking.TrackingStatusReply.trajectories:type_name -> weed_tracking.Trajectory
	13, // 2: weed_tracking.TrackingStatusReply.bounds:type_name -> weed_tracking.Bounds
	16, // 3: weed_tracking.Detections.detections:type_name -> weed_tracking.Detection
	18, // 4: weed_tracking.Bands.band:type_name -> weed_tracking.Band
	17, // 5: weed_tracking.GetDetectionsResponse.detections:type_name -> weed_tracking.Detections
	19, // 6: weed_tracking.GetDetectionsResponse.bands:type_name -> weed_tracking.Bands
	22, // 7: weed_tracking.TrajectoryMetadata.tracked_item_metadata:type_name -> weed_tracking.TrackedItemMetadata
	23, // 8: weed_tracking.GetTrajectoryMetadataResponse.metadata:type_name -> weed_tracking.TrajectoryMetadata
	4,  // 9: weed_tracking.ScoreState.target_state:type_name -> weed_tracking.TargetableState
	28, // 10: weed_tracking.ScoreState.scores:type_name -> weed_tracking.PerScannerScore
	30, // 11: weed_tracking.KillBox.top_left:type_name -> weed_tracking.Pos2D
	30, // 12: weed_tracking.KillBox.bottom_right:type_name -> weed_tracking.Pos2D
	1,  // 13: weed_tracking.TrajectorySnapshot.kill_status:type_name -> weed_tracking.KillStatus
	27, // 14: weed_tracking.TrajectorySnapshot.score:type_name -> weed_tracking.TrajectoryScore
	0,  // 15: weed_tracking.TrajectorySnapshot.invalid_score:type_name -> weed_tracking.InvalidScoreReason
	2,  // 16: weed_tracking.TrajectorySnapshot.duplicate_status:type_name -> weed_tracking.DuplicateStatus
	54, // 17: weed_tracking.TrajectorySnapshot.detection_classes:type_name -> weed_tracking.TrajectorySnapshot.DetectionClassesEntry
	3,  // 18: weed_tracking.TrajectorySnapshot.thinning_state:type_name -> weed_tracking.ThinningState
	29, // 19: weed_tracking.TrajectorySnapshot.score_state:type_name -> weed_tracking.ScoreState
	32, // 20: weed_tracking.TrajectorySnapshot.thresholds:type_name -> weed_tracking.Thresholds
	33, // 21: weed_tracking.TrajectorySnapshot.decisions:type_name -> weed_tracking.Decisions
	5,  // 22: weed_tracking.TrajectorySnapshot.classification:type_name -> weed_tracking.Classification
	55, // 23: weed_tracking.TrajectorySnapshot.embedding_distances:type_name -> weed_tracking.TrajectorySnapshot.EmbeddingDistancesEntry
	35, // 24: weed_tracking.TrajectorySnapshot.snapshot_metadata:type_name -> weed_tracking.SnapshotMetadata
	34, // 25: weed_tracking.DiagnosticsSnapshot.trajectories:type_name -> weed_tracking.TrajectorySnapshot
	36, // 26: weed_tracking.DiagnosticsSnapshot.bands:type_name -> weed_tracking.BandDefinition
	31, // 27: weed_tracking.DiagnosticsSnapshot.kill_boxes:type_name -> weed_tracking.KillBox
	37, // 28: weed_tracking.DiagnosticsSnapshot.banding_algorithm_snapshot:type_name -> weed_tracking.CLDAlgorithmSnapshot
	6,  // 29: weed_tracking.GetRecordingStatusResponse.status:type_name -> weed_tracking.RecordingStatus
	7,  // 30: weed_tracking.ConclusionCount.type:type_name -> weed_tracking.ConclusionType
	43, // 31: weed_tracking.ConclusionCounter.counts:type_name -> weed_tracking.ConclusionCount
	36, // 32: weed_tracking.BandDefinitions.bands:type_name -> weed_tracking.BandDefinition
	8,  // 33: weed_tracking.PlantCaptchaStatusResponse.status:type_name -> weed_tracking.PlantCaptchaStatus
	56, // 34: weed_tracking.PlantCaptchaStatusResponse.exemplar_counts:type_name -> weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry
	57, // 35: weed_tracking.WeedClasses.classes:type_name -> weed_tracking.WeedClasses.ClassesEntry
	58, // 36: weed_tracking.PlantCaptchaItemMetadata.categories:type_name -> weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry
	59, // 37: weed_tracking.PlantCaptchaItemMetadata.weed_categories:type_name -> weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry
	47, // 38: weed_tracking.PlantCaptchaItemMetadata.embedding_history:type_name -> weed_tracking.Embedding
	9,  // 39: weed_tracking.PlantCaptchaItemMetadata.initial_label:type_name -> weed_tracking.PlantCaptchaUserPrediction
	48, // 40: weed_tracking.PlantCaptchaItemMetadata.weed_classes_history:type_name -> weed_tracking.WeedClasses
	33, // 41: weed_tracking.PlantCaptchaItemMetadata.decisions:type_name -> weed_tracking.Decisions
	60, // 42: weed_tracking.PlantCaptchaItemMetadata.embedding_distances:type_name -> weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry
	25, // 43: weed_tracking.WeedTrackingService.Ping:input_type -> weed_tracking.PingRequest
	15, // 44: weed_tracking.WeedTrackingService.GetDetections:input_type -> weed_tracking.GetDetectionsRequest
	21, // 45: weed_tracking.WeedTrackingService.GetTrajectoryMetadata:input_type -> weed_tracking.GetTrajectoryMetadataRequest
	10, // 46: weed_tracking.WeedTrackingService.UpdateBands:input_type -> weed_tracking.Empty
	52, // 47: weed_tracking.WeedTrackingService.GetBooted:input_type -> weed_tracking.GetBootedRequest
	10, // 48: weed_tracking.WeedTrackingService.GetCurrentTrajectories:input_type -> weed_tracking.Empty
	41, // 49: weed_tracking.WeedTrackingService.StartSavingCropLineDetectionReplay:input_type -> weed_tracking.StartSavingCropLineDetectionReplayRequest
	39, // 50: weed_tracking.WeedTrackingService.StartRecordingDiagnostics:input_type -> weed_tracking.RecordDiagnosticsRequest
	10, // 51: weed_tracking.WeedTrackingService.GetDiagnosticsRecordingStatus:input_type -> weed_tracking.Empty
	10, // 52: weed_tracking.WeedTrackingService.RemoveRecordingsDirectory:input_type -> weed_tracking.Empty
	42, // 53: weed_tracking.WeedTrackingService.StartRecordingAimbotInputs:input_type -> weed_tracking.RecordAimbotInputRequest
	10, // 54: weed_tracking.WeedTrackingService.GetConclusionCounter:input_type -> weed_tracking.Empty
	10, // 55: weed_tracking.WeedTrackingService.GetBands:input_type -> weed_tracking.Empty
	10, // 56: weed_tracking.WeedTrackingService.StartPlantCaptcha:input_type -> weed_tracking.Empty
	10, // 57: weed_tracking.WeedTrackingService.GetPlantCaptchaStatus:input_type -> weed_tracking.Empty
	10, // 58: weed_tracking.WeedTrackingService.RemovePlantCaptchaDirectory:input_type -> weed_tracking.Empty
	10, // 59: weed_tracking.WeedTrackingService.CancelPlantCaptcha:input_type -> weed_tracking.Empty
	50, // 60: weed_tracking.WeedTrackingService.GetTargetingEnabled:input_type -> weed_tracking.GetTargetingEnabledRequest
	26, // 61: weed_tracking.WeedTrackingService.Ping:output_type -> weed_tracking.PongReply
	20, // 62: weed_tracking.WeedTrackingService.GetDetections:output_type -> weed_tracking.GetDetectionsResponse
	24, // 63: weed_tracking.WeedTrackingService.GetTrajectoryMetadata:output_type -> weed_tracking.GetTrajectoryMetadataResponse
	10, // 64: weed_tracking.WeedTrackingService.UpdateBands:output_type -> weed_tracking.Empty
	53, // 65: weed_tracking.WeedTrackingService.GetBooted:output_type -> weed_tracking.GetBootedResponse
	38, // 66: weed_tracking.WeedTrackingService.GetCurrentTrajectories:output_type -> weed_tracking.DiagnosticsSnapshot
	10, // 67: weed_tracking.WeedTrackingService.StartSavingCropLineDetectionReplay:output_type -> weed_tracking.Empty
	10, // 68: weed_tracking.WeedTrackingService.StartRecordingDiagnostics:output_type -> weed_tracking.Empty
	40, // 69: weed_tracking.WeedTrackingService.GetDiagnosticsRecordingStatus:output_type -> weed_tracking.GetRecordingStatusResponse
	10, // 70: weed_tracking.WeedTrackingService.RemoveRecordingsDirectory:output_type -> weed_tracking.Empty
	10, // 71: weed_tracking.WeedTrackingService.StartRecordingAimbotInputs:output_type -> weed_tracking.Empty
	44, // 72: weed_tracking.WeedTrackingService.GetConclusionCounter:output_type -> weed_tracking.ConclusionCounter
	45, // 73: weed_tracking.WeedTrackingService.GetBands:output_type -> weed_tracking.BandDefinitions
	10, // 74: weed_tracking.WeedTrackingService.StartPlantCaptcha:output_type -> weed_tracking.Empty
	46, // 75: weed_tracking.WeedTrackingService.GetPlantCaptchaStatus:output_type -> weed_tracking.PlantCaptchaStatusResponse
	10, // 76: weed_tracking.WeedTrackingService.RemovePlantCaptchaDirectory:output_type -> weed_tracking.Empty
	10, // 77: weed_tracking.WeedTrackingService.CancelPlantCaptcha:output_type -> weed_tracking.Empty
	51, // 78: weed_tracking.WeedTrackingService.GetTargetingEnabled:output_type -> weed_tracking.GetTargetingEnabledResponse
	61, // [61:79] is the sub-list for method output_type
	43, // [43:61] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_weed_tracking_weed_tracking_proto_init() }
func file_weed_tracking_weed_tracking_proto_init() {
	if File_weed_tracking_weed_tracking_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_weed_tracking_weed_tracking_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Trajectory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Target); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bounds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackingStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetectionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Detections); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Band); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bands); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetectionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrajectoryMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackedItemMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrajectoryMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTrajectoryMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PongReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrajectoryScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PerScannerScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScoreState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pos2D); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KillBox); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Thresholds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Decisions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrajectorySnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BandDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CLDAlgorithmSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiagnosticsSnapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordDiagnosticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordingStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartSavingCropLineDetectionReplayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordAimbotInputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConclusionCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConclusionCounter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BandDefinitions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Embedding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeedClasses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlantCaptchaItemMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetingEnabledRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetingEnabledResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBootedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_weed_tracking_weed_tracking_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBootedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_weed_tracking_weed_tracking_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*TrajectorySnapshot_Score)(nil),
		(*TrajectorySnapshot_InvalidScore)(nil),
	}
	file_weed_tracking_weed_tracking_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_weed_tracking_weed_tracking_proto_msgTypes[32].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_weed_tracking_weed_tracking_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_weed_tracking_weed_tracking_proto_goTypes,
		DependencyIndexes: file_weed_tracking_weed_tracking_proto_depIdxs,
		EnumInfos:         file_weed_tracking_weed_tracking_proto_enumTypes,
		MessageInfos:      file_weed_tracking_weed_tracking_proto_msgTypes,
	}.Build()
	File_weed_tracking_weed_tracking_proto = out.File
	file_weed_tracking_weed_tracking_proto_rawDesc = nil
	file_weed_tracking_weed_tracking_proto_goTypes = nil
	file_weed_tracking_weed_tracking_proto_depIdxs = nil
}
