// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.1
// 	protoc        v3.21.12
// source: portal/auth.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserDisplayRole int32

const (
	UserDisplayRole_unknown_role      UserDisplayRole = 0
	UserDisplayRole_robot_role        UserDisplayRole = 1
	UserDisplayRole_operator_basic    UserDisplayRole = 2
	UserDisplayRole_farm_manager      UserDisplayRole = 3
	UserDisplayRole_carbon_tech       UserDisplayRole = 4
	UserDisplayRole_carbon_basic      UserDisplayRole = 5
	UserDisplayRole_operator_advanced UserDisplayRole = 6
)

// Enum value maps for UserDisplayRole.
var (
	UserDisplayRole_name = map[int32]string{
		0: "unknown_role",
		1: "robot_role",
		2: "operator_basic",
		3: "farm_manager",
		4: "carbon_tech",
		5: "carbon_basic",
		6: "operator_advanced",
	}
	UserDisplayRole_value = map[string]int32{
		"unknown_role":      0,
		"robot_role":        1,
		"operator_basic":    2,
		"farm_manager":      3,
		"carbon_tech":       4,
		"carbon_basic":      5,
		"operator_advanced": 6,
	}
)

func (x UserDisplayRole) Enum() *UserDisplayRole {
	p := new(UserDisplayRole)
	*p = x
	return p
}

func (x UserDisplayRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserDisplayRole) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_auth_proto_enumTypes[0].Descriptor()
}

func (UserDisplayRole) Type() protoreflect.EnumType {
	return &file_portal_auth_proto_enumTypes[0]
}

func (x UserDisplayRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserDisplayRole.Descriptor instead.
func (UserDisplayRole) EnumDescriptor() ([]byte, []int) {
	return file_portal_auth_proto_rawDescGZIP(), []int{0}
}

type PermissionAction int32

const (
	PermissionAction_unknown_action PermissionAction = 0
	PermissionAction_read           PermissionAction = 1
	PermissionAction_update         PermissionAction = 2
)

// Enum value maps for PermissionAction.
var (
	PermissionAction_name = map[int32]string{
		0: "unknown_action",
		1: "read",
		2: "update",
	}
	PermissionAction_value = map[string]int32{
		"unknown_action": 0,
		"read":           1,
		"update":         2,
	}
)

func (x PermissionAction) Enum() *PermissionAction {
	p := new(PermissionAction)
	*p = x
	return p
}

func (x PermissionAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PermissionAction) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_auth_proto_enumTypes[1].Descriptor()
}

func (PermissionAction) Type() protoreflect.EnumType {
	return &file_portal_auth_proto_enumTypes[1]
}

func (x PermissionAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PermissionAction.Descriptor instead.
func (PermissionAction) EnumDescriptor() ([]byte, []int) {
	return file_portal_auth_proto_rawDescGZIP(), []int{1}
}

type PermissionResource int32

const (
	PermissionResource_unknown                PermissionResource = 0
	PermissionResource_alarms_customer        PermissionResource = 1
	PermissionResource_alarms_internal        PermissionResource = 2
	PermissionResource_almanacs               PermissionResource = 3
	PermissionResource_operator_settings      PermissionResource = 4
	PermissionResource_banding                PermissionResource = 5
	PermissionResource_banding_basic          PermissionResource = 6
	PermissionResource_banding_advanced       PermissionResource = 7
	PermissionResource_calibration            PermissionResource = 8
	PermissionResource_cameras                PermissionResource = 9
	PermissionResource_capture                PermissionResource = 10
	PermissionResource_chat                   PermissionResource = 11
	PermissionResource_configs                PermissionResource = 12
	PermissionResource_crops                  PermissionResource = 13
	PermissionResource_models_basic           PermissionResource = 14
	PermissionResource_models_pinned          PermissionResource = 15
	PermissionResource_models_nickname        PermissionResource = 16
	PermissionResource_thresholds             PermissionResource = 17
	PermissionResource_diagnostics            PermissionResource = 18
	PermissionResource_guides                 PermissionResource = 19
	PermissionResource_jobs                   PermissionResource = 20
	PermissionResource_captcha                PermissionResource = 21
	PermissionResource_lasers_row             PermissionResource = 22
	PermissionResource_lasers_basic           PermissionResource = 23
	PermissionResource_lasers_disable         PermissionResource = 24
	PermissionResource_lasers_enable          PermissionResource = 25
	PermissionResource_lasers_advanced        PermissionResource = 26
	PermissionResource_lasers                 PermissionResource = 27
	PermissionResource_feeds                  PermissionResource = 28
	PermissionResource_models_advanced        PermissionResource = 29
	PermissionResource_hardware               PermissionResource = 30
	PermissionResource_quick_tune             PermissionResource = 31
	PermissionResource_robot_ui               PermissionResource = 32
	PermissionResource_thinning               PermissionResource = 33
	PermissionResource_thinning_basic         PermissionResource = 34
	PermissionResource_thinning_advanced      PermissionResource = 35
	PermissionResource_software_basic         PermissionResource = 36
	PermissionResource_software_advanced      PermissionResource = 37
	PermissionResource_velocity_estimators    PermissionResource = 38
	PermissionResource_vizualization_basic    PermissionResource = 39
	PermissionResource_vizualization_advanced PermissionResource = 40
	PermissionResource_metrics                PermissionResource = 41
	PermissionResource_mode                   PermissionResource = 42
	PermissionResource_jobs_select            PermissionResource = 43
	PermissionResource_portal_settings        PermissionResource = 44
	// Deprecated: Do not use.
	PermissionResource_portal_labs             PermissionResource = 45
	PermissionResource_shortcuts_internal      PermissionResource = 46
	PermissionResource_admin_alarms            PermissionResource = 47
	PermissionResource_admin_cloud             PermissionResource = 48
	PermissionResource_customers               PermissionResource = 49
	PermissionResource_users                   PermissionResource = 50
	PermissionResource_users_invite            PermissionResource = 51
	PermissionResource_users_permissions       PermissionResource = 52
	PermissionResource_reports                 PermissionResource = 53
	PermissionResource_robots                  PermissionResource = 54
	PermissionResource_robots_assign           PermissionResource = 55
	PermissionResource_robots_status           PermissionResource = 56
	PermissionResource_robots_health           PermissionResource = 57
	PermissionResource_veselka                 PermissionResource = 58
	PermissionResource_metrics_internal        PermissionResource = 59
	PermissionResource_metrics_customer        PermissionResource = 60
	PermissionResource_crops_basic             PermissionResource = 61
	PermissionResource_crops_advanced          PermissionResource = 62
	PermissionResource_discriminators          PermissionResource = 63
	PermissionResource_operator_map            PermissionResource = 64
	PermissionResource_uploads                 PermissionResource = 65
	PermissionResource_farms                   PermissionResource = 66
	PermissionResource_images                  PermissionResource = 67
	PermissionResource_autotractor_jobs        PermissionResource = 68
	PermissionResource_plant_category_profiles PermissionResource = 69
	PermissionResource_portal_globals          PermissionResource = 70
	PermissionResource_autotractor_drive       PermissionResource = 71
)

// Enum value maps for PermissionResource.
var (
	PermissionResource_name = map[int32]string{
		0:  "unknown",
		1:  "alarms_customer",
		2:  "alarms_internal",
		3:  "almanacs",
		4:  "operator_settings",
		5:  "banding",
		6:  "banding_basic",
		7:  "banding_advanced",
		8:  "calibration",
		9:  "cameras",
		10: "capture",
		11: "chat",
		12: "configs",
		13: "crops",
		14: "models_basic",
		15: "models_pinned",
		16: "models_nickname",
		17: "thresholds",
		18: "diagnostics",
		19: "guides",
		20: "jobs",
		21: "captcha",
		22: "lasers_row",
		23: "lasers_basic",
		24: "lasers_disable",
		25: "lasers_enable",
		26: "lasers_advanced",
		27: "lasers",
		28: "feeds",
		29: "models_advanced",
		30: "hardware",
		31: "quick_tune",
		32: "robot_ui",
		33: "thinning",
		34: "thinning_basic",
		35: "thinning_advanced",
		36: "software_basic",
		37: "software_advanced",
		38: "velocity_estimators",
		39: "vizualization_basic",
		40: "vizualization_advanced",
		41: "metrics",
		42: "mode",
		43: "jobs_select",
		44: "portal_settings",
		45: "portal_labs",
		46: "shortcuts_internal",
		47: "admin_alarms",
		48: "admin_cloud",
		49: "customers",
		50: "users",
		51: "users_invite",
		52: "users_permissions",
		53: "reports",
		54: "robots",
		55: "robots_assign",
		56: "robots_status",
		57: "robots_health",
		58: "veselka",
		59: "metrics_internal",
		60: "metrics_customer",
		61: "crops_basic",
		62: "crops_advanced",
		63: "discriminators",
		64: "operator_map",
		65: "uploads",
		66: "farms",
		67: "images",
		68: "autotractor_jobs",
		69: "plant_category_profiles",
		70: "portal_globals",
		71: "autotractor_drive",
	}
	PermissionResource_value = map[string]int32{
		"unknown":                 0,
		"alarms_customer":         1,
		"alarms_internal":         2,
		"almanacs":                3,
		"operator_settings":       4,
		"banding":                 5,
		"banding_basic":           6,
		"banding_advanced":        7,
		"calibration":             8,
		"cameras":                 9,
		"capture":                 10,
		"chat":                    11,
		"configs":                 12,
		"crops":                   13,
		"models_basic":            14,
		"models_pinned":           15,
		"models_nickname":         16,
		"thresholds":              17,
		"diagnostics":             18,
		"guides":                  19,
		"jobs":                    20,
		"captcha":                 21,
		"lasers_row":              22,
		"lasers_basic":            23,
		"lasers_disable":          24,
		"lasers_enable":           25,
		"lasers_advanced":         26,
		"lasers":                  27,
		"feeds":                   28,
		"models_advanced":         29,
		"hardware":                30,
		"quick_tune":              31,
		"robot_ui":                32,
		"thinning":                33,
		"thinning_basic":          34,
		"thinning_advanced":       35,
		"software_basic":          36,
		"software_advanced":       37,
		"velocity_estimators":     38,
		"vizualization_basic":     39,
		"vizualization_advanced":  40,
		"metrics":                 41,
		"mode":                    42,
		"jobs_select":             43,
		"portal_settings":         44,
		"portal_labs":             45,
		"shortcuts_internal":      46,
		"admin_alarms":            47,
		"admin_cloud":             48,
		"customers":               49,
		"users":                   50,
		"users_invite":            51,
		"users_permissions":       52,
		"reports":                 53,
		"robots":                  54,
		"robots_assign":           55,
		"robots_status":           56,
		"robots_health":           57,
		"veselka":                 58,
		"metrics_internal":        59,
		"metrics_customer":        60,
		"crops_basic":             61,
		"crops_advanced":          62,
		"discriminators":          63,
		"operator_map":            64,
		"uploads":                 65,
		"farms":                   66,
		"images":                  67,
		"autotractor_jobs":        68,
		"plant_category_profiles": 69,
		"portal_globals":          70,
		"autotractor_drive":       71,
	}
)

func (x PermissionResource) Enum() *PermissionResource {
	p := new(PermissionResource)
	*p = x
	return p
}

func (x PermissionResource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PermissionResource) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_auth_proto_enumTypes[2].Descriptor()
}

func (PermissionResource) Type() protoreflect.EnumType {
	return &file_portal_auth_proto_enumTypes[2]
}

func (x PermissionResource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PermissionResource.Descriptor instead.
func (PermissionResource) EnumDescriptor() ([]byte, []int) {
	return file_portal_auth_proto_rawDescGZIP(), []int{2}
}

type PermissionDomain int32

const (
	PermissionDomain_unknown_domain PermissionDomain = 0
	PermissionDomain_none           PermissionDomain = 1
	PermissionDomain_self           PermissionDomain = 2
	PermissionDomain_robot          PermissionDomain = 3
	PermissionDomain_customer       PermissionDomain = 4
	PermissionDomain_all            PermissionDomain = 5
	PermissionDomain_templates      PermissionDomain = 6
)

// Enum value maps for PermissionDomain.
var (
	PermissionDomain_name = map[int32]string{
		0: "unknown_domain",
		1: "none",
		2: "self",
		3: "robot",
		4: "customer",
		5: "all",
		6: "templates",
	}
	PermissionDomain_value = map[string]int32{
		"unknown_domain": 0,
		"none":           1,
		"self":           2,
		"robot":          3,
		"customer":       4,
		"all":            5,
		"templates":      6,
	}
)

func (x PermissionDomain) Enum() *PermissionDomain {
	p := new(PermissionDomain)
	*p = x
	return p
}

func (x PermissionDomain) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PermissionDomain) Descriptor() protoreflect.EnumDescriptor {
	return file_portal_auth_proto_enumTypes[3].Descriptor()
}

func (PermissionDomain) Type() protoreflect.EnumType {
	return &file_portal_auth_proto_enumTypes[3]
}

func (x PermissionDomain) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PermissionDomain.Descriptor instead.
func (PermissionDomain) EnumDescriptor() ([]byte, []int) {
	return file_portal_auth_proto_rawDescGZIP(), []int{3}
}

var File_portal_auth_proto protoreflect.FileDescriptor

var file_portal_auth_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x12, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2a, 0x93, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x12, 0x0a,
	0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x0c, 0x66, 0x61, 0x72, 0x6d, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x74, 0x65,
	0x63, 0x68, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x5f, 0x62,
	0x61, 0x73, 0x69, 0x63, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x06, 0x2a, 0x3c, 0x0a,
	0x10, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x0e, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x72, 0x65, 0x61, 0x64, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x10, 0x02, 0x2a, 0x81, 0x0a, 0x0a, 0x12,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x73, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x63, 0x73, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x10, 0x04, 0x12, 0x0b,
	0x0a, 0x07, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x62,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x10, 0x06, 0x12, 0x14,
	0x0a, 0x10, 0x62, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x64, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x08, 0x12, 0x0b, 0x0a, 0x07, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73,
	0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x10, 0x0a, 0x12,
	0x08, 0x0a, 0x04, 0x63, 0x68, 0x61, 0x74, 0x10, 0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x10, 0x0c, 0x12, 0x09, 0x0a, 0x05, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x10,
	0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x10, 0x0e, 0x12, 0x11, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x5f, 0x70, 0x69,
	0x6e, 0x6e, 0x65, 0x64, 0x10, 0x0f, 0x12, 0x13, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x10, 0x10, 0x12, 0x0e, 0x0a, 0x0a, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x73, 0x10, 0x11, 0x12, 0x0f, 0x0a, 0x0b, 0x64,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x74, 0x69, 0x63, 0x73, 0x10, 0x12, 0x12, 0x0a, 0x0a, 0x06,
	0x67, 0x75, 0x69, 0x64, 0x65, 0x73, 0x10, 0x13, 0x12, 0x08, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73,
	0x10, 0x14, 0x12, 0x0b, 0x0a, 0x07, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x10, 0x15, 0x12,
	0x0e, 0x0a, 0x0a, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x72, 0x6f, 0x77, 0x10, 0x16, 0x12,
	0x10, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x10,
	0x17, 0x12, 0x12, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x10, 0x18, 0x12, 0x11, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x19, 0x12, 0x13, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x65,
	0x72, 0x73, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x1a, 0x12, 0x0a, 0x0a,
	0x06, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x73, 0x10, 0x1b, 0x12, 0x09, 0x0a, 0x05, 0x66, 0x65, 0x65,
	0x64, 0x73, 0x10, 0x1c, 0x12, 0x13, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x5f, 0x61,
	0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x1d, 0x12, 0x0c, 0x0a, 0x08, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x10, 0x1e, 0x12, 0x0e, 0x0a, 0x0a, 0x71, 0x75, 0x69, 0x63, 0x6b,
	0x5f, 0x74, 0x75, 0x6e, 0x65, 0x10, 0x1f, 0x12, 0x0c, 0x0a, 0x08, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x5f, 0x75, 0x69, 0x10, 0x20, 0x12, 0x0c, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x10, 0x21, 0x12, 0x12, 0x0a, 0x0e, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x10, 0x22, 0x12, 0x15, 0x0a, 0x11, 0x74, 0x68, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x23, 0x12, 0x12,
	0x0a, 0x0e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x69, 0x63,
	0x10, 0x24, 0x12, 0x15, 0x0a, 0x11, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x61,
	0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x25, 0x12, 0x17, 0x0a, 0x13, 0x76, 0x65, 0x6c,
	0x6f, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x6f, 0x72, 0x73,
	0x10, 0x26, 0x12, 0x17, 0x0a, 0x13, 0x76, 0x69, 0x7a, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x10, 0x27, 0x12, 0x1a, 0x0a, 0x16, 0x76,
	0x69, 0x7a, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x76,
	0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x28, 0x12, 0x0b, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x10, 0x29, 0x12, 0x08, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x10, 0x2a, 0x12, 0x0f,
	0x0a, 0x0b, 0x6a, 0x6f, 0x62, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x10, 0x2b, 0x12,
	0x13, 0x0a, 0x0f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x10, 0x2c, 0x12, 0x13, 0x0a, 0x0b, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x5f, 0x6c,
	0x61, 0x62, 0x73, 0x10, 0x2d, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x73, 0x68, 0x6f,
	0x72, 0x74, 0x63, 0x75, 0x74, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x10,
	0x2e, 0x12, 0x10, 0x0a, 0x0c, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6c, 0x61, 0x72, 0x6d,
	0x73, 0x10, 0x2f, 0x12, 0x0f, 0x0a, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x10, 0x30, 0x12, 0x0d, 0x0a, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x10, 0x31, 0x12, 0x09, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x10, 0x32, 0x12, 0x10,
	0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x10, 0x33,
	0x12, 0x15, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x10, 0x34, 0x12, 0x0b, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x73, 0x10, 0x35, 0x12, 0x0a, 0x0a, 0x06, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x73, 0x10, 0x36,
	0x12, 0x11, 0x0a, 0x0d, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x73, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x10, 0x37, 0x12, 0x11, 0x0a, 0x0d, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x73, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x10, 0x38, 0x12, 0x11, 0x0a, 0x0d, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x73,
	0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x10, 0x39, 0x12, 0x0b, 0x0a, 0x07, 0x76, 0x65, 0x73,
	0x65, 0x6c, 0x6b, 0x61, 0x10, 0x3a, 0x12, 0x14, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x10, 0x3b, 0x12, 0x14, 0x0a, 0x10,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x10, 0x3c, 0x12, 0x0f, 0x0a, 0x0b, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x10, 0x3d, 0x12, 0x12, 0x0a, 0x0e, 0x63, 0x72, 0x6f, 0x70, 0x73, 0x5f, 0x61, 0x64, 0x76,
	0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x3e, 0x12, 0x12, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x72,
	0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x10, 0x3f, 0x12, 0x10, 0x0a, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6d, 0x61, 0x70, 0x10, 0x40, 0x12, 0x0b, 0x0a,
	0x07, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x10, 0x41, 0x12, 0x09, 0x0a, 0x05, 0x66, 0x61,
	0x72, 0x6d, 0x73, 0x10, 0x42, 0x12, 0x0a, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x10,
	0x43, 0x12, 0x14, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x6f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x6a, 0x6f, 0x62, 0x73, 0x10, 0x44, 0x12, 0x1b, 0x0a, 0x17, 0x70, 0x6c, 0x61, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x10, 0x45, 0x12, 0x12, 0x0a, 0x0e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x5f, 0x67,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x73, 0x10, 0x46, 0x12, 0x15, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x10, 0x47, 0x2a,
	0x6b, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x0e, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x6e, 0x6f, 0x6e, 0x65, 0x10,
	0x01, 0x12, 0x08, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x61, 0x6c, 0x6c, 0x10, 0x05, 0x12, 0x0d, 0x0a,
	0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x10, 0x06, 0x42, 0x40, 0x5a, 0x3e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x2f, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_auth_proto_rawDescOnce sync.Once
	file_portal_auth_proto_rawDescData = file_portal_auth_proto_rawDesc
)

func file_portal_auth_proto_rawDescGZIP() []byte {
	file_portal_auth_proto_rawDescOnce.Do(func() {
		file_portal_auth_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_auth_proto_rawDescData)
	})
	return file_portal_auth_proto_rawDescData
}

var file_portal_auth_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_portal_auth_proto_goTypes = []interface{}{
	(UserDisplayRole)(0),    // 0: carbon.portal.auth.UserDisplayRole
	(PermissionAction)(0),   // 1: carbon.portal.auth.PermissionAction
	(PermissionResource)(0), // 2: carbon.portal.auth.PermissionResource
	(PermissionDomain)(0),   // 3: carbon.portal.auth.PermissionDomain
}
var file_portal_auth_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_portal_auth_proto_init() }
func file_portal_auth_proto_init() {
	if File_portal_auth_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_auth_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_portal_auth_proto_goTypes,
		DependencyIndexes: file_portal_auth_proto_depIdxs,
		EnumInfos:         file_portal_auth_proto_enumTypes,
	}.Build()
	File_portal_auth_proto = out.File
	file_portal_auth_proto_rawDesc = nil
	file_portal_auth_proto_goTypes = nil
	file_portal_auth_proto_depIdxs = nil
}
