///
//  Generated code. Do not modify.
//  source: weed_tracking/weed_tracking.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use invalidScoreReasonDescriptor instead')
const InvalidScoreReason$json = const {
  '1': 'InvalidScoreReason',
  '2': const [
    const {'1': 'NONE', '2': 0},
    const {'1': 'ANOTHER_LASER_SHOOTING', '2': 1},
    const {'1': 'SCANNER_POSITION_INVALID', '2': 2},
    const {'1': 'WEED_ALREADY_KILLED', '2': 3},
    const {'1': 'WEED_SHOT_BY_ANOTHER_LASER', '2': 4},
    const {'1': 'WEED_OUT_OF_BAND', '2': 5},
    const {'1': 'NOT_A_WEED', '2': 6},
    const {'1': 'SCORE_NEGATIVE', '2': 7},
    const {'1': 'INTERSECTED_WITH_NONSHOOTABLE', '2': 8},
    const {'1': 'EXTERMINATION_FAILURES_EXCEEDED_MAX', '2': 9},
    const {'1': 'DETECTIONS_OVER_OPPORTUNITIES_BELOW_MIN', '2': 10},
  ],
};

/// Descriptor for `InvalidScoreReason`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List invalidScoreReasonDescriptor = $convert.base64Decode('ChJJbnZhbGlkU2NvcmVSZWFzb24SCAoETk9ORRAAEhoKFkFOT1RIRVJfTEFTRVJfU0hPT1RJTkcQARIcChhTQ0FOTkVSX1BPU0lUSU9OX0lOVkFMSUQQAhIXChNXRUVEX0FMUkVBRFlfS0lMTEVEEAMSHgoaV0VFRF9TSE9UX0JZX0FOT1RIRVJfTEFTRVIQBBIUChBXRUVEX09VVF9PRl9CQU5EEAUSDgoKTk9UX0FfV0VFRBAGEhIKDlNDT1JFX05FR0FUSVZFEAcSIQodSU5URVJTRUNURURfV0lUSF9OT05TSE9PVEFCTEUQCBInCiNFWFRFUk1JTkFUSU9OX0ZBSUxVUkVTX0VYQ0VFREVEX01BWBAJEisKJ0RFVEVDVElPTlNfT1ZFUl9PUFBPUlRVTklUSUVTX0JFTE9XX01JThAK');
@$core.Deprecated('Use killStatusDescriptor instead')
const KillStatus$json = const {
  '1': 'KillStatus',
  '2': const [
    const {'1': 'STATUS_NOT_SHOT', '2': 0},
    const {'1': 'STATUS_BEING_SHOT', '2': 1},
    const {'1': 'STATUS_SHOT', '2': 2},
    const {'1': 'STATUS_PARTIALLY_SHOT', '2': 3},
    const {'1': 'STATUS_P2P_NOT_FOUND', '2': 4},
    const {'1': 'STATUS_ERROR', '2': 5},
    const {'1': 'STATUS_P2P_MISSING_CONTEXT', '2': 6},
  ],
};

/// Descriptor for `KillStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List killStatusDescriptor = $convert.base64Decode('CgpLaWxsU3RhdHVzEhMKD1NUQVRVU19OT1RfU0hPVBAAEhUKEVNUQVRVU19CRUlOR19TSE9UEAESDwoLU1RBVFVTX1NIT1QQAhIZChVTVEFUVVNfUEFSVElBTExZX1NIT1QQAxIYChRTVEFUVVNfUDJQX05PVF9GT1VORBAEEhAKDFNUQVRVU19FUlJPUhAFEh4KGlNUQVRVU19QMlBfTUlTU0lOR19DT05URVhUEAY=');
@$core.Deprecated('Use duplicateStatusDescriptor instead')
const DuplicateStatus$json = const {
  '1': 'DuplicateStatus',
  '2': const [
    const {'1': 'UNIQUE', '2': 0},
    const {'1': 'PRIMARY', '2': 1},
    const {'1': 'DUPLICATE', '2': 2},
  ],
};

/// Descriptor for `DuplicateStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List duplicateStatusDescriptor = $convert.base64Decode('Cg9EdXBsaWNhdGVTdGF0dXMSCgoGVU5JUVVFEAASCwoHUFJJTUFSWRABEg0KCURVUExJQ0FURRAC');
@$core.Deprecated('Use thinningStateDescriptor instead')
const ThinningState$json = const {
  '1': 'ThinningState',
  '2': const [
    const {'1': 'THINNING_UNSET', '2': 0},
    const {'1': 'THINNING_MARKED_FOR_THINNING', '2': 1},
    const {'1': 'THINNING_KEPT', '2': 2},
    const {'1': 'THINNING_IGNORED', '2': 3},
  ],
};

/// Descriptor for `ThinningState`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List thinningStateDescriptor = $convert.base64Decode('Cg1UaGlubmluZ1N0YXRlEhIKDlRISU5OSU5HX1VOU0VUEAASIAocVEhJTk5JTkdfTUFSS0VEX0ZPUl9USElOTklORxABEhEKDVRISU5OSU5HX0tFUFQQAhIUChBUSElOTklOR19JR05PUkVEEAM=');
@$core.Deprecated('Use targetableStateDescriptor instead')
const TargetableState$json = const {
  '1': 'TargetableState',
  '2': const [
    const {'1': 'TARGET_NOT_IN_SCHEDULER', '2': 0},
    const {'1': 'TARGET_SCORED', '2': 1},
    const {'1': 'TARGET_INTERSECTS_NON_SHOOTABLE', '2': 2},
    const {'1': 'TARGET_TOO_MANY_FAILURES', '2': 3},
    const {'1': 'TARGET_DOO_TOO_LOW', '2': 4},
    const {'1': 'TARGET_IGNORED_FROM_ALMANAC', '2': 5},
    const {'1': 'TARGET_OUT_OF_BAND', '2': 6},
    const {'1': 'TARGET_AVOID_FROM_ALMANAC', '2': 7},
  ],
};

/// Descriptor for `TargetableState`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List targetableStateDescriptor = $convert.base64Decode('Cg9UYXJnZXRhYmxlU3RhdGUSGwoXVEFSR0VUX05PVF9JTl9TQ0hFRFVMRVIQABIRCg1UQVJHRVRfU0NPUkVEEAESIwofVEFSR0VUX0lOVEVSU0VDVFNfTk9OX1NIT09UQUJMRRACEhwKGFRBUkdFVF9UT09fTUFOWV9GQUlMVVJFUxADEhYKElRBUkdFVF9ET09fVE9PX0xPVxAEEh8KG1RBUkdFVF9JR05PUkVEX0ZST01fQUxNQU5BQxAFEhYKElRBUkdFVF9PVVRfT0ZfQkFORBAGEh0KGVRBUkdFVF9BVk9JRF9GUk9NX0FMTUFOQUMQBw==');
@$core.Deprecated('Use classificationDescriptor instead')
const Classification$json = const {
  '1': 'Classification',
  '2': const [
    const {'1': 'CLASS_UNDECIDED', '2': 0},
    const {'1': 'CLASS_WEED', '2': 1},
    const {'1': 'CLASS_CROP', '2': 2},
    const {'1': 'CLASS_BOTH', '2': 3},
    const {'1': 'CLASS_UNKNOWN', '2': 4},
  ],
};

/// Descriptor for `Classification`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List classificationDescriptor = $convert.base64Decode('Cg5DbGFzc2lmaWNhdGlvbhITCg9DTEFTU19VTkRFQ0lERUQQABIOCgpDTEFTU19XRUVEEAESDgoKQ0xBU1NfQ1JPUBACEg4KCkNMQVNTX0JPVEgQAxIRCg1DTEFTU19VTktOT1dOEAQ=');
@$core.Deprecated('Use recordingStatusDescriptor instead')
const RecordingStatus$json = const {
  '1': 'RecordingStatus',
  '2': const [
    const {'1': 'NOT_RECORDING', '2': 0},
    const {'1': 'RECORDING_STARTED', '2': 1},
    const {'1': 'RECORDING_FINISHED', '2': 2},
    const {'1': 'RECORDING_FAILED', '2': 3},
  ],
};

/// Descriptor for `RecordingStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List recordingStatusDescriptor = $convert.base64Decode('Cg9SZWNvcmRpbmdTdGF0dXMSEQoNTk9UX1JFQ09SRElORxAAEhUKEVJFQ09SRElOR19TVEFSVEVEEAESFgoSUkVDT1JESU5HX0ZJTklTSEVEEAISFAoQUkVDT1JESU5HX0ZBSUxFRBAD');
@$core.Deprecated('Use conclusionTypeDescriptor instead')
const ConclusionType$json = const {
  '1': 'ConclusionType',
  '2': const [
    const {'1': 'NOT_WEEDING', '2': 0},
    const {'1': 'OUT_OF_BAND', '2': 1},
    const {'1': 'INTERSECTS_WITH_NON_SHOOTABLE', '2': 2},
    const {'1': 'OUT_OF_RANGE', '2': 3},
    const {'1': 'UNIMPORTANT', '2': 4},
    const {'1': 'NOT_SHOT', '2': 5},
    const {'1': 'PARTIALLY_SHOT', '2': 6},
    const {'1': 'SHOT', '2': 7},
    const {'1': 'P2P_NOT_FOUND', '2': 8},
    const {'1': 'ERROR', '2': 9},
    const {'1': 'FLICKER', '2': 10},
    const {'1': 'MARKED_FOR_THINNING', '2': 11},
    const {'1': 'NOT_TARGETED', '2': 12},
    const {'1': 'P2P_MISSING_CONTEXT', '2': 13},
  ],
};

/// Descriptor for `ConclusionType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List conclusionTypeDescriptor = $convert.base64Decode('Cg5Db25jbHVzaW9uVHlwZRIPCgtOT1RfV0VFRElORxAAEg8KC09VVF9PRl9CQU5EEAESIQodSU5URVJTRUNUU19XSVRIX05PTl9TSE9PVEFCTEUQAhIQCgxPVVRfT0ZfUkFOR0UQAxIPCgtVTklNUE9SVEFOVBAEEgwKCE5PVF9TSE9UEAUSEgoOUEFSVElBTExZX1NIT1QQBhIICgRTSE9UEAcSEQoNUDJQX05PVF9GT1VORBAIEgkKBUVSUk9SEAkSCwoHRkxJQ0tFUhAKEhcKE01BUktFRF9GT1JfVEhJTk5JTkcQCxIQCgxOT1RfVEFSR0VURUQQDBIXChNQMlBfTUlTU0lOR19DT05URVhUEA0=');
@$core.Deprecated('Use plantCaptchaStatusDescriptor instead')
const PlantCaptchaStatus$json = const {
  '1': 'PlantCaptchaStatus',
  '2': const [
    const {'1': 'NOT_STARTED', '2': 0},
    const {'1': 'CAPTCHA_STARTED', '2': 1},
    const {'1': 'CAPTCHA_FINISHED', '2': 2},
    const {'1': 'CAPTCHA_FAILED', '2': 3},
    const {'1': 'CAPTCHA_CANCELLED', '2': 4},
  ],
};

/// Descriptor for `PlantCaptchaStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List plantCaptchaStatusDescriptor = $convert.base64Decode('ChJQbGFudENhcHRjaGFTdGF0dXMSDwoLTk9UX1NUQVJURUQQABITCg9DQVBUQ0hBX1NUQVJURUQQARIUChBDQVBUQ0hBX0ZJTklTSEVEEAISEgoOQ0FQVENIQV9GQUlMRUQQAxIVChFDQVBUQ0hBX0NBTkNFTExFRBAE');
@$core.Deprecated('Use plantCaptchaUserPredictionDescriptor instead')
const PlantCaptchaUserPrediction$json = const {
  '1': 'PlantCaptchaUserPrediction',
  '2': const [
    const {'1': 'WEED', '2': 0},
    const {'1': 'CROP', '2': 1},
    const {'1': 'UNKNOWN', '2': 2},
    const {'1': 'OTHER', '2': 3},
    const {'1': 'IGNORE', '2': 4},
    const {'1': 'VOLUNTEER', '2': 5},
    const {'1': 'BENEFICIAL', '2': 6},
    const {'1': 'DEBRIS', '2': 7},
  ],
};

/// Descriptor for `PlantCaptchaUserPrediction`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List plantCaptchaUserPredictionDescriptor = $convert.base64Decode('ChpQbGFudENhcHRjaGFVc2VyUHJlZGljdGlvbhIICgRXRUVEEAASCAoEQ1JPUBABEgsKB1VOS05PV04QAhIJCgVPVEhFUhADEgoKBklHTk9SRRAEEg0KCVZPTFVOVEVFUhAFEg4KCkJFTkVGSUNJQUwQBhIKCgZERUJSSVMQBw==');
@$core.Deprecated('Use emptyDescriptor instead')
const Empty$json = const {
  '1': 'Empty',
};

/// Descriptor for `Empty`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List emptyDescriptor = $convert.base64Decode('CgVFbXB0eQ==');
@$core.Deprecated('Use trajectoryDescriptor instead')
const Trajectory$json = const {
  '1': 'Trajectory',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 13, '10': 'id'},
    const {'1': 'tracker_id', '3': 2, '4': 1, '5': 13, '10': 'trackerId'},
    const {'1': 'status', '3': 3, '4': 1, '5': 13, '10': 'status'},
    const {'1': 'is_weed', '3': 4, '4': 1, '5': 8, '10': 'isWeed'},
    const {'1': 'x_mm', '3': 5, '4': 1, '5': 1, '10': 'xMm'},
    const {'1': 'y_mm', '3': 6, '4': 1, '5': 1, '10': 'yMm'},
    const {'1': 'z_mm', '3': 7, '4': 1, '5': 1, '10': 'zMm'},
    const {'1': 'intersected_with_nonshootable', '3': 8, '4': 1, '5': 8, '10': 'intersectedWithNonshootable'},
    const {'1': 'nonshootable_type_string', '3': 9, '4': 1, '5': 9, '10': 'nonshootableTypeString'},
    const {'1': 'deduplicated_across_tracker', '3': 10, '4': 1, '5': 8, '10': 'deduplicatedAcrossTracker'},
  ],
  '7': const {'3': true},
};

/// Descriptor for `Trajectory`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectoryDescriptor = $convert.base64Decode('CgpUcmFqZWN0b3J5Eg4KAmlkGAEgASgNUgJpZBIdCgp0cmFja2VyX2lkGAIgASgNUgl0cmFja2VySWQSFgoGc3RhdHVzGAMgASgNUgZzdGF0dXMSFwoHaXNfd2VlZBgEIAEoCFIGaXNXZWVkEhEKBHhfbW0YBSABKAFSA3hNbRIRCgR5X21tGAYgASgBUgN5TW0SEQoEel9tbRgHIAEoAVIDek1tEkIKHWludGVyc2VjdGVkX3dpdGhfbm9uc2hvb3RhYmxlGAggASgIUhtpbnRlcnNlY3RlZFdpdGhOb25zaG9vdGFibGUSOAoYbm9uc2hvb3RhYmxlX3R5cGVfc3RyaW5nGAkgASgJUhZub25zaG9vdGFibGVUeXBlU3RyaW5nEj4KG2RlZHVwbGljYXRlZF9hY3Jvc3NfdHJhY2tlchgKIAEoCFIZZGVkdXBsaWNhdGVkQWNyb3NzVHJhY2tlcjoCGAE=');
@$core.Deprecated('Use targetDescriptor instead')
const Target$json = const {
  '1': 'Target',
  '2': const [
    const {'1': 'scanner_id', '3': 1, '4': 1, '5': 13, '10': 'scannerId'},
    const {'1': 'trajectory_id', '3': 2, '4': 1, '5': 13, '10': 'trajectoryId'},
    const {'1': 'next_trajectory_id', '3': 3, '4': 1, '5': 13, '10': 'nextTrajectoryId'},
    const {'1': 'starting_pos_y', '3': 4, '4': 1, '5': 2, '10': 'startingPosY'},
    const {'1': 'ending_pos_y', '3': 5, '4': 1, '5': 2, '10': 'endingPosY'},
  ],
  '7': const {'3': true},
};

/// Descriptor for `Target`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List targetDescriptor = $convert.base64Decode('CgZUYXJnZXQSHQoKc2Nhbm5lcl9pZBgBIAEoDVIJc2Nhbm5lcklkEiMKDXRyYWplY3RvcnlfaWQYAiABKA1SDHRyYWplY3RvcnlJZBIsChJuZXh0X3RyYWplY3RvcnlfaWQYAyABKA1SEG5leHRUcmFqZWN0b3J5SWQSJAoOc3RhcnRpbmdfcG9zX3kYBCABKAJSDHN0YXJ0aW5nUG9zWRIgCgxlbmRpbmdfcG9zX3kYBSABKAJSCmVuZGluZ1Bvc1k6AhgB');
@$core.Deprecated('Use boundsDescriptor instead')
const Bounds$json = const {
  '1': 'Bounds',
  '2': const [
    const {'1': 'tracker_id', '3': 1, '4': 1, '5': 9, '10': 'trackerId'},
    const {'1': 'max_pos_mm_y', '3': 2, '4': 1, '5': 2, '10': 'maxPosMmY'},
  ],
  '7': const {'3': true},
};

/// Descriptor for `Bounds`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List boundsDescriptor = $convert.base64Decode('CgZCb3VuZHMSHQoKdHJhY2tlcl9pZBgBIAEoCVIJdHJhY2tlcklkEh8KDG1heF9wb3NfbW1feRgCIAEoAlIJbWF4UG9zTW1ZOgIYAQ==');
@$core.Deprecated('Use trackingStatusReplyDescriptor instead')
const TrackingStatusReply$json = const {
  '1': 'TrackingStatusReply',
  '2': const [
    const {'1': 'targets', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.Target', '10': 'targets'},
    const {'1': 'trajectories', '3': 2, '4': 3, '5': 11, '6': '.weed_tracking.Trajectory', '10': 'trajectories'},
    const {'1': 'bounds', '3': 3, '4': 3, '5': 11, '6': '.weed_tracking.Bounds', '10': 'bounds'},
  ],
  '7': const {'3': true},
};

/// Descriptor for `TrackingStatusReply`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trackingStatusReplyDescriptor = $convert.base64Decode('ChNUcmFja2luZ1N0YXR1c1JlcGx5Ei8KB3RhcmdldHMYASADKAsyFS53ZWVkX3RyYWNraW5nLlRhcmdldFIHdGFyZ2V0cxI9Cgx0cmFqZWN0b3JpZXMYAiADKAsyGS53ZWVkX3RyYWNraW5nLlRyYWplY3RvcnlSDHRyYWplY3RvcmllcxItCgZib3VuZHMYAyADKAsyFS53ZWVkX3RyYWNraW5nLkJvdW5kc1IGYm91bmRzOgIYAQ==');
@$core.Deprecated('Use getDetectionsRequestDescriptor instead')
const GetDetectionsRequest$json = const {
  '1': 'GetDetectionsRequest',
  '2': const [
    const {'1': 'cam_id', '3': 1, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'max_x', '3': 3, '4': 1, '5': 5, '10': 'maxX'},
  ],
};

/// Descriptor for `GetDetectionsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDetectionsRequestDescriptor = $convert.base64Decode('ChRHZXREZXRlY3Rpb25zUmVxdWVzdBIVCgZjYW1faWQYASABKAlSBWNhbUlkEiEKDHRpbWVzdGFtcF9tcxgCIAEoA1ILdGltZXN0YW1wTXMSEwoFbWF4X3gYAyABKAVSBG1heFg=');
@$core.Deprecated('Use detectionDescriptor instead')
const Detection$json = const {
  '1': 'Detection',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 2, '10': 'x'},
    const {'1': 'y', '3': 2, '4': 1, '5': 2, '10': 'y'},
    const {'1': 'size', '3': 3, '4': 1, '5': 2, '10': 'size'},
    const {'1': 'clz', '3': 4, '4': 1, '5': 9, '10': 'clz'},
    const {'1': 'is_weed', '3': 5, '4': 1, '5': 8, '10': 'isWeed'},
    const {'1': 'out_of_band', '3': 6, '4': 1, '5': 8, '10': 'outOfBand'},
    const {'1': 'id', '3': 7, '4': 1, '5': 13, '10': 'id'},
    const {'1': 'score', '3': 8, '4': 1, '5': 2, '10': 'score'},
    const {'1': 'weed_score', '3': 9, '4': 1, '5': 2, '10': 'weedScore'},
    const {'1': 'crop_score', '3': 10, '4': 1, '5': 2, '10': 'cropScore'},
    const {'1': 'embedding', '3': 11, '4': 3, '5': 2, '10': 'embedding'},
    const {'1': 'plant_score', '3': 12, '4': 1, '5': 2, '10': 'plantScore'},
  ],
};

/// Descriptor for `Detection`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List detectionDescriptor = $convert.base64Decode('CglEZXRlY3Rpb24SDAoBeBgBIAEoAlIBeBIMCgF5GAIgASgCUgF5EhIKBHNpemUYAyABKAJSBHNpemUSEAoDY2x6GAQgASgJUgNjbHoSFwoHaXNfd2VlZBgFIAEoCFIGaXNXZWVkEh4KC291dF9vZl9iYW5kGAYgASgIUglvdXRPZkJhbmQSDgoCaWQYByABKA1SAmlkEhQKBXNjb3JlGAggASgCUgVzY29yZRIdCgp3ZWVkX3Njb3JlGAkgASgCUgl3ZWVkU2NvcmUSHQoKY3JvcF9zY29yZRgKIAEoAlIJY3JvcFNjb3JlEhwKCWVtYmVkZGluZxgLIAMoAlIJZW1iZWRkaW5nEh8KC3BsYW50X3Njb3JlGAwgASgCUgpwbGFudFNjb3Jl');
@$core.Deprecated('Use detectionsDescriptor instead')
const Detections$json = const {
  '1': 'Detections',
  '2': const [
    const {'1': 'detections', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.Detection', '10': 'detections'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `Detections`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List detectionsDescriptor = $convert.base64Decode('CgpEZXRlY3Rpb25zEjgKCmRldGVjdGlvbnMYASADKAsyGC53ZWVkX3RyYWNraW5nLkRldGVjdGlvblIKZGV0ZWN0aW9ucxIhCgx0aW1lc3RhbXBfbXMYAiABKANSC3RpbWVzdGFtcE1z');
@$core.Deprecated('Use bandDescriptor instead')
const Band$json = const {
  '1': 'Band',
  '2': const [
    const {'1': 'start_x_px', '3': 1, '4': 1, '5': 1, '10': 'startXPx'},
    const {'1': 'end_x_px', '3': 2, '4': 1, '5': 1, '10': 'endXPx'},
  ],
};

/// Descriptor for `Band`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bandDescriptor = $convert.base64Decode('CgRCYW5kEhwKCnN0YXJ0X3hfcHgYASABKAFSCHN0YXJ0WFB4EhgKCGVuZF94X3B4GAIgASgBUgZlbmRYUHg=');
@$core.Deprecated('Use bandsDescriptor instead')
const Bands$json = const {
  '1': 'Bands',
  '2': const [
    const {'1': 'band', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.Band', '10': 'band'},
    const {'1': 'banding_enabled', '3': 2, '4': 1, '5': 8, '10': 'bandingEnabled'},
    const {'1': 'row_has_bands_defined', '3': 3, '4': 1, '5': 8, '10': 'rowHasBandsDefined'},
  ],
};

/// Descriptor for `Bands`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bandsDescriptor = $convert.base64Decode('CgVCYW5kcxInCgRiYW5kGAEgAygLMhMud2VlZF90cmFja2luZy5CYW5kUgRiYW5kEicKD2JhbmRpbmdfZW5hYmxlZBgCIAEoCFIOYmFuZGluZ0VuYWJsZWQSMQoVcm93X2hhc19iYW5kc19kZWZpbmVkGAMgASgIUhJyb3dIYXNCYW5kc0RlZmluZWQ=');
@$core.Deprecated('Use getDetectionsResponseDescriptor instead')
const GetDetectionsResponse$json = const {
  '1': 'GetDetectionsResponse',
  '2': const [
    const {'1': 'detections', '3': 1, '4': 1, '5': 11, '6': '.weed_tracking.Detections', '10': 'detections'},
    const {'1': 'bands', '3': 2, '4': 1, '5': 11, '6': '.weed_tracking.Bands', '10': 'bands'},
  ],
};

/// Descriptor for `GetDetectionsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDetectionsResponseDescriptor = $convert.base64Decode('ChVHZXREZXRlY3Rpb25zUmVzcG9uc2USOQoKZGV0ZWN0aW9ucxgBIAEoCzIZLndlZWRfdHJhY2tpbmcuRGV0ZWN0aW9uc1IKZGV0ZWN0aW9ucxIqCgViYW5kcxgCIAEoCzIULndlZWRfdHJhY2tpbmcuQmFuZHNSBWJhbmRz');
@$core.Deprecated('Use getTrajectoryMetadataRequestDescriptor instead')
const GetTrajectoryMetadataRequest$json = const {
  '1': 'GetTrajectoryMetadataRequest',
};

/// Descriptor for `GetTrajectoryMetadataRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrajectoryMetadataRequestDescriptor = $convert.base64Decode('ChxHZXRUcmFqZWN0b3J5TWV0YWRhdGFSZXF1ZXN0');
@$core.Deprecated('Use trackedItemMetadataDescriptor instead')
const TrackedItemMetadata$json = const {
  '1': 'TrackedItemMetadata',
  '2': const [
    const {'1': 'detection_id', '3': 1, '4': 1, '5': 13, '10': 'detectionId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `TrackedItemMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trackedItemMetadataDescriptor = $convert.base64Decode('ChNUcmFja2VkSXRlbU1ldGFkYXRhEiEKDGRldGVjdGlvbl9pZBgBIAEoDVILZGV0ZWN0aW9uSWQSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcw==');
@$core.Deprecated('Use trajectoryMetadataDescriptor instead')
const TrajectoryMetadata$json = const {
  '1': 'TrajectoryMetadata',
  '2': const [
    const {'1': 'trajectory_id', '3': 1, '4': 1, '5': 13, '10': 'trajectoryId'},
    const {'1': 'cam_id', '3': 2, '4': 1, '5': 9, '10': 'camId'},
    const {'1': 'tracked_item_metadata', '3': 3, '4': 3, '5': 11, '6': '.weed_tracking.TrackedItemMetadata', '10': 'trackedItemMetadata'},
    const {'1': 'band_status', '3': 4, '4': 1, '5': 9, '10': 'bandStatus'},
  ],
};

/// Descriptor for `TrajectoryMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectoryMetadataDescriptor = $convert.base64Decode('ChJUcmFqZWN0b3J5TWV0YWRhdGESIwoNdHJhamVjdG9yeV9pZBgBIAEoDVIMdHJhamVjdG9yeUlkEhUKBmNhbV9pZBgCIAEoCVIFY2FtSWQSVgoVdHJhY2tlZF9pdGVtX21ldGFkYXRhGAMgAygLMiIud2VlZF90cmFja2luZy5UcmFja2VkSXRlbU1ldGFkYXRhUhN0cmFja2VkSXRlbU1ldGFkYXRhEh8KC2JhbmRfc3RhdHVzGAQgASgJUgpiYW5kU3RhdHVz');
@$core.Deprecated('Use getTrajectoryMetadataResponseDescriptor instead')
const GetTrajectoryMetadataResponse$json = const {
  '1': 'GetTrajectoryMetadataResponse',
  '2': const [
    const {'1': 'metadata', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.TrajectoryMetadata', '10': 'metadata'},
  ],
};

/// Descriptor for `GetTrajectoryMetadataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrajectoryMetadataResponseDescriptor = $convert.base64Decode('Ch1HZXRUcmFqZWN0b3J5TWV0YWRhdGFSZXNwb25zZRI9CghtZXRhZGF0YRgBIAMoCzIhLndlZWRfdHJhY2tpbmcuVHJhamVjdG9yeU1ldGFkYXRhUghtZXRhZGF0YQ==');
@$core.Deprecated('Use pingRequestDescriptor instead')
const PingRequest$json = const {
  '1': 'PingRequest',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 13, '10': 'x'},
  ],
};

/// Descriptor for `PingRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pingRequestDescriptor = $convert.base64Decode('CgtQaW5nUmVxdWVzdBIMCgF4GAEgASgNUgF4');
@$core.Deprecated('Use pongReplyDescriptor instead')
const PongReply$json = const {
  '1': 'PongReply',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 13, '10': 'x'},
  ],
};

/// Descriptor for `PongReply`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pongReplyDescriptor = $convert.base64Decode('CglQb25nUmVwbHkSDAoBeBgBIAEoDVIBeA==');
@$core.Deprecated('Use trajectoryScoreDescriptor instead')
const TrajectoryScore$json = const {
  '1': 'TrajectoryScore',
  '2': const [
    const {'1': 'score', '3': 1, '4': 1, '5': 4, '10': 'score'},
    const {'1': 'score_tilt', '3': 2, '4': 1, '5': 1, '10': 'scoreTilt'},
    const {'1': 'score_pan', '3': 3, '4': 1, '5': 1, '10': 'scorePan'},
    const {'1': 'score_busy', '3': 4, '4': 1, '5': 1, '10': 'scoreBusy'},
    const {'1': 'score_in_range', '3': 5, '4': 1, '5': 1, '10': 'scoreInRange'},
    const {'1': 'score_importance', '3': 6, '4': 1, '5': 1, '10': 'scoreImportance'},
    const {'1': 'score_size', '3': 7, '4': 1, '5': 1, '10': 'scoreSize'},
  ],
};

/// Descriptor for `TrajectoryScore`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectoryScoreDescriptor = $convert.base64Decode('Cg9UcmFqZWN0b3J5U2NvcmUSFAoFc2NvcmUYASABKARSBXNjb3JlEh0KCnNjb3JlX3RpbHQYAiABKAFSCXNjb3JlVGlsdBIbCglzY29yZV9wYW4YAyABKAFSCHNjb3JlUGFuEh0KCnNjb3JlX2J1c3kYBCABKAFSCXNjb3JlQnVzeRIkCg5zY29yZV9pbl9yYW5nZRgFIAEoAVIMc2NvcmVJblJhbmdlEikKEHNjb3JlX2ltcG9ydGFuY2UYBiABKAFSD3Njb3JlSW1wb3J0YW5jZRIdCgpzY29yZV9zaXplGAcgASgBUglzY29yZVNpemU=');
@$core.Deprecated('Use perScannerScoreDescriptor instead')
const PerScannerScore$json = const {
  '1': 'PerScannerScore',
  '2': const [
    const {'1': 'scanner_id', '3': 1, '4': 1, '5': 13, '10': 'scannerId'},
    const {'1': 'score', '3': 2, '4': 1, '5': 5, '10': 'score'},
  ],
};

/// Descriptor for `PerScannerScore`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List perScannerScoreDescriptor = $convert.base64Decode('Cg9QZXJTY2FubmVyU2NvcmUSHQoKc2Nhbm5lcl9pZBgBIAEoDVIJc2Nhbm5lcklkEhQKBXNjb3JlGAIgASgFUgVzY29yZQ==');
@$core.Deprecated('Use scoreStateDescriptor instead')
const ScoreState$json = const {
  '1': 'ScoreState',
  '2': const [
    const {'1': 'target_state', '3': 1, '4': 1, '5': 14, '6': '.weed_tracking.TargetableState', '10': 'targetState'},
    const {'1': 'scores', '3': 2, '4': 3, '5': 11, '6': '.weed_tracking.PerScannerScore', '10': 'scores'},
  ],
};

/// Descriptor for `ScoreState`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List scoreStateDescriptor = $convert.base64Decode('CgpTY29yZVN0YXRlEkEKDHRhcmdldF9zdGF0ZRgBIAEoDjIeLndlZWRfdHJhY2tpbmcuVGFyZ2V0YWJsZVN0YXRlUgt0YXJnZXRTdGF0ZRI2CgZzY29yZXMYAiADKAsyHi53ZWVkX3RyYWNraW5nLlBlclNjYW5uZXJTY29yZVIGc2NvcmVz');
@$core.Deprecated('Use pos2DDescriptor instead')
const Pos2D$json = const {
  '1': 'Pos2D',
  '2': const [
    const {'1': 'x', '3': 1, '4': 1, '5': 1, '10': 'x'},
    const {'1': 'y', '3': 2, '4': 1, '5': 1, '10': 'y'},
  ],
};

/// Descriptor for `Pos2D`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pos2DDescriptor = $convert.base64Decode('CgVQb3MyRBIMCgF4GAEgASgBUgF4EgwKAXkYAiABKAFSAXk=');
@$core.Deprecated('Use killBoxDescriptor instead')
const KillBox$json = const {
  '1': 'KillBox',
  '2': const [
    const {'1': 'scanner_id', '3': 1, '4': 1, '5': 13, '10': 'scannerId'},
    const {'1': 'top_left', '3': 2, '4': 1, '5': 11, '6': '.weed_tracking.Pos2D', '10': 'topLeft'},
    const {'1': 'bottom_right', '3': 3, '4': 1, '5': 11, '6': '.weed_tracking.Pos2D', '10': 'bottomRight'},
  ],
};

/// Descriptor for `KillBox`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List killBoxDescriptor = $convert.base64Decode('CgdLaWxsQm94Eh0KCnNjYW5uZXJfaWQYASABKA1SCXNjYW5uZXJJZBIvCgh0b3BfbGVmdBgCIAEoCzIULndlZWRfdHJhY2tpbmcuUG9zMkRSB3RvcExlZnQSNwoMYm90dG9tX3JpZ2h0GAMgASgLMhQud2VlZF90cmFja2luZy5Qb3MyRFILYm90dG9tUmlnaHQ=');
@$core.Deprecated('Use thresholdsDescriptor instead')
const Thresholds$json = const {
  '1': 'Thresholds',
  '2': const [
    const {
      '1': 'weeding',
      '3': 1,
      '4': 1,
      '5': 2,
      '8': const {'3': true},
      '10': 'weeding',
    },
    const {
      '1': 'thinning',
      '3': 2,
      '4': 1,
      '5': 2,
      '8': const {'3': true},
      '10': 'thinning',
    },
    const {
      '1': 'banding',
      '3': 3,
      '4': 1,
      '5': 2,
      '8': const {'3': true},
      '10': 'banding',
    },
    const {'1': 'passed_weeding', '3': 4, '4': 1, '5': 8, '10': 'passedWeeding'},
    const {'1': 'passed_thinning', '3': 5, '4': 1, '5': 8, '10': 'passedThinning'},
    const {'1': 'passed_banding', '3': 6, '4': 1, '5': 8, '10': 'passedBanding'},
  ],
};

/// Descriptor for `Thresholds`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List thresholdsDescriptor = $convert.base64Decode('CgpUaHJlc2hvbGRzEhwKB3dlZWRpbmcYASABKAJCAhgBUgd3ZWVkaW5nEh4KCHRoaW5uaW5nGAIgASgCQgIYAVIIdGhpbm5pbmcSHAoHYmFuZGluZxgDIAEoAkICGAFSB2JhbmRpbmcSJQoOcGFzc2VkX3dlZWRpbmcYBCABKAhSDXBhc3NlZFdlZWRpbmcSJwoPcGFzc2VkX3RoaW5uaW5nGAUgASgIUg5wYXNzZWRUaGlubmluZxIlCg5wYXNzZWRfYmFuZGluZxgGIAEoCFINcGFzc2VkQmFuZGluZw==');
@$core.Deprecated('Use decisionsDescriptor instead')
const Decisions$json = const {
  '1': 'Decisions',
  '2': const [
    const {'1': 'weeding_weed', '3': 1, '4': 1, '5': 8, '10': 'weedingWeed'},
    const {'1': 'weeding_crop', '3': 2, '4': 1, '5': 8, '10': 'weedingCrop'},
    const {'1': 'thinning_weed', '3': 3, '4': 1, '5': 8, '10': 'thinningWeed'},
    const {'1': 'thinning_crop', '3': 4, '4': 1, '5': 8, '10': 'thinningCrop'},
    const {'1': 'keepable_crop', '3': 5, '4': 1, '5': 8, '10': 'keepableCrop'},
    const {'1': 'banding_crop', '3': 6, '4': 1, '5': 8, '10': 'bandingCrop'},
  ],
};

/// Descriptor for `Decisions`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List decisionsDescriptor = $convert.base64Decode('CglEZWNpc2lvbnMSIQoMd2VlZGluZ193ZWVkGAEgASgIUgt3ZWVkaW5nV2VlZBIhCgx3ZWVkaW5nX2Nyb3AYAiABKAhSC3dlZWRpbmdDcm9wEiMKDXRoaW5uaW5nX3dlZWQYAyABKAhSDHRoaW5uaW5nV2VlZBIjCg10aGlubmluZ19jcm9wGAQgASgIUgx0aGlubmluZ0Nyb3ASIwoNa2VlcGFibGVfY3JvcBgFIAEoCFIMa2VlcGFibGVDcm9wEiEKDGJhbmRpbmdfY3JvcBgGIAEoCFILYmFuZGluZ0Nyb3A=');
@$core.Deprecated('Use trajectorySnapshotDescriptor instead')
const TrajectorySnapshot$json = const {
  '1': 'TrajectorySnapshot',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 13, '10': 'id'},
    const {'1': 'kill_status', '3': 2, '4': 1, '5': 14, '6': '.weed_tracking.KillStatus', '10': 'killStatus'},
    const {
      '1': 'is_weed',
      '3': 3,
      '4': 1,
      '5': 8,
      '8': const {'3': true},
      '10': 'isWeed',
    },
    const {'1': 'x_mm', '3': 4, '4': 1, '5': 1, '10': 'xMm'},
    const {'1': 'y_mm', '3': 5, '4': 1, '5': 1, '10': 'yMm'},
    const {'1': 'z_mm', '3': 6, '4': 1, '5': 1, '10': 'zMm'},
    const {'1': 'score', '3': 7, '4': 1, '5': 11, '6': '.weed_tracking.TrajectoryScore', '9': 0, '10': 'score'},
    const {'1': 'invalid_score', '3': 8, '4': 1, '5': 14, '6': '.weed_tracking.InvalidScoreReason', '9': 0, '10': 'invalidScore'},
    const {'1': 'radius_mm', '3': 9, '4': 1, '5': 1, '10': 'radiusMm'},
    const {'1': 'category', '3': 10, '4': 1, '5': 9, '10': 'category'},
    const {'1': 'shoot_time_requested_ms', '3': 11, '4': 1, '5': 4, '10': 'shootTimeRequestedMs'},
    const {'1': 'shoot_time_actual_ms', '3': 12, '4': 1, '5': 4, '10': 'shootTimeActualMs'},
    const {'1': 'marked_for_thinning', '3': 13, '4': 1, '5': 8, '10': 'markedForThinning'},
    const {'1': 'tracker_id', '3': 14, '4': 1, '5': 13, '10': 'trackerId'},
    const {'1': 'duplicate_status', '3': 15, '4': 1, '5': 14, '6': '.weed_tracking.DuplicateStatus', '10': 'duplicateStatus'},
    const {'1': 'duplicate_trajectory_id', '3': 16, '4': 1, '5': 13, '10': 'duplicateTrajectoryId'},
    const {'1': 'assigned_lasers', '3': 17, '4': 3, '5': 13, '10': 'assignedLasers'},
    const {'1': 'out_of_band', '3': 18, '4': 1, '5': 8, '10': 'outOfBand'},
    const {'1': 'intersected_with_nonshootable', '3': 19, '4': 1, '5': 8, '10': 'intersectedWithNonshootable'},
    const {'1': 'detection_classes', '3': 20, '4': 3, '5': 11, '6': '.weed_tracking.TrajectorySnapshot.DetectionClassesEntry', '10': 'detectionClasses'},
    const {'1': 'confidence', '3': 21, '4': 1, '5': 1, '10': 'confidence'},
    const {'1': 'thinning_state', '3': 22, '4': 1, '5': 14, '6': '.weed_tracking.ThinningState', '10': 'thinningState'},
    const {'1': 'global_pos', '3': 23, '4': 1, '5': 1, '10': 'globalPos'},
    const {'1': 'score_state', '3': 24, '4': 1, '5': 11, '6': '.weed_tracking.ScoreState', '10': 'scoreState'},
    const {'1': 'doo', '3': 25, '4': 1, '5': 2, '10': 'doo'},
    const {'1': 'distance_perspectives_count', '3': 26, '4': 1, '5': 13, '10': 'distancePerspectivesCount'},
    const {'1': 'size_category_index', '3': 27, '4': 1, '5': 5, '10': 'sizeCategoryIndex'},
    const {'1': 'speculative_allowed', '3': 28, '4': 1, '5': 8, '10': 'speculativeAllowed'},
    const {'1': 'protected_by_traj', '3': 29, '4': 1, '5': 8, '10': 'protectedByTraj'},
    const {'1': 'thresholds', '3': 30, '4': 1, '5': 11, '6': '.weed_tracking.Thresholds', '10': 'thresholds'},
    const {'1': 'decisions', '3': 31, '4': 1, '5': 11, '6': '.weed_tracking.Decisions', '10': 'decisions'},
    const {'1': 'crop_score', '3': 32, '4': 1, '5': 1, '10': 'cropScore'},
    const {'1': 'weed_score', '3': 33, '4': 1, '5': 1, '10': 'weedScore'},
    const {'1': 'plant_score', '3': 34, '4': 1, '5': 1, '10': 'plantScore'},
    const {'1': 'num_detections_used_for_decision', '3': 35, '4': 1, '5': 13, '10': 'numDetectionsUsedForDecision'},
    const {'1': 'classification', '3': 36, '4': 1, '5': 14, '6': '.weed_tracking.Classification', '10': 'classification'},
    const {'1': 'embedding_distances', '3': 37, '4': 3, '5': 11, '6': '.weed_tracking.TrajectorySnapshot.EmbeddingDistancesEntry', '10': 'embeddingDistances'},
    const {'1': 'speculative_shoot_time_actual_ms', '3': 38, '4': 1, '5': 13, '10': 'speculativeShootTimeActualMs'},
    const {'1': 'snapshot_metadata', '3': 39, '4': 1, '5': 11, '6': '.weed_tracking.SnapshotMetadata', '10': 'snapshotMetadata'},
  ],
  '3': const [TrajectorySnapshot_DetectionClassesEntry$json, TrajectorySnapshot_EmbeddingDistancesEntry$json],
  '8': const [
    const {'1': 'score_details'},
  ],
};

@$core.Deprecated('Use trajectorySnapshotDescriptor instead')
const TrajectorySnapshot_DetectionClassesEntry$json = const {
  '1': 'DetectionClassesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 1, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use trajectorySnapshotDescriptor instead')
const TrajectorySnapshot_EmbeddingDistancesEntry$json = const {
  '1': 'EmbeddingDistancesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `TrajectorySnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectorySnapshotDescriptor = $convert.base64Decode('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');
@$core.Deprecated('Use snapshotMetadataDescriptor instead')
const SnapshotMetadata$json = const {
  '1': 'SnapshotMetadata',
  '2': const [
    const {'1': 'pcam_id', '3': 1, '4': 1, '5': 9, '10': 'pcamId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'center_x_px', '3': 3, '4': 1, '5': 2, '10': 'centerXPx'},
    const {'1': 'center_y_px', '3': 4, '4': 1, '5': 2, '10': 'centerYPx'},
  ],
};

/// Descriptor for `SnapshotMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List snapshotMetadataDescriptor = $convert.base64Decode('ChBTbmFwc2hvdE1ldGFkYXRhEhcKB3BjYW1faWQYASABKAlSBnBjYW1JZBIhCgx0aW1lc3RhbXBfbXMYAiABKANSC3RpbWVzdGFtcE1zEh4KC2NlbnRlcl94X3B4GAMgASgCUgljZW50ZXJYUHgSHgoLY2VudGVyX3lfcHgYBCABKAJSCWNlbnRlcllQeA==');
@$core.Deprecated('Use bandDefinitionDescriptor instead')
const BandDefinition$json = const {
  '1': 'BandDefinition',
  '2': const [
    const {'1': 'offset_mm', '3': 1, '4': 1, '5': 2, '10': 'offsetMm'},
    const {'1': 'width_mm', '3': 2, '4': 1, '5': 2, '10': 'widthMm'},
    const {'1': 'id', '3': 3, '4': 1, '5': 5, '10': 'id'},
  ],
};

/// Descriptor for `BandDefinition`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bandDefinitionDescriptor = $convert.base64Decode('Cg5CYW5kRGVmaW5pdGlvbhIbCglvZmZzZXRfbW0YASABKAJSCG9mZnNldE1tEhkKCHdpZHRoX21tGAIgASgCUgd3aWR0aE1tEg4KAmlkGAMgASgFUgJpZA==');
@$core.Deprecated('Use cLDAlgorithmSnapshotDescriptor instead')
const CLDAlgorithmSnapshot$json = const {
  '1': 'CLDAlgorithmSnapshot',
  '2': const [
    const {'1': 'timestamp_ms', '3': 1, '4': 1, '5': 4, '10': 'timestampMs'},
    const {'1': 'graph_points_x', '3': 2, '4': 3, '5': 2, '10': 'graphPointsX'},
    const {'1': 'graph_points_y', '3': 3, '4': 3, '5': 2, '10': 'graphPointsY'},
    const {'1': 'minimas_x', '3': 4, '4': 3, '5': 2, '10': 'minimasX'},
    const {'1': 'minimas_y', '3': 5, '4': 3, '5': 2, '10': 'minimasY'},
    const {'1': 'lines', '3': 6, '4': 3, '5': 2, '10': 'lines'},
  ],
};

/// Descriptor for `CLDAlgorithmSnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cLDAlgorithmSnapshotDescriptor = $convert.base64Decode('ChRDTERBbGdvcml0aG1TbmFwc2hvdBIhCgx0aW1lc3RhbXBfbXMYASABKARSC3RpbWVzdGFtcE1zEiQKDmdyYXBoX3BvaW50c194GAIgAygCUgxncmFwaFBvaW50c1gSJAoOZ3JhcGhfcG9pbnRzX3kYAyADKAJSDGdyYXBoUG9pbnRzWRIbCgltaW5pbWFzX3gYBCADKAJSCG1pbmltYXNYEhsKCW1pbmltYXNfeRgFIAMoAlIIbWluaW1hc1kSFAoFbGluZXMYBiADKAJSBWxpbmVz');
@$core.Deprecated('Use diagnosticsSnapshotDescriptor instead')
const DiagnosticsSnapshot$json = const {
  '1': 'DiagnosticsSnapshot',
  '2': const [
    const {'1': 'timestamp_ms', '3': 1, '4': 1, '5': 4, '10': 'timestampMs'},
    const {'1': 'trajectories', '3': 2, '4': 3, '5': 11, '6': '.weed_tracking.TrajectorySnapshot', '10': 'trajectories'},
    const {'1': 'bands', '3': 3, '4': 3, '5': 11, '6': '.weed_tracking.BandDefinition', '10': 'bands'},
    const {'1': 'kill_boxes', '3': 4, '4': 3, '5': 11, '6': '.weed_tracking.KillBox', '10': 'killBoxes'},
    const {'1': 'banding_algorithm_snapshot', '3': 5, '4': 1, '5': 11, '6': '.weed_tracking.CLDAlgorithmSnapshot', '9': 0, '10': 'bandingAlgorithmSnapshot', '17': true},
  ],
  '8': const [
    const {'1': '_banding_algorithm_snapshot'},
  ],
};

/// Descriptor for `DiagnosticsSnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List diagnosticsSnapshotDescriptor = $convert.base64Decode('ChNEaWFnbm9zdGljc1NuYXBzaG90EiEKDHRpbWVzdGFtcF9tcxgBIAEoBFILdGltZXN0YW1wTXMSRQoMdHJhamVjdG9yaWVzGAIgAygLMiEud2VlZF90cmFja2luZy5UcmFqZWN0b3J5U25hcHNob3RSDHRyYWplY3RvcmllcxIzCgViYW5kcxgDIAMoCzIdLndlZWRfdHJhY2tpbmcuQmFuZERlZmluaXRpb25SBWJhbmRzEjUKCmtpbGxfYm94ZXMYBCADKAsyFi53ZWVkX3RyYWNraW5nLktpbGxCb3hSCWtpbGxCb3hlcxJmChpiYW5kaW5nX2FsZ29yaXRobV9zbmFwc2hvdBgFIAEoCzIjLndlZWRfdHJhY2tpbmcuQ0xEQWxnb3JpdGhtU25hcHNob3RIAFIYYmFuZGluZ0FsZ29yaXRobVNuYXBzaG90iAEBQh0KG19iYW5kaW5nX2FsZ29yaXRobV9zbmFwc2hvdA==');
@$core.Deprecated('Use recordDiagnosticsRequestDescriptor instead')
const RecordDiagnosticsRequest$json = const {
  '1': 'RecordDiagnosticsRequest',
  '2': const [
    const {'1': 'ttl_sec', '3': 1, '4': 1, '5': 13, '10': 'ttlSec'},
    const {'1': 'crop_images_per_sec', '3': 2, '4': 1, '5': 2, '10': 'cropImagesPerSec'},
    const {'1': 'weed_images_per_sec', '3': 3, '4': 1, '5': 2, '10': 'weedImagesPerSec'},
  ],
};

/// Descriptor for `RecordDiagnosticsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List recordDiagnosticsRequestDescriptor = $convert.base64Decode('ChhSZWNvcmREaWFnbm9zdGljc1JlcXVlc3QSFwoHdHRsX3NlYxgBIAEoDVIGdHRsU2VjEi0KE2Nyb3BfaW1hZ2VzX3Blcl9zZWMYAiABKAJSEGNyb3BJbWFnZXNQZXJTZWMSLQoTd2VlZF9pbWFnZXNfcGVyX3NlYxgDIAEoAlIQd2VlZEltYWdlc1BlclNlYw==');
@$core.Deprecated('Use getRecordingStatusResponseDescriptor instead')
const GetRecordingStatusResponse$json = const {
  '1': 'GetRecordingStatusResponse',
  '2': const [
    const {'1': 'status', '3': 1, '4': 1, '5': 14, '6': '.weed_tracking.RecordingStatus', '10': 'status'},
  ],
};

/// Descriptor for `GetRecordingStatusResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRecordingStatusResponseDescriptor = $convert.base64Decode('ChpHZXRSZWNvcmRpbmdTdGF0dXNSZXNwb25zZRI2CgZzdGF0dXMYASABKA4yHi53ZWVkX3RyYWNraW5nLlJlY29yZGluZ1N0YXR1c1IGc3RhdHVz');
@$core.Deprecated('Use startSavingCropLineDetectionReplayRequestDescriptor instead')
const StartSavingCropLineDetectionReplayRequest$json = const {
  '1': 'StartSavingCropLineDetectionReplayRequest',
  '2': const [
    const {'1': 'filename', '3': 1, '4': 1, '5': 9, '10': 'filename'},
    const {'1': 'ttl_ms', '3': 2, '4': 1, '5': 13, '10': 'ttlMs'},
  ],
};

/// Descriptor for `StartSavingCropLineDetectionReplayRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startSavingCropLineDetectionReplayRequestDescriptor = $convert.base64Decode('CilTdGFydFNhdmluZ0Nyb3BMaW5lRGV0ZWN0aW9uUmVwbGF5UmVxdWVzdBIaCghmaWxlbmFtZRgBIAEoCVIIZmlsZW5hbWUSFQoGdHRsX21zGAIgASgNUgV0dGxNcw==');
@$core.Deprecated('Use recordAimbotInputRequestDescriptor instead')
const RecordAimbotInputRequest$json = const {
  '1': 'RecordAimbotInputRequest',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'ttl_ms', '3': 2, '4': 1, '5': 13, '10': 'ttlMs'},
    const {'1': 'rotary_ticks', '3': 3, '4': 1, '5': 8, '9': 0, '10': 'rotaryTicks', '17': true},
    const {'1': 'deepweed', '3': 4, '4': 1, '5': 8, '9': 1, '10': 'deepweed', '17': true},
    const {'1': 'lane_heights', '3': 5, '4': 1, '5': 8, '9': 2, '10': 'laneHeights', '17': true},
  ],
  '8': const [
    const {'1': '_rotary_ticks'},
    const {'1': '_deepweed'},
    const {'1': '_lane_heights'},
  ],
};

/// Descriptor for `RecordAimbotInputRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List recordAimbotInputRequestDescriptor = $convert.base64Decode('ChhSZWNvcmRBaW1ib3RJbnB1dFJlcXVlc3QSEgoEbmFtZRgBIAEoCVIEbmFtZRIVCgZ0dGxfbXMYAiABKA1SBXR0bE1zEiYKDHJvdGFyeV90aWNrcxgDIAEoCEgAUgtyb3RhcnlUaWNrc4gBARIfCghkZWVwd2VlZBgEIAEoCEgBUghkZWVwd2VlZIgBARImCgxsYW5lX2hlaWdodHMYBSABKAhIAlILbGFuZUhlaWdodHOIAQFCDwoNX3JvdGFyeV90aWNrc0ILCglfZGVlcHdlZWRCDwoNX2xhbmVfaGVpZ2h0cw==');
@$core.Deprecated('Use conclusionCountDescriptor instead')
const ConclusionCount$json = const {
  '1': 'ConclusionCount',
  '2': const [
    const {'1': 'type', '3': 1, '4': 1, '5': 14, '6': '.weed_tracking.ConclusionType', '10': 'type'},
    const {'1': 'count', '3': 2, '4': 1, '5': 13, '10': 'count'},
  ],
};

/// Descriptor for `ConclusionCount`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List conclusionCountDescriptor = $convert.base64Decode('Cg9Db25jbHVzaW9uQ291bnQSMQoEdHlwZRgBIAEoDjIdLndlZWRfdHJhY2tpbmcuQ29uY2x1c2lvblR5cGVSBHR5cGUSFAoFY291bnQYAiABKA1SBWNvdW50');
@$core.Deprecated('Use conclusionCounterDescriptor instead')
const ConclusionCounter$json = const {
  '1': 'ConclusionCounter',
  '2': const [
    const {'1': 'counts', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.ConclusionCount', '10': 'counts'},
  ],
};

/// Descriptor for `ConclusionCounter`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List conclusionCounterDescriptor = $convert.base64Decode('ChFDb25jbHVzaW9uQ291bnRlchI2CgZjb3VudHMYASADKAsyHi53ZWVkX3RyYWNraW5nLkNvbmNsdXNpb25Db3VudFIGY291bnRz');
@$core.Deprecated('Use bandDefinitionsDescriptor instead')
const BandDefinitions$json = const {
  '1': 'BandDefinitions',
  '2': const [
    const {'1': 'bands', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.BandDefinition', '10': 'bands'},
    const {'1': 'banding_enabled', '3': 2, '4': 1, '5': 8, '10': 'bandingEnabled'},
    const {'1': 'row_has_bands_defined', '3': 3, '4': 1, '5': 8, '10': 'rowHasBandsDefined'},
  ],
};

/// Descriptor for `BandDefinitions`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bandDefinitionsDescriptor = $convert.base64Decode('Cg9CYW5kRGVmaW5pdGlvbnMSMwoFYmFuZHMYASADKAsyHS53ZWVkX3RyYWNraW5nLkJhbmREZWZpbml0aW9uUgViYW5kcxInCg9iYW5kaW5nX2VuYWJsZWQYAiABKAhSDmJhbmRpbmdFbmFibGVkEjEKFXJvd19oYXNfYmFuZHNfZGVmaW5lZBgDIAEoCFIScm93SGFzQmFuZHNEZWZpbmVk');
@$core.Deprecated('Use plantCaptchaStatusResponseDescriptor instead')
const PlantCaptchaStatusResponse$json = const {
  '1': 'PlantCaptchaStatusResponse',
  '2': const [
    const {'1': 'status', '3': 1, '4': 1, '5': 14, '6': '.weed_tracking.PlantCaptchaStatus', '10': 'status'},
    const {'1': 'total_images', '3': 2, '4': 1, '5': 5, '10': 'totalImages'},
    const {'1': 'images_taken', '3': 3, '4': 1, '5': 5, '10': 'imagesTaken'},
    const {'1': 'metadata_taken', '3': 4, '4': 1, '5': 5, '10': 'metadataTaken'},
    const {'1': 'exemplar_counts', '3': 5, '4': 3, '5': 11, '6': '.weed_tracking.PlantCaptchaStatusResponse.ExemplarCountsEntry', '10': 'exemplarCounts'},
  ],
  '3': const [PlantCaptchaStatusResponse_ExemplarCountsEntry$json],
};

@$core.Deprecated('Use plantCaptchaStatusResponseDescriptor instead')
const PlantCaptchaStatusResponse_ExemplarCountsEntry$json = const {
  '1': 'ExemplarCountsEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 5, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `PlantCaptchaStatusResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List plantCaptchaStatusResponseDescriptor = $convert.base64Decode('ChpQbGFudENhcHRjaGFTdGF0dXNSZXNwb25zZRI5CgZzdGF0dXMYASABKA4yIS53ZWVkX3RyYWNraW5nLlBsYW50Q2FwdGNoYVN0YXR1c1IGc3RhdHVzEiEKDHRvdGFsX2ltYWdlcxgCIAEoBVILdG90YWxJbWFnZXMSIQoMaW1hZ2VzX3Rha2VuGAMgASgFUgtpbWFnZXNUYWtlbhIlCg5tZXRhZGF0YV90YWtlbhgEIAEoBVINbWV0YWRhdGFUYWtlbhJmCg9leGVtcGxhcl9jb3VudHMYBSADKAsyPS53ZWVkX3RyYWNraW5nLlBsYW50Q2FwdGNoYVN0YXR1c1Jlc3BvbnNlLkV4ZW1wbGFyQ291bnRzRW50cnlSDmV4ZW1wbGFyQ291bnRzGkEKE0V4ZW1wbGFyQ291bnRzRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSFAoFdmFsdWUYAiABKAVSBXZhbHVlOgI4AQ==');
@$core.Deprecated('Use embeddingDescriptor instead')
const Embedding$json = const {
  '1': 'Embedding',
  '2': const [
    const {'1': 'elements', '3': 1, '4': 3, '5': 2, '10': 'elements'},
  ],
};

/// Descriptor for `Embedding`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List embeddingDescriptor = $convert.base64Decode('CglFbWJlZGRpbmcSGgoIZWxlbWVudHMYASADKAJSCGVsZW1lbnRz');
@$core.Deprecated('Use weedClassesDescriptor instead')
const WeedClasses$json = const {
  '1': 'WeedClasses',
  '2': const [
    const {'1': 'classes', '3': 1, '4': 3, '5': 11, '6': '.weed_tracking.WeedClasses.ClassesEntry', '10': 'classes'},
  ],
  '3': const [WeedClasses_ClassesEntry$json],
};

@$core.Deprecated('Use weedClassesDescriptor instead')
const WeedClasses_ClassesEntry$json = const {
  '1': 'ClassesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `WeedClasses`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List weedClassesDescriptor = $convert.base64Decode('CgtXZWVkQ2xhc3NlcxJBCgdjbGFzc2VzGAEgAygLMicud2VlZF90cmFja2luZy5XZWVkQ2xhc3Nlcy5DbGFzc2VzRW50cnlSB2NsYXNzZXMaOgoMQ2xhc3Nlc0VudHJ5EhAKA2tleRgBIAEoCVIDa2V5EhQKBXZhbHVlGAIgASgCUgV2YWx1ZToCOAE=');
@$core.Deprecated('Use plantCaptchaItemMetadataDescriptor instead')
const PlantCaptchaItemMetadata$json = const {
  '1': 'PlantCaptchaItemMetadata',
  '2': const [
    const {'1': 'confidence', '3': 1, '4': 1, '5': 2, '10': 'confidence'},
    const {'1': 'x_px', '3': 2, '4': 1, '5': 5, '10': 'xPx'},
    const {'1': 'y_px', '3': 3, '4': 1, '5': 5, '10': 'yPx'},
    const {'1': 'x_mm', '3': 4, '4': 1, '5': 1, '10': 'xMm'},
    const {'1': 'y_mm', '3': 5, '4': 1, '5': 1, '10': 'yMm'},
    const {'1': 'z_mm', '3': 6, '4': 1, '5': 1, '10': 'zMm'},
    const {'1': 'size_mm', '3': 7, '4': 1, '5': 2, '10': 'sizeMm'},
    const {'1': 'categories', '3': 8, '4': 3, '5': 11, '6': '.weed_tracking.PlantCaptchaItemMetadata.CategoriesEntry', '10': 'categories'},
    const {'1': 'doo', '3': 9, '4': 1, '5': 2, '10': 'doo'},
    const {'1': 'is_weed', '3': 10, '4': 1, '5': 8, '10': 'isWeed'},
    const {'1': 'intersected_with_nonshootable', '3': 11, '4': 1, '5': 8, '10': 'intersectedWithNonshootable'},
    const {'1': 'confidence_history', '3': 12, '4': 3, '5': 2, '10': 'confidenceHistory'},
    const {'1': 'is_in_band', '3': 13, '4': 1, '5': 8, '10': 'isInBand'},
    const {'1': 'id', '3': 14, '4': 1, '5': 9, '10': 'id'},
    const {'1': 'size_px', '3': 15, '4': 1, '5': 2, '10': 'sizePx'},
    const {'1': 'shoot_time_ms', '3': 16, '4': 1, '5': 13, '10': 'shootTimeMs'},
    const {'1': 'weed_confidence_history', '3': 17, '4': 3, '5': 2, '10': 'weedConfidenceHistory'},
    const {'1': 'crop_confidence_history', '3': 18, '4': 3, '5': 2, '10': 'cropConfidenceHistory'},
    const {'1': 'size_category_index', '3': 19, '4': 1, '5': 5, '10': 'sizeCategoryIndex'},
    const {'1': 'weed_categories', '3': 20, '4': 3, '5': 11, '6': '.weed_tracking.PlantCaptchaItemMetadata.WeedCategoriesEntry', '10': 'weedCategories'},
    const {'1': 'embedding_history', '3': 21, '4': 3, '5': 11, '6': '.weed_tracking.Embedding', '10': 'embeddingHistory'},
    const {'1': 'initial_label', '3': 22, '4': 1, '5': 14, '6': '.weed_tracking.PlantCaptchaUserPrediction', '10': 'initialLabel'},
    const {'1': 'plant_confidence_history', '3': 23, '4': 3, '5': 2, '10': 'plantConfidenceHistory'},
    const {'1': 'weed_classes_history', '3': 24, '4': 3, '5': 11, '6': '.weed_tracking.WeedClasses', '10': 'weedClassesHistory'},
    const {'1': 'size_mm_history', '3': 25, '4': 3, '5': 2, '10': 'sizeMmHistory'},
    const {'1': 'decisions', '3': 26, '4': 1, '5': 11, '6': '.weed_tracking.Decisions', '10': 'decisions'},
    const {'1': 'num_detections_used_for_decision', '3': 27, '4': 1, '5': 13, '10': 'numDetectionsUsedForDecision'},
    const {'1': 'embedding_distances', '3': 28, '4': 3, '5': 11, '6': '.weed_tracking.PlantCaptchaItemMetadata.EmbeddingDistancesEntry', '10': 'embeddingDistances'},
  ],
  '3': const [PlantCaptchaItemMetadata_CategoriesEntry$json, PlantCaptchaItemMetadata_WeedCategoriesEntry$json, PlantCaptchaItemMetadata_EmbeddingDistancesEntry$json],
};

@$core.Deprecated('Use plantCaptchaItemMetadataDescriptor instead')
const PlantCaptchaItemMetadata_CategoriesEntry$json = const {
  '1': 'CategoriesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use plantCaptchaItemMetadataDescriptor instead')
const PlantCaptchaItemMetadata_WeedCategoriesEntry$json = const {
  '1': 'WeedCategoriesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use plantCaptchaItemMetadataDescriptor instead')
const PlantCaptchaItemMetadata_EmbeddingDistancesEntry$json = const {
  '1': 'EmbeddingDistancesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `PlantCaptchaItemMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List plantCaptchaItemMetadataDescriptor = $convert.base64Decode('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');
@$core.Deprecated('Use getTargetingEnabledRequestDescriptor instead')
const GetTargetingEnabledRequest$json = const {
  '1': 'GetTargetingEnabledRequest',
};

/// Descriptor for `GetTargetingEnabledRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTargetingEnabledRequestDescriptor = $convert.base64Decode('ChpHZXRUYXJnZXRpbmdFbmFibGVkUmVxdWVzdA==');
@$core.Deprecated('Use getTargetingEnabledResponseDescriptor instead')
const GetTargetingEnabledResponse$json = const {
  '1': 'GetTargetingEnabledResponse',
  '2': const [
    const {'1': 'enabled', '3': 1, '4': 1, '5': 8, '10': 'enabled'},
  ],
};

/// Descriptor for `GetTargetingEnabledResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTargetingEnabledResponseDescriptor = $convert.base64Decode('ChtHZXRUYXJnZXRpbmdFbmFibGVkUmVzcG9uc2USGAoHZW5hYmxlZBgBIAEoCFIHZW5hYmxlZA==');
@$core.Deprecated('Use getBootedRequestDescriptor instead')
const GetBootedRequest$json = const {
  '1': 'GetBootedRequest',
};

/// Descriptor for `GetBootedRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getBootedRequestDescriptor = $convert.base64Decode('ChBHZXRCb290ZWRSZXF1ZXN0');
@$core.Deprecated('Use getBootedResponseDescriptor instead')
const GetBootedResponse$json = const {
  '1': 'GetBootedResponse',
  '2': const [
    const {'1': 'booted', '3': 1, '4': 1, '5': 8, '10': 'booted'},
  ],
};

/// Descriptor for `GetBootedResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getBootedResponseDescriptor = $convert.base64Decode('ChFHZXRCb290ZWRSZXNwb25zZRIWCgZib290ZWQYASABKAhSBmJvb3RlZA==');
