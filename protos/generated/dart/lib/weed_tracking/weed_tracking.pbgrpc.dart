///
//  Generated code. Do not modify.
//  source: weed_tracking/weed_tracking.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'weed_tracking.pb.dart' as $4;
export 'weed_tracking.pb.dart';

class WeedTrackingServiceClient extends $grpc.Client {
  static final _$ping = $grpc.ClientMethod<$4.PingRequest, $4.PongReply>(
      '/weed_tracking.WeedTrackingService/Ping',
      ($4.PingRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $4.PongReply.fromBuffer(value));
  static final _$getDetections =
      $grpc.ClientMethod<$4.GetDetectionsRequest, $4.GetDetectionsResponse>(
          '/weed_tracking.WeedTrackingService/GetDetections',
          ($4.GetDetectionsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $4.GetDetectionsResponse.fromBuffer(value));
  static final _$getTrajectoryMetadata = $grpc.ClientMethod<
          $4.GetTrajectoryMetadataRequest, $4.GetTrajectoryMetadataResponse>(
      '/weed_tracking.WeedTrackingService/GetTrajectoryMetadata',
      ($4.GetTrajectoryMetadataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $4.GetTrajectoryMetadataResponse.fromBuffer(value));
  static final _$updateBands = $grpc.ClientMethod<$4.Empty, $4.Empty>(
      '/weed_tracking.WeedTrackingService/UpdateBands',
      ($4.Empty value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$getBooted =
      $grpc.ClientMethod<$4.GetBootedRequest, $4.GetBootedResponse>(
          '/weed_tracking.WeedTrackingService/GetBooted',
          ($4.GetBootedRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $4.GetBootedResponse.fromBuffer(value));
  static final _$getCurrentTrajectories =
      $grpc.ClientMethod<$4.Empty, $4.DiagnosticsSnapshot>(
          '/weed_tracking.WeedTrackingService/GetCurrentTrajectories',
          ($4.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $4.DiagnosticsSnapshot.fromBuffer(value));
  static final _$startSavingCropLineDetectionReplay = $grpc.ClientMethod<
          $4.StartSavingCropLineDetectionReplayRequest, $4.Empty>(
      '/weed_tracking.WeedTrackingService/StartSavingCropLineDetectionReplay',
      ($4.StartSavingCropLineDetectionReplayRequest value) =>
          value.writeToBuffer(),
      ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$startRecordingDiagnostics =
      $grpc.ClientMethod<$4.RecordDiagnosticsRequest, $4.Empty>(
          '/weed_tracking.WeedTrackingService/StartRecordingDiagnostics',
          ($4.RecordDiagnosticsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$getDiagnosticsRecordingStatus =
      $grpc.ClientMethod<$4.Empty, $4.GetRecordingStatusResponse>(
          '/weed_tracking.WeedTrackingService/GetDiagnosticsRecordingStatus',
          ($4.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $4.GetRecordingStatusResponse.fromBuffer(value));
  static final _$removeRecordingsDirectory =
      $grpc.ClientMethod<$4.Empty, $4.Empty>(
          '/weed_tracking.WeedTrackingService/RemoveRecordingsDirectory',
          ($4.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$startRecordingAimbotInputs =
      $grpc.ClientMethod<$4.RecordAimbotInputRequest, $4.Empty>(
          '/weed_tracking.WeedTrackingService/StartRecordingAimbotInputs',
          ($4.RecordAimbotInputRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$getConclusionCounter =
      $grpc.ClientMethod<$4.Empty, $4.ConclusionCounter>(
          '/weed_tracking.WeedTrackingService/GetConclusionCounter',
          ($4.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $4.ConclusionCounter.fromBuffer(value));
  static final _$getBands = $grpc.ClientMethod<$4.Empty, $4.BandDefinitions>(
      '/weed_tracking.WeedTrackingService/GetBands',
      ($4.Empty value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $4.BandDefinitions.fromBuffer(value));
  static final _$startPlantCaptcha = $grpc.ClientMethod<$4.Empty, $4.Empty>(
      '/weed_tracking.WeedTrackingService/StartPlantCaptcha',
      ($4.Empty value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$getPlantCaptchaStatus =
      $grpc.ClientMethod<$4.Empty, $4.PlantCaptchaStatusResponse>(
          '/weed_tracking.WeedTrackingService/GetPlantCaptchaStatus',
          ($4.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $4.PlantCaptchaStatusResponse.fromBuffer(value));
  static final _$removePlantCaptchaDirectory =
      $grpc.ClientMethod<$4.Empty, $4.Empty>(
          '/weed_tracking.WeedTrackingService/RemovePlantCaptchaDirectory',
          ($4.Empty value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$cancelPlantCaptcha = $grpc.ClientMethod<$4.Empty, $4.Empty>(
      '/weed_tracking.WeedTrackingService/CancelPlantCaptcha',
      ($4.Empty value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $4.Empty.fromBuffer(value));
  static final _$getTargetingEnabled = $grpc.ClientMethod<
          $4.GetTargetingEnabledRequest, $4.GetTargetingEnabledResponse>(
      '/weed_tracking.WeedTrackingService/GetTargetingEnabled',
      ($4.GetTargetingEnabledRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $4.GetTargetingEnabledResponse.fromBuffer(value));

  WeedTrackingServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$4.PongReply> ping($4.PingRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$ping, request, options: options);
  }

  $grpc.ResponseFuture<$4.GetDetectionsResponse> getDetections(
      $4.GetDetectionsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDetections, request, options: options);
  }

  $grpc.ResponseFuture<$4.GetTrajectoryMetadataResponse> getTrajectoryMetadata(
      $4.GetTrajectoryMetadataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTrajectoryMetadata, request, options: options);
  }

  $grpc.ResponseFuture<$4.Empty> updateBands($4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$updateBands, request, options: options);
  }

  $grpc.ResponseFuture<$4.GetBootedResponse> getBooted(
      $4.GetBootedRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getBooted, request, options: options);
  }

  $grpc.ResponseFuture<$4.DiagnosticsSnapshot> getCurrentTrajectories(
      $4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCurrentTrajectories, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.Empty> startSavingCropLineDetectionReplay(
      $4.StartSavingCropLineDetectionReplayRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$startSavingCropLineDetectionReplay, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.Empty> startRecordingDiagnostics(
      $4.RecordDiagnosticsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$startRecordingDiagnostics, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.GetRecordingStatusResponse>
      getDiagnosticsRecordingStatus($4.Empty request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDiagnosticsRecordingStatus, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.Empty> removeRecordingsDirectory($4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$removeRecordingsDirectory, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.Empty> startRecordingAimbotInputs(
      $4.RecordAimbotInputRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$startRecordingAimbotInputs, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.ConclusionCounter> getConclusionCounter(
      $4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getConclusionCounter, request, options: options);
  }

  $grpc.ResponseFuture<$4.BandDefinitions> getBands($4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getBands, request, options: options);
  }

  $grpc.ResponseFuture<$4.Empty> startPlantCaptcha($4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$startPlantCaptcha, request, options: options);
  }

  $grpc.ResponseFuture<$4.PlantCaptchaStatusResponse> getPlantCaptchaStatus(
      $4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getPlantCaptchaStatus, request, options: options);
  }

  $grpc.ResponseFuture<$4.Empty> removePlantCaptchaDirectory($4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$removePlantCaptchaDirectory, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.Empty> cancelPlantCaptcha($4.Empty request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$cancelPlantCaptcha, request, options: options);
  }

  $grpc.ResponseFuture<$4.GetTargetingEnabledResponse> getTargetingEnabled(
      $4.GetTargetingEnabledRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTargetingEnabled, request, options: options);
  }
}

abstract class WeedTrackingServiceBase extends $grpc.Service {
  $core.String get $name => 'weed_tracking.WeedTrackingService';

  WeedTrackingServiceBase() {
    $addMethod($grpc.ServiceMethod<$4.PingRequest, $4.PongReply>(
        'Ping',
        ping_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.PingRequest.fromBuffer(value),
        ($4.PongReply value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$4.GetDetectionsRequest, $4.GetDetectionsResponse>(
            'GetDetections',
            getDetections_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $4.GetDetectionsRequest.fromBuffer(value),
            ($4.GetDetectionsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.GetTrajectoryMetadataRequest,
            $4.GetTrajectoryMetadataResponse>(
        'GetTrajectoryMetadata',
        getTrajectoryMetadata_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $4.GetTrajectoryMetadataRequest.fromBuffer(value),
        ($4.GetTrajectoryMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.Empty>(
        'UpdateBands',
        updateBands_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.GetBootedRequest, $4.GetBootedResponse>(
        'GetBooted',
        getBooted_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.GetBootedRequest.fromBuffer(value),
        ($4.GetBootedResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.DiagnosticsSnapshot>(
        'GetCurrentTrajectories',
        getCurrentTrajectories_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.DiagnosticsSnapshot value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.StartSavingCropLineDetectionReplayRequest,
            $4.Empty>(
        'StartSavingCropLineDetectionReplay',
        startSavingCropLineDetectionReplay_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $4.StartSavingCropLineDetectionReplayRequest.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.RecordDiagnosticsRequest, $4.Empty>(
        'StartRecordingDiagnostics',
        startRecordingDiagnostics_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $4.RecordDiagnosticsRequest.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.GetRecordingStatusResponse>(
        'GetDiagnosticsRecordingStatus',
        getDiagnosticsRecordingStatus_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.GetRecordingStatusResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.Empty>(
        'RemoveRecordingsDirectory',
        removeRecordingsDirectory_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.RecordAimbotInputRequest, $4.Empty>(
        'StartRecordingAimbotInputs',
        startRecordingAimbotInputs_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $4.RecordAimbotInputRequest.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.ConclusionCounter>(
        'GetConclusionCounter',
        getConclusionCounter_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.ConclusionCounter value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.BandDefinitions>(
        'GetBands',
        getBands_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.BandDefinitions value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.Empty>(
        'StartPlantCaptcha',
        startPlantCaptcha_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.PlantCaptchaStatusResponse>(
        'GetPlantCaptchaStatus',
        getPlantCaptchaStatus_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.PlantCaptchaStatusResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.Empty>(
        'RemovePlantCaptchaDirectory',
        removePlantCaptchaDirectory_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.Empty, $4.Empty>(
        'CancelPlantCaptcha',
        cancelPlantCaptcha_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $4.Empty.fromBuffer(value),
        ($4.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$4.GetTargetingEnabledRequest,
            $4.GetTargetingEnabledResponse>(
        'GetTargetingEnabled',
        getTargetingEnabled_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $4.GetTargetingEnabledRequest.fromBuffer(value),
        ($4.GetTargetingEnabledResponse value) => value.writeToBuffer()));
  }

  $async.Future<$4.PongReply> ping_Pre(
      $grpc.ServiceCall call, $async.Future<$4.PingRequest> request) async {
    return ping(call, await request);
  }

  $async.Future<$4.GetDetectionsResponse> getDetections_Pre(
      $grpc.ServiceCall call,
      $async.Future<$4.GetDetectionsRequest> request) async {
    return getDetections(call, await request);
  }

  $async.Future<$4.GetTrajectoryMetadataResponse> getTrajectoryMetadata_Pre(
      $grpc.ServiceCall call,
      $async.Future<$4.GetTrajectoryMetadataRequest> request) async {
    return getTrajectoryMetadata(call, await request);
  }

  $async.Future<$4.Empty> updateBands_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return updateBands(call, await request);
  }

  $async.Future<$4.GetBootedResponse> getBooted_Pre($grpc.ServiceCall call,
      $async.Future<$4.GetBootedRequest> request) async {
    return getBooted(call, await request);
  }

  $async.Future<$4.DiagnosticsSnapshot> getCurrentTrajectories_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return getCurrentTrajectories(call, await request);
  }

  $async.Future<$4.Empty> startSavingCropLineDetectionReplay_Pre(
      $grpc.ServiceCall call,
      $async.Future<$4.StartSavingCropLineDetectionReplayRequest>
          request) async {
    return startSavingCropLineDetectionReplay(call, await request);
  }

  $async.Future<$4.Empty> startRecordingDiagnostics_Pre($grpc.ServiceCall call,
      $async.Future<$4.RecordDiagnosticsRequest> request) async {
    return startRecordingDiagnostics(call, await request);
  }

  $async.Future<$4.GetRecordingStatusResponse>
      getDiagnosticsRecordingStatus_Pre(
          $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return getDiagnosticsRecordingStatus(call, await request);
  }

  $async.Future<$4.Empty> removeRecordingsDirectory_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return removeRecordingsDirectory(call, await request);
  }

  $async.Future<$4.Empty> startRecordingAimbotInputs_Pre($grpc.ServiceCall call,
      $async.Future<$4.RecordAimbotInputRequest> request) async {
    return startRecordingAimbotInputs(call, await request);
  }

  $async.Future<$4.ConclusionCounter> getConclusionCounter_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return getConclusionCounter(call, await request);
  }

  $async.Future<$4.BandDefinitions> getBands_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return getBands(call, await request);
  }

  $async.Future<$4.Empty> startPlantCaptcha_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return startPlantCaptcha(call, await request);
  }

  $async.Future<$4.PlantCaptchaStatusResponse> getPlantCaptchaStatus_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return getPlantCaptchaStatus(call, await request);
  }

  $async.Future<$4.Empty> removePlantCaptchaDirectory_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return removePlantCaptchaDirectory(call, await request);
  }

  $async.Future<$4.Empty> cancelPlantCaptcha_Pre(
      $grpc.ServiceCall call, $async.Future<$4.Empty> request) async {
    return cancelPlantCaptcha(call, await request);
  }

  $async.Future<$4.GetTargetingEnabledResponse> getTargetingEnabled_Pre(
      $grpc.ServiceCall call,
      $async.Future<$4.GetTargetingEnabledRequest> request) async {
    return getTargetingEnabled(call, await request);
  }

  $async.Future<$4.PongReply> ping(
      $grpc.ServiceCall call, $4.PingRequest request);
  $async.Future<$4.GetDetectionsResponse> getDetections(
      $grpc.ServiceCall call, $4.GetDetectionsRequest request);
  $async.Future<$4.GetTrajectoryMetadataResponse> getTrajectoryMetadata(
      $grpc.ServiceCall call, $4.GetTrajectoryMetadataRequest request);
  $async.Future<$4.Empty> updateBands($grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.GetBootedResponse> getBooted(
      $grpc.ServiceCall call, $4.GetBootedRequest request);
  $async.Future<$4.DiagnosticsSnapshot> getCurrentTrajectories(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.Empty> startSavingCropLineDetectionReplay(
      $grpc.ServiceCall call,
      $4.StartSavingCropLineDetectionReplayRequest request);
  $async.Future<$4.Empty> startRecordingDiagnostics(
      $grpc.ServiceCall call, $4.RecordDiagnosticsRequest request);
  $async.Future<$4.GetRecordingStatusResponse> getDiagnosticsRecordingStatus(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.Empty> removeRecordingsDirectory(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.Empty> startRecordingAimbotInputs(
      $grpc.ServiceCall call, $4.RecordAimbotInputRequest request);
  $async.Future<$4.ConclusionCounter> getConclusionCounter(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.BandDefinitions> getBands(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.Empty> startPlantCaptcha(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.PlantCaptchaStatusResponse> getPlantCaptchaStatus(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.Empty> removePlantCaptchaDirectory(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.Empty> cancelPlantCaptcha(
      $grpc.ServiceCall call, $4.Empty request);
  $async.Future<$4.GetTargetingEnabledResponse> getTargetingEnabled(
      $grpc.ServiceCall call, $4.GetTargetingEnabledRequest request);
}
