///
//  Generated code. Do not modify.
//  source: rtc/location_history.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'location_history.pb.dart' as $56;
import '../google/protobuf/empty.pb.dart' as $53;
export 'location_history.pb.dart';

class LocationHistoryClient extends $grpc.Client {
  static final _$logLocationHistory =
      $grpc.ClientMethod<$56.LogLocationHistoryRequest, $53.Empty>(
          '/carbon.rtc.LocationHistory/LogLocationHistory',
          ($56.LogLocationHistoryRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $53.Empty.fromBuffer(value));
  static final _$listRobots =
      $grpc.ClientMethod<$56.ListRobotsRequest, $56.ListRobotsResponse>(
          '/carbon.rtc.LocationHistory/ListRobots',
          ($56.ListRobotsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $56.ListRobotsResponse.fromBuffer(value));
  static final _$listLocationHistory = $grpc.ClientMethod<
          $56.ListLocationHistoryRequest, $56.ListLocationHistoryResponse>(
      '/carbon.rtc.LocationHistory/ListLocationHistory',
      ($56.ListLocationHistoryRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $56.ListLocationHistoryResponse.fromBuffer(value));
  static final _$streamLocation =
      $grpc.ClientMethod<$56.LocationHistoryRecord, $53.Empty>(
          '/carbon.rtc.LocationHistory/StreamLocation',
          ($56.LocationHistoryRecord value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $53.Empty.fromBuffer(value));

  LocationHistoryClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$53.Empty> logLocationHistory(
      $56.LogLocationHistoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$logLocationHistory, request, options: options);
  }

  $grpc.ResponseFuture<$56.ListRobotsResponse> listRobots(
      $56.ListRobotsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listRobots, request, options: options);
  }

  $grpc.ResponseFuture<$56.ListLocationHistoryResponse> listLocationHistory(
      $56.ListLocationHistoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listLocationHistory, request, options: options);
  }

  $grpc.ResponseFuture<$53.Empty> streamLocation(
      $async.Stream<$56.LocationHistoryRecord> request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$streamLocation, request, options: options)
        .single;
  }
}

abstract class LocationHistoryServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.LocationHistory';

  LocationHistoryServiceBase() {
    $addMethod($grpc.ServiceMethod<$56.LogLocationHistoryRequest, $53.Empty>(
        'LogLocationHistory',
        logLocationHistory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $56.LogLocationHistoryRequest.fromBuffer(value),
        ($53.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$56.ListRobotsRequest, $56.ListRobotsResponse>(
            'ListRobots',
            listRobots_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $56.ListRobotsRequest.fromBuffer(value),
            ($56.ListRobotsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$56.ListLocationHistoryRequest,
            $56.ListLocationHistoryResponse>(
        'ListLocationHistory',
        listLocationHistory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $56.ListLocationHistoryRequest.fromBuffer(value),
        ($56.ListLocationHistoryResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$56.LocationHistoryRecord, $53.Empty>(
        'StreamLocation',
        streamLocation,
        true,
        false,
        ($core.List<$core.int> value) =>
            $56.LocationHistoryRecord.fromBuffer(value),
        ($53.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$53.Empty> logLocationHistory_Pre($grpc.ServiceCall call,
      $async.Future<$56.LogLocationHistoryRequest> request) async {
    return logLocationHistory(call, await request);
  }

  $async.Future<$56.ListRobotsResponse> listRobots_Pre($grpc.ServiceCall call,
      $async.Future<$56.ListRobotsRequest> request) async {
    return listRobots(call, await request);
  }

  $async.Future<$56.ListLocationHistoryResponse> listLocationHistory_Pre(
      $grpc.ServiceCall call,
      $async.Future<$56.ListLocationHistoryRequest> request) async {
    return listLocationHistory(call, await request);
  }

  $async.Future<$53.Empty> logLocationHistory(
      $grpc.ServiceCall call, $56.LogLocationHistoryRequest request);
  $async.Future<$56.ListRobotsResponse> listRobots(
      $grpc.ServiceCall call, $56.ListRobotsRequest request);
  $async.Future<$56.ListLocationHistoryResponse> listLocationHistory(
      $grpc.ServiceCall call, $56.ListLocationHistoryRequest request);
  $async.Future<$53.Empty> streamLocation(
      $grpc.ServiceCall call, $async.Stream<$56.LocationHistoryRecord> request);
}
