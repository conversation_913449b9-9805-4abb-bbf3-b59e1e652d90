///
//  Generated code. Do not modify.
//  source: rtc/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'jobs.pb.dart' as $55;
export 'jobs.pb.dart';

class JobServiceClient extends $grpc.Client {
  static final _$createIntervention = $grpc.ClientMethod<
          $55.CreateInterventionRequest, $55.CreateInterventionResponse>(
      '/carbon.rtc.JobService/CreateIntervention',
      ($55.CreateInterventionRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $55.CreateInterventionResponse.fromBuffer(value));
  static final _$getActiveTask =
      $grpc.ClientMethod<$55.GetActiveTaskRequest, $55.GetActiveTaskResponse>(
          '/carbon.rtc.JobService/GetActiveTask',
          ($55.GetActiveTaskRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $55.GetActiveTaskResponse.fromBuffer(value));
  static final _$getTask =
      $grpc.ClientMethod<$55.GetTaskRequest, $55.GetTaskResponse>(
          '/carbon.rtc.JobService/GetTask',
          ($55.GetTaskRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $55.GetTaskResponse.fromBuffer(value));
  static final _$getNextActiveObjective = $grpc.ClientMethod<
          $55.GetNextActiveObjectiveRequest,
          $55.GetNextActiveObjectiveResponse>(
      '/carbon.rtc.JobService/GetNextActiveObjective',
      ($55.GetNextActiveObjectiveRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $55.GetNextActiveObjectiveResponse.fromBuffer(value));
  static final _$updateTask =
      $grpc.ClientMethod<$55.UpdateTaskRequest, $55.UpdateTaskResponse>(
          '/carbon.rtc.JobService/UpdateTask',
          ($55.UpdateTaskRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $55.UpdateTaskResponse.fromBuffer(value));

  JobServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$55.CreateInterventionResponse> createIntervention(
      $55.CreateInterventionRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$createIntervention, request, options: options);
  }

  $grpc.ResponseFuture<$55.GetActiveTaskResponse> getActiveTask(
      $55.GetActiveTaskRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getActiveTask, request, options: options);
  }

  $grpc.ResponseFuture<$55.GetTaskResponse> getTask($55.GetTaskRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTask, request, options: options);
  }

  $grpc.ResponseFuture<$55.GetNextActiveObjectiveResponse>
      getNextActiveObjective($55.GetNextActiveObjectiveRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextActiveObjective, request,
        options: options);
  }

  $grpc.ResponseFuture<$55.UpdateTaskResponse> updateTask(
      $55.UpdateTaskRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$updateTask, request, options: options);
  }
}

abstract class JobServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.JobService';

  JobServiceBase() {
    $addMethod($grpc.ServiceMethod<$55.CreateInterventionRequest,
            $55.CreateInterventionResponse>(
        'CreateIntervention',
        createIntervention_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $55.CreateInterventionRequest.fromBuffer(value),
        ($55.CreateInterventionResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$55.GetActiveTaskRequest,
            $55.GetActiveTaskResponse>(
        'GetActiveTask',
        getActiveTask_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $55.GetActiveTaskRequest.fromBuffer(value),
        ($55.GetActiveTaskResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$55.GetTaskRequest, $55.GetTaskResponse>(
        'GetTask',
        getTask_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $55.GetTaskRequest.fromBuffer(value),
        ($55.GetTaskResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$55.GetNextActiveObjectiveRequest,
            $55.GetNextActiveObjectiveResponse>(
        'GetNextActiveObjective',
        getNextActiveObjective_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $55.GetNextActiveObjectiveRequest.fromBuffer(value),
        ($55.GetNextActiveObjectiveResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$55.UpdateTaskRequest, $55.UpdateTaskResponse>(
            'UpdateTask',
            updateTask_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $55.UpdateTaskRequest.fromBuffer(value),
            ($55.UpdateTaskResponse value) => value.writeToBuffer()));
  }

  $async.Future<$55.CreateInterventionResponse> createIntervention_Pre(
      $grpc.ServiceCall call,
      $async.Future<$55.CreateInterventionRequest> request) async {
    return createIntervention(call, await request);
  }

  $async.Future<$55.GetActiveTaskResponse> getActiveTask_Pre(
      $grpc.ServiceCall call,
      $async.Future<$55.GetActiveTaskRequest> request) async {
    return getActiveTask(call, await request);
  }

  $async.Future<$55.GetTaskResponse> getTask_Pre(
      $grpc.ServiceCall call, $async.Future<$55.GetTaskRequest> request) async {
    return getTask(call, await request);
  }

  $async.Future<$55.GetNextActiveObjectiveResponse> getNextActiveObjective_Pre(
      $grpc.ServiceCall call,
      $async.Future<$55.GetNextActiveObjectiveRequest> request) async {
    return getNextActiveObjective(call, await request);
  }

  $async.Future<$55.UpdateTaskResponse> updateTask_Pre($grpc.ServiceCall call,
      $async.Future<$55.UpdateTaskRequest> request) async {
    return updateTask(call, await request);
  }

  $async.Future<$55.CreateInterventionResponse> createIntervention(
      $grpc.ServiceCall call, $55.CreateInterventionRequest request);
  $async.Future<$55.GetActiveTaskResponse> getActiveTask(
      $grpc.ServiceCall call, $55.GetActiveTaskRequest request);
  $async.Future<$55.GetTaskResponse> getTask(
      $grpc.ServiceCall call, $55.GetTaskRequest request);
  $async.Future<$55.GetNextActiveObjectiveResponse> getNextActiveObjective(
      $grpc.ServiceCall call, $55.GetNextActiveObjectiveRequest request);
  $async.Future<$55.UpdateTaskResponse> updateTask(
      $grpc.ServiceCall call, $55.UpdateTaskRequest request);
}
