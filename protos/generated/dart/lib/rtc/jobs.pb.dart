///
//  Generated code. Do not modify.
//  source: rtc/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/struct.pb.dart' as $83;
import '../geo/geo.pb.dart' as $75;
import '../google/protobuf/timestamp.pb.dart' as $70;
import '../google/protobuf/duration.pb.dart' as $84;
import '../google/protobuf/empty.pb.dart' as $53;

import 'jobs.pbenum.dart';

export 'jobs.pbenum.dart';

class Objective extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Objective', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressPercent', $pb.PbFieldType.O3)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'priority', $pb.PbFieldType.O3)
    ..e<Objective_ObjectiveType>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: Objective_ObjectiveType.OBJECTIVE_TYPE_UNSPECIFIED, valueOf: Objective_ObjectiveType.valueOf, enumValues: Objective_ObjectiveType.values)
    ..aOM<$83.Struct>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'data', subBuilder: $83.Struct.create)
    ..aOM<ObjectiveAssignment>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignment', subBuilder: ObjectiveAssignment.create)
    ..a<$fixnum.Int64>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  Objective._() : super();
  factory Objective({
    $fixnum.Int64? id,
    $core.String? name,
    $core.String? description,
    $core.int? progressPercent,
    $core.int? priority,
    Objective_ObjectiveType? type,
    $83.Struct? data,
    ObjectiveAssignment? assignment,
    $fixnum.Int64? jobId,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (name != null) {
      _result.name = name;
    }
    if (description != null) {
      _result.description = description;
    }
    if (progressPercent != null) {
      _result.progressPercent = progressPercent;
    }
    if (priority != null) {
      _result.priority = priority;
    }
    if (type != null) {
      _result.type = type;
    }
    if (data != null) {
      _result.data = data;
    }
    if (assignment != null) {
      _result.assignment = assignment;
    }
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory Objective.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Objective.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Objective clone() => Objective()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Objective copyWith(void Function(Objective) updates) => super.copyWith((message) => updates(message as Objective)) as Objective; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Objective create() => Objective._();
  Objective createEmptyInstance() => create();
  static $pb.PbList<Objective> createRepeated() => $pb.PbList<Objective>();
  @$core.pragma('dart2js:noInline')
  static Objective getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Objective>(create);
  static Objective? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get id => $_getI64(0);
  @$pb.TagNumber(1)
  set id($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get description => $_getSZ(2);
  @$pb.TagNumber(3)
  set description($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDescription() => $_has(2);
  @$pb.TagNumber(3)
  void clearDescription() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get progressPercent => $_getIZ(3);
  @$pb.TagNumber(4)
  set progressPercent($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasProgressPercent() => $_has(3);
  @$pb.TagNumber(4)
  void clearProgressPercent() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get priority => $_getIZ(4);
  @$pb.TagNumber(5)
  set priority($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPriority() => $_has(4);
  @$pb.TagNumber(5)
  void clearPriority() => clearField(5);

  @$pb.TagNumber(6)
  Objective_ObjectiveType get type => $_getN(5);
  @$pb.TagNumber(6)
  set type(Objective_ObjectiveType v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasType() => $_has(5);
  @$pb.TagNumber(6)
  void clearType() => clearField(6);

  @$pb.TagNumber(7)
  $83.Struct get data => $_getN(6);
  @$pb.TagNumber(7)
  set data($83.Struct v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasData() => $_has(6);
  @$pb.TagNumber(7)
  void clearData() => clearField(7);
  @$pb.TagNumber(7)
  $83.Struct ensureData() => $_ensure(6);

  @$pb.TagNumber(8)
  ObjectiveAssignment get assignment => $_getN(7);
  @$pb.TagNumber(8)
  set assignment(ObjectiveAssignment v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasAssignment() => $_has(7);
  @$pb.TagNumber(8)
  void clearAssignment() => clearField(8);
  @$pb.TagNumber(8)
  ObjectiveAssignment ensureAssignment() => $_ensure(7);

  @$pb.TagNumber(9)
  $fixnum.Int64 get jobId => $_getI64(8);
  @$pb.TagNumber(9)
  set jobId($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasJobId() => $_has(8);
  @$pb.TagNumber(9)
  void clearJobId() => clearField(9);
}

class LaserWeedRowObjectiveData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LaserWeedRowObjectiveData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowNum', $pb.PbFieldType.OU3)
    ..aOM<$75.AbLine>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'abLine', subBuilder: $75.AbLine.create)
    ..hasRequiredFields = false
  ;

  LaserWeedRowObjectiveData._() : super();
  factory LaserWeedRowObjectiveData({
    $core.int? rowNum,
    $75.AbLine? abLine,
  }) {
    final _result = create();
    if (rowNum != null) {
      _result.rowNum = rowNum;
    }
    if (abLine != null) {
      _result.abLine = abLine;
    }
    return _result;
  }
  factory LaserWeedRowObjectiveData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LaserWeedRowObjectiveData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LaserWeedRowObjectiveData clone() => LaserWeedRowObjectiveData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LaserWeedRowObjectiveData copyWith(void Function(LaserWeedRowObjectiveData) updates) => super.copyWith((message) => updates(message as LaserWeedRowObjectiveData)) as LaserWeedRowObjectiveData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LaserWeedRowObjectiveData create() => LaserWeedRowObjectiveData._();
  LaserWeedRowObjectiveData createEmptyInstance() => create();
  static $pb.PbList<LaserWeedRowObjectiveData> createRepeated() => $pb.PbList<LaserWeedRowObjectiveData>();
  @$core.pragma('dart2js:noInline')
  static LaserWeedRowObjectiveData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LaserWeedRowObjectiveData>(create);
  static LaserWeedRowObjectiveData? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get rowNum => $_getIZ(0);
  @$pb.TagNumber(1)
  set rowNum($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRowNum() => $_has(0);
  @$pb.TagNumber(1)
  void clearRowNum() => clearField(1);

  @$pb.TagNumber(2)
  $75.AbLine get abLine => $_getN(1);
  @$pb.TagNumber(2)
  set abLine($75.AbLine v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAbLine() => $_has(1);
  @$pb.TagNumber(2)
  void clearAbLine() => clearField(2);
  @$pb.TagNumber(2)
  $75.AbLine ensureAbLine() => $_ensure(1);
}

class ObjectiveList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ObjectiveList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<Objective>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectives', $pb.PbFieldType.PM, subBuilder: Objective.create)
    ..hasRequiredFields = false
  ;

  ObjectiveList._() : super();
  factory ObjectiveList({
    $core.Iterable<Objective>? objectives,
  }) {
    final _result = create();
    if (objectives != null) {
      _result.objectives.addAll(objectives);
    }
    return _result;
  }
  factory ObjectiveList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ObjectiveList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ObjectiveList clone() => ObjectiveList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ObjectiveList copyWith(void Function(ObjectiveList) updates) => super.copyWith((message) => updates(message as ObjectiveList)) as ObjectiveList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ObjectiveList create() => ObjectiveList._();
  ObjectiveList createEmptyInstance() => create();
  static $pb.PbList<ObjectiveList> createRepeated() => $pb.PbList<ObjectiveList>();
  @$core.pragma('dart2js:noInline')
  static ObjectiveList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ObjectiveList>(create);
  static ObjectiveList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Objective> get objectives => $_getList(0);
}

class ListObjectivesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListObjectivesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pc<Objective>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectives', $pb.PbFieldType.PM, subBuilder: Objective.create)
    ..hasRequiredFields = false
  ;

  ListObjectivesResponse._() : super();
  factory ListObjectivesResponse({
    $core.String? pageToken,
    $core.Iterable<Objective>? objectives,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (objectives != null) {
      _result.objectives.addAll(objectives);
    }
    return _result;
  }
  factory ListObjectivesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListObjectivesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListObjectivesResponse clone() => ListObjectivesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListObjectivesResponse copyWith(void Function(ListObjectivesResponse) updates) => super.copyWith((message) => updates(message as ListObjectivesResponse)) as ListObjectivesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListObjectivesResponse create() => ListObjectivesResponse._();
  ListObjectivesResponse createEmptyInstance() => create();
  static $pb.PbList<ListObjectivesResponse> createRepeated() => $pb.PbList<ListObjectivesResponse>();
  @$core.pragma('dart2js:noInline')
  static ListObjectivesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListObjectivesResponse>(create);
  static ListObjectivesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<Objective> get objectives => $_getList(1);
}

class GetNextActiveObjectiveRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveObjectiveRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectiveId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  GetNextActiveObjectiveRequest._() : super();
  factory GetNextActiveObjectiveRequest({
    $fixnum.Int64? objectiveId,
  }) {
    final _result = create();
    if (objectiveId != null) {
      _result.objectiveId = objectiveId;
    }
    return _result;
  }
  factory GetNextActiveObjectiveRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveObjectiveRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveObjectiveRequest clone() => GetNextActiveObjectiveRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveObjectiveRequest copyWith(void Function(GetNextActiveObjectiveRequest) updates) => super.copyWith((message) => updates(message as GetNextActiveObjectiveRequest)) as GetNextActiveObjectiveRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveObjectiveRequest create() => GetNextActiveObjectiveRequest._();
  GetNextActiveObjectiveRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveObjectiveRequest> createRepeated() => $pb.PbList<GetNextActiveObjectiveRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveObjectiveRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveObjectiveRequest>(create);
  static GetNextActiveObjectiveRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get objectiveId => $_getI64(0);
  @$pb.TagNumber(1)
  set objectiveId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasObjectiveId() => $_has(0);
  @$pb.TagNumber(1)
  void clearObjectiveId() => clearField(1);
}

class GetNextActiveObjectiveResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveObjectiveResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<Objective>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objective', subBuilder: Objective.create)
    ..hasRequiredFields = false
  ;

  GetNextActiveObjectiveResponse._() : super();
  factory GetNextActiveObjectiveResponse({
    Objective? objective,
  }) {
    final _result = create();
    if (objective != null) {
      _result.objective = objective;
    }
    return _result;
  }
  factory GetNextActiveObjectiveResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveObjectiveResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveObjectiveResponse clone() => GetNextActiveObjectiveResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveObjectiveResponse copyWith(void Function(GetNextActiveObjectiveResponse) updates) => super.copyWith((message) => updates(message as GetNextActiveObjectiveResponse)) as GetNextActiveObjectiveResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveObjectiveResponse create() => GetNextActiveObjectiveResponse._();
  GetNextActiveObjectiveResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveObjectiveResponse> createRepeated() => $pb.PbList<GetNextActiveObjectiveResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveObjectiveResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveObjectiveResponse>(create);
  static GetNextActiveObjectiveResponse? _defaultInstance;

  @$pb.TagNumber(2)
  Objective get objective => $_getN(0);
  @$pb.TagNumber(2)
  set objective(Objective v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasObjective() => $_has(0);
  @$pb.TagNumber(2)
  void clearObjective() => clearField(2);
  @$pb.TagNumber(2)
  Objective ensureObjective() => $_ensure(0);
}

class ObjectiveAssignment extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ObjectiveAssignment', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectiveId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..hasRequiredFields = false
  ;

  ObjectiveAssignment._() : super();
  factory ObjectiveAssignment({
    $fixnum.Int64? id,
    $fixnum.Int64? objectiveId,
    $core.String? robotSerial,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (objectiveId != null) {
      _result.objectiveId = objectiveId;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    return _result;
  }
  factory ObjectiveAssignment.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ObjectiveAssignment.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ObjectiveAssignment clone() => ObjectiveAssignment()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ObjectiveAssignment copyWith(void Function(ObjectiveAssignment) updates) => super.copyWith((message) => updates(message as ObjectiveAssignment)) as ObjectiveAssignment; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ObjectiveAssignment create() => ObjectiveAssignment._();
  ObjectiveAssignment createEmptyInstance() => create();
  static $pb.PbList<ObjectiveAssignment> createRepeated() => $pb.PbList<ObjectiveAssignment>();
  @$core.pragma('dart2js:noInline')
  static ObjectiveAssignment getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ObjectiveAssignment>(create);
  static ObjectiveAssignment? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get id => $_getI64(0);
  @$pb.TagNumber(1)
  set id($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get objectiveId => $_getI64(1);
  @$pb.TagNumber(2)
  set objectiveId($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasObjectiveId() => $_has(1);
  @$pb.TagNumber(2)
  void clearObjectiveId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get robotSerial => $_getSZ(2);
  @$pb.TagNumber(3)
  set robotSerial($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotSerial() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotSerial() => clearField(3);
}

class ObjectiveAssignmentList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ObjectiveAssignmentList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<ObjectiveAssignment>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignments', $pb.PbFieldType.PM, subBuilder: ObjectiveAssignment.create)
    ..hasRequiredFields = false
  ;

  ObjectiveAssignmentList._() : super();
  factory ObjectiveAssignmentList({
    $core.Iterable<ObjectiveAssignment>? assignments,
  }) {
    final _result = create();
    if (assignments != null) {
      _result.assignments.addAll(assignments);
    }
    return _result;
  }
  factory ObjectiveAssignmentList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ObjectiveAssignmentList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ObjectiveAssignmentList clone() => ObjectiveAssignmentList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ObjectiveAssignmentList copyWith(void Function(ObjectiveAssignmentList) updates) => super.copyWith((message) => updates(message as ObjectiveAssignmentList)) as ObjectiveAssignmentList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ObjectiveAssignmentList create() => ObjectiveAssignmentList._();
  ObjectiveAssignmentList createEmptyInstance() => create();
  static $pb.PbList<ObjectiveAssignmentList> createRepeated() => $pb.PbList<ObjectiveAssignmentList>();
  @$core.pragma('dart2js:noInline')
  static ObjectiveAssignmentList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ObjectiveAssignmentList>(create);
  static ObjectiveAssignmentList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ObjectiveAssignment> get assignments => $_getList(0);
}

class ListObjectiveAssignmentsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListObjectiveAssignmentsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pc<ObjectiveAssignment>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignments', $pb.PbFieldType.PM, subBuilder: ObjectiveAssignment.create)
    ..hasRequiredFields = false
  ;

  ListObjectiveAssignmentsResponse._() : super();
  factory ListObjectiveAssignmentsResponse({
    $core.String? pageToken,
    $core.Iterable<ObjectiveAssignment>? assignments,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (assignments != null) {
      _result.assignments.addAll(assignments);
    }
    return _result;
  }
  factory ListObjectiveAssignmentsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListObjectiveAssignmentsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListObjectiveAssignmentsResponse clone() => ListObjectiveAssignmentsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListObjectiveAssignmentsResponse copyWith(void Function(ListObjectiveAssignmentsResponse) updates) => super.copyWith((message) => updates(message as ListObjectiveAssignmentsResponse)) as ListObjectiveAssignmentsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListObjectiveAssignmentsResponse create() => ListObjectiveAssignmentsResponse._();
  ListObjectiveAssignmentsResponse createEmptyInstance() => create();
  static $pb.PbList<ListObjectiveAssignmentsResponse> createRepeated() => $pb.PbList<ListObjectiveAssignmentsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListObjectiveAssignmentsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListObjectiveAssignmentsResponse>(create);
  static ListObjectiveAssignmentsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<ObjectiveAssignment> get assignments => $_getList(1);
}

class RobotWhitelistEntry extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotWhitelistEntry', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..hasRequiredFields = false
  ;

  RobotWhitelistEntry._() : super();
  factory RobotWhitelistEntry({
    $core.String? robotSerial,
  }) {
    final _result = create();
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    return _result;
  }
  factory RobotWhitelistEntry.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotWhitelistEntry.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotWhitelistEntry clone() => RobotWhitelistEntry()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotWhitelistEntry copyWith(void Function(RobotWhitelistEntry) updates) => super.copyWith((message) => updates(message as RobotWhitelistEntry)) as RobotWhitelistEntry; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotWhitelistEntry create() => RobotWhitelistEntry._();
  RobotWhitelistEntry createEmptyInstance() => create();
  static $pb.PbList<RobotWhitelistEntry> createRepeated() => $pb.PbList<RobotWhitelistEntry>();
  @$core.pragma('dart2js:noInline')
  static RobotWhitelistEntry getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotWhitelistEntry>(create);
  static RobotWhitelistEntry? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get robotSerial => $_getSZ(0);
  @$pb.TagNumber(1)
  set robotSerial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRobotSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearRobotSerial() => clearField(1);
}

class RobotWhitelist extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotWhitelist', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<RobotWhitelistEntry>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'entries', $pb.PbFieldType.PM, subBuilder: RobotWhitelistEntry.create)
    ..hasRequiredFields = false
  ;

  RobotWhitelist._() : super();
  factory RobotWhitelist({
    $core.Iterable<RobotWhitelistEntry>? entries,
  }) {
    final _result = create();
    if (entries != null) {
      _result.entries.addAll(entries);
    }
    return _result;
  }
  factory RobotWhitelist.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotWhitelist.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotWhitelist clone() => RobotWhitelist()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotWhitelist copyWith(void Function(RobotWhitelist) updates) => super.copyWith((message) => updates(message as RobotWhitelist)) as RobotWhitelist; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotWhitelist create() => RobotWhitelist._();
  RobotWhitelist createEmptyInstance() => create();
  static $pb.PbList<RobotWhitelist> createRepeated() => $pb.PbList<RobotWhitelist>();
  @$core.pragma('dart2js:noInline')
  static RobotWhitelist getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotWhitelist>(create);
  static RobotWhitelist? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<RobotWhitelistEntry> get entries => $_getList(0);
}

enum Task_TaskDetails {
  sequence, 
  manual, 
  goToAndFace, 
  followPath, 
  tractorState, 
  laserWeed, 
  stopAutonomy, 
  goToReversiblePath, 
  notSet
}

class Task extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, Task_TaskDetails> _Task_TaskDetailsByTag = {
    10 : Task_TaskDetails.sequence,
    11 : Task_TaskDetails.manual,
    12 : Task_TaskDetails.goToAndFace,
    13 : Task_TaskDetails.followPath,
    14 : Task_TaskDetails.tractorState,
    15 : Task_TaskDetails.laserWeed,
    17 : Task_TaskDetails.stopAutonomy,
    22 : Task_TaskDetails.goToReversiblePath,
    0 : Task_TaskDetails.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Task', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [10, 11, 12, 13, 14, 15, 17, 22])
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOM<$70.Timestamp>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$70.Timestamp>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$84.Duration>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expectedDuration', subBuilder: $84.Duration.create)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusInfo')
    ..pc<TractorState>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expectedTractorState', $pb.PbFieldType.PM, subBuilder: TractorState.create)
    ..e<State>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state', $pb.PbFieldType.OE, defaultOrMaker: State.STATE_UNSPECIFIED, valueOf: State.valueOf, enumValues: State.values)
    ..a<$core.int>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'priority', $pb.PbFieldType.O3)
    ..aOM<SequenceTask>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sequence', subBuilder: SequenceTask.create)
    ..aOM<ManualTask>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'manual', subBuilder: ManualTask.create)
    ..aOM<GoToAndFaceTask>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'goToAndFace', subBuilder: GoToAndFaceTask.create)
    ..aOM<FollowPathTask>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'followPath', subBuilder: FollowPathTask.create)
    ..aOM<SetTractorStateTask>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'tractorState', subBuilder: SetTractorStateTask.create)
    ..aOM<LaserWeedTask>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'laserWeed', subBuilder: LaserWeedTask.create)
    ..a<$fixnum.Int64>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectiveId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOM<StopAutonomyTask>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'stopAutonomy', subBuilder: StopAutonomyTask.create)
    ..aOM<$75.Point>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startHeading', $pb.PbFieldType.OD)
    ..aOM<$75.Point>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endHeading', $pb.PbFieldType.OD)
    ..aOM<GoToReversiblePathTask>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'goToReversiblePath', subBuilder: GoToReversiblePathTask.create)
    ..aOB(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'manuallyAssisted')
    ..aOM<$70.Timestamp>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressUpdatedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$75.Point>(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressHeading', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  Task._() : super();
  factory Task({
    $fixnum.Int64? id,
    $core.String? name,
    $70.Timestamp? startedAt,
    $70.Timestamp? endedAt,
    $84.Duration? expectedDuration,
    $core.String? statusInfo,
    $core.Iterable<TractorState>? expectedTractorState,
    State? state,
    $core.int? priority,
    SequenceTask? sequence,
    ManualTask? manual,
    GoToAndFaceTask? goToAndFace,
    FollowPathTask? followPath,
    SetTractorStateTask? tractorState,
    LaserWeedTask? laserWeed,
    $fixnum.Int64? objectiveId,
    StopAutonomyTask? stopAutonomy,
    $75.Point? startLocation,
    $core.double? startHeading,
    $75.Point? endLocation,
    $core.double? endHeading,
    GoToReversiblePathTask? goToReversiblePath,
    $core.bool? manuallyAssisted,
    $70.Timestamp? progressUpdatedAt,
    $75.Point? progressLocation,
    $core.double? progressHeading,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (name != null) {
      _result.name = name;
    }
    if (startedAt != null) {
      _result.startedAt = startedAt;
    }
    if (endedAt != null) {
      _result.endedAt = endedAt;
    }
    if (expectedDuration != null) {
      _result.expectedDuration = expectedDuration;
    }
    if (statusInfo != null) {
      _result.statusInfo = statusInfo;
    }
    if (expectedTractorState != null) {
      _result.expectedTractorState.addAll(expectedTractorState);
    }
    if (state != null) {
      _result.state = state;
    }
    if (priority != null) {
      _result.priority = priority;
    }
    if (sequence != null) {
      _result.sequence = sequence;
    }
    if (manual != null) {
      _result.manual = manual;
    }
    if (goToAndFace != null) {
      _result.goToAndFace = goToAndFace;
    }
    if (followPath != null) {
      _result.followPath = followPath;
    }
    if (tractorState != null) {
      _result.tractorState = tractorState;
    }
    if (laserWeed != null) {
      _result.laserWeed = laserWeed;
    }
    if (objectiveId != null) {
      _result.objectiveId = objectiveId;
    }
    if (stopAutonomy != null) {
      _result.stopAutonomy = stopAutonomy;
    }
    if (startLocation != null) {
      _result.startLocation = startLocation;
    }
    if (startHeading != null) {
      _result.startHeading = startHeading;
    }
    if (endLocation != null) {
      _result.endLocation = endLocation;
    }
    if (endHeading != null) {
      _result.endHeading = endHeading;
    }
    if (goToReversiblePath != null) {
      _result.goToReversiblePath = goToReversiblePath;
    }
    if (manuallyAssisted != null) {
      _result.manuallyAssisted = manuallyAssisted;
    }
    if (progressUpdatedAt != null) {
      _result.progressUpdatedAt = progressUpdatedAt;
    }
    if (progressLocation != null) {
      _result.progressLocation = progressLocation;
    }
    if (progressHeading != null) {
      _result.progressHeading = progressHeading;
    }
    return _result;
  }
  factory Task.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Task.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Task clone() => Task()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Task copyWith(void Function(Task) updates) => super.copyWith((message) => updates(message as Task)) as Task; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Task create() => Task._();
  Task createEmptyInstance() => create();
  static $pb.PbList<Task> createRepeated() => $pb.PbList<Task>();
  @$core.pragma('dart2js:noInline')
  static Task getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Task>(create);
  static Task? _defaultInstance;

  Task_TaskDetails whichTaskDetails() => _Task_TaskDetailsByTag[$_whichOneof(0)]!;
  void clearTaskDetails() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $fixnum.Int64 get id => $_getI64(0);
  @$pb.TagNumber(1)
  set id($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $70.Timestamp get startedAt => $_getN(2);
  @$pb.TagNumber(3)
  set startedAt($70.Timestamp v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartedAt() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartedAt() => clearField(3);
  @$pb.TagNumber(3)
  $70.Timestamp ensureStartedAt() => $_ensure(2);

  @$pb.TagNumber(4)
  $70.Timestamp get endedAt => $_getN(3);
  @$pb.TagNumber(4)
  set endedAt($70.Timestamp v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasEndedAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearEndedAt() => clearField(4);
  @$pb.TagNumber(4)
  $70.Timestamp ensureEndedAt() => $_ensure(3);

  @$pb.TagNumber(5)
  $84.Duration get expectedDuration => $_getN(4);
  @$pb.TagNumber(5)
  set expectedDuration($84.Duration v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasExpectedDuration() => $_has(4);
  @$pb.TagNumber(5)
  void clearExpectedDuration() => clearField(5);
  @$pb.TagNumber(5)
  $84.Duration ensureExpectedDuration() => $_ensure(4);

  @$pb.TagNumber(6)
  $core.String get statusInfo => $_getSZ(5);
  @$pb.TagNumber(6)
  set statusInfo($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasStatusInfo() => $_has(5);
  @$pb.TagNumber(6)
  void clearStatusInfo() => clearField(6);

  @$pb.TagNumber(7)
  $core.List<TractorState> get expectedTractorState => $_getList(6);

  @$pb.TagNumber(8)
  State get state => $_getN(7);
  @$pb.TagNumber(8)
  set state(State v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasState() => $_has(7);
  @$pb.TagNumber(8)
  void clearState() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get priority => $_getIZ(8);
  @$pb.TagNumber(9)
  set priority($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasPriority() => $_has(8);
  @$pb.TagNumber(9)
  void clearPriority() => clearField(9);

  @$pb.TagNumber(10)
  SequenceTask get sequence => $_getN(9);
  @$pb.TagNumber(10)
  set sequence(SequenceTask v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasSequence() => $_has(9);
  @$pb.TagNumber(10)
  void clearSequence() => clearField(10);
  @$pb.TagNumber(10)
  SequenceTask ensureSequence() => $_ensure(9);

  @$pb.TagNumber(11)
  ManualTask get manual => $_getN(10);
  @$pb.TagNumber(11)
  set manual(ManualTask v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasManual() => $_has(10);
  @$pb.TagNumber(11)
  void clearManual() => clearField(11);
  @$pb.TagNumber(11)
  ManualTask ensureManual() => $_ensure(10);

  @$pb.TagNumber(12)
  GoToAndFaceTask get goToAndFace => $_getN(11);
  @$pb.TagNumber(12)
  set goToAndFace(GoToAndFaceTask v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasGoToAndFace() => $_has(11);
  @$pb.TagNumber(12)
  void clearGoToAndFace() => clearField(12);
  @$pb.TagNumber(12)
  GoToAndFaceTask ensureGoToAndFace() => $_ensure(11);

  @$pb.TagNumber(13)
  FollowPathTask get followPath => $_getN(12);
  @$pb.TagNumber(13)
  set followPath(FollowPathTask v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasFollowPath() => $_has(12);
  @$pb.TagNumber(13)
  void clearFollowPath() => clearField(13);
  @$pb.TagNumber(13)
  FollowPathTask ensureFollowPath() => $_ensure(12);

  @$pb.TagNumber(14)
  SetTractorStateTask get tractorState => $_getN(13);
  @$pb.TagNumber(14)
  set tractorState(SetTractorStateTask v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasTractorState() => $_has(13);
  @$pb.TagNumber(14)
  void clearTractorState() => clearField(14);
  @$pb.TagNumber(14)
  SetTractorStateTask ensureTractorState() => $_ensure(13);

  @$pb.TagNumber(15)
  LaserWeedTask get laserWeed => $_getN(14);
  @$pb.TagNumber(15)
  set laserWeed(LaserWeedTask v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasLaserWeed() => $_has(14);
  @$pb.TagNumber(15)
  void clearLaserWeed() => clearField(15);
  @$pb.TagNumber(15)
  LaserWeedTask ensureLaserWeed() => $_ensure(14);

  @$pb.TagNumber(16)
  $fixnum.Int64 get objectiveId => $_getI64(15);
  @$pb.TagNumber(16)
  set objectiveId($fixnum.Int64 v) { $_setInt64(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasObjectiveId() => $_has(15);
  @$pb.TagNumber(16)
  void clearObjectiveId() => clearField(16);

  @$pb.TagNumber(17)
  StopAutonomyTask get stopAutonomy => $_getN(16);
  @$pb.TagNumber(17)
  set stopAutonomy(StopAutonomyTask v) { setField(17, v); }
  @$pb.TagNumber(17)
  $core.bool hasStopAutonomy() => $_has(16);
  @$pb.TagNumber(17)
  void clearStopAutonomy() => clearField(17);
  @$pb.TagNumber(17)
  StopAutonomyTask ensureStopAutonomy() => $_ensure(16);

  @$pb.TagNumber(18)
  $75.Point get startLocation => $_getN(17);
  @$pb.TagNumber(18)
  set startLocation($75.Point v) { setField(18, v); }
  @$pb.TagNumber(18)
  $core.bool hasStartLocation() => $_has(17);
  @$pb.TagNumber(18)
  void clearStartLocation() => clearField(18);
  @$pb.TagNumber(18)
  $75.Point ensureStartLocation() => $_ensure(17);

  @$pb.TagNumber(19)
  $core.double get startHeading => $_getN(18);
  @$pb.TagNumber(19)
  set startHeading($core.double v) { $_setDouble(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasStartHeading() => $_has(18);
  @$pb.TagNumber(19)
  void clearStartHeading() => clearField(19);

  @$pb.TagNumber(20)
  $75.Point get endLocation => $_getN(19);
  @$pb.TagNumber(20)
  set endLocation($75.Point v) { setField(20, v); }
  @$pb.TagNumber(20)
  $core.bool hasEndLocation() => $_has(19);
  @$pb.TagNumber(20)
  void clearEndLocation() => clearField(20);
  @$pb.TagNumber(20)
  $75.Point ensureEndLocation() => $_ensure(19);

  @$pb.TagNumber(21)
  $core.double get endHeading => $_getN(20);
  @$pb.TagNumber(21)
  set endHeading($core.double v) { $_setDouble(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasEndHeading() => $_has(20);
  @$pb.TagNumber(21)
  void clearEndHeading() => clearField(21);

  @$pb.TagNumber(22)
  GoToReversiblePathTask get goToReversiblePath => $_getN(21);
  @$pb.TagNumber(22)
  set goToReversiblePath(GoToReversiblePathTask v) { setField(22, v); }
  @$pb.TagNumber(22)
  $core.bool hasGoToReversiblePath() => $_has(21);
  @$pb.TagNumber(22)
  void clearGoToReversiblePath() => clearField(22);
  @$pb.TagNumber(22)
  GoToReversiblePathTask ensureGoToReversiblePath() => $_ensure(21);

  @$pb.TagNumber(23)
  $core.bool get manuallyAssisted => $_getBF(22);
  @$pb.TagNumber(23)
  set manuallyAssisted($core.bool v) { $_setBool(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasManuallyAssisted() => $_has(22);
  @$pb.TagNumber(23)
  void clearManuallyAssisted() => clearField(23);

  @$pb.TagNumber(24)
  $70.Timestamp get progressUpdatedAt => $_getN(23);
  @$pb.TagNumber(24)
  set progressUpdatedAt($70.Timestamp v) { setField(24, v); }
  @$pb.TagNumber(24)
  $core.bool hasProgressUpdatedAt() => $_has(23);
  @$pb.TagNumber(24)
  void clearProgressUpdatedAt() => clearField(24);
  @$pb.TagNumber(24)
  $70.Timestamp ensureProgressUpdatedAt() => $_ensure(23);

  @$pb.TagNumber(25)
  $75.Point get progressLocation => $_getN(24);
  @$pb.TagNumber(25)
  set progressLocation($75.Point v) { setField(25, v); }
  @$pb.TagNumber(25)
  $core.bool hasProgressLocation() => $_has(24);
  @$pb.TagNumber(25)
  void clearProgressLocation() => clearField(25);
  @$pb.TagNumber(25)
  $75.Point ensureProgressLocation() => $_ensure(24);

  @$pb.TagNumber(26)
  $core.double get progressHeading => $_getN(25);
  @$pb.TagNumber(26)
  set progressHeading($core.double v) { $_setDouble(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasProgressHeading() => $_has(25);
  @$pb.TagNumber(26)
  void clearProgressHeading() => clearField(26);
}

class StopAutonomyTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopAutonomyTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StopAutonomyTask._() : super();
  factory StopAutonomyTask() => create();
  factory StopAutonomyTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopAutonomyTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopAutonomyTask clone() => StopAutonomyTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopAutonomyTask copyWith(void Function(StopAutonomyTask) updates) => super.copyWith((message) => updates(message as StopAutonomyTask)) as StopAutonomyTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopAutonomyTask create() => StopAutonomyTask._();
  StopAutonomyTask createEmptyInstance() => create();
  static $pb.PbList<StopAutonomyTask> createRepeated() => $pb.PbList<StopAutonomyTask>();
  @$core.pragma('dart2js:noInline')
  static StopAutonomyTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopAutonomyTask>(create);
  static StopAutonomyTask? _defaultInstance;
}

class LaserWeedTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LaserWeedTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.LineString>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'path', subBuilder: $75.LineString.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pathIsReversible')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEnabled')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningEnabled')
    ..aOM<SpatialPathTolerance>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'tolerances', subBuilder: SpatialPathTolerance.create)
    ..hasRequiredFields = false
  ;

  LaserWeedTask._() : super();
  factory LaserWeedTask({
    $75.LineString? path,
    $core.bool? pathIsReversible,
    $core.bool? weedingEnabled,
    $core.bool? thinningEnabled,
    SpatialPathTolerance? tolerances,
  }) {
    final _result = create();
    if (path != null) {
      _result.path = path;
    }
    if (pathIsReversible != null) {
      _result.pathIsReversible = pathIsReversible;
    }
    if (weedingEnabled != null) {
      _result.weedingEnabled = weedingEnabled;
    }
    if (thinningEnabled != null) {
      _result.thinningEnabled = thinningEnabled;
    }
    if (tolerances != null) {
      _result.tolerances = tolerances;
    }
    return _result;
  }
  factory LaserWeedTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LaserWeedTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LaserWeedTask clone() => LaserWeedTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LaserWeedTask copyWith(void Function(LaserWeedTask) updates) => super.copyWith((message) => updates(message as LaserWeedTask)) as LaserWeedTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LaserWeedTask create() => LaserWeedTask._();
  LaserWeedTask createEmptyInstance() => create();
  static $pb.PbList<LaserWeedTask> createRepeated() => $pb.PbList<LaserWeedTask>();
  @$core.pragma('dart2js:noInline')
  static LaserWeedTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LaserWeedTask>(create);
  static LaserWeedTask? _defaultInstance;

  @$pb.TagNumber(1)
  $75.LineString get path => $_getN(0);
  @$pb.TagNumber(1)
  set path($75.LineString v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPath() => $_has(0);
  @$pb.TagNumber(1)
  void clearPath() => clearField(1);
  @$pb.TagNumber(1)
  $75.LineString ensurePath() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get pathIsReversible => $_getBF(1);
  @$pb.TagNumber(2)
  set pathIsReversible($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPathIsReversible() => $_has(1);
  @$pb.TagNumber(2)
  void clearPathIsReversible() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get weedingEnabled => $_getBF(2);
  @$pb.TagNumber(3)
  set weedingEnabled($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasWeedingEnabled() => $_has(2);
  @$pb.TagNumber(3)
  void clearWeedingEnabled() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get thinningEnabled => $_getBF(3);
  @$pb.TagNumber(4)
  set thinningEnabled($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasThinningEnabled() => $_has(3);
  @$pb.TagNumber(4)
  void clearThinningEnabled() => clearField(4);

  @$pb.TagNumber(6)
  SpatialPathTolerance get tolerances => $_getN(4);
  @$pb.TagNumber(6)
  set tolerances(SpatialPathTolerance v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasTolerances() => $_has(4);
  @$pb.TagNumber(6)
  void clearTolerances() => clearField(6);
  @$pb.TagNumber(6)
  SpatialPathTolerance ensureTolerances() => $_ensure(4);
}

class SequenceTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SequenceTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<Task>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'items', $pb.PbFieldType.PM, subBuilder: Task.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'atomic')
    ..hasRequiredFields = false
  ;

  SequenceTask._() : super();
  factory SequenceTask({
    $core.Iterable<Task>? items,
    $core.bool? atomic,
  }) {
    final _result = create();
    if (items != null) {
      _result.items.addAll(items);
    }
    if (atomic != null) {
      _result.atomic = atomic;
    }
    return _result;
  }
  factory SequenceTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SequenceTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SequenceTask clone() => SequenceTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SequenceTask copyWith(void Function(SequenceTask) updates) => super.copyWith((message) => updates(message as SequenceTask)) as SequenceTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SequenceTask create() => SequenceTask._();
  SequenceTask createEmptyInstance() => create();
  static $pb.PbList<SequenceTask> createRepeated() => $pb.PbList<SequenceTask>();
  @$core.pragma('dart2js:noInline')
  static SequenceTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SequenceTask>(create);
  static SequenceTask? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Task> get items => $_getList(0);

  @$pb.TagNumber(2)
  $core.bool get atomic => $_getBF(1);
  @$pb.TagNumber(2)
  set atomic($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAtomic() => $_has(1);
  @$pb.TagNumber(2)
  void clearAtomic() => clearField(2);
}

class ManualTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ManualTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'instructions')
    ..hasRequiredFields = false
  ;

  ManualTask._() : super();
  factory ManualTask({
    $core.String? instructions,
  }) {
    final _result = create();
    if (instructions != null) {
      _result.instructions = instructions;
    }
    return _result;
  }
  factory ManualTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ManualTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ManualTask clone() => ManualTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ManualTask copyWith(void Function(ManualTask) updates) => super.copyWith((message) => updates(message as ManualTask)) as ManualTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ManualTask create() => ManualTask._();
  ManualTask createEmptyInstance() => create();
  static $pb.PbList<ManualTask> createRepeated() => $pb.PbList<ManualTask>();
  @$core.pragma('dart2js:noInline')
  static ManualTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ManualTask>(create);
  static ManualTask? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get instructions => $_getSZ(0);
  @$pb.TagNumber(1)
  set instructions($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasInstructions() => $_has(0);
  @$pb.TagNumber(1)
  void clearInstructions() => clearField(1);
}

class GoToAndFaceTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GoToAndFaceTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'point', subBuilder: $75.Point.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'heading', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  GoToAndFaceTask._() : super();
  factory GoToAndFaceTask({
    $75.Point? point,
    $core.double? heading,
  }) {
    final _result = create();
    if (point != null) {
      _result.point = point;
    }
    if (heading != null) {
      _result.heading = heading;
    }
    return _result;
  }
  factory GoToAndFaceTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GoToAndFaceTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GoToAndFaceTask clone() => GoToAndFaceTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GoToAndFaceTask copyWith(void Function(GoToAndFaceTask) updates) => super.copyWith((message) => updates(message as GoToAndFaceTask)) as GoToAndFaceTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GoToAndFaceTask create() => GoToAndFaceTask._();
  GoToAndFaceTask createEmptyInstance() => create();
  static $pb.PbList<GoToAndFaceTask> createRepeated() => $pb.PbList<GoToAndFaceTask>();
  @$core.pragma('dart2js:noInline')
  static GoToAndFaceTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GoToAndFaceTask>(create);
  static GoToAndFaceTask? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Point get point => $_getN(0);
  @$pb.TagNumber(1)
  set point($75.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPoint() => $_has(0);
  @$pb.TagNumber(1)
  void clearPoint() => clearField(1);
  @$pb.TagNumber(1)
  $75.Point ensurePoint() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get heading => $_getN(1);
  @$pb.TagNumber(2)
  set heading($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasHeading() => $_has(1);
  @$pb.TagNumber(2)
  void clearHeading() => clearField(2);
}

class GoToReversiblePathTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GoToReversiblePathTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.LineString>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'path', subBuilder: $75.LineString.create)
    ..aOM<SpatialPathTolerance>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'tolerances', subBuilder: SpatialPathTolerance.create)
    ..hasRequiredFields = false
  ;

  GoToReversiblePathTask._() : super();
  factory GoToReversiblePathTask({
    $75.LineString? path,
    SpatialPathTolerance? tolerances,
  }) {
    final _result = create();
    if (path != null) {
      _result.path = path;
    }
    if (tolerances != null) {
      _result.tolerances = tolerances;
    }
    return _result;
  }
  factory GoToReversiblePathTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GoToReversiblePathTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GoToReversiblePathTask clone() => GoToReversiblePathTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GoToReversiblePathTask copyWith(void Function(GoToReversiblePathTask) updates) => super.copyWith((message) => updates(message as GoToReversiblePathTask)) as GoToReversiblePathTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GoToReversiblePathTask create() => GoToReversiblePathTask._();
  GoToReversiblePathTask createEmptyInstance() => create();
  static $pb.PbList<GoToReversiblePathTask> createRepeated() => $pb.PbList<GoToReversiblePathTask>();
  @$core.pragma('dart2js:noInline')
  static GoToReversiblePathTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GoToReversiblePathTask>(create);
  static GoToReversiblePathTask? _defaultInstance;

  @$pb.TagNumber(1)
  $75.LineString get path => $_getN(0);
  @$pb.TagNumber(1)
  set path($75.LineString v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPath() => $_has(0);
  @$pb.TagNumber(1)
  void clearPath() => clearField(1);
  @$pb.TagNumber(1)
  $75.LineString ensurePath() => $_ensure(0);

  @$pb.TagNumber(2)
  SpatialPathTolerance get tolerances => $_getN(1);
  @$pb.TagNumber(2)
  set tolerances(SpatialPathTolerance v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTolerances() => $_has(1);
  @$pb.TagNumber(2)
  void clearTolerances() => clearField(2);
  @$pb.TagNumber(2)
  SpatialPathTolerance ensureTolerances() => $_ensure(1);
}

class FollowPathTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FollowPathTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.LineString>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'path', subBuilder: $75.LineString.create)
    ..aOM<SpeedSetting>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'speed', subBuilder: SpeedSetting.create)
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'stopOnCompletion')
    ..hasRequiredFields = false
  ;

  FollowPathTask._() : super();
  factory FollowPathTask({
    $75.LineString? path,
    SpeedSetting? speed,
    $core.bool? stopOnCompletion,
  }) {
    final _result = create();
    if (path != null) {
      _result.path = path;
    }
    if (speed != null) {
      _result.speed = speed;
    }
    if (stopOnCompletion != null) {
      _result.stopOnCompletion = stopOnCompletion;
    }
    return _result;
  }
  factory FollowPathTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FollowPathTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FollowPathTask clone() => FollowPathTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FollowPathTask copyWith(void Function(FollowPathTask) updates) => super.copyWith((message) => updates(message as FollowPathTask)) as FollowPathTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FollowPathTask create() => FollowPathTask._();
  FollowPathTask createEmptyInstance() => create();
  static $pb.PbList<FollowPathTask> createRepeated() => $pb.PbList<FollowPathTask>();
  @$core.pragma('dart2js:noInline')
  static FollowPathTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FollowPathTask>(create);
  static FollowPathTask? _defaultInstance;

  @$pb.TagNumber(1)
  $75.LineString get path => $_getN(0);
  @$pb.TagNumber(1)
  set path($75.LineString v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPath() => $_has(0);
  @$pb.TagNumber(1)
  void clearPath() => clearField(1);
  @$pb.TagNumber(1)
  $75.LineString ensurePath() => $_ensure(0);

  @$pb.TagNumber(2)
  SpeedSetting get speed => $_getN(1);
  @$pb.TagNumber(2)
  set speed(SpeedSetting v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasSpeed() => $_has(1);
  @$pb.TagNumber(2)
  void clearSpeed() => clearField(2);
  @$pb.TagNumber(2)
  SpeedSetting ensureSpeed() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.bool get stopOnCompletion => $_getBF(2);
  @$pb.TagNumber(3)
  set stopOnCompletion($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasStopOnCompletion() => $_has(2);
  @$pb.TagNumber(3)
  void clearStopOnCompletion() => clearField(3);
}

enum SpeedSetting_Speed {
  constantMph, 
  remoteOperatorControlled, 
  implementControlled, 
  notSet
}

class SpeedSetting extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, SpeedSetting_Speed> _SpeedSetting_SpeedByTag = {
    1 : SpeedSetting_Speed.constantMph,
    2 : SpeedSetting_Speed.remoteOperatorControlled,
    3 : SpeedSetting_Speed.implementControlled,
    0 : SpeedSetting_Speed.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SpeedSetting', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3])
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'constantMph', $pb.PbFieldType.OD)
    ..aOM<$53.Empty>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'remoteOperatorControlled', subBuilder: $53.Empty.create)
    ..aOM<$53.Empty>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'implementControlled', subBuilder: $53.Empty.create)
    ..hasRequiredFields = false
  ;

  SpeedSetting._() : super();
  factory SpeedSetting({
    $core.double? constantMph,
    $53.Empty? remoteOperatorControlled,
    $53.Empty? implementControlled,
  }) {
    final _result = create();
    if (constantMph != null) {
      _result.constantMph = constantMph;
    }
    if (remoteOperatorControlled != null) {
      _result.remoteOperatorControlled = remoteOperatorControlled;
    }
    if (implementControlled != null) {
      _result.implementControlled = implementControlled;
    }
    return _result;
  }
  factory SpeedSetting.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SpeedSetting.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SpeedSetting clone() => SpeedSetting()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SpeedSetting copyWith(void Function(SpeedSetting) updates) => super.copyWith((message) => updates(message as SpeedSetting)) as SpeedSetting; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SpeedSetting create() => SpeedSetting._();
  SpeedSetting createEmptyInstance() => create();
  static $pb.PbList<SpeedSetting> createRepeated() => $pb.PbList<SpeedSetting>();
  @$core.pragma('dart2js:noInline')
  static SpeedSetting getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SpeedSetting>(create);
  static SpeedSetting? _defaultInstance;

  SpeedSetting_Speed whichSpeed() => _SpeedSetting_SpeedByTag[$_whichOneof(0)]!;
  void clearSpeed() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.double get constantMph => $_getN(0);
  @$pb.TagNumber(1)
  set constantMph($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasConstantMph() => $_has(0);
  @$pb.TagNumber(1)
  void clearConstantMph() => clearField(1);

  @$pb.TagNumber(2)
  $53.Empty get remoteOperatorControlled => $_getN(1);
  @$pb.TagNumber(2)
  set remoteOperatorControlled($53.Empty v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasRemoteOperatorControlled() => $_has(1);
  @$pb.TagNumber(2)
  void clearRemoteOperatorControlled() => clearField(2);
  @$pb.TagNumber(2)
  $53.Empty ensureRemoteOperatorControlled() => $_ensure(1);

  @$pb.TagNumber(3)
  $53.Empty get implementControlled => $_getN(2);
  @$pb.TagNumber(3)
  set implementControlled($53.Empty v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasImplementControlled() => $_has(2);
  @$pb.TagNumber(3)
  void clearImplementControlled() => clearField(3);
  @$pb.TagNumber(3)
  $53.Empty ensureImplementControlled() => $_ensure(2);
}

class SetTractorStateTask extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetTractorStateTask', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<TractorState>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state', $pb.PbFieldType.PM, subBuilder: TractorState.create)
    ..hasRequiredFields = false
  ;

  SetTractorStateTask._() : super();
  factory SetTractorStateTask({
    $core.Iterable<TractorState>? state,
  }) {
    final _result = create();
    if (state != null) {
      _result.state.addAll(state);
    }
    return _result;
  }
  factory SetTractorStateTask.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetTractorStateTask.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetTractorStateTask clone() => SetTractorStateTask()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetTractorStateTask copyWith(void Function(SetTractorStateTask) updates) => super.copyWith((message) => updates(message as SetTractorStateTask)) as SetTractorStateTask; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetTractorStateTask create() => SetTractorStateTask._();
  SetTractorStateTask createEmptyInstance() => create();
  static $pb.PbList<SetTractorStateTask> createRepeated() => $pb.PbList<SetTractorStateTask>();
  @$core.pragma('dart2js:noInline')
  static SetTractorStateTask getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetTractorStateTask>(create);
  static SetTractorStateTask? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<TractorState> get state => $_getList(0);
}

enum TractorState_State {
  gear, 
  hitch, 
  notSet
}

class TractorState extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, TractorState_State> _TractorState_StateByTag = {
    1 : TractorState_State.gear,
    2 : TractorState_State.hitch,
    0 : TractorState_State.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TractorState', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..e<TractorState_Gear>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gear', $pb.PbFieldType.OE, defaultOrMaker: TractorState_Gear.GEAR_UNSPECIFIED, valueOf: TractorState_Gear.valueOf, enumValues: TractorState_Gear.values)
    ..aOM<HitchState>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hitch', subBuilder: HitchState.create)
    ..hasRequiredFields = false
  ;

  TractorState._() : super();
  factory TractorState({
    TractorState_Gear? gear,
    HitchState? hitch,
  }) {
    final _result = create();
    if (gear != null) {
      _result.gear = gear;
    }
    if (hitch != null) {
      _result.hitch = hitch;
    }
    return _result;
  }
  factory TractorState.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TractorState.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TractorState clone() => TractorState()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TractorState copyWith(void Function(TractorState) updates) => super.copyWith((message) => updates(message as TractorState)) as TractorState; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TractorState create() => TractorState._();
  TractorState createEmptyInstance() => create();
  static $pb.PbList<TractorState> createRepeated() => $pb.PbList<TractorState>();
  @$core.pragma('dart2js:noInline')
  static TractorState getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TractorState>(create);
  static TractorState? _defaultInstance;

  TractorState_State whichState() => _TractorState_StateByTag[$_whichOneof(0)]!;
  void clearState() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  TractorState_Gear get gear => $_getN(0);
  @$pb.TagNumber(1)
  set gear(TractorState_Gear v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasGear() => $_has(0);
  @$pb.TagNumber(1)
  void clearGear() => clearField(1);

  @$pb.TagNumber(2)
  HitchState get hitch => $_getN(1);
  @$pb.TagNumber(2)
  set hitch(HitchState v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasHitch() => $_has(1);
  @$pb.TagNumber(2)
  void clearHitch() => clearField(2);
  @$pb.TagNumber(2)
  HitchState ensureHitch() => $_ensure(1);
}

enum HitchState_State {
  command, 
  position, 
  notSet
}

class HitchState extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, HitchState_State> _HitchState_StateByTag = {
    1 : HitchState_State.command,
    2 : HitchState_State.position,
    0 : HitchState_State.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'HitchState', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..e<HitchState_HitchCommand>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'command', $pb.PbFieldType.OE, defaultOrMaker: HitchState_HitchCommand.HITCH_COMMAND_UNSPECIFIED, valueOf: HitchState_HitchCommand.valueOf, enumValues: HitchState_HitchCommand.values)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'position', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  HitchState._() : super();
  factory HitchState({
    HitchState_HitchCommand? command,
    $core.double? position,
  }) {
    final _result = create();
    if (command != null) {
      _result.command = command;
    }
    if (position != null) {
      _result.position = position;
    }
    return _result;
  }
  factory HitchState.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory HitchState.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  HitchState clone() => HitchState()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  HitchState copyWith(void Function(HitchState) updates) => super.copyWith((message) => updates(message as HitchState)) as HitchState; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static HitchState create() => HitchState._();
  HitchState createEmptyInstance() => create();
  static $pb.PbList<HitchState> createRepeated() => $pb.PbList<HitchState>();
  @$core.pragma('dart2js:noInline')
  static HitchState getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HitchState>(create);
  static HitchState? _defaultInstance;

  HitchState_State whichState() => _HitchState_StateByTag[$_whichOneof(0)]!;
  void clearState() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  HitchState_HitchCommand get command => $_getN(0);
  @$pb.TagNumber(1)
  set command(HitchState_HitchCommand v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCommand() => $_has(0);
  @$pb.TagNumber(1)
  void clearCommand() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get position => $_getN(1);
  @$pb.TagNumber(2)
  set position($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPosition() => $_has(1);
  @$pb.TagNumber(2)
  void clearPosition() => clearField(2);
}

class TaskList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TaskList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<Task>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'tasks', $pb.PbFieldType.PM, subBuilder: Task.create)
    ..hasRequiredFields = false
  ;

  TaskList._() : super();
  factory TaskList({
    $core.Iterable<Task>? tasks,
  }) {
    final _result = create();
    if (tasks != null) {
      _result.tasks.addAll(tasks);
    }
    return _result;
  }
  factory TaskList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TaskList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TaskList clone() => TaskList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TaskList copyWith(void Function(TaskList) updates) => super.copyWith((message) => updates(message as TaskList)) as TaskList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TaskList create() => TaskList._();
  TaskList createEmptyInstance() => create();
  static $pb.PbList<TaskList> createRepeated() => $pb.PbList<TaskList>();
  @$core.pragma('dart2js:noInline')
  static TaskList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TaskList>(create);
  static TaskList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Task> get tasks => $_getList(0);
}

class ListTasksResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListTasksResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pc<Task>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'tasks', $pb.PbFieldType.PM, subBuilder: Task.create)
    ..hasRequiredFields = false
  ;

  ListTasksResponse._() : super();
  factory ListTasksResponse({
    $core.String? pageToken,
    $core.Iterable<Task>? tasks,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (tasks != null) {
      _result.tasks.addAll(tasks);
    }
    return _result;
  }
  factory ListTasksResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListTasksResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListTasksResponse clone() => ListTasksResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListTasksResponse copyWith(void Function(ListTasksResponse) updates) => super.copyWith((message) => updates(message as ListTasksResponse)) as ListTasksResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListTasksResponse create() => ListTasksResponse._();
  ListTasksResponse createEmptyInstance() => create();
  static $pb.PbList<ListTasksResponse> createRepeated() => $pb.PbList<ListTasksResponse>();
  @$core.pragma('dart2js:noInline')
  static ListTasksResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListTasksResponse>(create);
  static ListTasksResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<Task> get tasks => $_getList(1);
}

class SpatialPathTolerance extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SpatialPathTolerance', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'heading', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crosstrack', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'distance', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'continuousCrosstrack', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  SpatialPathTolerance._() : super();
  factory SpatialPathTolerance({
    $core.double? heading,
    $core.double? crosstrack,
    $core.double? distance,
    $core.double? continuousCrosstrack,
  }) {
    final _result = create();
    if (heading != null) {
      _result.heading = heading;
    }
    if (crosstrack != null) {
      _result.crosstrack = crosstrack;
    }
    if (distance != null) {
      _result.distance = distance;
    }
    if (continuousCrosstrack != null) {
      _result.continuousCrosstrack = continuousCrosstrack;
    }
    return _result;
  }
  factory SpatialPathTolerance.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SpatialPathTolerance.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SpatialPathTolerance clone() => SpatialPathTolerance()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SpatialPathTolerance copyWith(void Function(SpatialPathTolerance) updates) => super.copyWith((message) => updates(message as SpatialPathTolerance)) as SpatialPathTolerance; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SpatialPathTolerance create() => SpatialPathTolerance._();
  SpatialPathTolerance createEmptyInstance() => create();
  static $pb.PbList<SpatialPathTolerance> createRepeated() => $pb.PbList<SpatialPathTolerance>();
  @$core.pragma('dart2js:noInline')
  static SpatialPathTolerance getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SpatialPathTolerance>(create);
  static SpatialPathTolerance? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get heading => $_getN(0);
  @$pb.TagNumber(1)
  set heading($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHeading() => $_has(0);
  @$pb.TagNumber(1)
  void clearHeading() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get crosstrack => $_getN(1);
  @$pb.TagNumber(2)
  set crosstrack($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCrosstrack() => $_has(1);
  @$pb.TagNumber(2)
  void clearCrosstrack() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get distance => $_getN(2);
  @$pb.TagNumber(3)
  set distance($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDistance() => $_has(2);
  @$pb.TagNumber(3)
  void clearDistance() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get continuousCrosstrack => $_getN(3);
  @$pb.TagNumber(4)
  set continuousCrosstrack($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasContinuousCrosstrack() => $_has(3);
  @$pb.TagNumber(4)
  void clearContinuousCrosstrack() => clearField(4);
}

class Job extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Job', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOM<$70.Timestamp>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$70.Timestamp>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endedAt', subBuilder: $70.Timestamp.create)
    ..pc<Objective>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectives', $pb.PbFieldType.PM, subBuilder: Objective.create)
    ..e<State>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state', $pb.PbFieldType.OE, defaultOrMaker: State.STATE_UNSPECIFIED, valueOf: State.valueOf, enumValues: State.values)
    ..e<Job_JobType>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type', $pb.PbFieldType.OE, defaultOrMaker: Job_JobType.JOB_TYPE_UNSPECIFIED, valueOf: Job_JobType.valueOf, enumValues: Job_JobType.values)
    ..a<$fixnum.Int64>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'workOrderId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'farmId')
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fieldId')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerId')
    ..a<$core.int>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'priority', $pb.PbFieldType.O3)
    ..aOM<RobotWhitelist>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotWhitelist', subBuilder: RobotWhitelist.create)
    ..hasRequiredFields = false
  ;

  Job._() : super();
  factory Job({
    $fixnum.Int64? id,
    $core.String? name,
    $70.Timestamp? startedAt,
    $70.Timestamp? endedAt,
    $core.Iterable<Objective>? objectives,
    State? state,
    Job_JobType? type,
    $fixnum.Int64? workOrderId,
    $core.String? farmId,
    $core.String? fieldId,
    $core.String? customerId,
    $core.int? priority,
    RobotWhitelist? robotWhitelist,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (name != null) {
      _result.name = name;
    }
    if (startedAt != null) {
      _result.startedAt = startedAt;
    }
    if (endedAt != null) {
      _result.endedAt = endedAt;
    }
    if (objectives != null) {
      _result.objectives.addAll(objectives);
    }
    if (state != null) {
      _result.state = state;
    }
    if (type != null) {
      _result.type = type;
    }
    if (workOrderId != null) {
      _result.workOrderId = workOrderId;
    }
    if (farmId != null) {
      _result.farmId = farmId;
    }
    if (fieldId != null) {
      _result.fieldId = fieldId;
    }
    if (customerId != null) {
      _result.customerId = customerId;
    }
    if (priority != null) {
      _result.priority = priority;
    }
    if (robotWhitelist != null) {
      _result.robotWhitelist = robotWhitelist;
    }
    return _result;
  }
  factory Job.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Job.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Job clone() => Job()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Job copyWith(void Function(Job) updates) => super.copyWith((message) => updates(message as Job)) as Job; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Job create() => Job._();
  Job createEmptyInstance() => create();
  static $pb.PbList<Job> createRepeated() => $pb.PbList<Job>();
  @$core.pragma('dart2js:noInline')
  static Job getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Job>(create);
  static Job? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get id => $_getI64(0);
  @$pb.TagNumber(1)
  set id($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $70.Timestamp get startedAt => $_getN(2);
  @$pb.TagNumber(3)
  set startedAt($70.Timestamp v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartedAt() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartedAt() => clearField(3);
  @$pb.TagNumber(3)
  $70.Timestamp ensureStartedAt() => $_ensure(2);

  @$pb.TagNumber(4)
  $70.Timestamp get endedAt => $_getN(3);
  @$pb.TagNumber(4)
  set endedAt($70.Timestamp v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasEndedAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearEndedAt() => clearField(4);
  @$pb.TagNumber(4)
  $70.Timestamp ensureEndedAt() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.List<Objective> get objectives => $_getList(4);

  @$pb.TagNumber(6)
  State get state => $_getN(5);
  @$pb.TagNumber(6)
  set state(State v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasState() => $_has(5);
  @$pb.TagNumber(6)
  void clearState() => clearField(6);

  @$pb.TagNumber(7)
  Job_JobType get type => $_getN(6);
  @$pb.TagNumber(7)
  set type(Job_JobType v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasType() => $_has(6);
  @$pb.TagNumber(7)
  void clearType() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get workOrderId => $_getI64(7);
  @$pb.TagNumber(8)
  set workOrderId($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasWorkOrderId() => $_has(7);
  @$pb.TagNumber(8)
  void clearWorkOrderId() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get farmId => $_getSZ(8);
  @$pb.TagNumber(9)
  set farmId($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasFarmId() => $_has(8);
  @$pb.TagNumber(9)
  void clearFarmId() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get fieldId => $_getSZ(9);
  @$pb.TagNumber(10)
  set fieldId($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasFieldId() => $_has(9);
  @$pb.TagNumber(10)
  void clearFieldId() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get customerId => $_getSZ(10);
  @$pb.TagNumber(11)
  set customerId($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasCustomerId() => $_has(10);
  @$pb.TagNumber(11)
  void clearCustomerId() => clearField(11);

  @$pb.TagNumber(12)
  $core.int get priority => $_getIZ(11);
  @$pb.TagNumber(12)
  set priority($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasPriority() => $_has(11);
  @$pb.TagNumber(12)
  void clearPriority() => clearField(12);

  @$pb.TagNumber(13)
  RobotWhitelist get robotWhitelist => $_getN(12);
  @$pb.TagNumber(13)
  set robotWhitelist(RobotWhitelist v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasRobotWhitelist() => $_has(12);
  @$pb.TagNumber(13)
  void clearRobotWhitelist() => clearField(13);
  @$pb.TagNumber(13)
  RobotWhitelist ensureRobotWhitelist() => $_ensure(12);
}

class JobList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'JobList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<Job>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobs', $pb.PbFieldType.PM, subBuilder: Job.create)
    ..hasRequiredFields = false
  ;

  JobList._() : super();
  factory JobList({
    $core.Iterable<Job>? jobs,
  }) {
    final _result = create();
    if (jobs != null) {
      _result.jobs.addAll(jobs);
    }
    return _result;
  }
  factory JobList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory JobList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  JobList clone() => JobList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  JobList copyWith(void Function(JobList) updates) => super.copyWith((message) => updates(message as JobList)) as JobList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static JobList create() => JobList._();
  JobList createEmptyInstance() => create();
  static $pb.PbList<JobList> createRepeated() => $pb.PbList<JobList>();
  @$core.pragma('dart2js:noInline')
  static JobList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<JobList>(create);
  static JobList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Job> get jobs => $_getList(0);
}

class ListJobsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListJobsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pc<Job>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobs', $pb.PbFieldType.PM, subBuilder: Job.create)
    ..hasRequiredFields = false
  ;

  ListJobsResponse._() : super();
  factory ListJobsResponse({
    $core.String? pageToken,
    $core.Iterable<Job>? jobs,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (jobs != null) {
      _result.jobs.addAll(jobs);
    }
    return _result;
  }
  factory ListJobsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListJobsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListJobsResponse clone() => ListJobsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListJobsResponse copyWith(void Function(ListJobsResponse) updates) => super.copyWith((message) => updates(message as ListJobsResponse)) as ListJobsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListJobsResponse create() => ListJobsResponse._();
  ListJobsResponse createEmptyInstance() => create();
  static $pb.PbList<ListJobsResponse> createRepeated() => $pb.PbList<ListJobsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListJobsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListJobsResponse>(create);
  static ListJobsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<Job> get jobs => $_getList(1);
}

class WorkOrder extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WorkOrder', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOM<$70.Timestamp>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scheduledAt', subBuilder: $70.Timestamp.create)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'durationMinutes', $pb.PbFieldType.O3)
    ..pc<Job>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobs', $pb.PbFieldType.PM, subBuilder: Job.create)
    ..e<State>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state', $pb.PbFieldType.OE, defaultOrMaker: State.STATE_UNSPECIFIED, valueOf: State.valueOf, enumValues: State.values)
    ..hasRequiredFields = false
  ;

  WorkOrder._() : super();
  factory WorkOrder({
    $fixnum.Int64? id,
    $core.String? name,
    $70.Timestamp? scheduledAt,
    $core.int? durationMinutes,
    $core.Iterable<Job>? jobs,
    State? state,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (name != null) {
      _result.name = name;
    }
    if (scheduledAt != null) {
      _result.scheduledAt = scheduledAt;
    }
    if (durationMinutes != null) {
      _result.durationMinutes = durationMinutes;
    }
    if (jobs != null) {
      _result.jobs.addAll(jobs);
    }
    if (state != null) {
      _result.state = state;
    }
    return _result;
  }
  factory WorkOrder.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WorkOrder.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WorkOrder clone() => WorkOrder()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WorkOrder copyWith(void Function(WorkOrder) updates) => super.copyWith((message) => updates(message as WorkOrder)) as WorkOrder; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WorkOrder create() => WorkOrder._();
  WorkOrder createEmptyInstance() => create();
  static $pb.PbList<WorkOrder> createRepeated() => $pb.PbList<WorkOrder>();
  @$core.pragma('dart2js:noInline')
  static WorkOrder getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WorkOrder>(create);
  static WorkOrder? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get id => $_getI64(0);
  @$pb.TagNumber(1)
  set id($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $70.Timestamp get scheduledAt => $_getN(2);
  @$pb.TagNumber(3)
  set scheduledAt($70.Timestamp v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasScheduledAt() => $_has(2);
  @$pb.TagNumber(3)
  void clearScheduledAt() => clearField(3);
  @$pb.TagNumber(3)
  $70.Timestamp ensureScheduledAt() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.int get durationMinutes => $_getIZ(3);
  @$pb.TagNumber(4)
  set durationMinutes($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDurationMinutes() => $_has(3);
  @$pb.TagNumber(4)
  void clearDurationMinutes() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<Job> get jobs => $_getList(4);

  @$pb.TagNumber(6)
  State get state => $_getN(5);
  @$pb.TagNumber(6)
  set state(State v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasState() => $_has(5);
  @$pb.TagNumber(6)
  void clearState() => clearField(6);
}

class WorkOrderList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WorkOrderList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<WorkOrder>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'workOrders', $pb.PbFieldType.PM, subBuilder: WorkOrder.create)
    ..hasRequiredFields = false
  ;

  WorkOrderList._() : super();
  factory WorkOrderList({
    $core.Iterable<WorkOrder>? workOrders,
  }) {
    final _result = create();
    if (workOrders != null) {
      _result.workOrders.addAll(workOrders);
    }
    return _result;
  }
  factory WorkOrderList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WorkOrderList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WorkOrderList clone() => WorkOrderList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WorkOrderList copyWith(void Function(WorkOrderList) updates) => super.copyWith((message) => updates(message as WorkOrderList)) as WorkOrderList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WorkOrderList create() => WorkOrderList._();
  WorkOrderList createEmptyInstance() => create();
  static $pb.PbList<WorkOrderList> createRepeated() => $pb.PbList<WorkOrderList>();
  @$core.pragma('dart2js:noInline')
  static WorkOrderList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WorkOrderList>(create);
  static WorkOrderList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<WorkOrder> get workOrders => $_getList(0);
}

class ListWorkOrdersResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListWorkOrdersResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pc<WorkOrder>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'workOrders', $pb.PbFieldType.PM, subBuilder: WorkOrder.create)
    ..hasRequiredFields = false
  ;

  ListWorkOrdersResponse._() : super();
  factory ListWorkOrdersResponse({
    $core.String? pageToken,
    $core.Iterable<WorkOrder>? workOrders,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (workOrders != null) {
      _result.workOrders.addAll(workOrders);
    }
    return _result;
  }
  factory ListWorkOrdersResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListWorkOrdersResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListWorkOrdersResponse clone() => ListWorkOrdersResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListWorkOrdersResponse copyWith(void Function(ListWorkOrdersResponse) updates) => super.copyWith((message) => updates(message as ListWorkOrdersResponse)) as ListWorkOrdersResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListWorkOrdersResponse create() => ListWorkOrdersResponse._();
  ListWorkOrdersResponse createEmptyInstance() => create();
  static $pb.PbList<ListWorkOrdersResponse> createRepeated() => $pb.PbList<ListWorkOrdersResponse>();
  @$core.pragma('dart2js:noInline')
  static ListWorkOrdersResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListWorkOrdersResponse>(create);
  static ListWorkOrdersResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<WorkOrder> get workOrders => $_getList(1);
}

class Intervention extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Intervention', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'qualification')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..e<State>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state', $pb.PbFieldType.OE, defaultOrMaker: State.STATE_UNSPECIFIED, valueOf: State.valueOf, enumValues: State.values)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..a<$fixnum.Int64>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..e<Intervention_InterventionCause>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cause', $pb.PbFieldType.OE, defaultOrMaker: Intervention_InterventionCause.INTERVENTION_CAUSE_UNSPECIFIED, valueOf: Intervention_InterventionCause.valueOf, enumValues: Intervention_InterventionCause.values)
    ..a<$core.int>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'priority', $pb.PbFieldType.O3)
    ..aOM<InterventionAssignment>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignment', subBuilder: InterventionAssignment.create)
    ..hasRequiredFields = false
  ;

  Intervention._() : super();
  factory Intervention({
    $fixnum.Int64? id,
    $fixnum.Int64? taskId,
    $core.String? qualification,
    $core.String? description,
    State? state,
    $core.String? robotSerial,
    $fixnum.Int64? jobId,
    Intervention_InterventionCause? cause,
    $core.int? priority,
    InterventionAssignment? assignment,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (taskId != null) {
      _result.taskId = taskId;
    }
    if (qualification != null) {
      _result.qualification = qualification;
    }
    if (description != null) {
      _result.description = description;
    }
    if (state != null) {
      _result.state = state;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (jobId != null) {
      _result.jobId = jobId;
    }
    if (cause != null) {
      _result.cause = cause;
    }
    if (priority != null) {
      _result.priority = priority;
    }
    if (assignment != null) {
      _result.assignment = assignment;
    }
    return _result;
  }
  factory Intervention.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Intervention.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Intervention clone() => Intervention()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Intervention copyWith(void Function(Intervention) updates) => super.copyWith((message) => updates(message as Intervention)) as Intervention; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Intervention create() => Intervention._();
  Intervention createEmptyInstance() => create();
  static $pb.PbList<Intervention> createRepeated() => $pb.PbList<Intervention>();
  @$core.pragma('dart2js:noInline')
  static Intervention getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Intervention>(create);
  static Intervention? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get id => $_getI64(0);
  @$pb.TagNumber(1)
  set id($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get taskId => $_getI64(1);
  @$pb.TagNumber(2)
  set taskId($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTaskId() => $_has(1);
  @$pb.TagNumber(2)
  void clearTaskId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get qualification => $_getSZ(2);
  @$pb.TagNumber(3)
  set qualification($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasQualification() => $_has(2);
  @$pb.TagNumber(3)
  void clearQualification() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get description => $_getSZ(3);
  @$pb.TagNumber(4)
  set description($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDescription() => $_has(3);
  @$pb.TagNumber(4)
  void clearDescription() => clearField(4);

  @$pb.TagNumber(5)
  State get state => $_getN(4);
  @$pb.TagNumber(5)
  set state(State v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasState() => $_has(4);
  @$pb.TagNumber(5)
  void clearState() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get robotSerial => $_getSZ(5);
  @$pb.TagNumber(6)
  set robotSerial($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasRobotSerial() => $_has(5);
  @$pb.TagNumber(6)
  void clearRobotSerial() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get jobId => $_getI64(6);
  @$pb.TagNumber(7)
  set jobId($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasJobId() => $_has(6);
  @$pb.TagNumber(7)
  void clearJobId() => clearField(7);

  @$pb.TagNumber(8)
  Intervention_InterventionCause get cause => $_getN(7);
  @$pb.TagNumber(8)
  set cause(Intervention_InterventionCause v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasCause() => $_has(7);
  @$pb.TagNumber(8)
  void clearCause() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get priority => $_getIZ(8);
  @$pb.TagNumber(9)
  set priority($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasPriority() => $_has(8);
  @$pb.TagNumber(9)
  void clearPriority() => clearField(9);

  @$pb.TagNumber(10)
  InterventionAssignment get assignment => $_getN(9);
  @$pb.TagNumber(10)
  set assignment(InterventionAssignment v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasAssignment() => $_has(9);
  @$pb.TagNumber(10)
  void clearAssignment() => clearField(10);
  @$pb.TagNumber(10)
  InterventionAssignment ensureAssignment() => $_ensure(9);
}

class InterventionList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'InterventionList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<Intervention>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intervention', $pb.PbFieldType.PM, subBuilder: Intervention.create)
    ..hasRequiredFields = false
  ;

  InterventionList._() : super();
  factory InterventionList({
    $core.Iterable<Intervention>? intervention,
  }) {
    final _result = create();
    if (intervention != null) {
      _result.intervention.addAll(intervention);
    }
    return _result;
  }
  factory InterventionList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory InterventionList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  InterventionList clone() => InterventionList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  InterventionList copyWith(void Function(InterventionList) updates) => super.copyWith((message) => updates(message as InterventionList)) as InterventionList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static InterventionList create() => InterventionList._();
  InterventionList createEmptyInstance() => create();
  static $pb.PbList<InterventionList> createRepeated() => $pb.PbList<InterventionList>();
  @$core.pragma('dart2js:noInline')
  static InterventionList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<InterventionList>(create);
  static InterventionList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Intervention> get intervention => $_getList(0);
}

class InterventionAssignment extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'InterventionAssignment', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userId')
    ..aOM<$70.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assignedAt', subBuilder: $70.Timestamp.create)
    ..hasRequiredFields = false
  ;

  InterventionAssignment._() : super();
  factory InterventionAssignment({
    $core.String? userId,
    $70.Timestamp? assignedAt,
  }) {
    final _result = create();
    if (userId != null) {
      _result.userId = userId;
    }
    if (assignedAt != null) {
      _result.assignedAt = assignedAt;
    }
    return _result;
  }
  factory InterventionAssignment.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory InterventionAssignment.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  InterventionAssignment clone() => InterventionAssignment()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  InterventionAssignment copyWith(void Function(InterventionAssignment) updates) => super.copyWith((message) => updates(message as InterventionAssignment)) as InterventionAssignment; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static InterventionAssignment create() => InterventionAssignment._();
  InterventionAssignment createEmptyInstance() => create();
  static $pb.PbList<InterventionAssignment> createRepeated() => $pb.PbList<InterventionAssignment>();
  @$core.pragma('dart2js:noInline')
  static InterventionAssignment getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<InterventionAssignment>(create);
  static InterventionAssignment? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get userId => $_getSZ(0);
  @$pb.TagNumber(1)
  set userId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUserId() => $_has(0);
  @$pb.TagNumber(1)
  void clearUserId() => clearField(1);

  @$pb.TagNumber(2)
  $70.Timestamp get assignedAt => $_getN(1);
  @$pb.TagNumber(2)
  set assignedAt($70.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAssignedAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearAssignedAt() => clearField(2);
  @$pb.TagNumber(2)
  $70.Timestamp ensureAssignedAt() => $_ensure(1);
}

class ListInterventionsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListInterventionsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageSize', $pb.PbFieldType.O3)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerials')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assigedUserIds')
    ..aOB(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'assigned')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskIds')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobIds')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'causes')
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'states')
    ..hasRequiredFields = false
  ;

  ListInterventionsRequest._() : super();
  factory ListInterventionsRequest({
    $core.int? pageSize,
    $core.String? pageToken,
    $core.String? robotSerials,
    $core.String? assigedUserIds,
    $core.bool? assigned,
    $core.String? taskIds,
    $core.String? jobIds,
    $core.String? causes,
    $core.String? states,
  }) {
    final _result = create();
    if (pageSize != null) {
      _result.pageSize = pageSize;
    }
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (robotSerials != null) {
      _result.robotSerials = robotSerials;
    }
    if (assigedUserIds != null) {
      _result.assigedUserIds = assigedUserIds;
    }
    if (assigned != null) {
      _result.assigned = assigned;
    }
    if (taskIds != null) {
      _result.taskIds = taskIds;
    }
    if (jobIds != null) {
      _result.jobIds = jobIds;
    }
    if (causes != null) {
      _result.causes = causes;
    }
    if (states != null) {
      _result.states = states;
    }
    return _result;
  }
  factory ListInterventionsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListInterventionsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListInterventionsRequest clone() => ListInterventionsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListInterventionsRequest copyWith(void Function(ListInterventionsRequest) updates) => super.copyWith((message) => updates(message as ListInterventionsRequest)) as ListInterventionsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListInterventionsRequest create() => ListInterventionsRequest._();
  ListInterventionsRequest createEmptyInstance() => create();
  static $pb.PbList<ListInterventionsRequest> createRepeated() => $pb.PbList<ListInterventionsRequest>();
  @$core.pragma('dart2js:noInline')
  static ListInterventionsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListInterventionsRequest>(create);
  static ListInterventionsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get pageSize => $_getIZ(0);
  @$pb.TagNumber(1)
  set pageSize($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageSize() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageSize() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get pageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set pageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearPageToken() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get robotSerials => $_getSZ(2);
  @$pb.TagNumber(3)
  set robotSerials($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotSerials() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotSerials() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get assigedUserIds => $_getSZ(3);
  @$pb.TagNumber(4)
  set assigedUserIds($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasAssigedUserIds() => $_has(3);
  @$pb.TagNumber(4)
  void clearAssigedUserIds() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get assigned => $_getBF(4);
  @$pb.TagNumber(5)
  set assigned($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasAssigned() => $_has(4);
  @$pb.TagNumber(5)
  void clearAssigned() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get taskIds => $_getSZ(5);
  @$pb.TagNumber(6)
  set taskIds($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasTaskIds() => $_has(5);
  @$pb.TagNumber(6)
  void clearTaskIds() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get jobIds => $_getSZ(6);
  @$pb.TagNumber(7)
  set jobIds($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasJobIds() => $_has(6);
  @$pb.TagNumber(7)
  void clearJobIds() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get causes => $_getSZ(7);
  @$pb.TagNumber(8)
  set causes($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasCauses() => $_has(7);
  @$pb.TagNumber(8)
  void clearCauses() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get states => $_getSZ(8);
  @$pb.TagNumber(9)
  set states($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasStates() => $_has(8);
  @$pb.TagNumber(9)
  void clearStates() => clearField(9);
}

class ListInterventionsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListInterventionsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pc<Intervention>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'interventions', $pb.PbFieldType.PM, subBuilder: Intervention.create)
    ..hasRequiredFields = false
  ;

  ListInterventionsResponse._() : super();
  factory ListInterventionsResponse({
    $core.String? pageToken,
    $core.Iterable<Intervention>? interventions,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (interventions != null) {
      _result.interventions.addAll(interventions);
    }
    return _result;
  }
  factory ListInterventionsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListInterventionsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListInterventionsResponse clone() => ListInterventionsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListInterventionsResponse copyWith(void Function(ListInterventionsResponse) updates) => super.copyWith((message) => updates(message as ListInterventionsResponse)) as ListInterventionsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListInterventionsResponse create() => ListInterventionsResponse._();
  ListInterventionsResponse createEmptyInstance() => create();
  static $pb.PbList<ListInterventionsResponse> createRepeated() => $pb.PbList<ListInterventionsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListInterventionsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListInterventionsResponse>(create);
  static ListInterventionsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<Intervention> get interventions => $_getList(1);
}

class CreateInterventionRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreateInterventionRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<Intervention>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intervention', subBuilder: Intervention.create)
    ..hasRequiredFields = false
  ;

  CreateInterventionRequest._() : super();
  factory CreateInterventionRequest({
    Intervention? intervention,
  }) {
    final _result = create();
    if (intervention != null) {
      _result.intervention = intervention;
    }
    return _result;
  }
  factory CreateInterventionRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateInterventionRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateInterventionRequest clone() => CreateInterventionRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateInterventionRequest copyWith(void Function(CreateInterventionRequest) updates) => super.copyWith((message) => updates(message as CreateInterventionRequest)) as CreateInterventionRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreateInterventionRequest create() => CreateInterventionRequest._();
  CreateInterventionRequest createEmptyInstance() => create();
  static $pb.PbList<CreateInterventionRequest> createRepeated() => $pb.PbList<CreateInterventionRequest>();
  @$core.pragma('dart2js:noInline')
  static CreateInterventionRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateInterventionRequest>(create);
  static CreateInterventionRequest? _defaultInstance;

  @$pb.TagNumber(1)
  Intervention get intervention => $_getN(0);
  @$pb.TagNumber(1)
  set intervention(Intervention v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasIntervention() => $_has(0);
  @$pb.TagNumber(1)
  void clearIntervention() => clearField(1);
  @$pb.TagNumber(1)
  Intervention ensureIntervention() => $_ensure(0);
}

class CreateInterventionResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreateInterventionResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<Intervention>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intervention', subBuilder: Intervention.create)
    ..hasRequiredFields = false
  ;

  CreateInterventionResponse._() : super();
  factory CreateInterventionResponse({
    Intervention? intervention,
  }) {
    final _result = create();
    if (intervention != null) {
      _result.intervention = intervention;
    }
    return _result;
  }
  factory CreateInterventionResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateInterventionResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateInterventionResponse clone() => CreateInterventionResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateInterventionResponse copyWith(void Function(CreateInterventionResponse) updates) => super.copyWith((message) => updates(message as CreateInterventionResponse)) as CreateInterventionResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreateInterventionResponse create() => CreateInterventionResponse._();
  CreateInterventionResponse createEmptyInstance() => create();
  static $pb.PbList<CreateInterventionResponse> createRepeated() => $pb.PbList<CreateInterventionResponse>();
  @$core.pragma('dart2js:noInline')
  static CreateInterventionResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateInterventionResponse>(create);
  static CreateInterventionResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Intervention get intervention => $_getN(0);
  @$pb.TagNumber(1)
  set intervention(Intervention v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasIntervention() => $_has(0);
  @$pb.TagNumber(1)
  void clearIntervention() => clearField(1);
  @$pb.TagNumber(1)
  Intervention ensureIntervention() => $_ensure(0);
}

class GetActiveTaskRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetActiveTaskRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.Point>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'currentLocation', subBuilder: $75.Point.create)
    ..hasRequiredFields = false
  ;

  GetActiveTaskRequest._() : super();
  factory GetActiveTaskRequest({
    $75.Point? currentLocation,
  }) {
    final _result = create();
    if (currentLocation != null) {
      _result.currentLocation = currentLocation;
    }
    return _result;
  }
  factory GetActiveTaskRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetActiveTaskRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetActiveTaskRequest clone() => GetActiveTaskRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetActiveTaskRequest copyWith(void Function(GetActiveTaskRequest) updates) => super.copyWith((message) => updates(message as GetActiveTaskRequest)) as GetActiveTaskRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetActiveTaskRequest create() => GetActiveTaskRequest._();
  GetActiveTaskRequest createEmptyInstance() => create();
  static $pb.PbList<GetActiveTaskRequest> createRepeated() => $pb.PbList<GetActiveTaskRequest>();
  @$core.pragma('dart2js:noInline')
  static GetActiveTaskRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetActiveTaskRequest>(create);
  static GetActiveTaskRequest? _defaultInstance;

  @$pb.TagNumber(2)
  $75.Point get currentLocation => $_getN(0);
  @$pb.TagNumber(2)
  set currentLocation($75.Point v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasCurrentLocation() => $_has(0);
  @$pb.TagNumber(2)
  void clearCurrentLocation() => clearField(2);
  @$pb.TagNumber(2)
  $75.Point ensureCurrentLocation() => $_ensure(0);
}

class GetActiveTaskResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetActiveTaskResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<Task>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'task', subBuilder: Task.create)
    ..hasRequiredFields = false
  ;

  GetActiveTaskResponse._() : super();
  factory GetActiveTaskResponse({
    Task? task,
  }) {
    final _result = create();
    if (task != null) {
      _result.task = task;
    }
    return _result;
  }
  factory GetActiveTaskResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetActiveTaskResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetActiveTaskResponse clone() => GetActiveTaskResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetActiveTaskResponse copyWith(void Function(GetActiveTaskResponse) updates) => super.copyWith((message) => updates(message as GetActiveTaskResponse)) as GetActiveTaskResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetActiveTaskResponse create() => GetActiveTaskResponse._();
  GetActiveTaskResponse createEmptyInstance() => create();
  static $pb.PbList<GetActiveTaskResponse> createRepeated() => $pb.PbList<GetActiveTaskResponse>();
  @$core.pragma('dart2js:noInline')
  static GetActiveTaskResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetActiveTaskResponse>(create);
  static GetActiveTaskResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Task get task => $_getN(0);
  @$pb.TagNumber(1)
  set task(Task v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTask() => $_has(0);
  @$pb.TagNumber(1)
  void clearTask() => clearField(1);
  @$pb.TagNumber(1)
  Task ensureTask() => $_ensure(0);
}

class GetTaskRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTaskRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  GetTaskRequest._() : super();
  factory GetTaskRequest({
    $fixnum.Int64? taskId,
  }) {
    final _result = create();
    if (taskId != null) {
      _result.taskId = taskId;
    }
    return _result;
  }
  factory GetTaskRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTaskRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTaskRequest clone() => GetTaskRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTaskRequest copyWith(void Function(GetTaskRequest) updates) => super.copyWith((message) => updates(message as GetTaskRequest)) as GetTaskRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTaskRequest create() => GetTaskRequest._();
  GetTaskRequest createEmptyInstance() => create();
  static $pb.PbList<GetTaskRequest> createRepeated() => $pb.PbList<GetTaskRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTaskRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTaskRequest>(create);
  static GetTaskRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get taskId => $_getI64(0);
  @$pb.TagNumber(1)
  set taskId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTaskId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTaskId() => clearField(1);
}

class GetTaskResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTaskResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<Task>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'task', subBuilder: Task.create)
    ..hasRequiredFields = false
  ;

  GetTaskResponse._() : super();
  factory GetTaskResponse({
    Task? task,
  }) {
    final _result = create();
    if (task != null) {
      _result.task = task;
    }
    return _result;
  }
  factory GetTaskResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTaskResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTaskResponse clone() => GetTaskResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTaskResponse copyWith(void Function(GetTaskResponse) updates) => super.copyWith((message) => updates(message as GetTaskResponse)) as GetTaskResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTaskResponse create() => GetTaskResponse._();
  GetTaskResponse createEmptyInstance() => create();
  static $pb.PbList<GetTaskResponse> createRepeated() => $pb.PbList<GetTaskResponse>();
  @$core.pragma('dart2js:noInline')
  static GetTaskResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTaskResponse>(create);
  static GetTaskResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Task get task => $_getN(0);
  @$pb.TagNumber(1)
  set task(Task v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTask() => $_has(0);
  @$pb.TagNumber(1)
  void clearTask() => clearField(1);
  @$pb.TagNumber(1)
  Task ensureTask() => $_ensure(0);
}

class UpdateTaskRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UpdateTaskRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..e<State>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state', $pb.PbFieldType.OE, defaultOrMaker: State.STATE_UNSPECIFIED, valueOf: State.valueOf, enumValues: State.values)
    ..aOM<$70.Timestamp>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$75.Point>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startHeading', $pb.PbFieldType.OD)
    ..aOM<$70.Timestamp>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$75.Point>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endHeading', $pb.PbFieldType.OD)
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusInfo')
    ..aOM<$70.Timestamp>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressUpdatedAt', subBuilder: $70.Timestamp.create)
    ..aOM<$75.Point>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'progressHeading', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  UpdateTaskRequest._() : super();
  factory UpdateTaskRequest({
    $fixnum.Int64? taskId,
    State? state,
    $70.Timestamp? startedAt,
    $75.Point? startLocation,
    $core.double? startHeading,
    $70.Timestamp? endedAt,
    $75.Point? endLocation,
    $core.double? endHeading,
    $core.String? statusInfo,
    $70.Timestamp? progressUpdatedAt,
    $75.Point? progressLocation,
    $core.double? progressHeading,
  }) {
    final _result = create();
    if (taskId != null) {
      _result.taskId = taskId;
    }
    if (state != null) {
      _result.state = state;
    }
    if (startedAt != null) {
      _result.startedAt = startedAt;
    }
    if (startLocation != null) {
      _result.startLocation = startLocation;
    }
    if (startHeading != null) {
      _result.startHeading = startHeading;
    }
    if (endedAt != null) {
      _result.endedAt = endedAt;
    }
    if (endLocation != null) {
      _result.endLocation = endLocation;
    }
    if (endHeading != null) {
      _result.endHeading = endHeading;
    }
    if (statusInfo != null) {
      _result.statusInfo = statusInfo;
    }
    if (progressUpdatedAt != null) {
      _result.progressUpdatedAt = progressUpdatedAt;
    }
    if (progressLocation != null) {
      _result.progressLocation = progressLocation;
    }
    if (progressHeading != null) {
      _result.progressHeading = progressHeading;
    }
    return _result;
  }
  factory UpdateTaskRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UpdateTaskRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UpdateTaskRequest clone() => UpdateTaskRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UpdateTaskRequest copyWith(void Function(UpdateTaskRequest) updates) => super.copyWith((message) => updates(message as UpdateTaskRequest)) as UpdateTaskRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UpdateTaskRequest create() => UpdateTaskRequest._();
  UpdateTaskRequest createEmptyInstance() => create();
  static $pb.PbList<UpdateTaskRequest> createRepeated() => $pb.PbList<UpdateTaskRequest>();
  @$core.pragma('dart2js:noInline')
  static UpdateTaskRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UpdateTaskRequest>(create);
  static UpdateTaskRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get taskId => $_getI64(0);
  @$pb.TagNumber(1)
  set taskId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTaskId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTaskId() => clearField(1);

  @$pb.TagNumber(2)
  State get state => $_getN(1);
  @$pb.TagNumber(2)
  set state(State v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasState() => $_has(1);
  @$pb.TagNumber(2)
  void clearState() => clearField(2);

  @$pb.TagNumber(3)
  $70.Timestamp get startedAt => $_getN(2);
  @$pb.TagNumber(3)
  set startedAt($70.Timestamp v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartedAt() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartedAt() => clearField(3);
  @$pb.TagNumber(3)
  $70.Timestamp ensureStartedAt() => $_ensure(2);

  @$pb.TagNumber(4)
  $75.Point get startLocation => $_getN(3);
  @$pb.TagNumber(4)
  set startLocation($75.Point v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasStartLocation() => $_has(3);
  @$pb.TagNumber(4)
  void clearStartLocation() => clearField(4);
  @$pb.TagNumber(4)
  $75.Point ensureStartLocation() => $_ensure(3);

  @$pb.TagNumber(5)
  $core.double get startHeading => $_getN(4);
  @$pb.TagNumber(5)
  set startHeading($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasStartHeading() => $_has(4);
  @$pb.TagNumber(5)
  void clearStartHeading() => clearField(5);

  @$pb.TagNumber(6)
  $70.Timestamp get endedAt => $_getN(5);
  @$pb.TagNumber(6)
  set endedAt($70.Timestamp v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasEndedAt() => $_has(5);
  @$pb.TagNumber(6)
  void clearEndedAt() => clearField(6);
  @$pb.TagNumber(6)
  $70.Timestamp ensureEndedAt() => $_ensure(5);

  @$pb.TagNumber(7)
  $75.Point get endLocation => $_getN(6);
  @$pb.TagNumber(7)
  set endLocation($75.Point v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasEndLocation() => $_has(6);
  @$pb.TagNumber(7)
  void clearEndLocation() => clearField(7);
  @$pb.TagNumber(7)
  $75.Point ensureEndLocation() => $_ensure(6);

  @$pb.TagNumber(8)
  $core.double get endHeading => $_getN(7);
  @$pb.TagNumber(8)
  set endHeading($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasEndHeading() => $_has(7);
  @$pb.TagNumber(8)
  void clearEndHeading() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get statusInfo => $_getSZ(8);
  @$pb.TagNumber(9)
  set statusInfo($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasStatusInfo() => $_has(8);
  @$pb.TagNumber(9)
  void clearStatusInfo() => clearField(9);

  @$pb.TagNumber(10)
  $70.Timestamp get progressUpdatedAt => $_getN(9);
  @$pb.TagNumber(10)
  set progressUpdatedAt($70.Timestamp v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasProgressUpdatedAt() => $_has(9);
  @$pb.TagNumber(10)
  void clearProgressUpdatedAt() => clearField(10);
  @$pb.TagNumber(10)
  $70.Timestamp ensureProgressUpdatedAt() => $_ensure(9);

  @$pb.TagNumber(11)
  $75.Point get progressLocation => $_getN(10);
  @$pb.TagNumber(11)
  set progressLocation($75.Point v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasProgressLocation() => $_has(10);
  @$pb.TagNumber(11)
  void clearProgressLocation() => clearField(11);
  @$pb.TagNumber(11)
  $75.Point ensureProgressLocation() => $_ensure(10);

  @$pb.TagNumber(12)
  $core.double get progressHeading => $_getN(11);
  @$pb.TagNumber(12)
  set progressHeading($core.double v) { $_setDouble(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasProgressHeading() => $_has(11);
  @$pb.TagNumber(12)
  void clearProgressHeading() => clearField(12);
}

class UpdateTaskResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UpdateTaskResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<Task>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'task', subBuilder: Task.create)
    ..hasRequiredFields = false
  ;

  UpdateTaskResponse._() : super();
  factory UpdateTaskResponse({
    Task? task,
  }) {
    final _result = create();
    if (task != null) {
      _result.task = task;
    }
    return _result;
  }
  factory UpdateTaskResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UpdateTaskResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UpdateTaskResponse clone() => UpdateTaskResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UpdateTaskResponse copyWith(void Function(UpdateTaskResponse) updates) => super.copyWith((message) => updates(message as UpdateTaskResponse)) as UpdateTaskResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UpdateTaskResponse create() => UpdateTaskResponse._();
  UpdateTaskResponse createEmptyInstance() => create();
  static $pb.PbList<UpdateTaskResponse> createRepeated() => $pb.PbList<UpdateTaskResponse>();
  @$core.pragma('dart2js:noInline')
  static UpdateTaskResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UpdateTaskResponse>(create);
  static UpdateTaskResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Task get task => $_getN(0);
  @$pb.TagNumber(1)
  set task(Task v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTask() => $_has(0);
  @$pb.TagNumber(1)
  void clearTask() => clearField(1);
  @$pb.TagNumber(1)
  Task ensureTask() => $_ensure(0);
}

class StartManualTaskRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartManualTaskRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startHeading', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  StartManualTaskRequest._() : super();
  factory StartManualTaskRequest({
    $75.Point? startLocation,
    $core.double? startHeading,
  }) {
    final _result = create();
    if (startLocation != null) {
      _result.startLocation = startLocation;
    }
    if (startHeading != null) {
      _result.startHeading = startHeading;
    }
    return _result;
  }
  factory StartManualTaskRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartManualTaskRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartManualTaskRequest clone() => StartManualTaskRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartManualTaskRequest copyWith(void Function(StartManualTaskRequest) updates) => super.copyWith((message) => updates(message as StartManualTaskRequest)) as StartManualTaskRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartManualTaskRequest create() => StartManualTaskRequest._();
  StartManualTaskRequest createEmptyInstance() => create();
  static $pb.PbList<StartManualTaskRequest> createRepeated() => $pb.PbList<StartManualTaskRequest>();
  @$core.pragma('dart2js:noInline')
  static StartManualTaskRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartManualTaskRequest>(create);
  static StartManualTaskRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Point get startLocation => $_getN(0);
  @$pb.TagNumber(1)
  set startLocation($75.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStartLocation() => $_has(0);
  @$pb.TagNumber(1)
  void clearStartLocation() => clearField(1);
  @$pb.TagNumber(1)
  $75.Point ensureStartLocation() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get startHeading => $_getN(1);
  @$pb.TagNumber(2)
  set startHeading($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasStartHeading() => $_has(1);
  @$pb.TagNumber(2)
  void clearStartHeading() => clearField(2);
}

class StopManualTaskRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopManualTaskRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endLocation', subBuilder: $75.Point.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endHeading', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  StopManualTaskRequest._() : super();
  factory StopManualTaskRequest({
    $75.Point? endLocation,
    $core.double? endHeading,
  }) {
    final _result = create();
    if (endLocation != null) {
      _result.endLocation = endLocation;
    }
    if (endHeading != null) {
      _result.endHeading = endHeading;
    }
    return _result;
  }
  factory StopManualTaskRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopManualTaskRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopManualTaskRequest clone() => StopManualTaskRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopManualTaskRequest copyWith(void Function(StopManualTaskRequest) updates) => super.copyWith((message) => updates(message as StopManualTaskRequest)) as StopManualTaskRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopManualTaskRequest create() => StopManualTaskRequest._();
  StopManualTaskRequest createEmptyInstance() => create();
  static $pb.PbList<StopManualTaskRequest> createRepeated() => $pb.PbList<StopManualTaskRequest>();
  @$core.pragma('dart2js:noInline')
  static StopManualTaskRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopManualTaskRequest>(create);
  static StopManualTaskRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Point get endLocation => $_getN(0);
  @$pb.TagNumber(1)
  set endLocation($75.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasEndLocation() => $_has(0);
  @$pb.TagNumber(1)
  void clearEndLocation() => clearField(1);
  @$pb.TagNumber(1)
  $75.Point ensureEndLocation() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get endHeading => $_getN(1);
  @$pb.TagNumber(2)
  set endHeading($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasEndHeading() => $_has(1);
  @$pb.TagNumber(2)
  void clearEndHeading() => clearField(2);
}

