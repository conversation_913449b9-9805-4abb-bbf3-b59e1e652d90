///
//  Generated code. Do not modify.
//  source: rtc/device_location_history.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'device_location_history.pb.dart' as $52;
import '../google/protobuf/empty.pb.dart' as $53;
export 'device_location_history.pb.dart';

class DeviceLocationClient extends $grpc.Client {
  static final _$logLocationHistory =
      $grpc.ClientMethod<$52.LogDeviceLocationHistoryRequest, $53.Empty>(
          '/carbon.rtc.DeviceLocation/LogLocationHistory',
          ($52.LogDeviceLocationHistoryRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $53.Empty.fromBuffer(value));
  static final _$listDevices =
      $grpc.ClientMethod<$52.ListDevicesRequest, $52.ListDevicesResponse>(
          '/carbon.rtc.DeviceLocation/ListDevices',
          ($52.ListDevicesRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $52.ListDevicesResponse.fromBuffer(value));
  static final _$listLocationHistory = $grpc.ClientMethod<
          $52.ListDeviceLocationHistoryRequest,
          $52.ListDeviceLocationHistoryResponse>(
      '/carbon.rtc.DeviceLocation/ListLocationHistory',
      ($52.ListDeviceLocationHistoryRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $52.ListDeviceLocationHistoryResponse.fromBuffer(value));

  DeviceLocationClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$53.Empty> logLocationHistory(
      $52.LogDeviceLocationHistoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$logLocationHistory, request, options: options);
  }

  $grpc.ResponseFuture<$52.ListDevicesResponse> listDevices(
      $52.ListDevicesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listDevices, request, options: options);
  }

  $grpc.ResponseFuture<$52.ListDeviceLocationHistoryResponse>
      listLocationHistory($52.ListDeviceLocationHistoryRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listLocationHistory, request, options: options);
  }
}

abstract class DeviceLocationServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.DeviceLocation';

  DeviceLocationServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$52.LogDeviceLocationHistoryRequest, $53.Empty>(
            'LogLocationHistory',
            logLocationHistory_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $52.LogDeviceLocationHistoryRequest.fromBuffer(value),
            ($53.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$52.ListDevicesRequest, $52.ListDevicesResponse>(
            'ListDevices',
            listDevices_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $52.ListDevicesRequest.fromBuffer(value),
            ($52.ListDevicesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$52.ListDeviceLocationHistoryRequest,
            $52.ListDeviceLocationHistoryResponse>(
        'ListLocationHistory',
        listLocationHistory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $52.ListDeviceLocationHistoryRequest.fromBuffer(value),
        ($52.ListDeviceLocationHistoryResponse value) =>
            value.writeToBuffer()));
  }

  $async.Future<$53.Empty> logLocationHistory_Pre($grpc.ServiceCall call,
      $async.Future<$52.LogDeviceLocationHistoryRequest> request) async {
    return logLocationHistory(call, await request);
  }

  $async.Future<$52.ListDevicesResponse> listDevices_Pre($grpc.ServiceCall call,
      $async.Future<$52.ListDevicesRequest> request) async {
    return listDevices(call, await request);
  }

  $async.Future<$52.ListDeviceLocationHistoryResponse> listLocationHistory_Pre(
      $grpc.ServiceCall call,
      $async.Future<$52.ListDeviceLocationHistoryRequest> request) async {
    return listLocationHistory(call, await request);
  }

  $async.Future<$53.Empty> logLocationHistory(
      $grpc.ServiceCall call, $52.LogDeviceLocationHistoryRequest request);
  $async.Future<$52.ListDevicesResponse> listDevices(
      $grpc.ServiceCall call, $52.ListDevicesRequest request);
  $async.Future<$52.ListDeviceLocationHistoryResponse> listLocationHistory(
      $grpc.ServiceCall call, $52.ListDeviceLocationHistoryRequest request);
}
