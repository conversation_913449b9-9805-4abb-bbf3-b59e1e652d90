///
//  Generated code. Do not modify.
//  source: rtc/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use stateDescriptor instead')
const State$json = const {
  '1': 'State',
  '2': const [
    const {'1': 'STATE_UNSPECIFIED', '2': 0},
    const {'1': 'PENDING', '2': 1},
    const {'1': 'READY', '2': 2},
    const {'1': 'IN_PROGRESS', '2': 3},
    const {'1': 'COMPLETED', '2': 4},
    const {'1': 'CANCELLED', '2': 5},
    const {'1': 'PAUSED', '2': 6},
    const {'1': 'FAILED', '2': 7},
    const {'1': 'ACKNOWLEDGED', '2': 8},
    const {'1': 'NEW', '2': 9},
  ],
};

/// Descriptor for `State`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List stateDescriptor = $convert.base64Decode('CgVTdGF0ZRIVChFTVEFURV9VTlNQRUNJRklFRBAAEgsKB1BFTkRJTkcQARIJCgVSRUFEWRACEg8KC0lOX1BST0dSRVNTEAMSDQoJQ09NUExFVEVEEAQSDQoJQ0FOQ0VMTEVEEAUSCgoGUEFVU0VEEAYSCgoGRkFJTEVEEAcSEAoMQUNLTk9XTEVER0VEEAgSBwoDTkVXEAk=');
@$core.Deprecated('Use objectiveDescriptor instead')
const Objective$json = const {
  '1': 'Objective',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 4, '10': 'id'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'description', '3': 3, '4': 1, '5': 9, '10': 'description'},
    const {'1': 'progress_percent', '3': 4, '4': 1, '5': 5, '10': 'progressPercent'},
    const {'1': 'priority', '3': 5, '4': 1, '5': 5, '10': 'priority'},
    const {'1': 'type', '3': 6, '4': 1, '5': 14, '6': '.carbon.rtc.Objective.ObjectiveType', '10': 'type'},
    const {'1': 'data', '3': 7, '4': 1, '5': 11, '6': '.google.protobuf.Struct', '10': 'data'},
    const {'1': 'assignment', '3': 8, '4': 1, '5': 11, '6': '.carbon.rtc.ObjectiveAssignment', '10': 'assignment'},
    const {'1': 'job_id', '3': 9, '4': 1, '5': 4, '10': 'jobId'},
  ],
  '4': const [Objective_ObjectiveType$json],
};

@$core.Deprecated('Use objectiveDescriptor instead')
const Objective_ObjectiveType$json = const {
  '1': 'ObjectiveType',
  '2': const [
    const {'1': 'OBJECTIVE_TYPE_UNSPECIFIED', '2': 0},
    const {'1': 'LASER_WEED_ROW', '2': 1},
  ],
};

/// Descriptor for `Objective`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List objectiveDescriptor = $convert.base64Decode('CglPYmplY3RpdmUSDgoCaWQYASABKARSAmlkEhIKBG5hbWUYAiABKAlSBG5hbWUSIAoLZGVzY3JpcHRpb24YAyABKAlSC2Rlc2NyaXB0aW9uEikKEHByb2dyZXNzX3BlcmNlbnQYBCABKAVSD3Byb2dyZXNzUGVyY2VudBIaCghwcmlvcml0eRgFIAEoBVIIcHJpb3JpdHkSNwoEdHlwZRgGIAEoDjIjLmNhcmJvbi5ydGMuT2JqZWN0aXZlLk9iamVjdGl2ZVR5cGVSBHR5cGUSKwoEZGF0YRgHIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RSBGRhdGESPwoKYXNzaWdubWVudBgIIAEoCzIfLmNhcmJvbi5ydGMuT2JqZWN0aXZlQXNzaWdubWVudFIKYXNzaWdubWVudBIVCgZqb2JfaWQYCSABKARSBWpvYklkIkMKDU9iamVjdGl2ZVR5cGUSHgoaT0JKRUNUSVZFX1RZUEVfVU5TUEVDSUZJRUQQABISCg5MQVNFUl9XRUVEX1JPVxAB');
@$core.Deprecated('Use laserWeedRowObjectiveDataDescriptor instead')
const LaserWeedRowObjectiveData$json = const {
  '1': 'LaserWeedRowObjectiveData',
  '2': const [
    const {'1': 'row_num', '3': 1, '4': 1, '5': 13, '10': 'rowNum'},
    const {'1': 'ab_line', '3': 2, '4': 1, '5': 11, '6': '.carbon.geo.AbLine', '10': 'abLine'},
  ],
};

/// Descriptor for `LaserWeedRowObjectiveData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List laserWeedRowObjectiveDataDescriptor = $convert.base64Decode('ChlMYXNlcldlZWRSb3dPYmplY3RpdmVEYXRhEhcKB3Jvd19udW0YASABKA1SBnJvd051bRIrCgdhYl9saW5lGAIgASgLMhIuY2FyYm9uLmdlby5BYkxpbmVSBmFiTGluZQ==');
@$core.Deprecated('Use objectiveListDescriptor instead')
const ObjectiveList$json = const {
  '1': 'ObjectiveList',
  '2': const [
    const {'1': 'objectives', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.Objective', '10': 'objectives'},
  ],
};

/// Descriptor for `ObjectiveList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List objectiveListDescriptor = $convert.base64Decode('Cg1PYmplY3RpdmVMaXN0EjUKCm9iamVjdGl2ZXMYASADKAsyFS5jYXJib24ucnRjLk9iamVjdGl2ZVIKb2JqZWN0aXZlcw==');
@$core.Deprecated('Use listObjectivesResponseDescriptor instead')
const ListObjectivesResponse$json = const {
  '1': 'ListObjectivesResponse',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'objectives', '3': 2, '4': 3, '5': 11, '6': '.carbon.rtc.Objective', '10': 'objectives'},
  ],
};

/// Descriptor for `ListObjectivesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listObjectivesResponseDescriptor = $convert.base64Decode('ChZMaXN0T2JqZWN0aXZlc1Jlc3BvbnNlEh0KCnBhZ2VfdG9rZW4YASABKAlSCXBhZ2VUb2tlbhI1CgpvYmplY3RpdmVzGAIgAygLMhUuY2FyYm9uLnJ0Yy5PYmplY3RpdmVSCm9iamVjdGl2ZXM=');
@$core.Deprecated('Use getNextActiveObjectiveRequestDescriptor instead')
const GetNextActiveObjectiveRequest$json = const {
  '1': 'GetNextActiveObjectiveRequest',
  '2': const [
    const {'1': 'objective_id', '3': 1, '4': 1, '5': 4, '10': 'objectiveId'},
  ],
};

/// Descriptor for `GetNextActiveObjectiveRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextActiveObjectiveRequestDescriptor = $convert.base64Decode('Ch1HZXROZXh0QWN0aXZlT2JqZWN0aXZlUmVxdWVzdBIhCgxvYmplY3RpdmVfaWQYASABKARSC29iamVjdGl2ZUlk');
@$core.Deprecated('Use getNextActiveObjectiveResponseDescriptor instead')
const GetNextActiveObjectiveResponse$json = const {
  '1': 'GetNextActiveObjectiveResponse',
  '2': const [
    const {'1': 'objective', '3': 2, '4': 1, '5': 11, '6': '.carbon.rtc.Objective', '10': 'objective'},
  ],
};

/// Descriptor for `GetNextActiveObjectiveResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextActiveObjectiveResponseDescriptor = $convert.base64Decode('Ch5HZXROZXh0QWN0aXZlT2JqZWN0aXZlUmVzcG9uc2USMwoJb2JqZWN0aXZlGAIgASgLMhUuY2FyYm9uLnJ0Yy5PYmplY3RpdmVSCW9iamVjdGl2ZQ==');
@$core.Deprecated('Use objectiveAssignmentDescriptor instead')
const ObjectiveAssignment$json = const {
  '1': 'ObjectiveAssignment',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 4, '10': 'id'},
    const {'1': 'objective_id', '3': 2, '4': 1, '5': 4, '10': 'objectiveId'},
    const {'1': 'robot_serial', '3': 3, '4': 1, '5': 9, '10': 'robotSerial'},
  ],
};

/// Descriptor for `ObjectiveAssignment`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List objectiveAssignmentDescriptor = $convert.base64Decode('ChNPYmplY3RpdmVBc3NpZ25tZW50Eg4KAmlkGAEgASgEUgJpZBIhCgxvYmplY3RpdmVfaWQYAiABKARSC29iamVjdGl2ZUlkEiEKDHJvYm90X3NlcmlhbBgDIAEoCVILcm9ib3RTZXJpYWw=');
@$core.Deprecated('Use objectiveAssignmentListDescriptor instead')
const ObjectiveAssignmentList$json = const {
  '1': 'ObjectiveAssignmentList',
  '2': const [
    const {'1': 'assignments', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.ObjectiveAssignment', '10': 'assignments'},
  ],
};

/// Descriptor for `ObjectiveAssignmentList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List objectiveAssignmentListDescriptor = $convert.base64Decode('ChdPYmplY3RpdmVBc3NpZ25tZW50TGlzdBJBCgthc3NpZ25tZW50cxgBIAMoCzIfLmNhcmJvbi5ydGMuT2JqZWN0aXZlQXNzaWdubWVudFILYXNzaWdubWVudHM=');
@$core.Deprecated('Use listObjectiveAssignmentsResponseDescriptor instead')
const ListObjectiveAssignmentsResponse$json = const {
  '1': 'ListObjectiveAssignmentsResponse',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'assignments', '3': 2, '4': 3, '5': 11, '6': '.carbon.rtc.ObjectiveAssignment', '10': 'assignments'},
  ],
};

/// Descriptor for `ListObjectiveAssignmentsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listObjectiveAssignmentsResponseDescriptor = $convert.base64Decode('CiBMaXN0T2JqZWN0aXZlQXNzaWdubWVudHNSZXNwb25zZRIdCgpwYWdlX3Rva2VuGAEgASgJUglwYWdlVG9rZW4SQQoLYXNzaWdubWVudHMYAiADKAsyHy5jYXJib24ucnRjLk9iamVjdGl2ZUFzc2lnbm1lbnRSC2Fzc2lnbm1lbnRz');
@$core.Deprecated('Use robotWhitelistEntryDescriptor instead')
const RobotWhitelistEntry$json = const {
  '1': 'RobotWhitelistEntry',
  '2': const [
    const {'1': 'robot_serial', '3': 1, '4': 1, '5': 9, '10': 'robotSerial'},
  ],
};

/// Descriptor for `RobotWhitelistEntry`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List robotWhitelistEntryDescriptor = $convert.base64Decode('ChNSb2JvdFdoaXRlbGlzdEVudHJ5EiEKDHJvYm90X3NlcmlhbBgBIAEoCVILcm9ib3RTZXJpYWw=');
@$core.Deprecated('Use robotWhitelistDescriptor instead')
const RobotWhitelist$json = const {
  '1': 'RobotWhitelist',
  '2': const [
    const {'1': 'entries', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.RobotWhitelistEntry', '10': 'entries'},
  ],
};

/// Descriptor for `RobotWhitelist`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List robotWhitelistDescriptor = $convert.base64Decode('Cg5Sb2JvdFdoaXRlbGlzdBI5CgdlbnRyaWVzGAEgAygLMh8uY2FyYm9uLnJ0Yy5Sb2JvdFdoaXRlbGlzdEVudHJ5UgdlbnRyaWVz');
@$core.Deprecated('Use taskDescriptor instead')
const Task$json = const {
  '1': 'Task',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 4, '10': 'id'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'started_at', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'startedAt'},
    const {'1': 'ended_at', '3': 4, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'endedAt'},
    const {'1': 'expected_duration', '3': 5, '4': 1, '5': 11, '6': '.google.protobuf.Duration', '10': 'expectedDuration'},
    const {'1': 'status_info', '3': 6, '4': 1, '5': 9, '10': 'statusInfo'},
    const {'1': 'expected_tractor_state', '3': 7, '4': 3, '5': 11, '6': '.carbon.rtc.TractorState', '10': 'expectedTractorState'},
    const {'1': 'state', '3': 8, '4': 1, '5': 14, '6': '.carbon.rtc.State', '10': 'state'},
    const {'1': 'priority', '3': 9, '4': 1, '5': 5, '10': 'priority'},
    const {'1': 'objective_id', '3': 16, '4': 1, '5': 4, '10': 'objectiveId'},
    const {'1': 'start_location', '3': 18, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'startLocation'},
    const {'1': 'start_heading', '3': 19, '4': 1, '5': 1, '10': 'startHeading'},
    const {'1': 'end_location', '3': 20, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'endLocation'},
    const {'1': 'end_heading', '3': 21, '4': 1, '5': 1, '10': 'endHeading'},
    const {'1': 'manually_assisted', '3': 23, '4': 1, '5': 8, '10': 'manuallyAssisted'},
    const {'1': 'progress_updated_at', '3': 24, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'progressUpdatedAt'},
    const {'1': 'progress_location', '3': 25, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'progressLocation'},
    const {'1': 'progress_heading', '3': 26, '4': 1, '5': 1, '10': 'progressHeading'},
    const {'1': 'sequence', '3': 10, '4': 1, '5': 11, '6': '.carbon.rtc.SequenceTask', '9': 0, '10': 'sequence'},
    const {'1': 'manual', '3': 11, '4': 1, '5': 11, '6': '.carbon.rtc.ManualTask', '9': 0, '10': 'manual'},
    const {'1': 'go_to_and_face', '3': 12, '4': 1, '5': 11, '6': '.carbon.rtc.GoToAndFaceTask', '9': 0, '10': 'goToAndFace'},
    const {'1': 'follow_path', '3': 13, '4': 1, '5': 11, '6': '.carbon.rtc.FollowPathTask', '9': 0, '10': 'followPath'},
    const {'1': 'tractor_state', '3': 14, '4': 1, '5': 11, '6': '.carbon.rtc.SetTractorStateTask', '9': 0, '10': 'tractorState'},
    const {'1': 'laser_weed', '3': 15, '4': 1, '5': 11, '6': '.carbon.rtc.LaserWeedTask', '9': 0, '10': 'laserWeed'},
    const {'1': 'stop_autonomy', '3': 17, '4': 1, '5': 11, '6': '.carbon.rtc.StopAutonomyTask', '9': 0, '10': 'stopAutonomy'},
    const {'1': 'go_to_reversible_path', '3': 22, '4': 1, '5': 11, '6': '.carbon.rtc.GoToReversiblePathTask', '9': 0, '10': 'goToReversiblePath'},
  ],
  '8': const [
    const {'1': 'task_details'},
  ],
};

/// Descriptor for `Task`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List taskDescriptor = $convert.base64Decode('CgRUYXNrEg4KAmlkGAEgASgEUgJpZBISCgRuYW1lGAIgASgJUgRuYW1lEjkKCnN0YXJ0ZWRfYXQYAyABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wUglzdGFydGVkQXQSNQoIZW5kZWRfYXQYBCABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wUgdlbmRlZEF0EkYKEWV4cGVjdGVkX2R1cmF0aW9uGAUgASgLMhkuZ29vZ2xlLnByb3RvYnVmLkR1cmF0aW9uUhBleHBlY3RlZER1cmF0aW9uEh8KC3N0YXR1c19pbmZvGAYgASgJUgpzdGF0dXNJbmZvEk4KFmV4cGVjdGVkX3RyYWN0b3Jfc3RhdGUYByADKAsyGC5jYXJib24ucnRjLlRyYWN0b3JTdGF0ZVIUZXhwZWN0ZWRUcmFjdG9yU3RhdGUSJwoFc3RhdGUYCCABKA4yES5jYXJib24ucnRjLlN0YXRlUgVzdGF0ZRIaCghwcmlvcml0eRgJIAEoBVIIcHJpb3JpdHkSIQoMb2JqZWN0aXZlX2lkGBAgASgEUgtvYmplY3RpdmVJZBI4Cg5zdGFydF9sb2NhdGlvbhgSIAEoCzIRLmNhcmJvbi5nZW8uUG9pbnRSDXN0YXJ0TG9jYXRpb24SIwoNc3RhcnRfaGVhZGluZxgTIAEoAVIMc3RhcnRIZWFkaW5nEjQKDGVuZF9sb2NhdGlvbhgUIAEoCzIRLmNhcmJvbi5nZW8uUG9pbnRSC2VuZExvY2F0aW9uEh8KC2VuZF9oZWFkaW5nGBUgASgBUgplbmRIZWFkaW5nEisKEW1hbnVhbGx5X2Fzc2lzdGVkGBcgASgIUhBtYW51YWxseUFzc2lzdGVkEkoKE3Byb2dyZXNzX3VwZGF0ZWRfYXQYGCABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wUhFwcm9ncmVzc1VwZGF0ZWRBdBI+ChFwcm9ncmVzc19sb2NhdGlvbhgZIAEoCzIRLmNhcmJvbi5nZW8uUG9pbnRSEHByb2dyZXNzTG9jYXRpb24SKQoQcHJvZ3Jlc3NfaGVhZGluZxgaIAEoAVIPcHJvZ3Jlc3NIZWFkaW5nEjYKCHNlcXVlbmNlGAogASgLMhguY2FyYm9uLnJ0Yy5TZXF1ZW5jZVRhc2tIAFIIc2VxdWVuY2USMAoGbWFudWFsGAsgASgLMhYuY2FyYm9uLnJ0Yy5NYW51YWxUYXNrSABSBm1hbnVhbBJCCg5nb190b19hbmRfZmFjZRgMIAEoCzIbLmNhcmJvbi5ydGMuR29Ub0FuZEZhY2VUYXNrSABSC2dvVG9BbmRGYWNlEj0KC2ZvbGxvd19wYXRoGA0gASgLMhouY2FyYm9uLnJ0Yy5Gb2xsb3dQYXRoVGFza0gAUgpmb2xsb3dQYXRoEkYKDXRyYWN0b3Jfc3RhdGUYDiABKAsyHy5jYXJib24ucnRjLlNldFRyYWN0b3JTdGF0ZVRhc2tIAFIMdHJhY3RvclN0YXRlEjoKCmxhc2VyX3dlZWQYDyABKAsyGS5jYXJib24ucnRjLkxhc2VyV2VlZFRhc2tIAFIJbGFzZXJXZWVkEkMKDXN0b3BfYXV0b25vbXkYESABKAsyHC5jYXJib24ucnRjLlN0b3BBdXRvbm9teVRhc2tIAFIMc3RvcEF1dG9ub215ElcKFWdvX3RvX3JldmVyc2libGVfcGF0aBgWIAEoCzIiLmNhcmJvbi5ydGMuR29Ub1JldmVyc2libGVQYXRoVGFza0gAUhJnb1RvUmV2ZXJzaWJsZVBhdGhCDgoMdGFza19kZXRhaWxz');
@$core.Deprecated('Use stopAutonomyTaskDescriptor instead')
const StopAutonomyTask$json = const {
  '1': 'StopAutonomyTask',
};

/// Descriptor for `StopAutonomyTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List stopAutonomyTaskDescriptor = $convert.base64Decode('ChBTdG9wQXV0b25vbXlUYXNr');
@$core.Deprecated('Use laserWeedTaskDescriptor instead')
const LaserWeedTask$json = const {
  '1': 'LaserWeedTask',
  '2': const [
    const {'1': 'path', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.LineString', '10': 'path'},
    const {'1': 'path_is_reversible', '3': 2, '4': 1, '5': 8, '10': 'pathIsReversible'},
    const {'1': 'weeding_enabled', '3': 3, '4': 1, '5': 8, '10': 'weedingEnabled'},
    const {'1': 'thinning_enabled', '3': 4, '4': 1, '5': 8, '10': 'thinningEnabled'},
    const {'1': 'tolerances', '3': 6, '4': 1, '5': 11, '6': '.carbon.rtc.SpatialPathTolerance', '10': 'tolerances'},
  ],
};

/// Descriptor for `LaserWeedTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List laserWeedTaskDescriptor = $convert.base64Decode('Cg1MYXNlcldlZWRUYXNrEioKBHBhdGgYASABKAsyFi5jYXJib24uZ2VvLkxpbmVTdHJpbmdSBHBhdGgSLAoScGF0aF9pc19yZXZlcnNpYmxlGAIgASgIUhBwYXRoSXNSZXZlcnNpYmxlEicKD3dlZWRpbmdfZW5hYmxlZBgDIAEoCFIOd2VlZGluZ0VuYWJsZWQSKQoQdGhpbm5pbmdfZW5hYmxlZBgEIAEoCFIPdGhpbm5pbmdFbmFibGVkEkAKCnRvbGVyYW5jZXMYBiABKAsyIC5jYXJib24ucnRjLlNwYXRpYWxQYXRoVG9sZXJhbmNlUgp0b2xlcmFuY2Vz');
@$core.Deprecated('Use sequenceTaskDescriptor instead')
const SequenceTask$json = const {
  '1': 'SequenceTask',
  '2': const [
    const {'1': 'items', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.Task', '10': 'items'},
    const {'1': 'atomic', '3': 2, '4': 1, '5': 8, '10': 'atomic'},
  ],
};

/// Descriptor for `SequenceTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sequenceTaskDescriptor = $convert.base64Decode('CgxTZXF1ZW5jZVRhc2sSJgoFaXRlbXMYASADKAsyEC5jYXJib24ucnRjLlRhc2tSBWl0ZW1zEhYKBmF0b21pYxgCIAEoCFIGYXRvbWlj');
@$core.Deprecated('Use manualTaskDescriptor instead')
const ManualTask$json = const {
  '1': 'ManualTask',
  '2': const [
    const {'1': 'instructions', '3': 1, '4': 1, '5': 9, '10': 'instructions'},
  ],
};

/// Descriptor for `ManualTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List manualTaskDescriptor = $convert.base64Decode('CgpNYW51YWxUYXNrEiIKDGluc3RydWN0aW9ucxgBIAEoCVIMaW5zdHJ1Y3Rpb25z');
@$core.Deprecated('Use goToAndFaceTaskDescriptor instead')
const GoToAndFaceTask$json = const {
  '1': 'GoToAndFaceTask',
  '2': const [
    const {'1': 'point', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'point'},
    const {'1': 'heading', '3': 2, '4': 1, '5': 1, '10': 'heading'},
  ],
};

/// Descriptor for `GoToAndFaceTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List goToAndFaceTaskDescriptor = $convert.base64Decode('Cg9Hb1RvQW5kRmFjZVRhc2sSJwoFcG9pbnQYASABKAsyES5jYXJib24uZ2VvLlBvaW50UgVwb2ludBIYCgdoZWFkaW5nGAIgASgBUgdoZWFkaW5n');
@$core.Deprecated('Use goToReversiblePathTaskDescriptor instead')
const GoToReversiblePathTask$json = const {
  '1': 'GoToReversiblePathTask',
  '2': const [
    const {'1': 'path', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.LineString', '10': 'path'},
    const {'1': 'tolerances', '3': 2, '4': 1, '5': 11, '6': '.carbon.rtc.SpatialPathTolerance', '10': 'tolerances'},
  ],
};

/// Descriptor for `GoToReversiblePathTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List goToReversiblePathTaskDescriptor = $convert.base64Decode('ChZHb1RvUmV2ZXJzaWJsZVBhdGhUYXNrEioKBHBhdGgYASABKAsyFi5jYXJib24uZ2VvLkxpbmVTdHJpbmdSBHBhdGgSQAoKdG9sZXJhbmNlcxgCIAEoCzIgLmNhcmJvbi5ydGMuU3BhdGlhbFBhdGhUb2xlcmFuY2VSCnRvbGVyYW5jZXM=');
@$core.Deprecated('Use followPathTaskDescriptor instead')
const FollowPathTask$json = const {
  '1': 'FollowPathTask',
  '2': const [
    const {'1': 'path', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.LineString', '10': 'path'},
    const {'1': 'speed', '3': 2, '4': 1, '5': 11, '6': '.carbon.rtc.SpeedSetting', '10': 'speed'},
    const {'1': 'stop_on_completion', '3': 3, '4': 1, '5': 8, '10': 'stopOnCompletion'},
  ],
};

/// Descriptor for `FollowPathTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List followPathTaskDescriptor = $convert.base64Decode('Cg5Gb2xsb3dQYXRoVGFzaxIqCgRwYXRoGAEgASgLMhYuY2FyYm9uLmdlby5MaW5lU3RyaW5nUgRwYXRoEi4KBXNwZWVkGAIgASgLMhguY2FyYm9uLnJ0Yy5TcGVlZFNldHRpbmdSBXNwZWVkEiwKEnN0b3Bfb25fY29tcGxldGlvbhgDIAEoCFIQc3RvcE9uQ29tcGxldGlvbg==');
@$core.Deprecated('Use speedSettingDescriptor instead')
const SpeedSetting$json = const {
  '1': 'SpeedSetting',
  '2': const [
    const {'1': 'constant_mph', '3': 1, '4': 1, '5': 1, '9': 0, '10': 'constantMph'},
    const {'1': 'remote_operator_controlled', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.Empty', '9': 0, '10': 'remoteOperatorControlled'},
    const {'1': 'implement_controlled', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Empty', '9': 0, '10': 'implementControlled'},
  ],
  '8': const [
    const {'1': 'speed'},
  ],
};

/// Descriptor for `SpeedSetting`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List speedSettingDescriptor = $convert.base64Decode('CgxTcGVlZFNldHRpbmcSIwoMY29uc3RhbnRfbXBoGAEgASgBSABSC2NvbnN0YW50TXBoElYKGnJlbW90ZV9vcGVyYXRvcl9jb250cm9sbGVkGAIgASgLMhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5SABSGHJlbW90ZU9wZXJhdG9yQ29udHJvbGxlZBJLChRpbXBsZW1lbnRfY29udHJvbGxlZBgDIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eUgAUhNpbXBsZW1lbnRDb250cm9sbGVkQgcKBXNwZWVk');
@$core.Deprecated('Use setTractorStateTaskDescriptor instead')
const SetTractorStateTask$json = const {
  '1': 'SetTractorStateTask',
  '2': const [
    const {'1': 'state', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.TractorState', '10': 'state'},
  ],
};

/// Descriptor for `SetTractorStateTask`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List setTractorStateTaskDescriptor = $convert.base64Decode('ChNTZXRUcmFjdG9yU3RhdGVUYXNrEi4KBXN0YXRlGAEgAygLMhguY2FyYm9uLnJ0Yy5UcmFjdG9yU3RhdGVSBXN0YXRl');
@$core.Deprecated('Use tractorStateDescriptor instead')
const TractorState$json = const {
  '1': 'TractorState',
  '2': const [
    const {'1': 'gear', '3': 1, '4': 1, '5': 14, '6': '.carbon.rtc.TractorState.Gear', '9': 0, '10': 'gear'},
    const {'1': 'hitch', '3': 2, '4': 1, '5': 11, '6': '.carbon.rtc.HitchState', '9': 0, '10': 'hitch'},
  ],
  '4': const [TractorState_Gear$json],
  '8': const [
    const {'1': 'state'},
  ],
};

@$core.Deprecated('Use tractorStateDescriptor instead')
const TractorState_Gear$json = const {
  '1': 'Gear',
  '2': const [
    const {'1': 'GEAR_UNSPECIFIED', '2': 0},
    const {'1': 'PARK', '2': 1},
    const {'1': 'REVERSE', '2': 2},
    const {'1': 'NEUTRAL', '2': 3},
    const {'1': 'FORWARD', '2': 4},
    const {'1': 'POWERZERO', '2': 5},
  ],
};

/// Descriptor for `TractorState`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List tractorStateDescriptor = $convert.base64Decode('CgxUcmFjdG9yU3RhdGUSMwoEZ2VhchgBIAEoDjIdLmNhcmJvbi5ydGMuVHJhY3RvclN0YXRlLkdlYXJIAFIEZ2VhchIuCgVoaXRjaBgCIAEoCzIWLmNhcmJvbi5ydGMuSGl0Y2hTdGF0ZUgAUgVoaXRjaCJcCgRHZWFyEhQKEEdFQVJfVU5TUEVDSUZJRUQQABIICgRQQVJLEAESCwoHUkVWRVJTRRACEgsKB05FVVRSQUwQAxILCgdGT1JXQVJEEAQSDQoJUE9XRVJaRVJPEAVCBwoFc3RhdGU=');
@$core.Deprecated('Use hitchStateDescriptor instead')
const HitchState$json = const {
  '1': 'HitchState',
  '2': const [
    const {'1': 'command', '3': 1, '4': 1, '5': 14, '6': '.carbon.rtc.HitchState.HitchCommand', '9': 0, '10': 'command'},
    const {'1': 'position', '3': 2, '4': 1, '5': 1, '9': 0, '10': 'position'},
  ],
  '4': const [HitchState_HitchCommand$json],
  '8': const [
    const {'1': 'state'},
  ],
};

@$core.Deprecated('Use hitchStateDescriptor instead')
const HitchState_HitchCommand$json = const {
  '1': 'HitchCommand',
  '2': const [
    const {'1': 'HITCH_COMMAND_UNSPECIFIED', '2': 0},
    const {'1': 'RAISED', '2': 1},
    const {'1': 'LOWERED', '2': 2},
  ],
};

/// Descriptor for `HitchState`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List hitchStateDescriptor = $convert.base64Decode('CgpIaXRjaFN0YXRlEj8KB2NvbW1hbmQYASABKA4yIy5jYXJib24ucnRjLkhpdGNoU3RhdGUuSGl0Y2hDb21tYW5kSABSB2NvbW1hbmQSHAoIcG9zaXRpb24YAiABKAFIAFIIcG9zaXRpb24iRgoMSGl0Y2hDb21tYW5kEh0KGUhJVENIX0NPTU1BTkRfVU5TUEVDSUZJRUQQABIKCgZSQUlTRUQQARILCgdMT1dFUkVEEAJCBwoFc3RhdGU=');
@$core.Deprecated('Use taskListDescriptor instead')
const TaskList$json = const {
  '1': 'TaskList',
  '2': const [
    const {'1': 'tasks', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.Task', '10': 'tasks'},
  ],
};

/// Descriptor for `TaskList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List taskListDescriptor = $convert.base64Decode('CghUYXNrTGlzdBImCgV0YXNrcxgBIAMoCzIQLmNhcmJvbi5ydGMuVGFza1IFdGFza3M=');
@$core.Deprecated('Use listTasksResponseDescriptor instead')
const ListTasksResponse$json = const {
  '1': 'ListTasksResponse',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'tasks', '3': 2, '4': 3, '5': 11, '6': '.carbon.rtc.Task', '10': 'tasks'},
  ],
};

/// Descriptor for `ListTasksResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listTasksResponseDescriptor = $convert.base64Decode('ChFMaXN0VGFza3NSZXNwb25zZRIdCgpwYWdlX3Rva2VuGAEgASgJUglwYWdlVG9rZW4SJgoFdGFza3MYAiADKAsyEC5jYXJib24ucnRjLlRhc2tSBXRhc2tz');
@$core.Deprecated('Use spatialPathToleranceDescriptor instead')
const SpatialPathTolerance$json = const {
  '1': 'SpatialPathTolerance',
  '2': const [
    const {'1': 'heading', '3': 1, '4': 1, '5': 2, '10': 'heading'},
    const {'1': 'crosstrack', '3': 2, '4': 1, '5': 2, '10': 'crosstrack'},
    const {'1': 'distance', '3': 3, '4': 1, '5': 2, '10': 'distance'},
    const {'1': 'continuous_crosstrack', '3': 4, '4': 1, '5': 2, '10': 'continuousCrosstrack'},
  ],
};

/// Descriptor for `SpatialPathTolerance`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List spatialPathToleranceDescriptor = $convert.base64Decode('ChRTcGF0aWFsUGF0aFRvbGVyYW5jZRIYCgdoZWFkaW5nGAEgASgCUgdoZWFkaW5nEh4KCmNyb3NzdHJhY2sYAiABKAJSCmNyb3NzdHJhY2sSGgoIZGlzdGFuY2UYAyABKAJSCGRpc3RhbmNlEjMKFWNvbnRpbnVvdXNfY3Jvc3N0cmFjaxgEIAEoAlIUY29udGludW91c0Nyb3NzdHJhY2s=');
@$core.Deprecated('Use jobDescriptor instead')
const Job$json = const {
  '1': 'Job',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 4, '10': 'id'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'started_at', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'startedAt'},
    const {'1': 'ended_at', '3': 4, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'endedAt'},
    const {'1': 'objectives', '3': 5, '4': 3, '5': 11, '6': '.carbon.rtc.Objective', '10': 'objectives'},
    const {'1': 'state', '3': 6, '4': 1, '5': 14, '6': '.carbon.rtc.State', '10': 'state'},
    const {'1': 'type', '3': 7, '4': 1, '5': 14, '6': '.carbon.rtc.Job.JobType', '10': 'type'},
    const {'1': 'work_order_id', '3': 8, '4': 1, '5': 4, '9': 0, '10': 'workOrderId', '17': true},
    const {'1': 'farm_id', '3': 9, '4': 1, '5': 9, '10': 'farmId'},
    const {'1': 'field_id', '3': 10, '4': 1, '5': 9, '10': 'fieldId'},
    const {'1': 'customer_id', '3': 11, '4': 1, '5': 9, '10': 'customerId'},
    const {'1': 'priority', '3': 12, '4': 1, '5': 5, '10': 'priority'},
    const {'1': 'robot_whitelist', '3': 13, '4': 1, '5': 11, '6': '.carbon.rtc.RobotWhitelist', '10': 'robotWhitelist'},
  ],
  '4': const [Job_JobType$json],
  '8': const [
    const {'1': '_work_order_id'},
  ],
};

@$core.Deprecated('Use jobDescriptor instead')
const Job_JobType$json = const {
  '1': 'JobType',
  '2': const [
    const {'1': 'JOB_TYPE_UNSPECIFIED', '2': 0},
    const {'1': 'LASER_WEED', '2': 1},
  ],
};

/// Descriptor for `Job`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List jobDescriptor = $convert.base64Decode('CgNKb2ISDgoCaWQYASABKARSAmlkEhIKBG5hbWUYAiABKAlSBG5hbWUSOQoKc3RhcnRlZF9hdBgDIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXBSCXN0YXJ0ZWRBdBI1CghlbmRlZF9hdBgEIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXBSB2VuZGVkQXQSNQoKb2JqZWN0aXZlcxgFIAMoCzIVLmNhcmJvbi5ydGMuT2JqZWN0aXZlUgpvYmplY3RpdmVzEicKBXN0YXRlGAYgASgOMhEuY2FyYm9uLnJ0Yy5TdGF0ZVIFc3RhdGUSKwoEdHlwZRgHIAEoDjIXLmNhcmJvbi5ydGMuSm9iLkpvYlR5cGVSBHR5cGUSJwoNd29ya19vcmRlcl9pZBgIIAEoBEgAUgt3b3JrT3JkZXJJZIgBARIXCgdmYXJtX2lkGAkgASgJUgZmYXJtSWQSGQoIZmllbGRfaWQYCiABKAlSB2ZpZWxkSWQSHwoLY3VzdG9tZXJfaWQYCyABKAlSCmN1c3RvbWVySWQSGgoIcHJpb3JpdHkYDCABKAVSCHByaW9yaXR5EkMKD3JvYm90X3doaXRlbGlzdBgNIAEoCzIaLmNhcmJvbi5ydGMuUm9ib3RXaGl0ZWxpc3RSDnJvYm90V2hpdGVsaXN0IjMKB0pvYlR5cGUSGAoUSk9CX1RZUEVfVU5TUEVDSUZJRUQQABIOCgpMQVNFUl9XRUVEEAFCEAoOX3dvcmtfb3JkZXJfaWQ=');
@$core.Deprecated('Use jobListDescriptor instead')
const JobList$json = const {
  '1': 'JobList',
  '2': const [
    const {'1': 'jobs', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.Job', '10': 'jobs'},
  ],
};

/// Descriptor for `JobList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List jobListDescriptor = $convert.base64Decode('CgdKb2JMaXN0EiMKBGpvYnMYASADKAsyDy5jYXJib24ucnRjLkpvYlIEam9icw==');
@$core.Deprecated('Use listJobsResponseDescriptor instead')
const ListJobsResponse$json = const {
  '1': 'ListJobsResponse',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'jobs', '3': 2, '4': 3, '5': 11, '6': '.carbon.rtc.Job', '10': 'jobs'},
  ],
};

/// Descriptor for `ListJobsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listJobsResponseDescriptor = $convert.base64Decode('ChBMaXN0Sm9ic1Jlc3BvbnNlEh0KCnBhZ2VfdG9rZW4YASABKAlSCXBhZ2VUb2tlbhIjCgRqb2JzGAIgAygLMg8uY2FyYm9uLnJ0Yy5Kb2JSBGpvYnM=');
@$core.Deprecated('Use workOrderDescriptor instead')
const WorkOrder$json = const {
  '1': 'WorkOrder',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 4, '10': 'id'},
    const {'1': 'name', '3': 2, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'scheduled_at', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'scheduledAt'},
    const {'1': 'duration_minutes', '3': 4, '4': 1, '5': 5, '10': 'durationMinutes'},
    const {'1': 'jobs', '3': 5, '4': 3, '5': 11, '6': '.carbon.rtc.Job', '10': 'jobs'},
    const {'1': 'state', '3': 6, '4': 1, '5': 14, '6': '.carbon.rtc.State', '10': 'state'},
  ],
};

/// Descriptor for `WorkOrder`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List workOrderDescriptor = $convert.base64Decode('CglXb3JrT3JkZXISDgoCaWQYASABKARSAmlkEhIKBG5hbWUYAiABKAlSBG5hbWUSPQoMc2NoZWR1bGVkX2F0GAMgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcFILc2NoZWR1bGVkQXQSKQoQZHVyYXRpb25fbWludXRlcxgEIAEoBVIPZHVyYXRpb25NaW51dGVzEiMKBGpvYnMYBSADKAsyDy5jYXJib24ucnRjLkpvYlIEam9icxInCgVzdGF0ZRgGIAEoDjIRLmNhcmJvbi5ydGMuU3RhdGVSBXN0YXRl');
@$core.Deprecated('Use workOrderListDescriptor instead')
const WorkOrderList$json = const {
  '1': 'WorkOrderList',
  '2': const [
    const {'1': 'work_orders', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.WorkOrder', '10': 'workOrders'},
  ],
};

/// Descriptor for `WorkOrderList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List workOrderListDescriptor = $convert.base64Decode('Cg1Xb3JrT3JkZXJMaXN0EjYKC3dvcmtfb3JkZXJzGAEgAygLMhUuY2FyYm9uLnJ0Yy5Xb3JrT3JkZXJSCndvcmtPcmRlcnM=');
@$core.Deprecated('Use listWorkOrdersResponseDescriptor instead')
const ListWorkOrdersResponse$json = const {
  '1': 'ListWorkOrdersResponse',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'work_orders', '3': 2, '4': 3, '5': 11, '6': '.carbon.rtc.WorkOrder', '10': 'workOrders'},
  ],
};

/// Descriptor for `ListWorkOrdersResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listWorkOrdersResponseDescriptor = $convert.base64Decode('ChZMaXN0V29ya09yZGVyc1Jlc3BvbnNlEh0KCnBhZ2VfdG9rZW4YASABKAlSCXBhZ2VUb2tlbhI2Cgt3b3JrX29yZGVycxgCIAMoCzIVLmNhcmJvbi5ydGMuV29ya09yZGVyUgp3b3JrT3JkZXJz');
@$core.Deprecated('Use interventionDescriptor instead')
const Intervention$json = const {
  '1': 'Intervention',
  '2': const [
    const {'1': 'id', '3': 1, '4': 1, '5': 4, '10': 'id'},
    const {'1': 'task_id', '3': 2, '4': 1, '5': 4, '10': 'taskId'},
    const {'1': 'qualification', '3': 3, '4': 1, '5': 9, '10': 'qualification'},
    const {'1': 'description', '3': 4, '4': 1, '5': 9, '10': 'description'},
    const {'1': 'state', '3': 5, '4': 1, '5': 14, '6': '.carbon.rtc.State', '10': 'state'},
    const {'1': 'robot_serial', '3': 6, '4': 1, '5': 9, '10': 'robotSerial'},
    const {'1': 'job_id', '3': 7, '4': 1, '5': 4, '10': 'jobId'},
    const {'1': 'cause', '3': 8, '4': 1, '5': 14, '6': '.carbon.rtc.Intervention.InterventionCause', '10': 'cause'},
    const {'1': 'priority', '3': 9, '4': 1, '5': 5, '10': 'priority'},
    const {'1': 'assignment', '3': 10, '4': 1, '5': 11, '6': '.carbon.rtc.InterventionAssignment', '10': 'assignment'},
  ],
  '4': const [Intervention_InterventionCause$json],
};

@$core.Deprecated('Use interventionDescriptor instead')
const Intervention_InterventionCause$json = const {
  '1': 'InterventionCause',
  '2': const [
    const {'1': 'INTERVENTION_CAUSE_UNSPECIFIED', '2': 0},
    const {'1': 'SENSOR_TRIGGERED', '2': 1},
    const {'1': 'SAFETY_DRIVER_ACTION', '2': 2},
    const {'1': 'TRACTOR_REQUEST', '2': 3},
  ],
};

/// Descriptor for `Intervention`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List interventionDescriptor = $convert.base64Decode('CgxJbnRlcnZlbnRpb24SDgoCaWQYASABKARSAmlkEhcKB3Rhc2tfaWQYAiABKARSBnRhc2tJZBIkCg1xdWFsaWZpY2F0aW9uGAMgASgJUg1xdWFsaWZpY2F0aW9uEiAKC2Rlc2NyaXB0aW9uGAQgASgJUgtkZXNjcmlwdGlvbhInCgVzdGF0ZRgFIAEoDjIRLmNhcmJvbi5ydGMuU3RhdGVSBXN0YXRlEiEKDHJvYm90X3NlcmlhbBgGIAEoCVILcm9ib3RTZXJpYWwSFQoGam9iX2lkGAcgASgEUgVqb2JJZBJACgVjYXVzZRgIIAEoDjIqLmNhcmJvbi5ydGMuSW50ZXJ2ZW50aW9uLkludGVydmVudGlvbkNhdXNlUgVjYXVzZRIaCghwcmlvcml0eRgJIAEoBVIIcHJpb3JpdHkSQgoKYXNzaWdubWVudBgKIAEoCzIiLmNhcmJvbi5ydGMuSW50ZXJ2ZW50aW9uQXNzaWdubWVudFIKYXNzaWdubWVudCJ8ChFJbnRlcnZlbnRpb25DYXVzZRIiCh5JTlRFUlZFTlRJT05fQ0FVU0VfVU5TUEVDSUZJRUQQABIUChBTRU5TT1JfVFJJR0dFUkVEEAESGAoUU0FGRVRZX0RSSVZFUl9BQ1RJT04QAhITCg9UUkFDVE9SX1JFUVVFU1QQAw==');
@$core.Deprecated('Use interventionListDescriptor instead')
const InterventionList$json = const {
  '1': 'InterventionList',
  '2': const [
    const {'1': 'intervention', '3': 1, '4': 3, '5': 11, '6': '.carbon.rtc.Intervention', '10': 'intervention'},
  ],
};

/// Descriptor for `InterventionList`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List interventionListDescriptor = $convert.base64Decode('ChBJbnRlcnZlbnRpb25MaXN0EjwKDGludGVydmVudGlvbhgBIAMoCzIYLmNhcmJvbi5ydGMuSW50ZXJ2ZW50aW9uUgxpbnRlcnZlbnRpb24=');
@$core.Deprecated('Use interventionAssignmentDescriptor instead')
const InterventionAssignment$json = const {
  '1': 'InterventionAssignment',
  '2': const [
    const {'1': 'user_id', '3': 1, '4': 1, '5': 9, '10': 'userId'},
    const {'1': 'assigned_at', '3': 2, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '10': 'assignedAt'},
  ],
};

/// Descriptor for `InterventionAssignment`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List interventionAssignmentDescriptor = $convert.base64Decode('ChZJbnRlcnZlbnRpb25Bc3NpZ25tZW50EhcKB3VzZXJfaWQYASABKAlSBnVzZXJJZBI7Cgthc3NpZ25lZF9hdBgCIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXBSCmFzc2lnbmVkQXQ=');
@$core.Deprecated('Use listInterventionsRequestDescriptor instead')
const ListInterventionsRequest$json = const {
  '1': 'ListInterventionsRequest',
  '2': const [
    const {'1': 'page_size', '3': 1, '4': 1, '5': 5, '10': 'pageSize'},
    const {'1': 'page_token', '3': 2, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'robot_serials', '3': 3, '4': 1, '5': 9, '10': 'robotSerials'},
    const {'1': 'assiged_user_ids', '3': 4, '4': 1, '5': 9, '10': 'assigedUserIds'},
    const {'1': 'assigned', '3': 5, '4': 1, '5': 8, '10': 'assigned'},
    const {'1': 'task_ids', '3': 6, '4': 1, '5': 9, '10': 'taskIds'},
    const {'1': 'job_ids', '3': 7, '4': 1, '5': 9, '10': 'jobIds'},
    const {'1': 'causes', '3': 8, '4': 1, '5': 9, '10': 'causes'},
    const {'1': 'states', '3': 9, '4': 1, '5': 9, '10': 'states'},
  ],
};

/// Descriptor for `ListInterventionsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listInterventionsRequestDescriptor = $convert.base64Decode('ChhMaXN0SW50ZXJ2ZW50aW9uc1JlcXVlc3QSGwoJcGFnZV9zaXplGAEgASgFUghwYWdlU2l6ZRIdCgpwYWdlX3Rva2VuGAIgASgJUglwYWdlVG9rZW4SIwoNcm9ib3Rfc2VyaWFscxgDIAEoCVIMcm9ib3RTZXJpYWxzEigKEGFzc2lnZWRfdXNlcl9pZHMYBCABKAlSDmFzc2lnZWRVc2VySWRzEhoKCGFzc2lnbmVkGAUgASgIUghhc3NpZ25lZBIZCgh0YXNrX2lkcxgGIAEoCVIHdGFza0lkcxIXCgdqb2JfaWRzGAcgASgJUgZqb2JJZHMSFgoGY2F1c2VzGAggASgJUgZjYXVzZXMSFgoGc3RhdGVzGAkgASgJUgZzdGF0ZXM=');
@$core.Deprecated('Use listInterventionsResponseDescriptor instead')
const ListInterventionsResponse$json = const {
  '1': 'ListInterventionsResponse',
  '2': const [
    const {'1': 'page_token', '3': 1, '4': 1, '5': 9, '10': 'pageToken'},
    const {'1': 'interventions', '3': 2, '4': 3, '5': 11, '6': '.carbon.rtc.Intervention', '10': 'interventions'},
  ],
};

/// Descriptor for `ListInterventionsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listInterventionsResponseDescriptor = $convert.base64Decode('ChlMaXN0SW50ZXJ2ZW50aW9uc1Jlc3BvbnNlEh0KCnBhZ2VfdG9rZW4YASABKAlSCXBhZ2VUb2tlbhI+Cg1pbnRlcnZlbnRpb25zGAIgAygLMhguY2FyYm9uLnJ0Yy5JbnRlcnZlbnRpb25SDWludGVydmVudGlvbnM=');
@$core.Deprecated('Use createInterventionRequestDescriptor instead')
const CreateInterventionRequest$json = const {
  '1': 'CreateInterventionRequest',
  '2': const [
    const {'1': 'intervention', '3': 1, '4': 1, '5': 11, '6': '.carbon.rtc.Intervention', '10': 'intervention'},
  ],
};

/// Descriptor for `CreateInterventionRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createInterventionRequestDescriptor = $convert.base64Decode('ChlDcmVhdGVJbnRlcnZlbnRpb25SZXF1ZXN0EjwKDGludGVydmVudGlvbhgBIAEoCzIYLmNhcmJvbi5ydGMuSW50ZXJ2ZW50aW9uUgxpbnRlcnZlbnRpb24=');
@$core.Deprecated('Use createInterventionResponseDescriptor instead')
const CreateInterventionResponse$json = const {
  '1': 'CreateInterventionResponse',
  '2': const [
    const {'1': 'intervention', '3': 1, '4': 1, '5': 11, '6': '.carbon.rtc.Intervention', '10': 'intervention'},
  ],
};

/// Descriptor for `CreateInterventionResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createInterventionResponseDescriptor = $convert.base64Decode('ChpDcmVhdGVJbnRlcnZlbnRpb25SZXNwb25zZRI8CgxpbnRlcnZlbnRpb24YASABKAsyGC5jYXJib24ucnRjLkludGVydmVudGlvblIMaW50ZXJ2ZW50aW9u');
@$core.Deprecated('Use getActiveTaskRequestDescriptor instead')
const GetActiveTaskRequest$json = const {
  '1': 'GetActiveTaskRequest',
  '2': const [
    const {'1': 'current_location', '3': 2, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'currentLocation'},
  ],
};

/// Descriptor for `GetActiveTaskRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getActiveTaskRequestDescriptor = $convert.base64Decode('ChRHZXRBY3RpdmVUYXNrUmVxdWVzdBI8ChBjdXJyZW50X2xvY2F0aW9uGAIgASgLMhEuY2FyYm9uLmdlby5Qb2ludFIPY3VycmVudExvY2F0aW9u');
@$core.Deprecated('Use getActiveTaskResponseDescriptor instead')
const GetActiveTaskResponse$json = const {
  '1': 'GetActiveTaskResponse',
  '2': const [
    const {'1': 'task', '3': 1, '4': 1, '5': 11, '6': '.carbon.rtc.Task', '10': 'task'},
  ],
};

/// Descriptor for `GetActiveTaskResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getActiveTaskResponseDescriptor = $convert.base64Decode('ChVHZXRBY3RpdmVUYXNrUmVzcG9uc2USJAoEdGFzaxgBIAEoCzIQLmNhcmJvbi5ydGMuVGFza1IEdGFzaw==');
@$core.Deprecated('Use getTaskRequestDescriptor instead')
const GetTaskRequest$json = const {
  '1': 'GetTaskRequest',
  '2': const [
    const {'1': 'task_id', '3': 1, '4': 1, '5': 4, '10': 'taskId'},
  ],
};

/// Descriptor for `GetTaskRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTaskRequestDescriptor = $convert.base64Decode('Cg5HZXRUYXNrUmVxdWVzdBIXCgd0YXNrX2lkGAEgASgEUgZ0YXNrSWQ=');
@$core.Deprecated('Use getTaskResponseDescriptor instead')
const GetTaskResponse$json = const {
  '1': 'GetTaskResponse',
  '2': const [
    const {'1': 'task', '3': 1, '4': 1, '5': 11, '6': '.carbon.rtc.Task', '10': 'task'},
  ],
};

/// Descriptor for `GetTaskResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTaskResponseDescriptor = $convert.base64Decode('Cg9HZXRUYXNrUmVzcG9uc2USJAoEdGFzaxgBIAEoCzIQLmNhcmJvbi5ydGMuVGFza1IEdGFzaw==');
@$core.Deprecated('Use updateTaskRequestDescriptor instead')
const UpdateTaskRequest$json = const {
  '1': 'UpdateTaskRequest',
  '2': const [
    const {'1': 'task_id', '3': 1, '4': 1, '5': 4, '10': 'taskId'},
    const {'1': 'state', '3': 2, '4': 1, '5': 14, '6': '.carbon.rtc.State', '9': 0, '10': 'state', '17': true},
    const {'1': 'started_at', '3': 3, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '9': 1, '10': 'startedAt', '17': true},
    const {'1': 'start_location', '3': 4, '4': 1, '5': 11, '6': '.carbon.geo.Point', '9': 2, '10': 'startLocation', '17': true},
    const {'1': 'start_heading', '3': 5, '4': 1, '5': 1, '9': 3, '10': 'startHeading', '17': true},
    const {'1': 'ended_at', '3': 6, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '9': 4, '10': 'endedAt', '17': true},
    const {'1': 'end_location', '3': 7, '4': 1, '5': 11, '6': '.carbon.geo.Point', '9': 5, '10': 'endLocation', '17': true},
    const {'1': 'end_heading', '3': 8, '4': 1, '5': 1, '9': 6, '10': 'endHeading', '17': true},
    const {'1': 'status_info', '3': 9, '4': 1, '5': 9, '9': 7, '10': 'statusInfo', '17': true},
    const {'1': 'progress_updated_at', '3': 10, '4': 1, '5': 11, '6': '.google.protobuf.Timestamp', '9': 8, '10': 'progressUpdatedAt', '17': true},
    const {'1': 'progress_location', '3': 11, '4': 1, '5': 11, '6': '.carbon.geo.Point', '9': 9, '10': 'progressLocation', '17': true},
    const {'1': 'progress_heading', '3': 12, '4': 1, '5': 1, '9': 10, '10': 'progressHeading', '17': true},
  ],
  '8': const [
    const {'1': '_state'},
    const {'1': '_started_at'},
    const {'1': '_start_location'},
    const {'1': '_start_heading'},
    const {'1': '_ended_at'},
    const {'1': '_end_location'},
    const {'1': '_end_heading'},
    const {'1': '_status_info'},
    const {'1': '_progress_updated_at'},
    const {'1': '_progress_location'},
    const {'1': '_progress_heading'},
  ],
};

/// Descriptor for `UpdateTaskRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List updateTaskRequestDescriptor = $convert.base64Decode('ChFVcGRhdGVUYXNrUmVxdWVzdBIXCgd0YXNrX2lkGAEgASgEUgZ0YXNrSWQSLAoFc3RhdGUYAiABKA4yES5jYXJib24ucnRjLlN0YXRlSABSBXN0YXRliAEBEj4KCnN0YXJ0ZWRfYXQYAyABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wSAFSCXN0YXJ0ZWRBdIgBARI9Cg5zdGFydF9sb2NhdGlvbhgEIAEoCzIRLmNhcmJvbi5nZW8uUG9pbnRIAlINc3RhcnRMb2NhdGlvbogBARIoCg1zdGFydF9oZWFkaW5nGAUgASgBSANSDHN0YXJ0SGVhZGluZ4gBARI6CghlbmRlZF9hdBgGIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXBIBFIHZW5kZWRBdIgBARI5CgxlbmRfbG9jYXRpb24YByABKAsyES5jYXJib24uZ2VvLlBvaW50SAVSC2VuZExvY2F0aW9uiAEBEiQKC2VuZF9oZWFkaW5nGAggASgBSAZSCmVuZEhlYWRpbmeIAQESJAoLc3RhdHVzX2luZm8YCSABKAlIB1IKc3RhdHVzSW5mb4gBARJPChNwcm9ncmVzc191cGRhdGVkX2F0GAogASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcEgIUhFwcm9ncmVzc1VwZGF0ZWRBdIgBARJDChFwcm9ncmVzc19sb2NhdGlvbhgLIAEoCzIRLmNhcmJvbi5nZW8uUG9pbnRICVIQcHJvZ3Jlc3NMb2NhdGlvbogBARIuChBwcm9ncmVzc19oZWFkaW5nGAwgASgBSApSD3Byb2dyZXNzSGVhZGluZ4gBAUIICgZfc3RhdGVCDQoLX3N0YXJ0ZWRfYXRCEQoPX3N0YXJ0X2xvY2F0aW9uQhAKDl9zdGFydF9oZWFkaW5nQgsKCV9lbmRlZF9hdEIPCg1fZW5kX2xvY2F0aW9uQg4KDF9lbmRfaGVhZGluZ0IOCgxfc3RhdHVzX2luZm9CFgoUX3Byb2dyZXNzX3VwZGF0ZWRfYXRCFAoSX3Byb2dyZXNzX2xvY2F0aW9uQhMKEV9wcm9ncmVzc19oZWFkaW5n');
@$core.Deprecated('Use updateTaskResponseDescriptor instead')
const UpdateTaskResponse$json = const {
  '1': 'UpdateTaskResponse',
  '2': const [
    const {'1': 'task', '3': 1, '4': 1, '5': 11, '6': '.carbon.rtc.Task', '10': 'task'},
  ],
};

/// Descriptor for `UpdateTaskResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List updateTaskResponseDescriptor = $convert.base64Decode('ChJVcGRhdGVUYXNrUmVzcG9uc2USJAoEdGFzaxgBIAEoCzIQLmNhcmJvbi5ydGMuVGFza1IEdGFzaw==');
@$core.Deprecated('Use startManualTaskRequestDescriptor instead')
const StartManualTaskRequest$json = const {
  '1': 'StartManualTaskRequest',
  '2': const [
    const {'1': 'start_location', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'startLocation'},
    const {'1': 'start_heading', '3': 2, '4': 1, '5': 1, '10': 'startHeading'},
  ],
};

/// Descriptor for `StartManualTaskRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startManualTaskRequestDescriptor = $convert.base64Decode('ChZTdGFydE1hbnVhbFRhc2tSZXF1ZXN0EjgKDnN0YXJ0X2xvY2F0aW9uGAEgASgLMhEuY2FyYm9uLmdlby5Qb2ludFINc3RhcnRMb2NhdGlvbhIjCg1zdGFydF9oZWFkaW5nGAIgASgBUgxzdGFydEhlYWRpbmc=');
@$core.Deprecated('Use stopManualTaskRequestDescriptor instead')
const StopManualTaskRequest$json = const {
  '1': 'StopManualTaskRequest',
  '2': const [
    const {'1': 'end_location', '3': 1, '4': 1, '5': 11, '6': '.carbon.geo.Point', '10': 'endLocation'},
    const {'1': 'end_heading', '3': 2, '4': 1, '5': 1, '10': 'endHeading'},
  ],
};

/// Descriptor for `StopManualTaskRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List stopManualTaskRequestDescriptor = $convert.base64Decode('ChVTdG9wTWFudWFsVGFza1JlcXVlc3QSNAoMZW5kX2xvY2F0aW9uGAEgASgLMhEuY2FyYm9uLmdlby5Qb2ludFILZW5kTG9jYXRpb24SHwoLZW5kX2hlYWRpbmcYAiABKAFSCmVuZEhlYWRpbmc=');
