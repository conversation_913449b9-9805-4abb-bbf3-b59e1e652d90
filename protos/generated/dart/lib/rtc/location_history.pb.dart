///
//  Generated code. Do not modify.
//  source: rtc/location_history.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../geo/geo.pb.dart' as $75;
import '../google/protobuf/timestamp.pb.dart' as $70;

class RobotData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$fixnum.Int64>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'active')
    ..a<$fixnum.Int64>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectiveId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  RobotData._() : super();
  factory RobotData({
    $fixnum.Int64? taskId,
    $core.bool? active,
    $fixnum.Int64? objectiveId,
  }) {
    final _result = create();
    if (taskId != null) {
      _result.taskId = taskId;
    }
    if (active != null) {
      _result.active = active;
    }
    if (objectiveId != null) {
      _result.objectiveId = objectiveId;
    }
    return _result;
  }
  factory RobotData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotData clone() => RobotData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotData copyWith(void Function(RobotData) updates) => super.copyWith((message) => updates(message as RobotData)) as RobotData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotData create() => RobotData._();
  RobotData createEmptyInstance() => create();
  static $pb.PbList<RobotData> createRepeated() => $pb.PbList<RobotData>();
  @$core.pragma('dart2js:noInline')
  static RobotData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotData>(create);
  static RobotData? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get taskId => $_getI64(0);
  @$pb.TagNumber(1)
  set taskId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTaskId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTaskId() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get active => $_getBF(1);
  @$pb.TagNumber(2)
  set active($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearActive() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get objectiveId => $_getI64(2);
  @$pb.TagNumber(3)
  set objectiveId($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasObjectiveId() => $_has(2);
  @$pb.TagNumber(3)
  void clearObjectiveId() => clearField(3);
}

class LocationHistoryRecord extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LocationHistoryRecord', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<$75.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'point', subBuilder: $75.Point.create)
    ..aOM<$70.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $70.Timestamp.create)
    ..aOM<RobotData>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'data', subBuilder: RobotData.create)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'headingDegrees', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  LocationHistoryRecord._() : super();
  factory LocationHistoryRecord({
    $75.Point? point,
    $70.Timestamp? timestamp,
    RobotData? data,
    $core.double? headingDegrees,
  }) {
    final _result = create();
    if (point != null) {
      _result.point = point;
    }
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    if (data != null) {
      _result.data = data;
    }
    if (headingDegrees != null) {
      _result.headingDegrees = headingDegrees;
    }
    return _result;
  }
  factory LocationHistoryRecord.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LocationHistoryRecord.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LocationHistoryRecord clone() => LocationHistoryRecord()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LocationHistoryRecord copyWith(void Function(LocationHistoryRecord) updates) => super.copyWith((message) => updates(message as LocationHistoryRecord)) as LocationHistoryRecord; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LocationHistoryRecord create() => LocationHistoryRecord._();
  LocationHistoryRecord createEmptyInstance() => create();
  static $pb.PbList<LocationHistoryRecord> createRepeated() => $pb.PbList<LocationHistoryRecord>();
  @$core.pragma('dart2js:noInline')
  static LocationHistoryRecord getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LocationHistoryRecord>(create);
  static LocationHistoryRecord? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Point get point => $_getN(0);
  @$pb.TagNumber(1)
  set point($75.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPoint() => $_has(0);
  @$pb.TagNumber(1)
  void clearPoint() => clearField(1);
  @$pb.TagNumber(1)
  $75.Point ensurePoint() => $_ensure(0);

  @$pb.TagNumber(2)
  $70.Timestamp get timestamp => $_getN(1);
  @$pb.TagNumber(2)
  set timestamp($70.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => clearField(2);
  @$pb.TagNumber(2)
  $70.Timestamp ensureTimestamp() => $_ensure(1);

  @$pb.TagNumber(3)
  RobotData get data => $_getN(2);
  @$pb.TagNumber(3)
  set data(RobotData v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasData() => $_has(2);
  @$pb.TagNumber(3)
  void clearData() => clearField(3);
  @$pb.TagNumber(3)
  RobotData ensureData() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.double get headingDegrees => $_getN(3);
  @$pb.TagNumber(4)
  set headingDegrees($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasHeadingDegrees() => $_has(3);
  @$pb.TagNumber(4)
  void clearHeadingDegrees() => clearField(4);
}

class LocationHistoryRecordList extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LocationHistoryRecordList', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..pc<LocationHistoryRecord>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'records', $pb.PbFieldType.PM, subBuilder: LocationHistoryRecord.create)
    ..hasRequiredFields = false
  ;

  LocationHistoryRecordList._() : super();
  factory LocationHistoryRecordList({
    $core.Iterable<LocationHistoryRecord>? records,
  }) {
    final _result = create();
    if (records != null) {
      _result.records.addAll(records);
    }
    return _result;
  }
  factory LocationHistoryRecordList.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LocationHistoryRecordList.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LocationHistoryRecordList clone() => LocationHistoryRecordList()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LocationHistoryRecordList copyWith(void Function(LocationHistoryRecordList) updates) => super.copyWith((message) => updates(message as LocationHistoryRecordList)) as LocationHistoryRecordList; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LocationHistoryRecordList create() => LocationHistoryRecordList._();
  LocationHistoryRecordList createEmptyInstance() => create();
  static $pb.PbList<LocationHistoryRecordList> createRepeated() => $pb.PbList<LocationHistoryRecordList>();
  @$core.pragma('dart2js:noInline')
  static LocationHistoryRecordList getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LocationHistoryRecordList>(create);
  static LocationHistoryRecordList? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<LocationHistoryRecord> get records => $_getList(0);
}

class LogLocationHistoryRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LogLocationHistoryRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOM<LocationHistoryRecordList>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'history', subBuilder: LocationHistoryRecordList.create)
    ..hasRequiredFields = false
  ;

  LogLocationHistoryRequest._() : super();
  factory LogLocationHistoryRequest({
    LocationHistoryRecordList? history,
  }) {
    final _result = create();
    if (history != null) {
      _result.history = history;
    }
    return _result;
  }
  factory LogLocationHistoryRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LogLocationHistoryRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LogLocationHistoryRequest clone() => LogLocationHistoryRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LogLocationHistoryRequest copyWith(void Function(LogLocationHistoryRequest) updates) => super.copyWith((message) => updates(message as LogLocationHistoryRequest)) as LogLocationHistoryRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LogLocationHistoryRequest create() => LogLocationHistoryRequest._();
  LogLocationHistoryRequest createEmptyInstance() => create();
  static $pb.PbList<LogLocationHistoryRequest> createRepeated() => $pb.PbList<LogLocationHistoryRequest>();
  @$core.pragma('dart2js:noInline')
  static LogLocationHistoryRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LogLocationHistoryRequest>(create);
  static LogLocationHistoryRequest? _defaultInstance;

  @$pb.TagNumber(1)
  LocationHistoryRecordList get history => $_getN(0);
  @$pb.TagNumber(1)
  set history(LocationHistoryRecordList v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHistory() => $_has(0);
  @$pb.TagNumber(1)
  void clearHistory() => clearField(1);
  @$pb.TagNumber(1)
  LocationHistoryRecordList ensureHistory() => $_ensure(0);
}

class ListRobotsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListRobotsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageSize', $pb.PbFieldType.O3)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..pPS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerials')
    ..hasRequiredFields = false
  ;

  ListRobotsRequest._() : super();
  factory ListRobotsRequest({
    $core.int? pageSize,
    $core.String? pageToken,
    $core.Iterable<$core.String>? robotSerials,
  }) {
    final _result = create();
    if (pageSize != null) {
      _result.pageSize = pageSize;
    }
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (robotSerials != null) {
      _result.robotSerials.addAll(robotSerials);
    }
    return _result;
  }
  factory ListRobotsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListRobotsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListRobotsRequest clone() => ListRobotsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListRobotsRequest copyWith(void Function(ListRobotsRequest) updates) => super.copyWith((message) => updates(message as ListRobotsRequest)) as ListRobotsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListRobotsRequest create() => ListRobotsRequest._();
  ListRobotsRequest createEmptyInstance() => create();
  static $pb.PbList<ListRobotsRequest> createRepeated() => $pb.PbList<ListRobotsRequest>();
  @$core.pragma('dart2js:noInline')
  static ListRobotsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListRobotsRequest>(create);
  static ListRobotsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get pageSize => $_getIZ(0);
  @$pb.TagNumber(1)
  set pageSize($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageSize() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageSize() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get pageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set pageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearPageToken() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.String> get robotSerials => $_getList(2);
}

class ListRobotsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListRobotsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextPageToken')
    ..pc<RobotSummary>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robots', $pb.PbFieldType.PM, subBuilder: RobotSummary.create)
    ..hasRequiredFields = false
  ;

  ListRobotsResponse._() : super();
  factory ListRobotsResponse({
    $core.String? nextPageToken,
    $core.Iterable<RobotSummary>? robots,
  }) {
    final _result = create();
    if (nextPageToken != null) {
      _result.nextPageToken = nextPageToken;
    }
    if (robots != null) {
      _result.robots.addAll(robots);
    }
    return _result;
  }
  factory ListRobotsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListRobotsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListRobotsResponse clone() => ListRobotsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListRobotsResponse copyWith(void Function(ListRobotsResponse) updates) => super.copyWith((message) => updates(message as ListRobotsResponse)) as ListRobotsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListRobotsResponse create() => ListRobotsResponse._();
  ListRobotsResponse createEmptyInstance() => create();
  static $pb.PbList<ListRobotsResponse> createRepeated() => $pb.PbList<ListRobotsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListRobotsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListRobotsResponse>(create);
  static ListRobotsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get nextPageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set nextPageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNextPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearNextPageToken() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<RobotSummary> get robots => $_getList(1);
}

class RobotSummary extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotSummary', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..aOM<LocationHistoryRecord>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastSeen', subBuilder: LocationHistoryRecord.create)
    ..hasRequiredFields = false
  ;

  RobotSummary._() : super();
  factory RobotSummary({
    $core.String? serial,
    LocationHistoryRecord? lastSeen,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (lastSeen != null) {
      _result.lastSeen = lastSeen;
    }
    return _result;
  }
  factory RobotSummary.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotSummary.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotSummary clone() => RobotSummary()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotSummary copyWith(void Function(RobotSummary) updates) => super.copyWith((message) => updates(message as RobotSummary)) as RobotSummary; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotSummary create() => RobotSummary._();
  RobotSummary createEmptyInstance() => create();
  static $pb.PbList<RobotSummary> createRepeated() => $pb.PbList<RobotSummary>();
  @$core.pragma('dart2js:noInline')
  static RobotSummary getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotSummary>(create);
  static RobotSummary? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  LocationHistoryRecord get lastSeen => $_getN(1);
  @$pb.TagNumber(2)
  set lastSeen(LocationHistoryRecord v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLastSeen() => $_has(1);
  @$pb.TagNumber(2)
  void clearLastSeen() => clearField(2);
  @$pb.TagNumber(2)
  LocationHistoryRecord ensureLastSeen() => $_ensure(1);
}

class ListLocationHistoryRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListLocationHistoryRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageSize', $pb.PbFieldType.O3)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..aOM<$70.Timestamp>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'start', subBuilder: $70.Timestamp.create)
    ..aOM<$70.Timestamp>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'end', subBuilder: $70.Timestamp.create)
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'desc')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'includeClosest')
    ..a<$fixnum.Int64>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$fixnum.Int64>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'objectiveId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  ListLocationHistoryRequest._() : super();
  factory ListLocationHistoryRequest({
    $core.int? pageSize,
    $core.String? pageToken,
    $core.String? robotSerial,
    $70.Timestamp? start,
    $70.Timestamp? end,
    $core.bool? desc,
    $core.bool? includeClosest,
    $fixnum.Int64? taskId,
    $fixnum.Int64? objectiveId,
  }) {
    final _result = create();
    if (pageSize != null) {
      _result.pageSize = pageSize;
    }
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (start != null) {
      _result.start = start;
    }
    if (end != null) {
      _result.end = end;
    }
    if (desc != null) {
      _result.desc = desc;
    }
    if (includeClosest != null) {
      _result.includeClosest = includeClosest;
    }
    if (taskId != null) {
      _result.taskId = taskId;
    }
    if (objectiveId != null) {
      _result.objectiveId = objectiveId;
    }
    return _result;
  }
  factory ListLocationHistoryRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListLocationHistoryRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListLocationHistoryRequest clone() => ListLocationHistoryRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListLocationHistoryRequest copyWith(void Function(ListLocationHistoryRequest) updates) => super.copyWith((message) => updates(message as ListLocationHistoryRequest)) as ListLocationHistoryRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListLocationHistoryRequest create() => ListLocationHistoryRequest._();
  ListLocationHistoryRequest createEmptyInstance() => create();
  static $pb.PbList<ListLocationHistoryRequest> createRepeated() => $pb.PbList<ListLocationHistoryRequest>();
  @$core.pragma('dart2js:noInline')
  static ListLocationHistoryRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListLocationHistoryRequest>(create);
  static ListLocationHistoryRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get pageSize => $_getIZ(0);
  @$pb.TagNumber(1)
  set pageSize($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageSize() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageSize() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get pageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set pageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearPageToken() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get robotSerial => $_getSZ(2);
  @$pb.TagNumber(3)
  set robotSerial($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotSerial() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotSerial() => clearField(3);

  @$pb.TagNumber(4)
  $70.Timestamp get start => $_getN(3);
  @$pb.TagNumber(4)
  set start($70.Timestamp v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasStart() => $_has(3);
  @$pb.TagNumber(4)
  void clearStart() => clearField(4);
  @$pb.TagNumber(4)
  $70.Timestamp ensureStart() => $_ensure(3);

  @$pb.TagNumber(5)
  $70.Timestamp get end => $_getN(4);
  @$pb.TagNumber(5)
  set end($70.Timestamp v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasEnd() => $_has(4);
  @$pb.TagNumber(5)
  void clearEnd() => clearField(5);
  @$pb.TagNumber(5)
  $70.Timestamp ensureEnd() => $_ensure(4);

  @$pb.TagNumber(6)
  $core.bool get desc => $_getBF(5);
  @$pb.TagNumber(6)
  set desc($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDesc() => $_has(5);
  @$pb.TagNumber(6)
  void clearDesc() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get includeClosest => $_getBF(6);
  @$pb.TagNumber(7)
  set includeClosest($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasIncludeClosest() => $_has(6);
  @$pb.TagNumber(7)
  void clearIncludeClosest() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get taskId => $_getI64(7);
  @$pb.TagNumber(8)
  set taskId($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasTaskId() => $_has(7);
  @$pb.TagNumber(8)
  void clearTaskId() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get objectiveId => $_getI64(8);
  @$pb.TagNumber(9)
  set objectiveId($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasObjectiveId() => $_has(8);
  @$pb.TagNumber(9)
  void clearObjectiveId() => clearField(9);
}

class ListLocationHistoryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListLocationHistoryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.rtc'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextPageToken')
    ..aOM<LocationHistoryRecordList>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'history', subBuilder: LocationHistoryRecordList.create)
    ..hasRequiredFields = false
  ;

  ListLocationHistoryResponse._() : super();
  factory ListLocationHistoryResponse({
    $core.String? nextPageToken,
    LocationHistoryRecordList? history,
  }) {
    final _result = create();
    if (nextPageToken != null) {
      _result.nextPageToken = nextPageToken;
    }
    if (history != null) {
      _result.history = history;
    }
    return _result;
  }
  factory ListLocationHistoryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListLocationHistoryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListLocationHistoryResponse clone() => ListLocationHistoryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListLocationHistoryResponse copyWith(void Function(ListLocationHistoryResponse) updates) => super.copyWith((message) => updates(message as ListLocationHistoryResponse)) as ListLocationHistoryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListLocationHistoryResponse create() => ListLocationHistoryResponse._();
  ListLocationHistoryResponse createEmptyInstance() => create();
  static $pb.PbList<ListLocationHistoryResponse> createRepeated() => $pb.PbList<ListLocationHistoryResponse>();
  @$core.pragma('dart2js:noInline')
  static ListLocationHistoryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListLocationHistoryResponse>(create);
  static ListLocationHistoryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get nextPageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set nextPageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNextPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearNextPageToken() => clearField(1);

  @$pb.TagNumber(2)
  LocationHistoryRecordList get history => $_getN(1);
  @$pb.TagNumber(2)
  set history(LocationHistoryRecordList v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasHistory() => $_has(1);
  @$pb.TagNumber(2)
  void clearHistory() => clearField(2);
  @$pb.TagNumber(2)
  LocationHistoryRecordList ensureHistory() => $_ensure(1);
}

