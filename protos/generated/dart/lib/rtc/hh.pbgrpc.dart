///
//  Generated code. Do not modify.
//  source: rtc/hh.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'hh.pb.dart' as $54;
import '../google/protobuf/empty.pb.dart' as $53;
export 'hh.pb.dart';

class RobotStateClient extends $grpc.Client {
  static final _$setRobotRequiredState =
      $grpc.ClientMethod<$54.SetRobotRequiredStateRequest, $53.Empty>(
          '/carbon.rtc.RobotState/SetRobotRequiredState',
          ($54.SetRobotRequiredStateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $53.Empty.fromBuffer(value));
  static final _$getNextRequiredState = $grpc.ClientMethod<
          $54.GetRobotRequiredStateRequest, $54.GetRobotRequiredStateResponse>(
      '/carbon.rtc.RobotState/GetNextRequiredState',
      ($54.GetRobotRequiredStateRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $54.GetRobotRequiredStateResponse.fromBuffer(value));
  static final _$robotRequirementStream =
      $grpc.ClientMethod<$54.RobotStatusInfo, $54.RobotRequiredState>(
          '/carbon.rtc.RobotState/RobotRequirementStream',
          ($54.RobotStatusInfo value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $54.RobotRequiredState.fromBuffer(value));

  RobotStateClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$53.Empty> setRobotRequiredState(
      $54.SetRobotRequiredStateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$setRobotRequiredState, request, options: options);
  }

  $grpc.ResponseFuture<$54.GetRobotRequiredStateResponse> getNextRequiredState(
      $54.GetRobotRequiredStateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextRequiredState, request, options: options);
  }

  $grpc.ResponseStream<$54.RobotRequiredState> robotRequirementStream(
      $async.Stream<$54.RobotStatusInfo> request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$robotRequirementStream, request,
        options: options);
  }
}

abstract class RobotStateServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.rtc.RobotState';

  RobotStateServiceBase() {
    $addMethod($grpc.ServiceMethod<$54.SetRobotRequiredStateRequest, $53.Empty>(
        'SetRobotRequiredState',
        setRobotRequiredState_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $54.SetRobotRequiredStateRequest.fromBuffer(value),
        ($53.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$54.GetRobotRequiredStateRequest,
            $54.GetRobotRequiredStateResponse>(
        'GetNextRequiredState',
        getNextRequiredState_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $54.GetRobotRequiredStateRequest.fromBuffer(value),
        ($54.GetRobotRequiredStateResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$54.RobotStatusInfo, $54.RobotRequiredState>(
        'RobotRequirementStream',
        robotRequirementStream,
        true,
        true,
        ($core.List<$core.int> value) => $54.RobotStatusInfo.fromBuffer(value),
        ($54.RobotRequiredState value) => value.writeToBuffer()));
  }

  $async.Future<$53.Empty> setRobotRequiredState_Pre($grpc.ServiceCall call,
      $async.Future<$54.SetRobotRequiredStateRequest> request) async {
    return setRobotRequiredState(call, await request);
  }

  $async.Future<$54.GetRobotRequiredStateResponse> getNextRequiredState_Pre(
      $grpc.ServiceCall call,
      $async.Future<$54.GetRobotRequiredStateRequest> request) async {
    return getNextRequiredState(call, await request);
  }

  $async.Future<$53.Empty> setRobotRequiredState(
      $grpc.ServiceCall call, $54.SetRobotRequiredStateRequest request);
  $async.Future<$54.GetRobotRequiredStateResponse> getNextRequiredState(
      $grpc.ServiceCall call, $54.GetRobotRequiredStateRequest request);
  $async.Stream<$54.RobotRequiredState> robotRequirementStream(
      $grpc.ServiceCall call, $async.Stream<$54.RobotStatusInfo> request);
}
