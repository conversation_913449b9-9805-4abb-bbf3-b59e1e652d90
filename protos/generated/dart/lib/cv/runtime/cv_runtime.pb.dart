///
//  Generated code. Do not modify.
//  source: cv/runtime/cv_runtime.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../../weed_tracking/weed_tracking.pb.dart' as $4;

import 'cv_runtime.pbenum.dart';
import '../../lib/common/camera/camera.pbenum.dart' as $60;
import '../cv.pbenum.dart' as $61;

export 'cv_runtime.pbenum.dart';

class MaskExpressions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MaskExpressions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<MaskExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exprs', $pb.PbFieldType.PM, subBuilder: MaskExpression.create)
    ..hasRequiredFields = false
  ;

  MaskExpressions._() : super();
  factory MaskExpressions({
    $core.Iterable<MaskExpression>? exprs,
  }) {
    final _result = create();
    if (exprs != null) {
      _result.exprs.addAll(exprs);
    }
    return _result;
  }
  factory MaskExpressions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MaskExpressions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MaskExpressions clone() => MaskExpressions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MaskExpressions copyWith(void Function(MaskExpressions) updates) => super.copyWith((message) => updates(message as MaskExpressions)) as MaskExpressions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MaskExpressions create() => MaskExpressions._();
  MaskExpressions createEmptyInstance() => create();
  static $pb.PbList<MaskExpressions> createRepeated() => $pb.PbList<MaskExpressions>();
  @$core.pragma('dart2js:noInline')
  static MaskExpressions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MaskExpressions>(create);
  static MaskExpressions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<MaskExpression> get exprs => $_getList(0);
}

class TargetSafetyZone extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'TargetSafetyZone', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'up', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'down', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'left', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'right', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  TargetSafetyZone._() : super();
  factory TargetSafetyZone({
    $core.double? up,
    $core.double? down,
    $core.double? left,
    $core.double? right,
  }) {
    final _result = create();
    if (up != null) {
      _result.up = up;
    }
    if (down != null) {
      _result.down = down;
    }
    if (left != null) {
      _result.left = left;
    }
    if (right != null) {
      _result.right = right;
    }
    return _result;
  }
  factory TargetSafetyZone.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TargetSafetyZone.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TargetSafetyZone clone() => TargetSafetyZone()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TargetSafetyZone copyWith(void Function(TargetSafetyZone) updates) => super.copyWith((message) => updates(message as TargetSafetyZone)) as TargetSafetyZone; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static TargetSafetyZone create() => TargetSafetyZone._();
  TargetSafetyZone createEmptyInstance() => create();
  static $pb.PbList<TargetSafetyZone> createRepeated() => $pb.PbList<TargetSafetyZone>();
  @$core.pragma('dart2js:noInline')
  static TargetSafetyZone getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TargetSafetyZone>(create);
  static TargetSafetyZone? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get up => $_getN(0);
  @$pb.TagNumber(1)
  set up($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUp() => $_has(0);
  @$pb.TagNumber(1)
  void clearUp() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get down => $_getN(1);
  @$pb.TagNumber(2)
  set down($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDown() => $_has(1);
  @$pb.TagNumber(2)
  void clearDown() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get left => $_getN(2);
  @$pb.TagNumber(3)
  set left($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLeft() => $_has(2);
  @$pb.TagNumber(3)
  void clearLeft() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get right => $_getN(3);
  @$pb.TagNumber(4)
  set right($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasRight() => $_has(3);
  @$pb.TagNumber(4)
  void clearRight() => clearField(4);
}

class P2PContext extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PContext', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictCamId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictTimestampMs')
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictCoordX', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictCoordY', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  P2PContext._() : super();
  factory P2PContext({
    $core.String? predictCamId,
    $fixnum.Int64? predictTimestampMs,
    $core.double? predictCoordX,
    $core.double? predictCoordY,
  }) {
    final _result = create();
    if (predictCamId != null) {
      _result.predictCamId = predictCamId;
    }
    if (predictTimestampMs != null) {
      _result.predictTimestampMs = predictTimestampMs;
    }
    if (predictCoordX != null) {
      _result.predictCoordX = predictCoordX;
    }
    if (predictCoordY != null) {
      _result.predictCoordY = predictCoordY;
    }
    return _result;
  }
  factory P2PContext.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PContext.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PContext clone() => P2PContext()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PContext copyWith(void Function(P2PContext) updates) => super.copyWith((message) => updates(message as P2PContext)) as P2PContext; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PContext create() => P2PContext._();
  P2PContext createEmptyInstance() => create();
  static $pb.PbList<P2PContext> createRepeated() => $pb.PbList<P2PContext>();
  @$core.pragma('dart2js:noInline')
  static P2PContext getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PContext>(create);
  static P2PContext? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get predictCamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set predictCamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPredictCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPredictCamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get predictTimestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set predictTimestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPredictTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearPredictTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get predictCoordX => $_getN(2);
  @$pb.TagNumber(3)
  set predictCoordX($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPredictCoordX() => $_has(2);
  @$pb.TagNumber(3)
  void clearPredictCoordX() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get predictCoordY => $_getN(3);
  @$pb.TagNumber(4)
  set predictCoordY($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPredictCoordY() => $_has(3);
  @$pb.TagNumber(4)
  void clearPredictCoordY() => clearField(4);
}

class SetP2PContextRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetP2PContextRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCamId')
    ..aOM<P2PContext>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'primaryContext', subBuilder: P2PContext.create)
    ..aOM<P2PContext>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'secondaryContext', subBuilder: P2PContext.create)
    ..aOM<TargetSafetyZone>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'safetyZone', subBuilder: TargetSafetyZone.create)
    ..hasRequiredFields = false
  ;

  SetP2PContextRequest._() : super();
  factory SetP2PContextRequest({
    $core.String? targetCamId,
    P2PContext? primaryContext,
    P2PContext? secondaryContext,
    TargetSafetyZone? safetyZone,
  }) {
    final _result = create();
    if (targetCamId != null) {
      _result.targetCamId = targetCamId;
    }
    if (primaryContext != null) {
      _result.primaryContext = primaryContext;
    }
    if (secondaryContext != null) {
      _result.secondaryContext = secondaryContext;
    }
    if (safetyZone != null) {
      _result.safetyZone = safetyZone;
    }
    return _result;
  }
  factory SetP2PContextRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetP2PContextRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetP2PContextRequest clone() => SetP2PContextRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetP2PContextRequest copyWith(void Function(SetP2PContextRequest) updates) => super.copyWith((message) => updates(message as SetP2PContextRequest)) as SetP2PContextRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetP2PContextRequest create() => SetP2PContextRequest._();
  SetP2PContextRequest createEmptyInstance() => create();
  static $pb.PbList<SetP2PContextRequest> createRepeated() => $pb.PbList<SetP2PContextRequest>();
  @$core.pragma('dart2js:noInline')
  static SetP2PContextRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetP2PContextRequest>(create);
  static SetP2PContextRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get targetCamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set targetCamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetCamId() => clearField(1);

  @$pb.TagNumber(2)
  P2PContext get primaryContext => $_getN(1);
  @$pb.TagNumber(2)
  set primaryContext(P2PContext v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasPrimaryContext() => $_has(1);
  @$pb.TagNumber(2)
  void clearPrimaryContext() => clearField(2);
  @$pb.TagNumber(2)
  P2PContext ensurePrimaryContext() => $_ensure(1);

  @$pb.TagNumber(3)
  P2PContext get secondaryContext => $_getN(2);
  @$pb.TagNumber(3)
  set secondaryContext(P2PContext v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasSecondaryContext() => $_has(2);
  @$pb.TagNumber(3)
  void clearSecondaryContext() => clearField(3);
  @$pb.TagNumber(3)
  P2PContext ensureSecondaryContext() => $_ensure(2);

  @$pb.TagNumber(4)
  TargetSafetyZone get safetyZone => $_getN(3);
  @$pb.TagNumber(4)
  set safetyZone(TargetSafetyZone v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasSafetyZone() => $_has(3);
  @$pb.TagNumber(4)
  void clearSafetyZone() => clearField(4);
  @$pb.TagNumber(4)
  TargetSafetyZone ensureSafetyZone() => $_ensure(3);
}

class SetP2PContextResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetP2PContextResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetP2PContextResponse._() : super();
  factory SetP2PContextResponse() => create();
  factory SetP2PContextResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetP2PContextResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetP2PContextResponse clone() => SetP2PContextResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetP2PContextResponse copyWith(void Function(SetP2PContextResponse) updates) => super.copyWith((message) => updates(message as SetP2PContextResponse)) as SetP2PContextResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetP2PContextResponse create() => SetP2PContextResponse._();
  SetP2PContextResponse createEmptyInstance() => create();
  static $pb.PbList<SetP2PContextResponse> createRepeated() => $pb.PbList<SetP2PContextResponse>();
  @$core.pragma('dart2js:noInline')
  static SetP2PContextResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetP2PContextResponse>(create);
  static SetP2PContextResponse? _defaultInstance;
}

class GetBufferNameRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetBufferNameRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..e<BufferUseCase>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'useCase', $pb.PbFieldType.OE, defaultOrMaker: BufferUseCase.P2P, valueOf: BufferUseCase.valueOf, enumValues: BufferUseCase.values)
    ..hasRequiredFields = false
  ;

  GetBufferNameRequest._() : super();
  factory GetBufferNameRequest({
    $core.String? camId,
    BufferUseCase? useCase,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (useCase != null) {
      _result.useCase = useCase;
    }
    return _result;
  }
  factory GetBufferNameRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetBufferNameRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetBufferNameRequest clone() => GetBufferNameRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetBufferNameRequest copyWith(void Function(GetBufferNameRequest) updates) => super.copyWith((message) => updates(message as GetBufferNameRequest)) as GetBufferNameRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetBufferNameRequest create() => GetBufferNameRequest._();
  GetBufferNameRequest createEmptyInstance() => create();
  static $pb.PbList<GetBufferNameRequest> createRepeated() => $pb.PbList<GetBufferNameRequest>();
  @$core.pragma('dart2js:noInline')
  static GetBufferNameRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetBufferNameRequest>(create);
  static GetBufferNameRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  BufferUseCase get useCase => $_getN(1);
  @$pb.TagNumber(2)
  set useCase(BufferUseCase v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUseCase() => $_has(1);
  @$pb.TagNumber(2)
  void clearUseCase() => clearField(2);
}

class GetBufferNameResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetBufferNameResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bufferName')
    ..hasRequiredFields = false
  ;

  GetBufferNameResponse._() : super();
  factory GetBufferNameResponse({
    $core.String? bufferName,
  }) {
    final _result = create();
    if (bufferName != null) {
      _result.bufferName = bufferName;
    }
    return _result;
  }
  factory GetBufferNameResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetBufferNameResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetBufferNameResponse clone() => GetBufferNameResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetBufferNameResponse copyWith(void Function(GetBufferNameResponse) updates) => super.copyWith((message) => updates(message as GetBufferNameResponse)) as GetBufferNameResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetBufferNameResponse create() => GetBufferNameResponse._();
  GetBufferNameResponse createEmptyInstance() => create();
  static $pb.PbList<GetBufferNameResponse> createRepeated() => $pb.PbList<GetBufferNameResponse>();
  @$core.pragma('dart2js:noInline')
  static GetBufferNameResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetBufferNameResponse>(create);
  static GetBufferNameResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get bufferName => $_getSZ(0);
  @$pb.TagNumber(1)
  set bufferName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBufferName() => $_has(0);
  @$pb.TagNumber(1)
  void clearBufferName() => clearField(1);
}

class GetCameraDimensionsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraDimensionsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  GetCameraDimensionsRequest._() : super();
  factory GetCameraDimensionsRequest({
    $core.String? camId,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory GetCameraDimensionsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraDimensionsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraDimensionsRequest clone() => GetCameraDimensionsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraDimensionsRequest copyWith(void Function(GetCameraDimensionsRequest) updates) => super.copyWith((message) => updates(message as GetCameraDimensionsRequest)) as GetCameraDimensionsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraDimensionsRequest create() => GetCameraDimensionsRequest._();
  GetCameraDimensionsRequest createEmptyInstance() => create();
  static $pb.PbList<GetCameraDimensionsRequest> createRepeated() => $pb.PbList<GetCameraDimensionsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetCameraDimensionsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraDimensionsRequest>(create);
  static GetCameraDimensionsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);
}

class GetCameraDimensionsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraDimensionsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'height')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'transpose')
    ..hasRequiredFields = false
  ;

  GetCameraDimensionsResponse._() : super();
  factory GetCameraDimensionsResponse({
    $fixnum.Int64? width,
    $fixnum.Int64? height,
    $core.bool? transpose,
  }) {
    final _result = create();
    if (width != null) {
      _result.width = width;
    }
    if (height != null) {
      _result.height = height;
    }
    if (transpose != null) {
      _result.transpose = transpose;
    }
    return _result;
  }
  factory GetCameraDimensionsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraDimensionsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraDimensionsResponse clone() => GetCameraDimensionsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraDimensionsResponse copyWith(void Function(GetCameraDimensionsResponse) updates) => super.copyWith((message) => updates(message as GetCameraDimensionsResponse)) as GetCameraDimensionsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraDimensionsResponse create() => GetCameraDimensionsResponse._();
  GetCameraDimensionsResponse createEmptyInstance() => create();
  static $pb.PbList<GetCameraDimensionsResponse> createRepeated() => $pb.PbList<GetCameraDimensionsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetCameraDimensionsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraDimensionsResponse>(create);
  static GetCameraDimensionsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get width => $_getI64(0);
  @$pb.TagNumber(1)
  set width($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWidth() => $_has(0);
  @$pb.TagNumber(1)
  void clearWidth() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get height => $_getI64(1);
  @$pb.TagNumber(2)
  set height($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasHeight() => $_has(1);
  @$pb.TagNumber(2)
  void clearHeight() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get transpose => $_getBF(2);
  @$pb.TagNumber(3)
  set transpose($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTranspose() => $_has(2);
  @$pb.TagNumber(3)
  void clearTranspose() => clearField(3);
}

class StartP2PDataCaptureRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartP2PDataCaptureRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCamId')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captureMissRate', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captureSuccessRate', $pb.PbFieldType.OF)
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captureEnabled')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'capturePath')
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'afterTimestampMs')
    ..hasRequiredFields = false
  ;

  StartP2PDataCaptureRequest._() : super();
  factory StartP2PDataCaptureRequest({
    $core.String? targetCamId,
    $core.double? captureMissRate,
    $core.double? captureSuccessRate,
    $core.bool? captureEnabled,
    $core.String? capturePath,
    $fixnum.Int64? afterTimestampMs,
  }) {
    final _result = create();
    if (targetCamId != null) {
      _result.targetCamId = targetCamId;
    }
    if (captureMissRate != null) {
      _result.captureMissRate = captureMissRate;
    }
    if (captureSuccessRate != null) {
      _result.captureSuccessRate = captureSuccessRate;
    }
    if (captureEnabled != null) {
      _result.captureEnabled = captureEnabled;
    }
    if (capturePath != null) {
      _result.capturePath = capturePath;
    }
    if (afterTimestampMs != null) {
      _result.afterTimestampMs = afterTimestampMs;
    }
    return _result;
  }
  factory StartP2PDataCaptureRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartP2PDataCaptureRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartP2PDataCaptureRequest clone() => StartP2PDataCaptureRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartP2PDataCaptureRequest copyWith(void Function(StartP2PDataCaptureRequest) updates) => super.copyWith((message) => updates(message as StartP2PDataCaptureRequest)) as StartP2PDataCaptureRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartP2PDataCaptureRequest create() => StartP2PDataCaptureRequest._();
  StartP2PDataCaptureRequest createEmptyInstance() => create();
  static $pb.PbList<StartP2PDataCaptureRequest> createRepeated() => $pb.PbList<StartP2PDataCaptureRequest>();
  @$core.pragma('dart2js:noInline')
  static StartP2PDataCaptureRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartP2PDataCaptureRequest>(create);
  static StartP2PDataCaptureRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get targetCamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set targetCamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get captureMissRate => $_getN(1);
  @$pb.TagNumber(2)
  set captureMissRate($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCaptureMissRate() => $_has(1);
  @$pb.TagNumber(2)
  void clearCaptureMissRate() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get captureSuccessRate => $_getN(2);
  @$pb.TagNumber(3)
  set captureSuccessRate($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCaptureSuccessRate() => $_has(2);
  @$pb.TagNumber(3)
  void clearCaptureSuccessRate() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get captureEnabled => $_getBF(3);
  @$pb.TagNumber(4)
  set captureEnabled($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCaptureEnabled() => $_has(3);
  @$pb.TagNumber(4)
  void clearCaptureEnabled() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get capturePath => $_getSZ(4);
  @$pb.TagNumber(5)
  set capturePath($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCapturePath() => $_has(4);
  @$pb.TagNumber(5)
  void clearCapturePath() => clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get afterTimestampMs => $_getI64(5);
  @$pb.TagNumber(6)
  set afterTimestampMs($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAfterTimestampMs() => $_has(5);
  @$pb.TagNumber(6)
  void clearAfterTimestampMs() => clearField(6);
}

class StartP2PDataCaptureResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartP2PDataCaptureResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StartP2PDataCaptureResponse._() : super();
  factory StartP2PDataCaptureResponse() => create();
  factory StartP2PDataCaptureResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartP2PDataCaptureResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartP2PDataCaptureResponse clone() => StartP2PDataCaptureResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartP2PDataCaptureResponse copyWith(void Function(StartP2PDataCaptureResponse) updates) => super.copyWith((message) => updates(message as StartP2PDataCaptureResponse)) as StartP2PDataCaptureResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartP2PDataCaptureResponse create() => StartP2PDataCaptureResponse._();
  StartP2PDataCaptureResponse createEmptyInstance() => create();
  static $pb.PbList<StartP2PDataCaptureResponse> createRepeated() => $pb.PbList<StartP2PDataCaptureResponse>();
  @$core.pragma('dart2js:noInline')
  static StartP2PDataCaptureResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartP2PDataCaptureResponse>(create);
  static StartP2PDataCaptureResponse? _defaultInstance;
}

class StopP2PDataCaptureRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopP2PDataCaptureRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCamId')
    ..hasRequiredFields = false
  ;

  StopP2PDataCaptureRequest._() : super();
  factory StopP2PDataCaptureRequest({
    $core.String? targetCamId,
  }) {
    final _result = create();
    if (targetCamId != null) {
      _result.targetCamId = targetCamId;
    }
    return _result;
  }
  factory StopP2PDataCaptureRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopP2PDataCaptureRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopP2PDataCaptureRequest clone() => StopP2PDataCaptureRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopP2PDataCaptureRequest copyWith(void Function(StopP2PDataCaptureRequest) updates) => super.copyWith((message) => updates(message as StopP2PDataCaptureRequest)) as StopP2PDataCaptureRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopP2PDataCaptureRequest create() => StopP2PDataCaptureRequest._();
  StopP2PDataCaptureRequest createEmptyInstance() => create();
  static $pb.PbList<StopP2PDataCaptureRequest> createRepeated() => $pb.PbList<StopP2PDataCaptureRequest>();
  @$core.pragma('dart2js:noInline')
  static StopP2PDataCaptureRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopP2PDataCaptureRequest>(create);
  static StopP2PDataCaptureRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get targetCamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set targetCamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetCamId() => clearField(1);
}

class StopP2PDataCaptureResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopP2PDataCaptureResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StopP2PDataCaptureResponse._() : super();
  factory StopP2PDataCaptureResponse() => create();
  factory StopP2PDataCaptureResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopP2PDataCaptureResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopP2PDataCaptureResponse clone() => StopP2PDataCaptureResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopP2PDataCaptureResponse copyWith(void Function(StopP2PDataCaptureResponse) updates) => super.copyWith((message) => updates(message as StopP2PDataCaptureResponse)) as StopP2PDataCaptureResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopP2PDataCaptureResponse create() => StopP2PDataCaptureResponse._();
  StopP2PDataCaptureResponse createEmptyInstance() => create();
  static $pb.PbList<StopP2PDataCaptureResponse> createRepeated() => $pb.PbList<StopP2PDataCaptureResponse>();
  @$core.pragma('dart2js:noInline')
  static StopP2PDataCaptureResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopP2PDataCaptureResponse>(create);
  static StopP2PDataCaptureResponse? _defaultInstance;
}

class PointDetectionCategory extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PointDetectionCategory', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'threshold', $pb.PbFieldType.OF)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..hasRequiredFields = false
  ;

  PointDetectionCategory._() : super();
  factory PointDetectionCategory({
    $core.double? threshold,
    $core.String? category,
  }) {
    final _result = create();
    if (threshold != null) {
      _result.threshold = threshold;
    }
    if (category != null) {
      _result.category = category;
    }
    return _result;
  }
  factory PointDetectionCategory.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PointDetectionCategory.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PointDetectionCategory clone() => PointDetectionCategory()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PointDetectionCategory copyWith(void Function(PointDetectionCategory) updates) => super.copyWith((message) => updates(message as PointDetectionCategory)) as PointDetectionCategory; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PointDetectionCategory create() => PointDetectionCategory._();
  PointDetectionCategory createEmptyInstance() => create();
  static $pb.PbList<PointDetectionCategory> createRepeated() => $pb.PbList<PointDetectionCategory>();
  @$core.pragma('dart2js:noInline')
  static PointDetectionCategory getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PointDetectionCategory>(create);
  static PointDetectionCategory? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get threshold => $_getN(0);
  @$pb.TagNumber(1)
  set threshold($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasThreshold() => $_has(0);
  @$pb.TagNumber(1)
  void clearThreshold() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get category => $_getSZ(1);
  @$pb.TagNumber(2)
  set category($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCategory() => $_has(1);
  @$pb.TagNumber(2)
  void clearCategory() => clearField(2);
}

class SegmentationDetectionCategory extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SegmentationDetectionCategory', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'threshold', $pb.PbFieldType.OF)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'safetyRadiusIn', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  SegmentationDetectionCategory._() : super();
  factory SegmentationDetectionCategory({
    $core.double? threshold,
    $core.String? category,
    $core.double? safetyRadiusIn,
  }) {
    final _result = create();
    if (threshold != null) {
      _result.threshold = threshold;
    }
    if (category != null) {
      _result.category = category;
    }
    if (safetyRadiusIn != null) {
      _result.safetyRadiusIn = safetyRadiusIn;
    }
    return _result;
  }
  factory SegmentationDetectionCategory.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SegmentationDetectionCategory.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SegmentationDetectionCategory clone() => SegmentationDetectionCategory()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SegmentationDetectionCategory copyWith(void Function(SegmentationDetectionCategory) updates) => super.copyWith((message) => updates(message as SegmentationDetectionCategory)) as SegmentationDetectionCategory; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SegmentationDetectionCategory create() => SegmentationDetectionCategory._();
  SegmentationDetectionCategory createEmptyInstance() => create();
  static $pb.PbList<SegmentationDetectionCategory> createRepeated() => $pb.PbList<SegmentationDetectionCategory>();
  @$core.pragma('dart2js:noInline')
  static SegmentationDetectionCategory getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SegmentationDetectionCategory>(create);
  static SegmentationDetectionCategory? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get threshold => $_getN(0);
  @$pb.TagNumber(1)
  set threshold($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasThreshold() => $_has(0);
  @$pb.TagNumber(1)
  void clearThreshold() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get category => $_getSZ(1);
  @$pb.TagNumber(2)
  set category($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCategory() => $_has(1);
  @$pb.TagNumber(2)
  void clearCategory() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get safetyRadiusIn => $_getN(2);
  @$pb.TagNumber(3)
  set safetyRadiusIn($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSafetyRadiusIn() => $_has(2);
  @$pb.TagNumber(3)
  void clearSafetyRadiusIn() => clearField(3);
}

class DeepweedDetectionCriteriaSetting extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeepweedDetectionCriteriaSetting', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<PointDetectionCategory>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pointCategories', $pb.PbFieldType.PM, subBuilder: PointDetectionCategory.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedPointThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropPointThreshold', $pb.PbFieldType.OF)
    ..pc<SegmentationDetectionCategory>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'segmentationCategories', $pb.PbFieldType.PM, subBuilder: SegmentationDetectionCategory.create)
    ..hasRequiredFields = false
  ;

  DeepweedDetectionCriteriaSetting._() : super();
  factory DeepweedDetectionCriteriaSetting({
    $core.Iterable<PointDetectionCategory>? pointCategories,
    $core.double? weedPointThreshold,
    $core.double? cropPointThreshold,
    $core.Iterable<SegmentationDetectionCategory>? segmentationCategories,
  }) {
    final _result = create();
    if (pointCategories != null) {
      _result.pointCategories.addAll(pointCategories);
    }
    if (weedPointThreshold != null) {
      _result.weedPointThreshold = weedPointThreshold;
    }
    if (cropPointThreshold != null) {
      _result.cropPointThreshold = cropPointThreshold;
    }
    if (segmentationCategories != null) {
      _result.segmentationCategories.addAll(segmentationCategories);
    }
    return _result;
  }
  factory DeepweedDetectionCriteriaSetting.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeepweedDetectionCriteriaSetting.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeepweedDetectionCriteriaSetting clone() => DeepweedDetectionCriteriaSetting()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeepweedDetectionCriteriaSetting copyWith(void Function(DeepweedDetectionCriteriaSetting) updates) => super.copyWith((message) => updates(message as DeepweedDetectionCriteriaSetting)) as DeepweedDetectionCriteriaSetting; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeepweedDetectionCriteriaSetting create() => DeepweedDetectionCriteriaSetting._();
  DeepweedDetectionCriteriaSetting createEmptyInstance() => create();
  static $pb.PbList<DeepweedDetectionCriteriaSetting> createRepeated() => $pb.PbList<DeepweedDetectionCriteriaSetting>();
  @$core.pragma('dart2js:noInline')
  static DeepweedDetectionCriteriaSetting getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeepweedDetectionCriteriaSetting>(create);
  static DeepweedDetectionCriteriaSetting? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<PointDetectionCategory> get pointCategories => $_getList(0);

  @$pb.TagNumber(2)
  $core.double get weedPointThreshold => $_getN(1);
  @$pb.TagNumber(2)
  set weedPointThreshold($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWeedPointThreshold() => $_has(1);
  @$pb.TagNumber(2)
  void clearWeedPointThreshold() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get cropPointThreshold => $_getN(2);
  @$pb.TagNumber(3)
  set cropPointThreshold($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCropPointThreshold() => $_has(2);
  @$pb.TagNumber(3)
  void clearCropPointThreshold() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<SegmentationDetectionCategory> get segmentationCategories => $_getList(3);
}

class SetDeepweedDetectionCriteriaRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetDeepweedDetectionCriteriaRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedPointThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropPointThreshold', $pb.PbFieldType.OF)
    ..pc<PointDetectionCategory>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pointCategories', $pb.PbFieldType.PM, subBuilder: PointDetectionCategory.create)
    ..pc<SegmentationDetectionCategory>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'segmentationCategories', $pb.PbFieldType.PM, subBuilder: SegmentationDetectionCategory.create)
    ..hasRequiredFields = false
  ;

  SetDeepweedDetectionCriteriaRequest._() : super();
  factory SetDeepweedDetectionCriteriaRequest({
    $core.double? weedPointThreshold,
    $core.double? cropPointThreshold,
    $core.Iterable<PointDetectionCategory>? pointCategories,
    $core.Iterable<SegmentationDetectionCategory>? segmentationCategories,
  }) {
    final _result = create();
    if (weedPointThreshold != null) {
      _result.weedPointThreshold = weedPointThreshold;
    }
    if (cropPointThreshold != null) {
      _result.cropPointThreshold = cropPointThreshold;
    }
    if (pointCategories != null) {
      _result.pointCategories.addAll(pointCategories);
    }
    if (segmentationCategories != null) {
      _result.segmentationCategories.addAll(segmentationCategories);
    }
    return _result;
  }
  factory SetDeepweedDetectionCriteriaRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetDeepweedDetectionCriteriaRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetDeepweedDetectionCriteriaRequest clone() => SetDeepweedDetectionCriteriaRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetDeepweedDetectionCriteriaRequest copyWith(void Function(SetDeepweedDetectionCriteriaRequest) updates) => super.copyWith((message) => updates(message as SetDeepweedDetectionCriteriaRequest)) as SetDeepweedDetectionCriteriaRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetDeepweedDetectionCriteriaRequest create() => SetDeepweedDetectionCriteriaRequest._();
  SetDeepweedDetectionCriteriaRequest createEmptyInstance() => create();
  static $pb.PbList<SetDeepweedDetectionCriteriaRequest> createRepeated() => $pb.PbList<SetDeepweedDetectionCriteriaRequest>();
  @$core.pragma('dart2js:noInline')
  static SetDeepweedDetectionCriteriaRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetDeepweedDetectionCriteriaRequest>(create);
  static SetDeepweedDetectionCriteriaRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get weedPointThreshold => $_getN(0);
  @$pb.TagNumber(1)
  set weedPointThreshold($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedPointThreshold() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedPointThreshold() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get cropPointThreshold => $_getN(1);
  @$pb.TagNumber(2)
  set cropPointThreshold($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCropPointThreshold() => $_has(1);
  @$pb.TagNumber(2)
  void clearCropPointThreshold() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<PointDetectionCategory> get pointCategories => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<SegmentationDetectionCategory> get segmentationCategories => $_getList(3);
}

class SetDeepweedDetectionCriteriaResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetDeepweedDetectionCriteriaResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetDeepweedDetectionCriteriaResponse._() : super();
  factory SetDeepweedDetectionCriteriaResponse() => create();
  factory SetDeepweedDetectionCriteriaResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetDeepweedDetectionCriteriaResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetDeepweedDetectionCriteriaResponse clone() => SetDeepweedDetectionCriteriaResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetDeepweedDetectionCriteriaResponse copyWith(void Function(SetDeepweedDetectionCriteriaResponse) updates) => super.copyWith((message) => updates(message as SetDeepweedDetectionCriteriaResponse)) as SetDeepweedDetectionCriteriaResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetDeepweedDetectionCriteriaResponse create() => SetDeepweedDetectionCriteriaResponse._();
  SetDeepweedDetectionCriteriaResponse createEmptyInstance() => create();
  static $pb.PbList<SetDeepweedDetectionCriteriaResponse> createRepeated() => $pb.PbList<SetDeepweedDetectionCriteriaResponse>();
  @$core.pragma('dart2js:noInline')
  static SetDeepweedDetectionCriteriaResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetDeepweedDetectionCriteriaResponse>(create);
  static SetDeepweedDetectionCriteriaResponse? _defaultInstance;
}

class GetDeepweedDetectionCriteriaRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedDetectionCriteriaRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetDeepweedDetectionCriteriaRequest._() : super();
  factory GetDeepweedDetectionCriteriaRequest() => create();
  factory GetDeepweedDetectionCriteriaRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedDetectionCriteriaRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedDetectionCriteriaRequest clone() => GetDeepweedDetectionCriteriaRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedDetectionCriteriaRequest copyWith(void Function(GetDeepweedDetectionCriteriaRequest) updates) => super.copyWith((message) => updates(message as GetDeepweedDetectionCriteriaRequest)) as GetDeepweedDetectionCriteriaRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedDetectionCriteriaRequest create() => GetDeepweedDetectionCriteriaRequest._();
  GetDeepweedDetectionCriteriaRequest createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedDetectionCriteriaRequest> createRepeated() => $pb.PbList<GetDeepweedDetectionCriteriaRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedDetectionCriteriaRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedDetectionCriteriaRequest>(create);
  static GetDeepweedDetectionCriteriaRequest? _defaultInstance;
}

class GetDeepweedDetectionCriteriaResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedDetectionCriteriaResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedPointThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropPointThreshold', $pb.PbFieldType.OF)
    ..pc<PointDetectionCategory>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pointCategories', $pb.PbFieldType.PM, subBuilder: PointDetectionCategory.create)
    ..pc<SegmentationDetectionCategory>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'segmentationCategories', $pb.PbFieldType.PM, subBuilder: SegmentationDetectionCategory.create)
    ..hasRequiredFields = false
  ;

  GetDeepweedDetectionCriteriaResponse._() : super();
  factory GetDeepweedDetectionCriteriaResponse({
    $core.double? weedPointThreshold,
    $core.double? cropPointThreshold,
    $core.Iterable<PointDetectionCategory>? pointCategories,
    $core.Iterable<SegmentationDetectionCategory>? segmentationCategories,
  }) {
    final _result = create();
    if (weedPointThreshold != null) {
      _result.weedPointThreshold = weedPointThreshold;
    }
    if (cropPointThreshold != null) {
      _result.cropPointThreshold = cropPointThreshold;
    }
    if (pointCategories != null) {
      _result.pointCategories.addAll(pointCategories);
    }
    if (segmentationCategories != null) {
      _result.segmentationCategories.addAll(segmentationCategories);
    }
    return _result;
  }
  factory GetDeepweedDetectionCriteriaResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedDetectionCriteriaResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedDetectionCriteriaResponse clone() => GetDeepweedDetectionCriteriaResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedDetectionCriteriaResponse copyWith(void Function(GetDeepweedDetectionCriteriaResponse) updates) => super.copyWith((message) => updates(message as GetDeepweedDetectionCriteriaResponse)) as GetDeepweedDetectionCriteriaResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedDetectionCriteriaResponse create() => GetDeepweedDetectionCriteriaResponse._();
  GetDeepweedDetectionCriteriaResponse createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedDetectionCriteriaResponse> createRepeated() => $pb.PbList<GetDeepweedDetectionCriteriaResponse>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedDetectionCriteriaResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedDetectionCriteriaResponse>(create);
  static GetDeepweedDetectionCriteriaResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get weedPointThreshold => $_getN(0);
  @$pb.TagNumber(1)
  set weedPointThreshold($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedPointThreshold() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedPointThreshold() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get cropPointThreshold => $_getN(1);
  @$pb.TagNumber(2)
  set cropPointThreshold($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCropPointThreshold() => $_has(1);
  @$pb.TagNumber(2)
  void clearCropPointThreshold() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<PointDetectionCategory> get pointCategories => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<SegmentationDetectionCategory> get segmentationCategories => $_getList(3);
}

class GetDeepweedSupportedCategoriesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedSupportedCategoriesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  GetDeepweedSupportedCategoriesRequest._() : super();
  factory GetDeepweedSupportedCategoriesRequest({
    $core.String? camId,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory GetDeepweedSupportedCategoriesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedSupportedCategoriesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedSupportedCategoriesRequest clone() => GetDeepweedSupportedCategoriesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedSupportedCategoriesRequest copyWith(void Function(GetDeepweedSupportedCategoriesRequest) updates) => super.copyWith((message) => updates(message as GetDeepweedSupportedCategoriesRequest)) as GetDeepweedSupportedCategoriesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedSupportedCategoriesRequest create() => GetDeepweedSupportedCategoriesRequest._();
  GetDeepweedSupportedCategoriesRequest createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedSupportedCategoriesRequest> createRepeated() => $pb.PbList<GetDeepweedSupportedCategoriesRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedSupportedCategoriesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedSupportedCategoriesRequest>(create);
  static GetDeepweedSupportedCategoriesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);
}

class GetDeepweedSupportedCategoriesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedSupportedCategoriesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'segmentationCategories')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pointCategories')
    ..hasRequiredFields = false
  ;

  GetDeepweedSupportedCategoriesResponse._() : super();
  factory GetDeepweedSupportedCategoriesResponse({
    $core.Iterable<$core.String>? segmentationCategories,
    $core.Iterable<$core.String>? pointCategories,
  }) {
    final _result = create();
    if (segmentationCategories != null) {
      _result.segmentationCategories.addAll(segmentationCategories);
    }
    if (pointCategories != null) {
      _result.pointCategories.addAll(pointCategories);
    }
    return _result;
  }
  factory GetDeepweedSupportedCategoriesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedSupportedCategoriesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedSupportedCategoriesResponse clone() => GetDeepweedSupportedCategoriesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedSupportedCategoriesResponse copyWith(void Function(GetDeepweedSupportedCategoriesResponse) updates) => super.copyWith((message) => updates(message as GetDeepweedSupportedCategoriesResponse)) as GetDeepweedSupportedCategoriesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedSupportedCategoriesResponse create() => GetDeepweedSupportedCategoriesResponse._();
  GetDeepweedSupportedCategoriesResponse createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedSupportedCategoriesResponse> createRepeated() => $pb.PbList<GetDeepweedSupportedCategoriesResponse>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedSupportedCategoriesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedSupportedCategoriesResponse>(create);
  static GetDeepweedSupportedCategoriesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get segmentationCategories => $_getList(0);

  @$pb.TagNumber(2)
  $core.List<$core.String> get pointCategories => $_getList(1);
}

class SetDeeplabMaskExpressionsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetDeeplabMaskExpressionsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..e<BufferUseCase>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'useCase', $pb.PbFieldType.OE, defaultOrMaker: BufferUseCase.P2P, valueOf: BufferUseCase.valueOf, enumValues: BufferUseCase.values)
    ..pc<MaskExpression>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exprs', $pb.PbFieldType.PM, subBuilder: MaskExpression.create)
    ..hasRequiredFields = false
  ;

  SetDeeplabMaskExpressionsRequest._() : super();
  factory SetDeeplabMaskExpressionsRequest({
    $core.String? camId,
    BufferUseCase? useCase,
    $core.Iterable<MaskExpression>? exprs,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (useCase != null) {
      _result.useCase = useCase;
    }
    if (exprs != null) {
      _result.exprs.addAll(exprs);
    }
    return _result;
  }
  factory SetDeeplabMaskExpressionsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetDeeplabMaskExpressionsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetDeeplabMaskExpressionsRequest clone() => SetDeeplabMaskExpressionsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetDeeplabMaskExpressionsRequest copyWith(void Function(SetDeeplabMaskExpressionsRequest) updates) => super.copyWith((message) => updates(message as SetDeeplabMaskExpressionsRequest)) as SetDeeplabMaskExpressionsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetDeeplabMaskExpressionsRequest create() => SetDeeplabMaskExpressionsRequest._();
  SetDeeplabMaskExpressionsRequest createEmptyInstance() => create();
  static $pb.PbList<SetDeeplabMaskExpressionsRequest> createRepeated() => $pb.PbList<SetDeeplabMaskExpressionsRequest>();
  @$core.pragma('dart2js:noInline')
  static SetDeeplabMaskExpressionsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetDeeplabMaskExpressionsRequest>(create);
  static SetDeeplabMaskExpressionsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  BufferUseCase get useCase => $_getN(1);
  @$pb.TagNumber(2)
  set useCase(BufferUseCase v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUseCase() => $_has(1);
  @$pb.TagNumber(2)
  void clearUseCase() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<MaskExpression> get exprs => $_getList(2);
}

class SetDeeplabMaskExpressionsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetDeeplabMaskExpressionsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetDeeplabMaskExpressionsResponse._() : super();
  factory SetDeeplabMaskExpressionsResponse() => create();
  factory SetDeeplabMaskExpressionsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetDeeplabMaskExpressionsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetDeeplabMaskExpressionsResponse clone() => SetDeeplabMaskExpressionsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetDeeplabMaskExpressionsResponse copyWith(void Function(SetDeeplabMaskExpressionsResponse) updates) => super.copyWith((message) => updates(message as SetDeeplabMaskExpressionsResponse)) as SetDeeplabMaskExpressionsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetDeeplabMaskExpressionsResponse create() => SetDeeplabMaskExpressionsResponse._();
  SetDeeplabMaskExpressionsResponse createEmptyInstance() => create();
  static $pb.PbList<SetDeeplabMaskExpressionsResponse> createRepeated() => $pb.PbList<SetDeeplabMaskExpressionsResponse>();
  @$core.pragma('dart2js:noInline')
  static SetDeeplabMaskExpressionsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetDeeplabMaskExpressionsResponse>(create);
  static SetDeeplabMaskExpressionsResponse? _defaultInstance;
}

class GetDeepweedIndexToCategoryRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedIndexToCategoryRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  GetDeepweedIndexToCategoryRequest._() : super();
  factory GetDeepweedIndexToCategoryRequest({
    $core.String? camId,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory GetDeepweedIndexToCategoryRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedIndexToCategoryRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedIndexToCategoryRequest clone() => GetDeepweedIndexToCategoryRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedIndexToCategoryRequest copyWith(void Function(GetDeepweedIndexToCategoryRequest) updates) => super.copyWith((message) => updates(message as GetDeepweedIndexToCategoryRequest)) as GetDeepweedIndexToCategoryRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedIndexToCategoryRequest create() => GetDeepweedIndexToCategoryRequest._();
  GetDeepweedIndexToCategoryRequest createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedIndexToCategoryRequest> createRepeated() => $pb.PbList<GetDeepweedIndexToCategoryRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedIndexToCategoryRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedIndexToCategoryRequest>(create);
  static GetDeepweedIndexToCategoryRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);
}

class GetDeepweedIndexToCategoryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedIndexToCategoryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..m<$core.int, $core.String>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedPointIndexToCategory', entryClassName: 'GetDeepweedIndexToCategoryResponse.WeedPointIndexToCategoryEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('cv.runtime.proto'))
    ..m<$core.int, $core.String>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropPointIndexToCategory', entryClassName: 'GetDeepweedIndexToCategoryResponse.CropPointIndexToCategoryEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('cv.runtime.proto'))
    ..m<$core.int, $core.String>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intersectionIndexToCategory', entryClassName: 'GetDeepweedIndexToCategoryResponse.IntersectionIndexToCategoryEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('cv.runtime.proto'))
    ..hasRequiredFields = false
  ;

  GetDeepweedIndexToCategoryResponse._() : super();
  factory GetDeepweedIndexToCategoryResponse({
    $core.Map<$core.int, $core.String>? weedPointIndexToCategory,
    $core.Map<$core.int, $core.String>? cropPointIndexToCategory,
    $core.Map<$core.int, $core.String>? intersectionIndexToCategory,
  }) {
    final _result = create();
    if (weedPointIndexToCategory != null) {
      _result.weedPointIndexToCategory.addAll(weedPointIndexToCategory);
    }
    if (cropPointIndexToCategory != null) {
      _result.cropPointIndexToCategory.addAll(cropPointIndexToCategory);
    }
    if (intersectionIndexToCategory != null) {
      _result.intersectionIndexToCategory.addAll(intersectionIndexToCategory);
    }
    return _result;
  }
  factory GetDeepweedIndexToCategoryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedIndexToCategoryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedIndexToCategoryResponse clone() => GetDeepweedIndexToCategoryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedIndexToCategoryResponse copyWith(void Function(GetDeepweedIndexToCategoryResponse) updates) => super.copyWith((message) => updates(message as GetDeepweedIndexToCategoryResponse)) as GetDeepweedIndexToCategoryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedIndexToCategoryResponse create() => GetDeepweedIndexToCategoryResponse._();
  GetDeepweedIndexToCategoryResponse createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedIndexToCategoryResponse> createRepeated() => $pb.PbList<GetDeepweedIndexToCategoryResponse>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedIndexToCategoryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedIndexToCategoryResponse>(create);
  static GetDeepweedIndexToCategoryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.int, $core.String> get weedPointIndexToCategory => $_getMap(0);

  @$pb.TagNumber(2)
  $core.Map<$core.int, $core.String> get cropPointIndexToCategory => $_getMap(1);

  @$pb.TagNumber(3)
  $core.Map<$core.int, $core.String> get intersectionIndexToCategory => $_getMap(2);
}

class GetPredictCamMatrixRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPredictCamMatrixRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictCamId')
    ..hasRequiredFields = false
  ;

  GetPredictCamMatrixRequest._() : super();
  factory GetPredictCamMatrixRequest({
    $core.String? predictCamId,
  }) {
    final _result = create();
    if (predictCamId != null) {
      _result.predictCamId = predictCamId;
    }
    return _result;
  }
  factory GetPredictCamMatrixRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPredictCamMatrixRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPredictCamMatrixRequest clone() => GetPredictCamMatrixRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPredictCamMatrixRequest copyWith(void Function(GetPredictCamMatrixRequest) updates) => super.copyWith((message) => updates(message as GetPredictCamMatrixRequest)) as GetPredictCamMatrixRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPredictCamMatrixRequest create() => GetPredictCamMatrixRequest._();
  GetPredictCamMatrixRequest createEmptyInstance() => create();
  static $pb.PbList<GetPredictCamMatrixRequest> createRepeated() => $pb.PbList<GetPredictCamMatrixRequest>();
  @$core.pragma('dart2js:noInline')
  static GetPredictCamMatrixRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPredictCamMatrixRequest>(create);
  static GetPredictCamMatrixRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get predictCamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set predictCamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPredictCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPredictCamId() => clearField(1);
}

class GetPredictCamDistortionCoefficientsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPredictCamDistortionCoefficientsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictCamId')
    ..hasRequiredFields = false
  ;

  GetPredictCamDistortionCoefficientsRequest._() : super();
  factory GetPredictCamDistortionCoefficientsRequest({
    $core.String? predictCamId,
  }) {
    final _result = create();
    if (predictCamId != null) {
      _result.predictCamId = predictCamId;
    }
    return _result;
  }
  factory GetPredictCamDistortionCoefficientsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPredictCamDistortionCoefficientsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPredictCamDistortionCoefficientsRequest clone() => GetPredictCamDistortionCoefficientsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPredictCamDistortionCoefficientsRequest copyWith(void Function(GetPredictCamDistortionCoefficientsRequest) updates) => super.copyWith((message) => updates(message as GetPredictCamDistortionCoefficientsRequest)) as GetPredictCamDistortionCoefficientsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPredictCamDistortionCoefficientsRequest create() => GetPredictCamDistortionCoefficientsRequest._();
  GetPredictCamDistortionCoefficientsRequest createEmptyInstance() => create();
  static $pb.PbList<GetPredictCamDistortionCoefficientsRequest> createRepeated() => $pb.PbList<GetPredictCamDistortionCoefficientsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetPredictCamDistortionCoefficientsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPredictCamDistortionCoefficientsRequest>(create);
  static GetPredictCamDistortionCoefficientsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get predictCamId => $_getSZ(0);
  @$pb.TagNumber(1)
  set predictCamId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPredictCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPredictCamId() => clearField(1);
}

class SetCameraSettingsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetCameraSettingsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camIds')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exposureUs', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gamma', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gainDb', $pb.PbFieldType.OF)
    ..e<$60.LightSourcePreset>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lightSourcePreset', $pb.PbFieldType.OE, defaultOrMaker: $60.LightSourcePreset.kOff, valueOf: $60.LightSourcePreset.valueOf, enumValues: $60.LightSourcePreset.values)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wbRatioRed', $pb.PbFieldType.OF)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wbRatioGreen', $pb.PbFieldType.OF)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wbRatioBlue', $pb.PbFieldType.OF)
    ..aInt64(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'roiOffsetX')
    ..aInt64(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'roiOffsetY')
    ..aOB(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'mirror')
    ..aOB(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'flip')
    ..aOB(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'strobing')
    ..aOB(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ptp')
    ..aOB(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'autoWhitebalance')
    ..hasRequiredFields = false
  ;

  SetCameraSettingsRequest._() : super();
  factory SetCameraSettingsRequest({
    $core.Iterable<$core.String>? camIds,
    $core.double? exposureUs,
    $core.double? gamma,
    $core.double? gainDb,
    $60.LightSourcePreset? lightSourcePreset,
    $core.double? wbRatioRed,
    $core.double? wbRatioGreen,
    $core.double? wbRatioBlue,
    $fixnum.Int64? roiOffsetX,
    $fixnum.Int64? roiOffsetY,
    $core.bool? mirror,
    $core.bool? flip,
    $core.bool? strobing,
    $core.bool? ptp,
    $core.bool? autoWhitebalance,
  }) {
    final _result = create();
    if (camIds != null) {
      _result.camIds.addAll(camIds);
    }
    if (exposureUs != null) {
      _result.exposureUs = exposureUs;
    }
    if (gamma != null) {
      _result.gamma = gamma;
    }
    if (gainDb != null) {
      _result.gainDb = gainDb;
    }
    if (lightSourcePreset != null) {
      _result.lightSourcePreset = lightSourcePreset;
    }
    if (wbRatioRed != null) {
      _result.wbRatioRed = wbRatioRed;
    }
    if (wbRatioGreen != null) {
      _result.wbRatioGreen = wbRatioGreen;
    }
    if (wbRatioBlue != null) {
      _result.wbRatioBlue = wbRatioBlue;
    }
    if (roiOffsetX != null) {
      _result.roiOffsetX = roiOffsetX;
    }
    if (roiOffsetY != null) {
      _result.roiOffsetY = roiOffsetY;
    }
    if (mirror != null) {
      _result.mirror = mirror;
    }
    if (flip != null) {
      _result.flip = flip;
    }
    if (strobing != null) {
      _result.strobing = strobing;
    }
    if (ptp != null) {
      _result.ptp = ptp;
    }
    if (autoWhitebalance != null) {
      _result.autoWhitebalance = autoWhitebalance;
    }
    return _result;
  }
  factory SetCameraSettingsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetCameraSettingsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetCameraSettingsRequest clone() => SetCameraSettingsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetCameraSettingsRequest copyWith(void Function(SetCameraSettingsRequest) updates) => super.copyWith((message) => updates(message as SetCameraSettingsRequest)) as SetCameraSettingsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetCameraSettingsRequest create() => SetCameraSettingsRequest._();
  SetCameraSettingsRequest createEmptyInstance() => create();
  static $pb.PbList<SetCameraSettingsRequest> createRepeated() => $pb.PbList<SetCameraSettingsRequest>();
  @$core.pragma('dart2js:noInline')
  static SetCameraSettingsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetCameraSettingsRequest>(create);
  static SetCameraSettingsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get camIds => $_getList(0);

  @$pb.TagNumber(2)
  $core.double get exposureUs => $_getN(1);
  @$pb.TagNumber(2)
  set exposureUs($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasExposureUs() => $_has(1);
  @$pb.TagNumber(2)
  void clearExposureUs() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get gamma => $_getN(2);
  @$pb.TagNumber(3)
  set gamma($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGamma() => $_has(2);
  @$pb.TagNumber(3)
  void clearGamma() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get gainDb => $_getN(3);
  @$pb.TagNumber(4)
  set gainDb($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGainDb() => $_has(3);
  @$pb.TagNumber(4)
  void clearGainDb() => clearField(4);

  @$pb.TagNumber(5)
  $60.LightSourcePreset get lightSourcePreset => $_getN(4);
  @$pb.TagNumber(5)
  set lightSourcePreset($60.LightSourcePreset v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasLightSourcePreset() => $_has(4);
  @$pb.TagNumber(5)
  void clearLightSourcePreset() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get wbRatioRed => $_getN(5);
  @$pb.TagNumber(6)
  set wbRatioRed($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasWbRatioRed() => $_has(5);
  @$pb.TagNumber(6)
  void clearWbRatioRed() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get wbRatioGreen => $_getN(6);
  @$pb.TagNumber(7)
  set wbRatioGreen($core.double v) { $_setFloat(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasWbRatioGreen() => $_has(6);
  @$pb.TagNumber(7)
  void clearWbRatioGreen() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get wbRatioBlue => $_getN(7);
  @$pb.TagNumber(8)
  set wbRatioBlue($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasWbRatioBlue() => $_has(7);
  @$pb.TagNumber(8)
  void clearWbRatioBlue() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get roiOffsetX => $_getI64(8);
  @$pb.TagNumber(9)
  set roiOffsetX($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasRoiOffsetX() => $_has(8);
  @$pb.TagNumber(9)
  void clearRoiOffsetX() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get roiOffsetY => $_getI64(9);
  @$pb.TagNumber(10)
  set roiOffsetY($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasRoiOffsetY() => $_has(9);
  @$pb.TagNumber(10)
  void clearRoiOffsetY() => clearField(10);

  @$pb.TagNumber(11)
  $core.bool get mirror => $_getBF(10);
  @$pb.TagNumber(11)
  set mirror($core.bool v) { $_setBool(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasMirror() => $_has(10);
  @$pb.TagNumber(11)
  void clearMirror() => clearField(11);

  @$pb.TagNumber(12)
  $core.bool get flip => $_getBF(11);
  @$pb.TagNumber(12)
  set flip($core.bool v) { $_setBool(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasFlip() => $_has(11);
  @$pb.TagNumber(12)
  void clearFlip() => clearField(12);

  @$pb.TagNumber(13)
  $core.bool get strobing => $_getBF(12);
  @$pb.TagNumber(13)
  set strobing($core.bool v) { $_setBool(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasStrobing() => $_has(12);
  @$pb.TagNumber(13)
  void clearStrobing() => clearField(13);

  @$pb.TagNumber(14)
  $core.bool get ptp => $_getBF(13);
  @$pb.TagNumber(14)
  set ptp($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasPtp() => $_has(13);
  @$pb.TagNumber(14)
  void clearPtp() => clearField(14);

  @$pb.TagNumber(15)
  $core.bool get autoWhitebalance => $_getBF(14);
  @$pb.TagNumber(15)
  set autoWhitebalance($core.bool v) { $_setBool(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasAutoWhitebalance() => $_has(14);
  @$pb.TagNumber(15)
  void clearAutoWhitebalance() => clearField(15);
}

class SetCameraSettingsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetCameraSettingsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camIds')
    ..hasRequiredFields = false
  ;

  SetCameraSettingsResponse._() : super();
  factory SetCameraSettingsResponse({
    $core.Iterable<$core.String>? camIds,
  }) {
    final _result = create();
    if (camIds != null) {
      _result.camIds.addAll(camIds);
    }
    return _result;
  }
  factory SetCameraSettingsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetCameraSettingsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetCameraSettingsResponse clone() => SetCameraSettingsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetCameraSettingsResponse copyWith(void Function(SetCameraSettingsResponse) updates) => super.copyWith((message) => updates(message as SetCameraSettingsResponse)) as SetCameraSettingsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetCameraSettingsResponse create() => SetCameraSettingsResponse._();
  SetCameraSettingsResponse createEmptyInstance() => create();
  static $pb.PbList<SetCameraSettingsResponse> createRepeated() => $pb.PbList<SetCameraSettingsResponse>();
  @$core.pragma('dart2js:noInline')
  static SetCameraSettingsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetCameraSettingsResponse>(create);
  static SetCameraSettingsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get camIds => $_getList(0);
}

class SetAutoWhitebalanceRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetAutoWhitebalanceRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'enable')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camIds')
    ..hasRequiredFields = false
  ;

  SetAutoWhitebalanceRequest._() : super();
  factory SetAutoWhitebalanceRequest({
    $core.bool? enable,
    $core.Iterable<$core.String>? camIds,
  }) {
    final _result = create();
    if (enable != null) {
      _result.enable = enable;
    }
    if (camIds != null) {
      _result.camIds.addAll(camIds);
    }
    return _result;
  }
  factory SetAutoWhitebalanceRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetAutoWhitebalanceRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetAutoWhitebalanceRequest clone() => SetAutoWhitebalanceRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetAutoWhitebalanceRequest copyWith(void Function(SetAutoWhitebalanceRequest) updates) => super.copyWith((message) => updates(message as SetAutoWhitebalanceRequest)) as SetAutoWhitebalanceRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetAutoWhitebalanceRequest create() => SetAutoWhitebalanceRequest._();
  SetAutoWhitebalanceRequest createEmptyInstance() => create();
  static $pb.PbList<SetAutoWhitebalanceRequest> createRepeated() => $pb.PbList<SetAutoWhitebalanceRequest>();
  @$core.pragma('dart2js:noInline')
  static SetAutoWhitebalanceRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetAutoWhitebalanceRequest>(create);
  static SetAutoWhitebalanceRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get enable => $_getBF(0);
  @$pb.TagNumber(1)
  set enable($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasEnable() => $_has(0);
  @$pb.TagNumber(1)
  void clearEnable() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get camIds => $_getList(1);
}

class SetAutoWhitebalanceResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetAutoWhitebalanceResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetAutoWhitebalanceResponse._() : super();
  factory SetAutoWhitebalanceResponse() => create();
  factory SetAutoWhitebalanceResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetAutoWhitebalanceResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetAutoWhitebalanceResponse clone() => SetAutoWhitebalanceResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetAutoWhitebalanceResponse copyWith(void Function(SetAutoWhitebalanceResponse) updates) => super.copyWith((message) => updates(message as SetAutoWhitebalanceResponse)) as SetAutoWhitebalanceResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetAutoWhitebalanceResponse create() => SetAutoWhitebalanceResponse._();
  SetAutoWhitebalanceResponse createEmptyInstance() => create();
  static $pb.PbList<SetAutoWhitebalanceResponse> createRepeated() => $pb.PbList<SetAutoWhitebalanceResponse>();
  @$core.pragma('dart2js:noInline')
  static SetAutoWhitebalanceResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetAutoWhitebalanceResponse>(create);
  static SetAutoWhitebalanceResponse? _defaultInstance;
}

class GetCameraSettingsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraSettingsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camIds')
    ..hasRequiredFields = false
  ;

  GetCameraSettingsRequest._() : super();
  factory GetCameraSettingsRequest({
    $core.Iterable<$core.String>? camIds,
  }) {
    final _result = create();
    if (camIds != null) {
      _result.camIds.addAll(camIds);
    }
    return _result;
  }
  factory GetCameraSettingsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraSettingsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraSettingsRequest clone() => GetCameraSettingsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraSettingsRequest copyWith(void Function(GetCameraSettingsRequest) updates) => super.copyWith((message) => updates(message as GetCameraSettingsRequest)) as GetCameraSettingsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraSettingsRequest create() => GetCameraSettingsRequest._();
  GetCameraSettingsRequest createEmptyInstance() => create();
  static $pb.PbList<GetCameraSettingsRequest> createRepeated() => $pb.PbList<GetCameraSettingsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetCameraSettingsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraSettingsRequest>(create);
  static GetCameraSettingsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get camIds => $_getList(0);
}

class CameraSettingsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CameraSettingsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exposureUs', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gamma', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gainDb', $pb.PbFieldType.OF)
    ..e<$60.LightSourcePreset>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lightSourcePreset', $pb.PbFieldType.OE, defaultOrMaker: $60.LightSourcePreset.kOff, valueOf: $60.LightSourcePreset.valueOf, enumValues: $60.LightSourcePreset.values)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wbRatioRed', $pb.PbFieldType.OF)
    ..a<$core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wbRatioGreen', $pb.PbFieldType.OF)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wbRatioBlue', $pb.PbFieldType.OF)
    ..aInt64(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'roiWidth')
    ..aInt64(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'roiHeight')
    ..aInt64(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'roiOffsetX')
    ..aInt64(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'roiOffsetY')
    ..aInt64(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gpuId')
    ..aOB(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'mirror')
    ..aOB(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'flip')
    ..aOB(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'strobing')
    ..aOB(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ptp')
    ..aOB(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'autoWhitebalance')
    ..hasRequiredFields = false
  ;

  CameraSettingsResponse._() : super();
  factory CameraSettingsResponse({
    $core.String? camId,
    $core.double? exposureUs,
    $core.double? gamma,
    $core.double? gainDb,
    $60.LightSourcePreset? lightSourcePreset,
    $core.double? wbRatioRed,
    $core.double? wbRatioGreen,
    $core.double? wbRatioBlue,
    $fixnum.Int64? roiWidth,
    $fixnum.Int64? roiHeight,
    $fixnum.Int64? roiOffsetX,
    $fixnum.Int64? roiOffsetY,
    $fixnum.Int64? gpuId,
    $core.bool? mirror,
    $core.bool? flip,
    $core.bool? strobing,
    $core.bool? ptp,
    $core.bool? autoWhitebalance,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (exposureUs != null) {
      _result.exposureUs = exposureUs;
    }
    if (gamma != null) {
      _result.gamma = gamma;
    }
    if (gainDb != null) {
      _result.gainDb = gainDb;
    }
    if (lightSourcePreset != null) {
      _result.lightSourcePreset = lightSourcePreset;
    }
    if (wbRatioRed != null) {
      _result.wbRatioRed = wbRatioRed;
    }
    if (wbRatioGreen != null) {
      _result.wbRatioGreen = wbRatioGreen;
    }
    if (wbRatioBlue != null) {
      _result.wbRatioBlue = wbRatioBlue;
    }
    if (roiWidth != null) {
      _result.roiWidth = roiWidth;
    }
    if (roiHeight != null) {
      _result.roiHeight = roiHeight;
    }
    if (roiOffsetX != null) {
      _result.roiOffsetX = roiOffsetX;
    }
    if (roiOffsetY != null) {
      _result.roiOffsetY = roiOffsetY;
    }
    if (gpuId != null) {
      _result.gpuId = gpuId;
    }
    if (mirror != null) {
      _result.mirror = mirror;
    }
    if (flip != null) {
      _result.flip = flip;
    }
    if (strobing != null) {
      _result.strobing = strobing;
    }
    if (ptp != null) {
      _result.ptp = ptp;
    }
    if (autoWhitebalance != null) {
      _result.autoWhitebalance = autoWhitebalance;
    }
    return _result;
  }
  factory CameraSettingsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CameraSettingsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CameraSettingsResponse clone() => CameraSettingsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CameraSettingsResponse copyWith(void Function(CameraSettingsResponse) updates) => super.copyWith((message) => updates(message as CameraSettingsResponse)) as CameraSettingsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CameraSettingsResponse create() => CameraSettingsResponse._();
  CameraSettingsResponse createEmptyInstance() => create();
  static $pb.PbList<CameraSettingsResponse> createRepeated() => $pb.PbList<CameraSettingsResponse>();
  @$core.pragma('dart2js:noInline')
  static CameraSettingsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CameraSettingsResponse>(create);
  static CameraSettingsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get exposureUs => $_getN(1);
  @$pb.TagNumber(2)
  set exposureUs($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasExposureUs() => $_has(1);
  @$pb.TagNumber(2)
  void clearExposureUs() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get gamma => $_getN(2);
  @$pb.TagNumber(3)
  set gamma($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGamma() => $_has(2);
  @$pb.TagNumber(3)
  void clearGamma() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get gainDb => $_getN(3);
  @$pb.TagNumber(4)
  set gainDb($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGainDb() => $_has(3);
  @$pb.TagNumber(4)
  void clearGainDb() => clearField(4);

  @$pb.TagNumber(5)
  $60.LightSourcePreset get lightSourcePreset => $_getN(4);
  @$pb.TagNumber(5)
  set lightSourcePreset($60.LightSourcePreset v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasLightSourcePreset() => $_has(4);
  @$pb.TagNumber(5)
  void clearLightSourcePreset() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get wbRatioRed => $_getN(5);
  @$pb.TagNumber(6)
  set wbRatioRed($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasWbRatioRed() => $_has(5);
  @$pb.TagNumber(6)
  void clearWbRatioRed() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get wbRatioGreen => $_getN(6);
  @$pb.TagNumber(7)
  set wbRatioGreen($core.double v) { $_setFloat(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasWbRatioGreen() => $_has(6);
  @$pb.TagNumber(7)
  void clearWbRatioGreen() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get wbRatioBlue => $_getN(7);
  @$pb.TagNumber(8)
  set wbRatioBlue($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasWbRatioBlue() => $_has(7);
  @$pb.TagNumber(8)
  void clearWbRatioBlue() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get roiWidth => $_getI64(8);
  @$pb.TagNumber(9)
  set roiWidth($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasRoiWidth() => $_has(8);
  @$pb.TagNumber(9)
  void clearRoiWidth() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get roiHeight => $_getI64(9);
  @$pb.TagNumber(10)
  set roiHeight($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasRoiHeight() => $_has(9);
  @$pb.TagNumber(10)
  void clearRoiHeight() => clearField(10);

  @$pb.TagNumber(11)
  $fixnum.Int64 get roiOffsetX => $_getI64(10);
  @$pb.TagNumber(11)
  set roiOffsetX($fixnum.Int64 v) { $_setInt64(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasRoiOffsetX() => $_has(10);
  @$pb.TagNumber(11)
  void clearRoiOffsetX() => clearField(11);

  @$pb.TagNumber(12)
  $fixnum.Int64 get roiOffsetY => $_getI64(11);
  @$pb.TagNumber(12)
  set roiOffsetY($fixnum.Int64 v) { $_setInt64(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasRoiOffsetY() => $_has(11);
  @$pb.TagNumber(12)
  void clearRoiOffsetY() => clearField(12);

  @$pb.TagNumber(13)
  $fixnum.Int64 get gpuId => $_getI64(12);
  @$pb.TagNumber(13)
  set gpuId($fixnum.Int64 v) { $_setInt64(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasGpuId() => $_has(12);
  @$pb.TagNumber(13)
  void clearGpuId() => clearField(13);

  @$pb.TagNumber(14)
  $core.bool get mirror => $_getBF(13);
  @$pb.TagNumber(14)
  set mirror($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasMirror() => $_has(13);
  @$pb.TagNumber(14)
  void clearMirror() => clearField(14);

  @$pb.TagNumber(15)
  $core.bool get flip => $_getBF(14);
  @$pb.TagNumber(15)
  set flip($core.bool v) { $_setBool(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasFlip() => $_has(14);
  @$pb.TagNumber(15)
  void clearFlip() => clearField(15);

  @$pb.TagNumber(16)
  $core.bool get strobing => $_getBF(15);
  @$pb.TagNumber(16)
  set strobing($core.bool v) { $_setBool(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasStrobing() => $_has(15);
  @$pb.TagNumber(16)
  void clearStrobing() => clearField(16);

  @$pb.TagNumber(17)
  $core.bool get ptp => $_getBF(16);
  @$pb.TagNumber(17)
  set ptp($core.bool v) { $_setBool(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasPtp() => $_has(16);
  @$pb.TagNumber(17)
  void clearPtp() => clearField(17);

  @$pb.TagNumber(18)
  $core.bool get autoWhitebalance => $_getBF(17);
  @$pb.TagNumber(18)
  set autoWhitebalance($core.bool v) { $_setBool(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasAutoWhitebalance() => $_has(17);
  @$pb.TagNumber(18)
  void clearAutoWhitebalance() => clearField(18);
}

class GetCameraSettingsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraSettingsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<CameraSettingsResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cameraSettingsResponse', $pb.PbFieldType.PM, subBuilder: CameraSettingsResponse.create)
    ..hasRequiredFields = false
  ;

  GetCameraSettingsResponse._() : super();
  factory GetCameraSettingsResponse({
    $core.Iterable<CameraSettingsResponse>? cameraSettingsResponse,
  }) {
    final _result = create();
    if (cameraSettingsResponse != null) {
      _result.cameraSettingsResponse.addAll(cameraSettingsResponse);
    }
    return _result;
  }
  factory GetCameraSettingsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraSettingsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraSettingsResponse clone() => GetCameraSettingsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraSettingsResponse copyWith(void Function(GetCameraSettingsResponse) updates) => super.copyWith((message) => updates(message as GetCameraSettingsResponse)) as GetCameraSettingsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraSettingsResponse create() => GetCameraSettingsResponse._();
  GetCameraSettingsResponse createEmptyInstance() => create();
  static $pb.PbList<GetCameraSettingsResponse> createRepeated() => $pb.PbList<GetCameraSettingsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetCameraSettingsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraSettingsResponse>(create);
  static GetCameraSettingsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<CameraSettingsResponse> get cameraSettingsResponse => $_getList(0);
}

class StartBurstRecordFramesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartBurstRecordFramesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'durationMs')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'path')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dontCapturePredictImage')
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'downsampleFactor', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  StartBurstRecordFramesRequest._() : super();
  factory StartBurstRecordFramesRequest({
    $core.String? camId,
    $fixnum.Int64? durationMs,
    $core.String? path,
    $core.bool? dontCapturePredictImage,
    $core.int? downsampleFactor,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (durationMs != null) {
      _result.durationMs = durationMs;
    }
    if (path != null) {
      _result.path = path;
    }
    if (dontCapturePredictImage != null) {
      _result.dontCapturePredictImage = dontCapturePredictImage;
    }
    if (downsampleFactor != null) {
      _result.downsampleFactor = downsampleFactor;
    }
    return _result;
  }
  factory StartBurstRecordFramesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartBurstRecordFramesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartBurstRecordFramesRequest clone() => StartBurstRecordFramesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartBurstRecordFramesRequest copyWith(void Function(StartBurstRecordFramesRequest) updates) => super.copyWith((message) => updates(message as StartBurstRecordFramesRequest)) as StartBurstRecordFramesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartBurstRecordFramesRequest create() => StartBurstRecordFramesRequest._();
  StartBurstRecordFramesRequest createEmptyInstance() => create();
  static $pb.PbList<StartBurstRecordFramesRequest> createRepeated() => $pb.PbList<StartBurstRecordFramesRequest>();
  @$core.pragma('dart2js:noInline')
  static StartBurstRecordFramesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartBurstRecordFramesRequest>(create);
  static StartBurstRecordFramesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get durationMs => $_getI64(1);
  @$pb.TagNumber(2)
  set durationMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDurationMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearDurationMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get path => $_getSZ(2);
  @$pb.TagNumber(3)
  set path($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPath() => $_has(2);
  @$pb.TagNumber(3)
  void clearPath() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get dontCapturePredictImage => $_getBF(3);
  @$pb.TagNumber(4)
  set dontCapturePredictImage($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDontCapturePredictImage() => $_has(3);
  @$pb.TagNumber(4)
  void clearDontCapturePredictImage() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get downsampleFactor => $_getIZ(4);
  @$pb.TagNumber(5)
  set downsampleFactor($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDownsampleFactor() => $_has(4);
  @$pb.TagNumber(5)
  void clearDownsampleFactor() => clearField(5);
}

class StartBurstRecordFramesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartBurstRecordFramesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StartBurstRecordFramesResponse._() : super();
  factory StartBurstRecordFramesResponse() => create();
  factory StartBurstRecordFramesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartBurstRecordFramesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartBurstRecordFramesResponse clone() => StartBurstRecordFramesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartBurstRecordFramesResponse copyWith(void Function(StartBurstRecordFramesResponse) updates) => super.copyWith((message) => updates(message as StartBurstRecordFramesResponse)) as StartBurstRecordFramesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartBurstRecordFramesResponse create() => StartBurstRecordFramesResponse._();
  StartBurstRecordFramesResponse createEmptyInstance() => create();
  static $pb.PbList<StartBurstRecordFramesResponse> createRepeated() => $pb.PbList<StartBurstRecordFramesResponse>();
  @$core.pragma('dart2js:noInline')
  static StartBurstRecordFramesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartBurstRecordFramesResponse>(create);
  static StartBurstRecordFramesResponse? _defaultInstance;
}

class StopBurstRecordFramesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopBurstRecordFramesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastFrameTimestampMs')
    ..hasRequiredFields = false
  ;

  StopBurstRecordFramesRequest._() : super();
  factory StopBurstRecordFramesRequest({
    $core.String? camId,
    $fixnum.Int64? lastFrameTimestampMs,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (lastFrameTimestampMs != null) {
      _result.lastFrameTimestampMs = lastFrameTimestampMs;
    }
    return _result;
  }
  factory StopBurstRecordFramesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopBurstRecordFramesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopBurstRecordFramesRequest clone() => StopBurstRecordFramesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopBurstRecordFramesRequest copyWith(void Function(StopBurstRecordFramesRequest) updates) => super.copyWith((message) => updates(message as StopBurstRecordFramesRequest)) as StopBurstRecordFramesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopBurstRecordFramesRequest create() => StopBurstRecordFramesRequest._();
  StopBurstRecordFramesRequest createEmptyInstance() => create();
  static $pb.PbList<StopBurstRecordFramesRequest> createRepeated() => $pb.PbList<StopBurstRecordFramesRequest>();
  @$core.pragma('dart2js:noInline')
  static StopBurstRecordFramesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopBurstRecordFramesRequest>(create);
  static StopBurstRecordFramesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get lastFrameTimestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set lastFrameTimestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLastFrameTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearLastFrameTimestampMs() => clearField(2);
}

class StopBurstRecordFramesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopBurstRecordFramesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StopBurstRecordFramesResponse._() : super();
  factory StopBurstRecordFramesResponse() => create();
  factory StopBurstRecordFramesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopBurstRecordFramesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopBurstRecordFramesResponse clone() => StopBurstRecordFramesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopBurstRecordFramesResponse copyWith(void Function(StopBurstRecordFramesResponse) updates) => super.copyWith((message) => updates(message as StopBurstRecordFramesResponse)) as StopBurstRecordFramesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopBurstRecordFramesResponse create() => StopBurstRecordFramesResponse._();
  StopBurstRecordFramesResponse createEmptyInstance() => create();
  static $pb.PbList<StopBurstRecordFramesResponse> createRepeated() => $pb.PbList<StopBurstRecordFramesResponse>();
  @$core.pragma('dart2js:noInline')
  static StopBurstRecordFramesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopBurstRecordFramesResponse>(create);
  static StopBurstRecordFramesResponse? _defaultInstance;
}

class P2POutputProto extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2POutputProto', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'matched')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCoordX', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCoordY', $pb.PbFieldType.OF)
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetTimestampMs')
    ..aInt64(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictTimestampMs')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'safe')
    ..hasRequiredFields = false
  ;

  P2POutputProto._() : super();
  factory P2POutputProto({
    $core.bool? matched,
    $core.double? targetCoordX,
    $core.double? targetCoordY,
    $fixnum.Int64? targetTimestampMs,
    $fixnum.Int64? predictTimestampMs,
    $core.bool? safe,
  }) {
    final _result = create();
    if (matched != null) {
      _result.matched = matched;
    }
    if (targetCoordX != null) {
      _result.targetCoordX = targetCoordX;
    }
    if (targetCoordY != null) {
      _result.targetCoordY = targetCoordY;
    }
    if (targetTimestampMs != null) {
      _result.targetTimestampMs = targetTimestampMs;
    }
    if (predictTimestampMs != null) {
      _result.predictTimestampMs = predictTimestampMs;
    }
    if (safe != null) {
      _result.safe = safe;
    }
    return _result;
  }
  factory P2POutputProto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2POutputProto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2POutputProto clone() => P2POutputProto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2POutputProto copyWith(void Function(P2POutputProto) updates) => super.copyWith((message) => updates(message as P2POutputProto)) as P2POutputProto; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2POutputProto create() => P2POutputProto._();
  P2POutputProto createEmptyInstance() => create();
  static $pb.PbList<P2POutputProto> createRepeated() => $pb.PbList<P2POutputProto>();
  @$core.pragma('dart2js:noInline')
  static P2POutputProto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2POutputProto>(create);
  static P2POutputProto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get matched => $_getBF(0);
  @$pb.TagNumber(1)
  set matched($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasMatched() => $_has(0);
  @$pb.TagNumber(1)
  void clearMatched() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get targetCoordX => $_getN(1);
  @$pb.TagNumber(2)
  set targetCoordX($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetCoordX() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetCoordX() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get targetCoordY => $_getN(2);
  @$pb.TagNumber(3)
  set targetCoordY($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTargetCoordY() => $_has(2);
  @$pb.TagNumber(3)
  void clearTargetCoordY() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get targetTimestampMs => $_getI64(3);
  @$pb.TagNumber(4)
  set targetTimestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTargetTimestampMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearTargetTimestampMs() => clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get predictTimestampMs => $_getI64(4);
  @$pb.TagNumber(5)
  set predictTimestampMs($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPredictTimestampMs() => $_has(4);
  @$pb.TagNumber(5)
  void clearPredictTimestampMs() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get safe => $_getBF(5);
  @$pb.TagNumber(6)
  set safe($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSafe() => $_has(5);
  @$pb.TagNumber(6)
  void clearSafe() => clearField(6);
}

class GetNextP2POutputRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextP2POutputRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bufferName')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timeoutMs')
    ..hasRequiredFields = false
  ;

  GetNextP2POutputRequest._() : super();
  factory GetNextP2POutputRequest({
    $core.String? bufferName,
    $fixnum.Int64? timestampMs,
    $fixnum.Int64? timeoutMs,
  }) {
    final _result = create();
    if (bufferName != null) {
      _result.bufferName = bufferName;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (timeoutMs != null) {
      _result.timeoutMs = timeoutMs;
    }
    return _result;
  }
  factory GetNextP2POutputRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextP2POutputRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextP2POutputRequest clone() => GetNextP2POutputRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextP2POutputRequest copyWith(void Function(GetNextP2POutputRequest) updates) => super.copyWith((message) => updates(message as GetNextP2POutputRequest)) as GetNextP2POutputRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextP2POutputRequest create() => GetNextP2POutputRequest._();
  GetNextP2POutputRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextP2POutputRequest> createRepeated() => $pb.PbList<GetNextP2POutputRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextP2POutputRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextP2POutputRequest>(create);
  static GetNextP2POutputRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get bufferName => $_getSZ(0);
  @$pb.TagNumber(1)
  set bufferName($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBufferName() => $_has(0);
  @$pb.TagNumber(1)
  void clearBufferName() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timeoutMs => $_getI64(2);
  @$pb.TagNumber(3)
  set timeoutMs($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimeoutMs() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimeoutMs() => clearField(3);
}

class GetConnectorsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetConnectorsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camIds')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'connectorIds')
    ..hasRequiredFields = false
  ;

  GetConnectorsRequest._() : super();
  factory GetConnectorsRequest({
    $core.Iterable<$core.String>? camIds,
    $core.Iterable<$core.String>? connectorIds,
  }) {
    final _result = create();
    if (camIds != null) {
      _result.camIds.addAll(camIds);
    }
    if (connectorIds != null) {
      _result.connectorIds.addAll(connectorIds);
    }
    return _result;
  }
  factory GetConnectorsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetConnectorsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetConnectorsRequest clone() => GetConnectorsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetConnectorsRequest copyWith(void Function(GetConnectorsRequest) updates) => super.copyWith((message) => updates(message as GetConnectorsRequest)) as GetConnectorsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetConnectorsRequest create() => GetConnectorsRequest._();
  GetConnectorsRequest createEmptyInstance() => create();
  static $pb.PbList<GetConnectorsRequest> createRepeated() => $pb.PbList<GetConnectorsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetConnectorsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetConnectorsRequest>(create);
  static GetConnectorsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get camIds => $_getList(0);

  @$pb.TagNumber(2)
  $core.List<$core.String> get connectorIds => $_getList(1);
}

class ConnectorResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ConnectorResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'connectorId')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isEnabled')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reductionRatio')
    ..hasRequiredFields = false
  ;

  ConnectorResponse._() : super();
  factory ConnectorResponse({
    $core.String? camId,
    $core.String? connectorId,
    $core.bool? isEnabled,
    $fixnum.Int64? reductionRatio,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (connectorId != null) {
      _result.connectorId = connectorId;
    }
    if (isEnabled != null) {
      _result.isEnabled = isEnabled;
    }
    if (reductionRatio != null) {
      _result.reductionRatio = reductionRatio;
    }
    return _result;
  }
  factory ConnectorResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ConnectorResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ConnectorResponse clone() => ConnectorResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ConnectorResponse copyWith(void Function(ConnectorResponse) updates) => super.copyWith((message) => updates(message as ConnectorResponse)) as ConnectorResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ConnectorResponse create() => ConnectorResponse._();
  ConnectorResponse createEmptyInstance() => create();
  static $pb.PbList<ConnectorResponse> createRepeated() => $pb.PbList<ConnectorResponse>();
  @$core.pragma('dart2js:noInline')
  static ConnectorResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ConnectorResponse>(create);
  static ConnectorResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get connectorId => $_getSZ(1);
  @$pb.TagNumber(2)
  set connectorId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasConnectorId() => $_has(1);
  @$pb.TagNumber(2)
  void clearConnectorId() => clearField(2);

  @$pb.TagNumber(3)
  $core.bool get isEnabled => $_getBF(2);
  @$pb.TagNumber(3)
  set isEnabled($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIsEnabled() => $_has(2);
  @$pb.TagNumber(3)
  void clearIsEnabled() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get reductionRatio => $_getI64(3);
  @$pb.TagNumber(4)
  set reductionRatio($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasReductionRatio() => $_has(3);
  @$pb.TagNumber(4)
  void clearReductionRatio() => clearField(4);
}

class GetConnectorsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetConnectorsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<ConnectorResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'connectorResponse', $pb.PbFieldType.PM, subBuilder: ConnectorResponse.create)
    ..hasRequiredFields = false
  ;

  GetConnectorsResponse._() : super();
  factory GetConnectorsResponse({
    $core.Iterable<ConnectorResponse>? connectorResponse,
  }) {
    final _result = create();
    if (connectorResponse != null) {
      _result.connectorResponse.addAll(connectorResponse);
    }
    return _result;
  }
  factory GetConnectorsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetConnectorsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetConnectorsResponse clone() => GetConnectorsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetConnectorsResponse copyWith(void Function(GetConnectorsResponse) updates) => super.copyWith((message) => updates(message as GetConnectorsResponse)) as GetConnectorsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetConnectorsResponse create() => GetConnectorsResponse._();
  GetConnectorsResponse createEmptyInstance() => create();
  static $pb.PbList<GetConnectorsResponse> createRepeated() => $pb.PbList<GetConnectorsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetConnectorsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetConnectorsResponse>(create);
  static GetConnectorsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ConnectorResponse> get connectorResponse => $_getList(0);
}

class SetConnectorsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetConnectorsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camIds')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'connectorIds')
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isEnabled')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reductionRatio')
    ..hasRequiredFields = false
  ;

  SetConnectorsRequest._() : super();
  factory SetConnectorsRequest({
    $core.Iterable<$core.String>? camIds,
    $core.Iterable<$core.String>? connectorIds,
    $core.bool? isEnabled,
    $fixnum.Int64? reductionRatio,
  }) {
    final _result = create();
    if (camIds != null) {
      _result.camIds.addAll(camIds);
    }
    if (connectorIds != null) {
      _result.connectorIds.addAll(connectorIds);
    }
    if (isEnabled != null) {
      _result.isEnabled = isEnabled;
    }
    if (reductionRatio != null) {
      _result.reductionRatio = reductionRatio;
    }
    return _result;
  }
  factory SetConnectorsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetConnectorsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetConnectorsRequest clone() => SetConnectorsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetConnectorsRequest copyWith(void Function(SetConnectorsRequest) updates) => super.copyWith((message) => updates(message as SetConnectorsRequest)) as SetConnectorsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetConnectorsRequest create() => SetConnectorsRequest._();
  SetConnectorsRequest createEmptyInstance() => create();
  static $pb.PbList<SetConnectorsRequest> createRepeated() => $pb.PbList<SetConnectorsRequest>();
  @$core.pragma('dart2js:noInline')
  static SetConnectorsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetConnectorsRequest>(create);
  static SetConnectorsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get camIds => $_getList(0);

  @$pb.TagNumber(2)
  $core.List<$core.String> get connectorIds => $_getList(1);

  @$pb.TagNumber(3)
  $core.bool get isEnabled => $_getBF(2);
  @$pb.TagNumber(3)
  set isEnabled($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIsEnabled() => $_has(2);
  @$pb.TagNumber(3)
  void clearIsEnabled() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get reductionRatio => $_getI64(3);
  @$pb.TagNumber(4)
  set reductionRatio($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasReductionRatio() => $_has(3);
  @$pb.TagNumber(4)
  void clearReductionRatio() => clearField(4);
}

class SetConnectorsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetConnectorsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetConnectorsResponse._() : super();
  factory SetConnectorsResponse() => create();
  factory SetConnectorsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetConnectorsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetConnectorsResponse clone() => SetConnectorsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetConnectorsResponse copyWith(void Function(SetConnectorsResponse) updates) => super.copyWith((message) => updates(message as SetConnectorsResponse)) as SetConnectorsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetConnectorsResponse create() => SetConnectorsResponse._();
  SetConnectorsResponse createEmptyInstance() => create();
  static $pb.PbList<SetConnectorsResponse> createRepeated() => $pb.PbList<SetConnectorsResponse>();
  @$core.pragma('dart2js:noInline')
  static SetConnectorsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetConnectorsResponse>(create);
  static SetConnectorsResponse? _defaultInstance;
}

class NodeTiming extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'NodeTiming', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fpsMean', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fps99pct', $pb.PbFieldType.OF, protoName: 'fps_99pct')
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'latencyMsMean', $pb.PbFieldType.OF)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'latencyMs99pct', $pb.PbFieldType.OF, protoName: 'latency_ms_99pct')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'state')
    ..m<$core.String, $core.double>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'stateTimings', entryClassName: 'NodeTiming.StateTimingsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('cv.runtime.proto'))
    ..hasRequiredFields = false
  ;

  NodeTiming._() : super();
  factory NodeTiming({
    $core.String? name,
    $core.double? fpsMean,
    $core.double? fps99pct,
    $core.double? latencyMsMean,
    $core.double? latencyMs99pct,
    $core.String? state,
    $core.Map<$core.String, $core.double>? stateTimings,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (fpsMean != null) {
      _result.fpsMean = fpsMean;
    }
    if (fps99pct != null) {
      _result.fps99pct = fps99pct;
    }
    if (latencyMsMean != null) {
      _result.latencyMsMean = latencyMsMean;
    }
    if (latencyMs99pct != null) {
      _result.latencyMs99pct = latencyMs99pct;
    }
    if (state != null) {
      _result.state = state;
    }
    if (stateTimings != null) {
      _result.stateTimings.addAll(stateTimings);
    }
    return _result;
  }
  factory NodeTiming.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory NodeTiming.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  NodeTiming clone() => NodeTiming()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  NodeTiming copyWith(void Function(NodeTiming) updates) => super.copyWith((message) => updates(message as NodeTiming)) as NodeTiming; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static NodeTiming create() => NodeTiming._();
  NodeTiming createEmptyInstance() => create();
  static $pb.PbList<NodeTiming> createRepeated() => $pb.PbList<NodeTiming>();
  @$core.pragma('dart2js:noInline')
  static NodeTiming getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<NodeTiming>(create);
  static NodeTiming? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get fpsMean => $_getN(1);
  @$pb.TagNumber(2)
  set fpsMean($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasFpsMean() => $_has(1);
  @$pb.TagNumber(2)
  void clearFpsMean() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get fps99pct => $_getN(2);
  @$pb.TagNumber(3)
  set fps99pct($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasFps99pct() => $_has(2);
  @$pb.TagNumber(3)
  void clearFps99pct() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get latencyMsMean => $_getN(3);
  @$pb.TagNumber(4)
  set latencyMsMean($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLatencyMsMean() => $_has(3);
  @$pb.TagNumber(4)
  void clearLatencyMsMean() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get latencyMs99pct => $_getN(4);
  @$pb.TagNumber(5)
  set latencyMs99pct($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLatencyMs99pct() => $_has(4);
  @$pb.TagNumber(5)
  void clearLatencyMs99pct() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get state => $_getSZ(5);
  @$pb.TagNumber(6)
  set state($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasState() => $_has(5);
  @$pb.TagNumber(6)
  void clearState() => clearField(6);

  @$pb.TagNumber(7)
  $core.Map<$core.String, $core.double> get stateTimings => $_getMap(6);
}

class GetTimingRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTimingRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetTimingRequest._() : super();
  factory GetTimingRequest() => create();
  factory GetTimingRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTimingRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTimingRequest clone() => GetTimingRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTimingRequest copyWith(void Function(GetTimingRequest) updates) => super.copyWith((message) => updates(message as GetTimingRequest)) as GetTimingRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTimingRequest create() => GetTimingRequest._();
  GetTimingRequest createEmptyInstance() => create();
  static $pb.PbList<GetTimingRequest> createRepeated() => $pb.PbList<GetTimingRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTimingRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTimingRequest>(create);
  static GetTimingRequest? _defaultInstance;
}

class GetTimingResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetTimingResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<NodeTiming>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nodeTiming', $pb.PbFieldType.PM, subBuilder: NodeTiming.create)
    ..hasRequiredFields = false
  ;

  GetTimingResponse._() : super();
  factory GetTimingResponse({
    $core.Iterable<NodeTiming>? nodeTiming,
  }) {
    final _result = create();
    if (nodeTiming != null) {
      _result.nodeTiming.addAll(nodeTiming);
    }
    return _result;
  }
  factory GetTimingResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTimingResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTimingResponse clone() => GetTimingResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTimingResponse copyWith(void Function(GetTimingResponse) updates) => super.copyWith((message) => updates(message as GetTimingResponse)) as GetTimingResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetTimingResponse create() => GetTimingResponse._();
  GetTimingResponse createEmptyInstance() => create();
  static $pb.PbList<GetTimingResponse> createRepeated() => $pb.PbList<GetTimingResponse>();
  @$core.pragma('dart2js:noInline')
  static GetTimingResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTimingResponse>(create);
  static GetTimingResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<NodeTiming> get nodeTiming => $_getList(0);
}

class PredictRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'filePaths')
    ..p<$fixnum.Int64>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampsMs', $pb.PbFieldType.K6)
    ..hasRequiredFields = false
  ;

  PredictRequest._() : super();
  factory PredictRequest({
    $core.String? camId,
    $core.Iterable<$core.String>? filePaths,
    $core.Iterable<$fixnum.Int64>? timestampsMs,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (filePaths != null) {
      _result.filePaths.addAll(filePaths);
    }
    if (timestampsMs != null) {
      _result.timestampsMs.addAll(timestampsMs);
    }
    return _result;
  }
  factory PredictRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictRequest clone() => PredictRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictRequest copyWith(void Function(PredictRequest) updates) => super.copyWith((message) => updates(message as PredictRequest)) as PredictRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictRequest create() => PredictRequest._();
  PredictRequest createEmptyInstance() => create();
  static $pb.PbList<PredictRequest> createRepeated() => $pb.PbList<PredictRequest>();
  @$core.pragma('dart2js:noInline')
  static PredictRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictRequest>(create);
  static PredictRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get filePaths => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<$fixnum.Int64> get timestampsMs => $_getList(2);
}

class PredictResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PredictResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  PredictResponse._() : super();
  factory PredictResponse() => create();
  factory PredictResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PredictResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PredictResponse clone() => PredictResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PredictResponse copyWith(void Function(PredictResponse) updates) => super.copyWith((message) => updates(message as PredictResponse)) as PredictResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PredictResponse create() => PredictResponse._();
  PredictResponse createEmptyInstance() => create();
  static $pb.PbList<PredictResponse> createRepeated() => $pb.PbList<PredictResponse>();
  @$core.pragma('dart2js:noInline')
  static PredictResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PredictResponse>(create);
  static PredictResponse? _defaultInstance;
}

class LoadAndQueueRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadAndQueueRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'filePaths')
    ..p<$fixnum.Int64>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampsMs', $pb.PbFieldType.K6)
    ..hasRequiredFields = false
  ;

  LoadAndQueueRequest._() : super();
  factory LoadAndQueueRequest({
    $core.String? camId,
    $core.Iterable<$core.String>? filePaths,
    $core.Iterable<$fixnum.Int64>? timestampsMs,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (filePaths != null) {
      _result.filePaths.addAll(filePaths);
    }
    if (timestampsMs != null) {
      _result.timestampsMs.addAll(timestampsMs);
    }
    return _result;
  }
  factory LoadAndQueueRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadAndQueueRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadAndQueueRequest clone() => LoadAndQueueRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadAndQueueRequest copyWith(void Function(LoadAndQueueRequest) updates) => super.copyWith((message) => updates(message as LoadAndQueueRequest)) as LoadAndQueueRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadAndQueueRequest create() => LoadAndQueueRequest._();
  LoadAndQueueRequest createEmptyInstance() => create();
  static $pb.PbList<LoadAndQueueRequest> createRepeated() => $pb.PbList<LoadAndQueueRequest>();
  @$core.pragma('dart2js:noInline')
  static LoadAndQueueRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadAndQueueRequest>(create);
  static LoadAndQueueRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get filePaths => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<$fixnum.Int64> get timestampsMs => $_getList(2);
}

class LoadAndQueueResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadAndQueueResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  LoadAndQueueResponse._() : super();
  factory LoadAndQueueResponse() => create();
  factory LoadAndQueueResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadAndQueueResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadAndQueueResponse clone() => LoadAndQueueResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadAndQueueResponse copyWith(void Function(LoadAndQueueResponse) updates) => super.copyWith((message) => updates(message as LoadAndQueueResponse)) as LoadAndQueueResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadAndQueueResponse create() => LoadAndQueueResponse._();
  LoadAndQueueResponse createEmptyInstance() => create();
  static $pb.PbList<LoadAndQueueResponse> createRepeated() => $pb.PbList<LoadAndQueueResponse>();
  @$core.pragma('dart2js:noInline')
  static LoadAndQueueResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadAndQueueResponse>(create);
  static LoadAndQueueResponse? _defaultInstance;
}

class SetImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'filePath')
    ..hasRequiredFields = false
  ;

  SetImageRequest._() : super();
  factory SetImageRequest({
    $core.String? camId,
    $core.String? filePath,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (filePath != null) {
      _result.filePath = filePath;
    }
    return _result;
  }
  factory SetImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetImageRequest clone() => SetImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetImageRequest copyWith(void Function(SetImageRequest) updates) => super.copyWith((message) => updates(message as SetImageRequest)) as SetImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetImageRequest create() => SetImageRequest._();
  SetImageRequest createEmptyInstance() => create();
  static $pb.PbList<SetImageRequest> createRepeated() => $pb.PbList<SetImageRequest>();
  @$core.pragma('dart2js:noInline')
  static SetImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetImageRequest>(create);
  static SetImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get filePath => $_getSZ(1);
  @$pb.TagNumber(2)
  set filePath($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasFilePath() => $_has(1);
  @$pb.TagNumber(2)
  void clearFilePath() => clearField(2);
}

class SetImageResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetImageResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetImageResponse._() : super();
  factory SetImageResponse() => create();
  factory SetImageResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetImageResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetImageResponse clone() => SetImageResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetImageResponse copyWith(void Function(SetImageResponse) updates) => super.copyWith((message) => updates(message as SetImageResponse)) as SetImageResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetImageResponse create() => SetImageResponse._();
  SetImageResponse createEmptyInstance() => create();
  static $pb.PbList<SetImageResponse> createRepeated() => $pb.PbList<SetImageResponse>();
  @$core.pragma('dart2js:noInline')
  static SetImageResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetImageResponse>(create);
  static SetImageResponse? _defaultInstance;
}

class UnsetImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UnsetImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  UnsetImageRequest._() : super();
  factory UnsetImageRequest({
    $core.String? camId,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory UnsetImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UnsetImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UnsetImageRequest clone() => UnsetImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UnsetImageRequest copyWith(void Function(UnsetImageRequest) updates) => super.copyWith((message) => updates(message as UnsetImageRequest)) as UnsetImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UnsetImageRequest create() => UnsetImageRequest._();
  UnsetImageRequest createEmptyInstance() => create();
  static $pb.PbList<UnsetImageRequest> createRepeated() => $pb.PbList<UnsetImageRequest>();
  @$core.pragma('dart2js:noInline')
  static UnsetImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UnsetImageRequest>(create);
  static UnsetImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);
}

class UnsetImageResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UnsetImageResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  UnsetImageResponse._() : super();
  factory UnsetImageResponse() => create();
  factory UnsetImageResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UnsetImageResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UnsetImageResponse clone() => UnsetImageResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UnsetImageResponse copyWith(void Function(UnsetImageResponse) updates) => super.copyWith((message) => updates(message as UnsetImageResponse)) as UnsetImageResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UnsetImageResponse create() => UnsetImageResponse._();
  UnsetImageResponse createEmptyInstance() => create();
  static $pb.PbList<UnsetImageResponse> createRepeated() => $pb.PbList<UnsetImageResponse>();
  @$core.pragma('dart2js:noInline')
  static UnsetImageResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UnsetImageResponse>(create);
  static UnsetImageResponse? _defaultInstance;
}

class GetModelPathsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetModelPathsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetModelPathsRequest._() : super();
  factory GetModelPathsRequest() => create();
  factory GetModelPathsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetModelPathsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetModelPathsRequest clone() => GetModelPathsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetModelPathsRequest copyWith(void Function(GetModelPathsRequest) updates) => super.copyWith((message) => updates(message as GetModelPathsRequest)) as GetModelPathsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetModelPathsRequest create() => GetModelPathsRequest._();
  GetModelPathsRequest createEmptyInstance() => create();
  static $pb.PbList<GetModelPathsRequest> createRepeated() => $pb.PbList<GetModelPathsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetModelPathsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetModelPathsRequest>(create);
  static GetModelPathsRequest? _defaultInstance;
}

class GetModelPathsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetModelPathsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2p')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweed')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'furrows')
    ..hasRequiredFields = false
  ;

  GetModelPathsResponse._() : super();
  factory GetModelPathsResponse({
    $core.String? p2p,
    $core.String? deepweed,
    $core.String? furrows,
  }) {
    final _result = create();
    if (p2p != null) {
      _result.p2p = p2p;
    }
    if (deepweed != null) {
      _result.deepweed = deepweed;
    }
    if (furrows != null) {
      _result.furrows = furrows;
    }
    return _result;
  }
  factory GetModelPathsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetModelPathsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetModelPathsResponse clone() => GetModelPathsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetModelPathsResponse copyWith(void Function(GetModelPathsResponse) updates) => super.copyWith((message) => updates(message as GetModelPathsResponse)) as GetModelPathsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetModelPathsResponse create() => GetModelPathsResponse._();
  GetModelPathsResponse createEmptyInstance() => create();
  static $pb.PbList<GetModelPathsResponse> createRepeated() => $pb.PbList<GetModelPathsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetModelPathsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetModelPathsResponse>(create);
  static GetModelPathsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get p2p => $_getSZ(0);
  @$pb.TagNumber(1)
  set p2p($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasP2p() => $_has(0);
  @$pb.TagNumber(1)
  void clearP2p() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get deepweed => $_getSZ(1);
  @$pb.TagNumber(2)
  set deepweed($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDeepweed() => $_has(1);
  @$pb.TagNumber(2)
  void clearDeepweed() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get furrows => $_getSZ(2);
  @$pb.TagNumber(3)
  set furrows($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasFurrows() => $_has(2);
  @$pb.TagNumber(3)
  void clearFurrows() => clearField(3);
}

class GetCameraTemperaturesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraTemperaturesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetCameraTemperaturesRequest._() : super();
  factory GetCameraTemperaturesRequest() => create();
  factory GetCameraTemperaturesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraTemperaturesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraTemperaturesRequest clone() => GetCameraTemperaturesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraTemperaturesRequest copyWith(void Function(GetCameraTemperaturesRequest) updates) => super.copyWith((message) => updates(message as GetCameraTemperaturesRequest)) as GetCameraTemperaturesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraTemperaturesRequest create() => GetCameraTemperaturesRequest._();
  GetCameraTemperaturesRequest createEmptyInstance() => create();
  static $pb.PbList<GetCameraTemperaturesRequest> createRepeated() => $pb.PbList<GetCameraTemperaturesRequest>();
  @$core.pragma('dart2js:noInline')
  static GetCameraTemperaturesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraTemperaturesRequest>(create);
  static GetCameraTemperaturesRequest? _defaultInstance;
}

class CameraTemperature extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CameraTemperature', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'temperature', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  CameraTemperature._() : super();
  factory CameraTemperature({
    $core.String? camId,
    $core.double? temperature,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (temperature != null) {
      _result.temperature = temperature;
    }
    return _result;
  }
  factory CameraTemperature.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CameraTemperature.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CameraTemperature clone() => CameraTemperature()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CameraTemperature copyWith(void Function(CameraTemperature) updates) => super.copyWith((message) => updates(message as CameraTemperature)) as CameraTemperature; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CameraTemperature create() => CameraTemperature._();
  CameraTemperature createEmptyInstance() => create();
  static $pb.PbList<CameraTemperature> createRepeated() => $pb.PbList<CameraTemperature>();
  @$core.pragma('dart2js:noInline')
  static CameraTemperature getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CameraTemperature>(create);
  static CameraTemperature? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get temperature => $_getN(1);
  @$pb.TagNumber(2)
  set temperature($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTemperature() => $_has(1);
  @$pb.TagNumber(2)
  void clearTemperature() => clearField(2);
}

class GetCameraTemperaturesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraTemperaturesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<CameraTemperature>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'temperature', $pb.PbFieldType.PM, subBuilder: CameraTemperature.create)
    ..hasRequiredFields = false
  ;

  GetCameraTemperaturesResponse._() : super();
  factory GetCameraTemperaturesResponse({
    $core.Iterable<CameraTemperature>? temperature,
  }) {
    final _result = create();
    if (temperature != null) {
      _result.temperature.addAll(temperature);
    }
    return _result;
  }
  factory GetCameraTemperaturesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraTemperaturesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraTemperaturesResponse clone() => GetCameraTemperaturesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraTemperaturesResponse copyWith(void Function(GetCameraTemperaturesResponse) updates) => super.copyWith((message) => updates(message as GetCameraTemperaturesResponse)) as GetCameraTemperaturesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraTemperaturesResponse create() => GetCameraTemperaturesResponse._();
  GetCameraTemperaturesResponse createEmptyInstance() => create();
  static $pb.PbList<GetCameraTemperaturesResponse> createRepeated() => $pb.PbList<GetCameraTemperaturesResponse>();
  @$core.pragma('dart2js:noInline')
  static GetCameraTemperaturesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraTemperaturesResponse>(create);
  static GetCameraTemperaturesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<CameraTemperature> get temperature => $_getList(0);
}

class GeoLLA extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GeoLLA', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lng', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alt', $pb.PbFieldType.OD)
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  GeoLLA._() : super();
  factory GeoLLA({
    $core.double? lat,
    $core.double? lng,
    $core.double? alt,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (lat != null) {
      _result.lat = lat;
    }
    if (lng != null) {
      _result.lng = lng;
    }
    if (alt != null) {
      _result.alt = alt;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory GeoLLA.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GeoLLA.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GeoLLA clone() => GeoLLA()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GeoLLA copyWith(void Function(GeoLLA) updates) => super.copyWith((message) => updates(message as GeoLLA)) as GeoLLA; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GeoLLA create() => GeoLLA._();
  GeoLLA createEmptyInstance() => create();
  static $pb.PbList<GeoLLA> createRepeated() => $pb.PbList<GeoLLA>();
  @$core.pragma('dart2js:noInline')
  static GeoLLA getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GeoLLA>(create);
  static GeoLLA? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get lat => $_getN(0);
  @$pb.TagNumber(1)
  set lat($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasLat() => $_has(0);
  @$pb.TagNumber(1)
  void clearLat() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get lng => $_getN(1);
  @$pb.TagNumber(2)
  set lng($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLng() => $_has(1);
  @$pb.TagNumber(2)
  void clearLng() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get alt => $_getN(2);
  @$pb.TagNumber(3)
  set alt($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAlt() => $_has(2);
  @$pb.TagNumber(3)
  void clearAlt() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get timestampMs => $_getI64(3);
  @$pb.TagNumber(4)
  set timestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTimestampMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearTimestampMs() => clearField(4);
}

class GeoECEF extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GeoECEF', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'z', $pb.PbFieldType.OD)
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  GeoECEF._() : super();
  factory GeoECEF({
    $core.double? x,
    $core.double? y,
    $core.double? z,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    if (z != null) {
      _result.z = z;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory GeoECEF.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GeoECEF.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GeoECEF clone() => GeoECEF()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GeoECEF copyWith(void Function(GeoECEF) updates) => super.copyWith((message) => updates(message as GeoECEF)) as GeoECEF; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GeoECEF create() => GeoECEF._();
  GeoECEF createEmptyInstance() => create();
  static $pb.PbList<GeoECEF> createRepeated() => $pb.PbList<GeoECEF>();
  @$core.pragma('dart2js:noInline')
  static GeoECEF getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GeoECEF>(create);
  static GeoECEF? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get x => $_getN(0);
  @$pb.TagNumber(1)
  set x($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get y => $_getN(1);
  @$pb.TagNumber(2)
  set y($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasY() => $_has(1);
  @$pb.TagNumber(2)
  void clearY() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get z => $_getN(2);
  @$pb.TagNumber(3)
  set z($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasZ() => $_has(2);
  @$pb.TagNumber(3)
  void clearZ() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get timestampMs => $_getI64(3);
  @$pb.TagNumber(4)
  set timestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTimestampMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearTimestampMs() => clearField(4);
}

class SetGPSLocationRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetGPSLocationRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOM<GeoLLA>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lla', subBuilder: GeoLLA.create)
    ..aOM<GeoECEF>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecef', subBuilder: GeoECEF.create)
    ..hasRequiredFields = false
  ;

  SetGPSLocationRequest._() : super();
  factory SetGPSLocationRequest({
    GeoLLA? lla,
    GeoECEF? ecef,
  }) {
    final _result = create();
    if (lla != null) {
      _result.lla = lla;
    }
    if (ecef != null) {
      _result.ecef = ecef;
    }
    return _result;
  }
  factory SetGPSLocationRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetGPSLocationRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetGPSLocationRequest clone() => SetGPSLocationRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetGPSLocationRequest copyWith(void Function(SetGPSLocationRequest) updates) => super.copyWith((message) => updates(message as SetGPSLocationRequest)) as SetGPSLocationRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetGPSLocationRequest create() => SetGPSLocationRequest._();
  SetGPSLocationRequest createEmptyInstance() => create();
  static $pb.PbList<SetGPSLocationRequest> createRepeated() => $pb.PbList<SetGPSLocationRequest>();
  @$core.pragma('dart2js:noInline')
  static SetGPSLocationRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetGPSLocationRequest>(create);
  static SetGPSLocationRequest? _defaultInstance;

  @$pb.TagNumber(1)
  GeoLLA get lla => $_getN(0);
  @$pb.TagNumber(1)
  set lla(GeoLLA v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasLla() => $_has(0);
  @$pb.TagNumber(1)
  void clearLla() => clearField(1);
  @$pb.TagNumber(1)
  GeoLLA ensureLla() => $_ensure(0);

  @$pb.TagNumber(2)
  GeoECEF get ecef => $_getN(1);
  @$pb.TagNumber(2)
  set ecef(GeoECEF v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasEcef() => $_has(1);
  @$pb.TagNumber(2)
  void clearEcef() => clearField(2);
  @$pb.TagNumber(2)
  GeoECEF ensureEcef() => $_ensure(1);
}

class SetGPSLocationResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetGPSLocationResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetGPSLocationResponse._() : super();
  factory SetGPSLocationResponse() => create();
  factory SetGPSLocationResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetGPSLocationResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetGPSLocationResponse clone() => SetGPSLocationResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetGPSLocationResponse copyWith(void Function(SetGPSLocationResponse) updates) => super.copyWith((message) => updates(message as SetGPSLocationResponse)) as SetGPSLocationResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetGPSLocationResponse create() => SetGPSLocationResponse._();
  SetGPSLocationResponse createEmptyInstance() => create();
  static $pb.PbList<SetGPSLocationResponse> createRepeated() => $pb.PbList<SetGPSLocationResponse>();
  @$core.pragma('dart2js:noInline')
  static SetGPSLocationResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetGPSLocationResponse>(create);
  static SetGPSLocationResponse? _defaultInstance;
}

class SetImplementStatusRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetImplementStatusRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lifted')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'estopped')
    ..hasRequiredFields = false
  ;

  SetImplementStatusRequest._() : super();
  factory SetImplementStatusRequest({
    $core.bool? lifted,
    $core.bool? estopped,
  }) {
    final _result = create();
    if (lifted != null) {
      _result.lifted = lifted;
    }
    if (estopped != null) {
      _result.estopped = estopped;
    }
    return _result;
  }
  factory SetImplementStatusRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetImplementStatusRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetImplementStatusRequest clone() => SetImplementStatusRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetImplementStatusRequest copyWith(void Function(SetImplementStatusRequest) updates) => super.copyWith((message) => updates(message as SetImplementStatusRequest)) as SetImplementStatusRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetImplementStatusRequest create() => SetImplementStatusRequest._();
  SetImplementStatusRequest createEmptyInstance() => create();
  static $pb.PbList<SetImplementStatusRequest> createRepeated() => $pb.PbList<SetImplementStatusRequest>();
  @$core.pragma('dart2js:noInline')
  static SetImplementStatusRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetImplementStatusRequest>(create);
  static SetImplementStatusRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get lifted => $_getBF(0);
  @$pb.TagNumber(1)
  set lifted($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasLifted() => $_has(0);
  @$pb.TagNumber(1)
  void clearLifted() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get estopped => $_getBF(1);
  @$pb.TagNumber(2)
  set estopped($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasEstopped() => $_has(1);
  @$pb.TagNumber(2)
  void clearEstopped() => clearField(2);
}

class SetImplementStatusResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetImplementStatusResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetImplementStatusResponse._() : super();
  factory SetImplementStatusResponse() => create();
  factory SetImplementStatusResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetImplementStatusResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetImplementStatusResponse clone() => SetImplementStatusResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetImplementStatusResponse copyWith(void Function(SetImplementStatusResponse) updates) => super.copyWith((message) => updates(message as SetImplementStatusResponse)) as SetImplementStatusResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetImplementStatusResponse create() => SetImplementStatusResponse._();
  SetImplementStatusResponse createEmptyInstance() => create();
  static $pb.PbList<SetImplementStatusResponse> createRepeated() => $pb.PbList<SetImplementStatusResponse>();
  @$core.pragma('dart2js:noInline')
  static SetImplementStatusResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetImplementStatusResponse>(create);
  static SetImplementStatusResponse? _defaultInstance;
}

enum MaskExpression_Type {
  category, 
  dilate, 
  erode, 
  unionExpr, 
  intersect, 
  negate, 
  line, 
  allLines, 
  notSet
}

class MaskExpression extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, MaskExpression_Type> _MaskExpression_TypeByTag = {
    1 : MaskExpression_Type.category,
    2 : MaskExpression_Type.dilate,
    3 : MaskExpression_Type.erode,
    4 : MaskExpression_Type.unionExpr,
    5 : MaskExpression_Type.intersect,
    6 : MaskExpression_Type.negate,
    7 : MaskExpression_Type.line,
    8 : MaskExpression_Type.allLines,
    0 : MaskExpression_Type.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MaskExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3, 4, 5, 6, 7, 8])
    ..aOM<CategoryExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category', subBuilder: CategoryExpression.create)
    ..aOM<DilateExpression>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dilate', subBuilder: DilateExpression.create)
    ..aOM<ErodeExpression>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'erode', subBuilder: ErodeExpression.create)
    ..aOM<UnionExpression>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'unionExpr', subBuilder: UnionExpression.create)
    ..aOM<IntersectExpression>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'intersect', subBuilder: IntersectExpression.create)
    ..aOM<NegateExpression>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'negate', subBuilder: NegateExpression.create)
    ..aOM<LineExpression>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'line', subBuilder: LineExpression.create)
    ..aOM<AllLinesExpression>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'allLines', subBuilder: AllLinesExpression.create)
    ..hasRequiredFields = false
  ;

  MaskExpression._() : super();
  factory MaskExpression({
    CategoryExpression? category,
    DilateExpression? dilate,
    ErodeExpression? erode,
    UnionExpression? unionExpr,
    IntersectExpression? intersect,
    NegateExpression? negate,
    LineExpression? line,
    AllLinesExpression? allLines,
  }) {
    final _result = create();
    if (category != null) {
      _result.category = category;
    }
    if (dilate != null) {
      _result.dilate = dilate;
    }
    if (erode != null) {
      _result.erode = erode;
    }
    if (unionExpr != null) {
      _result.unionExpr = unionExpr;
    }
    if (intersect != null) {
      _result.intersect = intersect;
    }
    if (negate != null) {
      _result.negate = negate;
    }
    if (line != null) {
      _result.line = line;
    }
    if (allLines != null) {
      _result.allLines = allLines;
    }
    return _result;
  }
  factory MaskExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MaskExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MaskExpression clone() => MaskExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MaskExpression copyWith(void Function(MaskExpression) updates) => super.copyWith((message) => updates(message as MaskExpression)) as MaskExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MaskExpression create() => MaskExpression._();
  MaskExpression createEmptyInstance() => create();
  static $pb.PbList<MaskExpression> createRepeated() => $pb.PbList<MaskExpression>();
  @$core.pragma('dart2js:noInline')
  static MaskExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MaskExpression>(create);
  static MaskExpression? _defaultInstance;

  MaskExpression_Type whichType() => _MaskExpression_TypeByTag[$_whichOneof(0)]!;
  void clearType() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  CategoryExpression get category => $_getN(0);
  @$pb.TagNumber(1)
  set category(CategoryExpression v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCategory() => $_has(0);
  @$pb.TagNumber(1)
  void clearCategory() => clearField(1);
  @$pb.TagNumber(1)
  CategoryExpression ensureCategory() => $_ensure(0);

  @$pb.TagNumber(2)
  DilateExpression get dilate => $_getN(1);
  @$pb.TagNumber(2)
  set dilate(DilateExpression v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasDilate() => $_has(1);
  @$pb.TagNumber(2)
  void clearDilate() => clearField(2);
  @$pb.TagNumber(2)
  DilateExpression ensureDilate() => $_ensure(1);

  @$pb.TagNumber(3)
  ErodeExpression get erode => $_getN(2);
  @$pb.TagNumber(3)
  set erode(ErodeExpression v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasErode() => $_has(2);
  @$pb.TagNumber(3)
  void clearErode() => clearField(3);
  @$pb.TagNumber(3)
  ErodeExpression ensureErode() => $_ensure(2);

  @$pb.TagNumber(4)
  UnionExpression get unionExpr => $_getN(3);
  @$pb.TagNumber(4)
  set unionExpr(UnionExpression v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasUnionExpr() => $_has(3);
  @$pb.TagNumber(4)
  void clearUnionExpr() => clearField(4);
  @$pb.TagNumber(4)
  UnionExpression ensureUnionExpr() => $_ensure(3);

  @$pb.TagNumber(5)
  IntersectExpression get intersect => $_getN(4);
  @$pb.TagNumber(5)
  set intersect(IntersectExpression v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasIntersect() => $_has(4);
  @$pb.TagNumber(5)
  void clearIntersect() => clearField(5);
  @$pb.TagNumber(5)
  IntersectExpression ensureIntersect() => $_ensure(4);

  @$pb.TagNumber(6)
  NegateExpression get negate => $_getN(5);
  @$pb.TagNumber(6)
  set negate(NegateExpression v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasNegate() => $_has(5);
  @$pb.TagNumber(6)
  void clearNegate() => clearField(6);
  @$pb.TagNumber(6)
  NegateExpression ensureNegate() => $_ensure(5);

  @$pb.TagNumber(7)
  LineExpression get line => $_getN(6);
  @$pb.TagNumber(7)
  set line(LineExpression v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasLine() => $_has(6);
  @$pb.TagNumber(7)
  void clearLine() => clearField(7);
  @$pb.TagNumber(7)
  LineExpression ensureLine() => $_ensure(6);

  @$pb.TagNumber(8)
  AllLinesExpression get allLines => $_getN(7);
  @$pb.TagNumber(8)
  set allLines(AllLinesExpression v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasAllLines() => $_has(7);
  @$pb.TagNumber(8)
  void clearAllLines() => clearField(8);
  @$pb.TagNumber(8)
  AllLinesExpression ensureAllLines() => $_ensure(7);
}

class CategoryExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CategoryExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'threshold', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  CategoryExpression._() : super();
  factory CategoryExpression({
    $core.String? category,
    $core.double? threshold,
  }) {
    final _result = create();
    if (category != null) {
      _result.category = category;
    }
    if (threshold != null) {
      _result.threshold = threshold;
    }
    return _result;
  }
  factory CategoryExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CategoryExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CategoryExpression clone() => CategoryExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CategoryExpression copyWith(void Function(CategoryExpression) updates) => super.copyWith((message) => updates(message as CategoryExpression)) as CategoryExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CategoryExpression create() => CategoryExpression._();
  CategoryExpression createEmptyInstance() => create();
  static $pb.PbList<CategoryExpression> createRepeated() => $pb.PbList<CategoryExpression>();
  @$core.pragma('dart2js:noInline')
  static CategoryExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CategoryExpression>(create);
  static CategoryExpression? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get category => $_getSZ(0);
  @$pb.TagNumber(1)
  set category($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCategory() => $_has(0);
  @$pb.TagNumber(1)
  void clearCategory() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get threshold => $_getN(1);
  @$pb.TagNumber(2)
  set threshold($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasThreshold() => $_has(1);
  @$pb.TagNumber(2)
  void clearThreshold() => clearField(2);
}

class LineExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LineExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  LineExpression._() : super();
  factory LineExpression({
    $core.String? category,
    $core.double? x,
    $core.double? y,
  }) {
    final _result = create();
    if (category != null) {
      _result.category = category;
    }
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    return _result;
  }
  factory LineExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LineExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LineExpression clone() => LineExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LineExpression copyWith(void Function(LineExpression) updates) => super.copyWith((message) => updates(message as LineExpression)) as LineExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LineExpression create() => LineExpression._();
  LineExpression createEmptyInstance() => create();
  static $pb.PbList<LineExpression> createRepeated() => $pb.PbList<LineExpression>();
  @$core.pragma('dart2js:noInline')
  static LineExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LineExpression>(create);
  static LineExpression? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get category => $_getSZ(0);
  @$pb.TagNumber(1)
  set category($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCategory() => $_has(0);
  @$pb.TagNumber(1)
  void clearCategory() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get x => $_getN(1);
  @$pb.TagNumber(2)
  set x($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasX() => $_has(1);
  @$pb.TagNumber(2)
  void clearX() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get y => $_getN(2);
  @$pb.TagNumber(3)
  set y($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasY() => $_has(2);
  @$pb.TagNumber(3)
  void clearY() => clearField(3);
}

class AllLinesExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AllLinesExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'category')
    ..hasRequiredFields = false
  ;

  AllLinesExpression._() : super();
  factory AllLinesExpression({
    $core.String? category,
  }) {
    final _result = create();
    if (category != null) {
      _result.category = category;
    }
    return _result;
  }
  factory AllLinesExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AllLinesExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AllLinesExpression clone() => AllLinesExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AllLinesExpression copyWith(void Function(AllLinesExpression) updates) => super.copyWith((message) => updates(message as AllLinesExpression)) as AllLinesExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AllLinesExpression create() => AllLinesExpression._();
  AllLinesExpression createEmptyInstance() => create();
  static $pb.PbList<AllLinesExpression> createRepeated() => $pb.PbList<AllLinesExpression>();
  @$core.pragma('dart2js:noInline')
  static AllLinesExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AllLinesExpression>(create);
  static AllLinesExpression? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get category => $_getSZ(0);
  @$pb.TagNumber(1)
  set category($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCategory() => $_has(0);
  @$pb.TagNumber(1)
  void clearCategory() => clearField(1);
}

class DilateExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DilateExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOM<MaskExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expr', subBuilder: MaskExpression.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'size', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'granularity', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  DilateExpression._() : super();
  factory DilateExpression({
    MaskExpression? expr,
    $core.int? size,
    $core.int? granularity,
  }) {
    final _result = create();
    if (expr != null) {
      _result.expr = expr;
    }
    if (size != null) {
      _result.size = size;
    }
    if (granularity != null) {
      _result.granularity = granularity;
    }
    return _result;
  }
  factory DilateExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DilateExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DilateExpression clone() => DilateExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DilateExpression copyWith(void Function(DilateExpression) updates) => super.copyWith((message) => updates(message as DilateExpression)) as DilateExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DilateExpression create() => DilateExpression._();
  DilateExpression createEmptyInstance() => create();
  static $pb.PbList<DilateExpression> createRepeated() => $pb.PbList<DilateExpression>();
  @$core.pragma('dart2js:noInline')
  static DilateExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DilateExpression>(create);
  static DilateExpression? _defaultInstance;

  @$pb.TagNumber(1)
  MaskExpression get expr => $_getN(0);
  @$pb.TagNumber(1)
  set expr(MaskExpression v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasExpr() => $_has(0);
  @$pb.TagNumber(1)
  void clearExpr() => clearField(1);
  @$pb.TagNumber(1)
  MaskExpression ensureExpr() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get size => $_getIZ(1);
  @$pb.TagNumber(2)
  set size($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSize() => $_has(1);
  @$pb.TagNumber(2)
  void clearSize() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get granularity => $_getIZ(2);
  @$pb.TagNumber(3)
  set granularity($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGranularity() => $_has(2);
  @$pb.TagNumber(3)
  void clearGranularity() => clearField(3);
}

class ErodeExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ErodeExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOM<MaskExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expr', subBuilder: MaskExpression.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'size', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'granularity', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  ErodeExpression._() : super();
  factory ErodeExpression({
    MaskExpression? expr,
    $core.int? size,
    $core.int? granularity,
  }) {
    final _result = create();
    if (expr != null) {
      _result.expr = expr;
    }
    if (size != null) {
      _result.size = size;
    }
    if (granularity != null) {
      _result.granularity = granularity;
    }
    return _result;
  }
  factory ErodeExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ErodeExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ErodeExpression clone() => ErodeExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ErodeExpression copyWith(void Function(ErodeExpression) updates) => super.copyWith((message) => updates(message as ErodeExpression)) as ErodeExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ErodeExpression create() => ErodeExpression._();
  ErodeExpression createEmptyInstance() => create();
  static $pb.PbList<ErodeExpression> createRepeated() => $pb.PbList<ErodeExpression>();
  @$core.pragma('dart2js:noInline')
  static ErodeExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ErodeExpression>(create);
  static ErodeExpression? _defaultInstance;

  @$pb.TagNumber(1)
  MaskExpression get expr => $_getN(0);
  @$pb.TagNumber(1)
  set expr(MaskExpression v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasExpr() => $_has(0);
  @$pb.TagNumber(1)
  void clearExpr() => clearField(1);
  @$pb.TagNumber(1)
  MaskExpression ensureExpr() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get size => $_getIZ(1);
  @$pb.TagNumber(2)
  set size($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSize() => $_has(1);
  @$pb.TagNumber(2)
  void clearSize() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get granularity => $_getIZ(2);
  @$pb.TagNumber(3)
  set granularity($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGranularity() => $_has(2);
  @$pb.TagNumber(3)
  void clearGranularity() => clearField(3);
}

class UnionExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UnionExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<MaskExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exprs', $pb.PbFieldType.PM, subBuilder: MaskExpression.create)
    ..hasRequiredFields = false
  ;

  UnionExpression._() : super();
  factory UnionExpression({
    $core.Iterable<MaskExpression>? exprs,
  }) {
    final _result = create();
    if (exprs != null) {
      _result.exprs.addAll(exprs);
    }
    return _result;
  }
  factory UnionExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UnionExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UnionExpression clone() => UnionExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UnionExpression copyWith(void Function(UnionExpression) updates) => super.copyWith((message) => updates(message as UnionExpression)) as UnionExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UnionExpression create() => UnionExpression._();
  UnionExpression createEmptyInstance() => create();
  static $pb.PbList<UnionExpression> createRepeated() => $pb.PbList<UnionExpression>();
  @$core.pragma('dart2js:noInline')
  static UnionExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UnionExpression>(create);
  static UnionExpression? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<MaskExpression> get exprs => $_getList(0);
}

class IntersectExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'IntersectExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<MaskExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exprs', $pb.PbFieldType.PM, subBuilder: MaskExpression.create)
    ..hasRequiredFields = false
  ;

  IntersectExpression._() : super();
  factory IntersectExpression({
    $core.Iterable<MaskExpression>? exprs,
  }) {
    final _result = create();
    if (exprs != null) {
      _result.exprs.addAll(exprs);
    }
    return _result;
  }
  factory IntersectExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory IntersectExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  IntersectExpression clone() => IntersectExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  IntersectExpression copyWith(void Function(IntersectExpression) updates) => super.copyWith((message) => updates(message as IntersectExpression)) as IntersectExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static IntersectExpression create() => IntersectExpression._();
  IntersectExpression createEmptyInstance() => create();
  static $pb.PbList<IntersectExpression> createRepeated() => $pb.PbList<IntersectExpression>();
  @$core.pragma('dart2js:noInline')
  static IntersectExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<IntersectExpression>(create);
  static IntersectExpression? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<MaskExpression> get exprs => $_getList(0);
}

class NegateExpression extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'NegateExpression', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOM<MaskExpression>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expr', subBuilder: MaskExpression.create)
    ..hasRequiredFields = false
  ;

  NegateExpression._() : super();
  factory NegateExpression({
    MaskExpression? expr,
  }) {
    final _result = create();
    if (expr != null) {
      _result.expr = expr;
    }
    return _result;
  }
  factory NegateExpression.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory NegateExpression.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  NegateExpression clone() => NegateExpression()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  NegateExpression copyWith(void Function(NegateExpression) updates) => super.copyWith((message) => updates(message as NegateExpression)) as NegateExpression; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static NegateExpression create() => NegateExpression._();
  NegateExpression createEmptyInstance() => create();
  static $pb.PbList<NegateExpression> createRepeated() => $pb.PbList<NegateExpression>();
  @$core.pragma('dart2js:noInline')
  static NegateExpression getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<NegateExpression>(create);
  static NegateExpression? _defaultInstance;

  @$pb.TagNumber(1)
  MaskExpression get expr => $_getN(0);
  @$pb.TagNumber(1)
  set expr(MaskExpression v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasExpr() => $_has(0);
  @$pb.TagNumber(1)
  void clearExpr() => clearField(1);
  @$pb.TagNumber(1)
  MaskExpression ensureExpr() => $_ensure(0);
}

class SetImageScoreRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetImageScoreRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OD)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..pc<DeepweedDetection>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweedDetections', $pb.PbFieldType.PM, subBuilder: DeepweedDetection.create)
    ..hasRequiredFields = false
  ;

  SetImageScoreRequest._() : super();
  factory SetImageScoreRequest({
    $core.double? score,
    $fixnum.Int64? timestampMs,
    $core.String? camId,
    $core.Iterable<DeepweedDetection>? deepweedDetections,
  }) {
    final _result = create();
    if (score != null) {
      _result.score = score;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (deepweedDetections != null) {
      _result.deepweedDetections.addAll(deepweedDetections);
    }
    return _result;
  }
  factory SetImageScoreRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetImageScoreRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetImageScoreRequest clone() => SetImageScoreRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetImageScoreRequest copyWith(void Function(SetImageScoreRequest) updates) => super.copyWith((message) => updates(message as SetImageScoreRequest)) as SetImageScoreRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetImageScoreRequest create() => SetImageScoreRequest._();
  SetImageScoreRequest createEmptyInstance() => create();
  static $pb.PbList<SetImageScoreRequest> createRepeated() => $pb.PbList<SetImageScoreRequest>();
  @$core.pragma('dart2js:noInline')
  static SetImageScoreRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetImageScoreRequest>(create);
  static SetImageScoreRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get score => $_getN(0);
  @$pb.TagNumber(1)
  set score($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScore() => $_has(0);
  @$pb.TagNumber(1)
  void clearScore() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get camId => $_getSZ(2);
  @$pb.TagNumber(3)
  set camId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCamId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCamId() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<DeepweedDetection> get deepweedDetections => $_getList(3);
}

class SetImageScoreResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetImageScoreResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetImageScoreResponse._() : super();
  factory SetImageScoreResponse() => create();
  factory SetImageScoreResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetImageScoreResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetImageScoreResponse clone() => SetImageScoreResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetImageScoreResponse copyWith(void Function(SetImageScoreResponse) updates) => super.copyWith((message) => updates(message as SetImageScoreResponse)) as SetImageScoreResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetImageScoreResponse create() => SetImageScoreResponse._();
  SetImageScoreResponse createEmptyInstance() => create();
  static $pb.PbList<SetImageScoreResponse> createRepeated() => $pb.PbList<SetImageScoreResponse>();
  @$core.pragma('dart2js:noInline')
  static SetImageScoreResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetImageScoreResponse>(create);
  static SetImageScoreResponse? _defaultInstance;
}

class GetScoreQueueRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetScoreQueueRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreType')
    ..hasRequiredFields = false
  ;

  GetScoreQueueRequest._() : super();
  factory GetScoreQueueRequest({
    $core.String? scoreType,
  }) {
    final _result = create();
    if (scoreType != null) {
      _result.scoreType = scoreType;
    }
    return _result;
  }
  factory GetScoreQueueRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetScoreQueueRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetScoreQueueRequest clone() => GetScoreQueueRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetScoreQueueRequest copyWith(void Function(GetScoreQueueRequest) updates) => super.copyWith((message) => updates(message as GetScoreQueueRequest)) as GetScoreQueueRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetScoreQueueRequest create() => GetScoreQueueRequest._();
  GetScoreQueueRequest createEmptyInstance() => create();
  static $pb.PbList<GetScoreQueueRequest> createRepeated() => $pb.PbList<GetScoreQueueRequest>();
  @$core.pragma('dart2js:noInline')
  static GetScoreQueueRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetScoreQueueRequest>(create);
  static GetScoreQueueRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get scoreType => $_getSZ(0);
  @$pb.TagNumber(1)
  set scoreType($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScoreType() => $_has(0);
  @$pb.TagNumber(1)
  void clearScoreType() => clearField(1);
}

class ScoreObject extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ScoreObject', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OD)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  ScoreObject._() : super();
  factory ScoreObject({
    $core.double? score,
    $fixnum.Int64? timestampMs,
    $core.String? camId,
  }) {
    final _result = create();
    if (score != null) {
      _result.score = score;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory ScoreObject.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ScoreObject.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ScoreObject clone() => ScoreObject()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ScoreObject copyWith(void Function(ScoreObject) updates) => super.copyWith((message) => updates(message as ScoreObject)) as ScoreObject; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ScoreObject create() => ScoreObject._();
  ScoreObject createEmptyInstance() => create();
  static $pb.PbList<ScoreObject> createRepeated() => $pb.PbList<ScoreObject>();
  @$core.pragma('dart2js:noInline')
  static ScoreObject getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ScoreObject>(create);
  static ScoreObject? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get score => $_getN(0);
  @$pb.TagNumber(1)
  set score($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScore() => $_has(0);
  @$pb.TagNumber(1)
  void clearScore() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get camId => $_getSZ(2);
  @$pb.TagNumber(3)
  set camId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCamId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCamId() => clearField(3);
}

class GetScoreQueueResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetScoreQueueResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<ScoreObject>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreObject', $pb.PbFieldType.PM, subBuilder: ScoreObject.create)
    ..hasRequiredFields = false
  ;

  GetScoreQueueResponse._() : super();
  factory GetScoreQueueResponse({
    $core.Iterable<ScoreObject>? scoreObject,
  }) {
    final _result = create();
    if (scoreObject != null) {
      _result.scoreObject.addAll(scoreObject);
    }
    return _result;
  }
  factory GetScoreQueueResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetScoreQueueResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetScoreQueueResponse clone() => GetScoreQueueResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetScoreQueueResponse copyWith(void Function(GetScoreQueueResponse) updates) => super.copyWith((message) => updates(message as GetScoreQueueResponse)) as GetScoreQueueResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetScoreQueueResponse create() => GetScoreQueueResponse._();
  GetScoreQueueResponse createEmptyInstance() => create();
  static $pb.PbList<GetScoreQueueResponse> createRepeated() => $pb.PbList<GetScoreQueueResponse>();
  @$core.pragma('dart2js:noInline')
  static GetScoreQueueResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetScoreQueueResponse>(create);
  static GetScoreQueueResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ScoreObject> get scoreObject => $_getList(0);
}

class GetMaxImageScoreRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetMaxImageScoreRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreType')
    ..hasRequiredFields = false
  ;

  GetMaxImageScoreRequest._() : super();
  factory GetMaxImageScoreRequest({
    $core.String? scoreType,
  }) {
    final _result = create();
    if (scoreType != null) {
      _result.scoreType = scoreType;
    }
    return _result;
  }
  factory GetMaxImageScoreRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetMaxImageScoreRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetMaxImageScoreRequest clone() => GetMaxImageScoreRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetMaxImageScoreRequest copyWith(void Function(GetMaxImageScoreRequest) updates) => super.copyWith((message) => updates(message as GetMaxImageScoreRequest)) as GetMaxImageScoreRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetMaxImageScoreRequest create() => GetMaxImageScoreRequest._();
  GetMaxImageScoreRequest createEmptyInstance() => create();
  static $pb.PbList<GetMaxImageScoreRequest> createRepeated() => $pb.PbList<GetMaxImageScoreRequest>();
  @$core.pragma('dart2js:noInline')
  static GetMaxImageScoreRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetMaxImageScoreRequest>(create);
  static GetMaxImageScoreRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get scoreType => $_getSZ(0);
  @$pb.TagNumber(1)
  set scoreType($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScoreType() => $_has(0);
  @$pb.TagNumber(1)
  void clearScoreType() => clearField(1);
}

class GetMaxImageScoreResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetMaxImageScoreResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OD)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type')
    ..hasRequiredFields = false
  ;

  GetMaxImageScoreResponse._() : super();
  factory GetMaxImageScoreResponse({
    $core.double? score,
    $core.String? type,
  }) {
    final _result = create();
    if (score != null) {
      _result.score = score;
    }
    if (type != null) {
      _result.type = type;
    }
    return _result;
  }
  factory GetMaxImageScoreResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetMaxImageScoreResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetMaxImageScoreResponse clone() => GetMaxImageScoreResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetMaxImageScoreResponse copyWith(void Function(GetMaxImageScoreResponse) updates) => super.copyWith((message) => updates(message as GetMaxImageScoreResponse)) as GetMaxImageScoreResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetMaxImageScoreResponse create() => GetMaxImageScoreResponse._();
  GetMaxImageScoreResponse createEmptyInstance() => create();
  static $pb.PbList<GetMaxImageScoreResponse> createRepeated() => $pb.PbList<GetMaxImageScoreResponse>();
  @$core.pragma('dart2js:noInline')
  static GetMaxImageScoreResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetMaxImageScoreResponse>(create);
  static GetMaxImageScoreResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get score => $_getN(0);
  @$pb.TagNumber(1)
  set score($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScore() => $_has(0);
  @$pb.TagNumber(1)
  void clearScore() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get type => $_getSZ(1);
  @$pb.TagNumber(2)
  set type($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasType() => $_has(1);
  @$pb.TagNumber(2)
  void clearType() => clearField(2);
}

class GetMaxScoredImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetMaxScoredImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreType')
    ..hasRequiredFields = false
  ;

  GetMaxScoredImageRequest._() : super();
  factory GetMaxScoredImageRequest({
    $core.String? scoreType,
  }) {
    final _result = create();
    if (scoreType != null) {
      _result.scoreType = scoreType;
    }
    return _result;
  }
  factory GetMaxScoredImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetMaxScoredImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetMaxScoredImageRequest clone() => GetMaxScoredImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetMaxScoredImageRequest copyWith(void Function(GetMaxScoredImageRequest) updates) => super.copyWith((message) => updates(message as GetMaxScoredImageRequest)) as GetMaxScoredImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetMaxScoredImageRequest create() => GetMaxScoredImageRequest._();
  GetMaxScoredImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetMaxScoredImageRequest> createRepeated() => $pb.PbList<GetMaxScoredImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetMaxScoredImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetMaxScoredImageRequest>(create);
  static GetMaxScoredImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get scoreType => $_getSZ(0);
  @$pb.TagNumber(1)
  set scoreType($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScoreType() => $_has(0);
  @$pb.TagNumber(1)
  void clearScoreType() => clearField(1);
}

class GetLatestP2PImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetLatestP2PImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreType')
    ..e<$61.P2PCaptureReason>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reason', $pb.PbFieldType.OE, defaultOrMaker: $61.P2PCaptureReason.P2PCaptureReason_MISS, valueOf: $61.P2PCaptureReason.valueOf, enumValues: $61.P2PCaptureReason.values)
    ..hasRequiredFields = false
  ;

  GetLatestP2PImageRequest._() : super();
  factory GetLatestP2PImageRequest({
  @$core.Deprecated('This field is deprecated.')
    $core.String? scoreType,
    $61.P2PCaptureReason? reason,
  }) {
    final _result = create();
    if (scoreType != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.scoreType = scoreType;
    }
    if (reason != null) {
      _result.reason = reason;
    }
    return _result;
  }
  factory GetLatestP2PImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetLatestP2PImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetLatestP2PImageRequest clone() => GetLatestP2PImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetLatestP2PImageRequest copyWith(void Function(GetLatestP2PImageRequest) updates) => super.copyWith((message) => updates(message as GetLatestP2PImageRequest)) as GetLatestP2PImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetLatestP2PImageRequest create() => GetLatestP2PImageRequest._();
  GetLatestP2PImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetLatestP2PImageRequest> createRepeated() => $pb.PbList<GetLatestP2PImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetLatestP2PImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetLatestP2PImageRequest>(create);
  static GetLatestP2PImageRequest? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.String get scoreType => $_getSZ(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  set scoreType($core.String v) { $_setString(0, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.bool hasScoreType() => $_has(0);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  void clearScoreType() => clearField(1);

  @$pb.TagNumber(2)
  $61.P2PCaptureReason get reason => $_getN(1);
  @$pb.TagNumber(2)
  set reason($61.P2PCaptureReason v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasReason() => $_has(1);
  @$pb.TagNumber(2)
  void clearReason() => clearField(2);
}

class GetLatestImageRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetLatestImageRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  GetLatestImageRequest._() : super();
  factory GetLatestImageRequest({
    $core.String? camId,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory GetLatestImageRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetLatestImageRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetLatestImageRequest clone() => GetLatestImageRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetLatestImageRequest copyWith(void Function(GetLatestImageRequest) updates) => super.copyWith((message) => updates(message as GetLatestImageRequest)) as GetLatestImageRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetLatestImageRequest create() => GetLatestImageRequest._();
  GetLatestImageRequest createEmptyInstance() => create();
  static $pb.PbList<GetLatestImageRequest> createRepeated() => $pb.PbList<GetLatestImageRequest>();
  @$core.pragma('dart2js:noInline')
  static GetLatestImageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetLatestImageRequest>(create);
  static GetLatestImageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);
}

class FlushQueuesRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FlushQueuesRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pPS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreType')
    ..hasRequiredFields = false
  ;

  FlushQueuesRequest._() : super();
  factory FlushQueuesRequest({
    $core.Iterable<$core.String>? scoreType,
  }) {
    final _result = create();
    if (scoreType != null) {
      _result.scoreType.addAll(scoreType);
    }
    return _result;
  }
  factory FlushQueuesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FlushQueuesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FlushQueuesRequest clone() => FlushQueuesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FlushQueuesRequest copyWith(void Function(FlushQueuesRequest) updates) => super.copyWith((message) => updates(message as FlushQueuesRequest)) as FlushQueuesRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FlushQueuesRequest create() => FlushQueuesRequest._();
  FlushQueuesRequest createEmptyInstance() => create();
  static $pb.PbList<FlushQueuesRequest> createRepeated() => $pb.PbList<FlushQueuesRequest>();
  @$core.pragma('dart2js:noInline')
  static FlushQueuesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FlushQueuesRequest>(create);
  static FlushQueuesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get scoreType => $_getList(0);
}

class FlushQueuesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FlushQueuesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  FlushQueuesResponse._() : super();
  factory FlushQueuesResponse() => create();
  factory FlushQueuesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FlushQueuesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FlushQueuesResponse clone() => FlushQueuesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FlushQueuesResponse copyWith(void Function(FlushQueuesResponse) updates) => super.copyWith((message) => updates(message as FlushQueuesResponse)) as FlushQueuesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FlushQueuesResponse create() => FlushQueuesResponse._();
  FlushQueuesResponse createEmptyInstance() => create();
  static $pb.PbList<FlushQueuesResponse> createRepeated() => $pb.PbList<FlushQueuesResponse>();
  @$core.pragma('dart2js:noInline')
  static FlushQueuesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FlushQueuesResponse>(create);
  static FlushQueuesResponse? _defaultInstance;
}

class ImageAndMetadataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ImageAndMetadataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bytes', $pb.PbFieldType.OY)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'height', $pb.PbFieldType.O3)
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OD)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isoFormattedTime')
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaLat', $pb.PbFieldType.OD)
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaLng', $pb.PbFieldType.OD)
    ..a<$core.double>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaAlt', $pb.PbFieldType.OD)
    ..aInt64(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaTimestampMs')
    ..a<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefX', $pb.PbFieldType.OD)
    ..a<$core.double>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefY', $pb.PbFieldType.OD)
    ..a<$core.double>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefZ', $pb.PbFieldType.OD)
    ..aInt64(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefTimestampMs')
    ..a<$core.double>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ppi', $pb.PbFieldType.OF)
    ..aOS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'scoreType')
    ..aOS(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageType')
    ..aOS(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelUrl')
    ..aOS(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..p<$core.double>(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedHeightColumns', $pb.PbFieldType.KD)
    ..p<$core.double>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropHeightColumns', $pb.PbFieldType.KD)
    ..a<$core.double>(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bbhOffsetMm', $pb.PbFieldType.OD)
    ..a<$core.double>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'focusMetric', $pb.PbFieldType.OD)
    ..a<$core.double>(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exposureUs', $pb.PbFieldType.OD)
    ..a<$core.double>(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropPointThreshold', $pb.PbFieldType.OD)
    ..a<$core.double>(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedPointThreshold', $pb.PbFieldType.OD)
    ..aOB(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEnabled')
    ..aOB(29, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningEnabled')
    ..aOS(30, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweedId')
    ..aOS(31, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2pId')
    ..pc<$4.Detection>(32, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweedDetections', $pb.PbFieldType.PM, subBuilder: $4.Detection.create)
    ..a<$core.double>(33, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'segmentationThreshold', $pb.PbFieldType.OD)
    ..aOB(34, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'simulatorGenerated')
    ..hasRequiredFields = false
  ;

  ImageAndMetadataResponse._() : super();
  factory ImageAndMetadataResponse({
    $core.List<$core.int>? bytes,
    $core.int? width,
    $core.int? height,
    $fixnum.Int64? timestampMs,
    $core.double? score,
    $core.String? camId,
    $core.String? isoFormattedTime,
    $core.double? llaLat,
    $core.double? llaLng,
    $core.double? llaAlt,
    $fixnum.Int64? llaTimestampMs,
    $core.double? ecefX,
    $core.double? ecefY,
    $core.double? ecefZ,
    $fixnum.Int64? ecefTimestampMs,
    $core.double? ppi,
    $core.String? scoreType,
    $core.String? imageType,
    $core.String? modelUrl,
    $core.String? crop,
    $core.Iterable<$core.double>? weedHeightColumns,
    $core.Iterable<$core.double>? cropHeightColumns,
    $core.double? bbhOffsetMm,
    $core.double? focusMetric,
    $core.double? exposureUs,
    $core.double? cropPointThreshold,
    $core.double? weedPointThreshold,
    $core.bool? weedingEnabled,
    $core.bool? thinningEnabled,
    $core.String? deepweedId,
    $core.String? p2pId,
    $core.Iterable<$4.Detection>? deepweedDetections,
    $core.double? segmentationThreshold,
    $core.bool? simulatorGenerated,
  }) {
    final _result = create();
    if (bytes != null) {
      _result.bytes = bytes;
    }
    if (width != null) {
      _result.width = width;
    }
    if (height != null) {
      _result.height = height;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (score != null) {
      _result.score = score;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (isoFormattedTime != null) {
      _result.isoFormattedTime = isoFormattedTime;
    }
    if (llaLat != null) {
      _result.llaLat = llaLat;
    }
    if (llaLng != null) {
      _result.llaLng = llaLng;
    }
    if (llaAlt != null) {
      _result.llaAlt = llaAlt;
    }
    if (llaTimestampMs != null) {
      _result.llaTimestampMs = llaTimestampMs;
    }
    if (ecefX != null) {
      _result.ecefX = ecefX;
    }
    if (ecefY != null) {
      _result.ecefY = ecefY;
    }
    if (ecefZ != null) {
      _result.ecefZ = ecefZ;
    }
    if (ecefTimestampMs != null) {
      _result.ecefTimestampMs = ecefTimestampMs;
    }
    if (ppi != null) {
      _result.ppi = ppi;
    }
    if (scoreType != null) {
      _result.scoreType = scoreType;
    }
    if (imageType != null) {
      _result.imageType = imageType;
    }
    if (modelUrl != null) {
      _result.modelUrl = modelUrl;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (weedHeightColumns != null) {
      _result.weedHeightColumns.addAll(weedHeightColumns);
    }
    if (cropHeightColumns != null) {
      _result.cropHeightColumns.addAll(cropHeightColumns);
    }
    if (bbhOffsetMm != null) {
      _result.bbhOffsetMm = bbhOffsetMm;
    }
    if (focusMetric != null) {
      _result.focusMetric = focusMetric;
    }
    if (exposureUs != null) {
      _result.exposureUs = exposureUs;
    }
    if (cropPointThreshold != null) {
      _result.cropPointThreshold = cropPointThreshold;
    }
    if (weedPointThreshold != null) {
      _result.weedPointThreshold = weedPointThreshold;
    }
    if (weedingEnabled != null) {
      _result.weedingEnabled = weedingEnabled;
    }
    if (thinningEnabled != null) {
      _result.thinningEnabled = thinningEnabled;
    }
    if (deepweedId != null) {
      _result.deepweedId = deepweedId;
    }
    if (p2pId != null) {
      _result.p2pId = p2pId;
    }
    if (deepweedDetections != null) {
      _result.deepweedDetections.addAll(deepweedDetections);
    }
    if (segmentationThreshold != null) {
      _result.segmentationThreshold = segmentationThreshold;
    }
    if (simulatorGenerated != null) {
      _result.simulatorGenerated = simulatorGenerated;
    }
    return _result;
  }
  factory ImageAndMetadataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ImageAndMetadataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ImageAndMetadataResponse clone() => ImageAndMetadataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ImageAndMetadataResponse copyWith(void Function(ImageAndMetadataResponse) updates) => super.copyWith((message) => updates(message as ImageAndMetadataResponse)) as ImageAndMetadataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ImageAndMetadataResponse create() => ImageAndMetadataResponse._();
  ImageAndMetadataResponse createEmptyInstance() => create();
  static $pb.PbList<ImageAndMetadataResponse> createRepeated() => $pb.PbList<ImageAndMetadataResponse>();
  @$core.pragma('dart2js:noInline')
  static ImageAndMetadataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ImageAndMetadataResponse>(create);
  static ImageAndMetadataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get bytes => $_getN(0);
  @$pb.TagNumber(1)
  set bytes($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBytes() => $_has(0);
  @$pb.TagNumber(1)
  void clearBytes() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get width => $_getIZ(1);
  @$pb.TagNumber(2)
  set width($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWidth() => $_has(1);
  @$pb.TagNumber(2)
  void clearWidth() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get height => $_getIZ(2);
  @$pb.TagNumber(3)
  set height($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasHeight() => $_has(2);
  @$pb.TagNumber(3)
  void clearHeight() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get timestampMs => $_getI64(3);
  @$pb.TagNumber(4)
  set timestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTimestampMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearTimestampMs() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get score => $_getN(4);
  @$pb.TagNumber(5)
  set score($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasScore() => $_has(4);
  @$pb.TagNumber(5)
  void clearScore() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get camId => $_getSZ(5);
  @$pb.TagNumber(6)
  set camId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCamId() => $_has(5);
  @$pb.TagNumber(6)
  void clearCamId() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get isoFormattedTime => $_getSZ(6);
  @$pb.TagNumber(7)
  set isoFormattedTime($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasIsoFormattedTime() => $_has(6);
  @$pb.TagNumber(7)
  void clearIsoFormattedTime() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get llaLat => $_getN(7);
  @$pb.TagNumber(8)
  set llaLat($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLlaLat() => $_has(7);
  @$pb.TagNumber(8)
  void clearLlaLat() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get llaLng => $_getN(8);
  @$pb.TagNumber(9)
  set llaLng($core.double v) { $_setDouble(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasLlaLng() => $_has(8);
  @$pb.TagNumber(9)
  void clearLlaLng() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get llaAlt => $_getN(9);
  @$pb.TagNumber(10)
  set llaAlt($core.double v) { $_setDouble(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasLlaAlt() => $_has(9);
  @$pb.TagNumber(10)
  void clearLlaAlt() => clearField(10);

  @$pb.TagNumber(11)
  $fixnum.Int64 get llaTimestampMs => $_getI64(10);
  @$pb.TagNumber(11)
  set llaTimestampMs($fixnum.Int64 v) { $_setInt64(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasLlaTimestampMs() => $_has(10);
  @$pb.TagNumber(11)
  void clearLlaTimestampMs() => clearField(11);

  @$pb.TagNumber(12)
  $core.double get ecefX => $_getN(11);
  @$pb.TagNumber(12)
  set ecefX($core.double v) { $_setDouble(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasEcefX() => $_has(11);
  @$pb.TagNumber(12)
  void clearEcefX() => clearField(12);

  @$pb.TagNumber(13)
  $core.double get ecefY => $_getN(12);
  @$pb.TagNumber(13)
  set ecefY($core.double v) { $_setDouble(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasEcefY() => $_has(12);
  @$pb.TagNumber(13)
  void clearEcefY() => clearField(13);

  @$pb.TagNumber(14)
  $core.double get ecefZ => $_getN(13);
  @$pb.TagNumber(14)
  set ecefZ($core.double v) { $_setDouble(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasEcefZ() => $_has(13);
  @$pb.TagNumber(14)
  void clearEcefZ() => clearField(14);

  @$pb.TagNumber(15)
  $fixnum.Int64 get ecefTimestampMs => $_getI64(14);
  @$pb.TagNumber(15)
  set ecefTimestampMs($fixnum.Int64 v) { $_setInt64(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasEcefTimestampMs() => $_has(14);
  @$pb.TagNumber(15)
  void clearEcefTimestampMs() => clearField(15);

  @$pb.TagNumber(16)
  $core.double get ppi => $_getN(15);
  @$pb.TagNumber(16)
  set ppi($core.double v) { $_setFloat(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasPpi() => $_has(15);
  @$pb.TagNumber(16)
  void clearPpi() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get scoreType => $_getSZ(16);
  @$pb.TagNumber(17)
  set scoreType($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasScoreType() => $_has(16);
  @$pb.TagNumber(17)
  void clearScoreType() => clearField(17);

  @$pb.TagNumber(18)
  $core.String get imageType => $_getSZ(17);
  @$pb.TagNumber(18)
  set imageType($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasImageType() => $_has(17);
  @$pb.TagNumber(18)
  void clearImageType() => clearField(18);

  @$pb.TagNumber(19)
  $core.String get modelUrl => $_getSZ(18);
  @$pb.TagNumber(19)
  set modelUrl($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasModelUrl() => $_has(18);
  @$pb.TagNumber(19)
  void clearModelUrl() => clearField(19);

  @$pb.TagNumber(20)
  $core.String get crop => $_getSZ(19);
  @$pb.TagNumber(20)
  set crop($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasCrop() => $_has(19);
  @$pb.TagNumber(20)
  void clearCrop() => clearField(20);

  @$pb.TagNumber(21)
  $core.List<$core.double> get weedHeightColumns => $_getList(20);

  @$pb.TagNumber(22)
  $core.List<$core.double> get cropHeightColumns => $_getList(21);

  @$pb.TagNumber(23)
  $core.double get bbhOffsetMm => $_getN(22);
  @$pb.TagNumber(23)
  set bbhOffsetMm($core.double v) { $_setDouble(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasBbhOffsetMm() => $_has(22);
  @$pb.TagNumber(23)
  void clearBbhOffsetMm() => clearField(23);

  @$pb.TagNumber(24)
  $core.double get focusMetric => $_getN(23);
  @$pb.TagNumber(24)
  set focusMetric($core.double v) { $_setDouble(23, v); }
  @$pb.TagNumber(24)
  $core.bool hasFocusMetric() => $_has(23);
  @$pb.TagNumber(24)
  void clearFocusMetric() => clearField(24);

  @$pb.TagNumber(25)
  $core.double get exposureUs => $_getN(24);
  @$pb.TagNumber(25)
  set exposureUs($core.double v) { $_setDouble(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasExposureUs() => $_has(24);
  @$pb.TagNumber(25)
  void clearExposureUs() => clearField(25);

  @$pb.TagNumber(26)
  $core.double get cropPointThreshold => $_getN(25);
  @$pb.TagNumber(26)
  set cropPointThreshold($core.double v) { $_setDouble(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasCropPointThreshold() => $_has(25);
  @$pb.TagNumber(26)
  void clearCropPointThreshold() => clearField(26);

  @$pb.TagNumber(27)
  $core.double get weedPointThreshold => $_getN(26);
  @$pb.TagNumber(27)
  set weedPointThreshold($core.double v) { $_setDouble(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasWeedPointThreshold() => $_has(26);
  @$pb.TagNumber(27)
  void clearWeedPointThreshold() => clearField(27);

  @$pb.TagNumber(28)
  $core.bool get weedingEnabled => $_getBF(27);
  @$pb.TagNumber(28)
  set weedingEnabled($core.bool v) { $_setBool(27, v); }
  @$pb.TagNumber(28)
  $core.bool hasWeedingEnabled() => $_has(27);
  @$pb.TagNumber(28)
  void clearWeedingEnabled() => clearField(28);

  @$pb.TagNumber(29)
  $core.bool get thinningEnabled => $_getBF(28);
  @$pb.TagNumber(29)
  set thinningEnabled($core.bool v) { $_setBool(28, v); }
  @$pb.TagNumber(29)
  $core.bool hasThinningEnabled() => $_has(28);
  @$pb.TagNumber(29)
  void clearThinningEnabled() => clearField(29);

  @$pb.TagNumber(30)
  $core.String get deepweedId => $_getSZ(29);
  @$pb.TagNumber(30)
  set deepweedId($core.String v) { $_setString(29, v); }
  @$pb.TagNumber(30)
  $core.bool hasDeepweedId() => $_has(29);
  @$pb.TagNumber(30)
  void clearDeepweedId() => clearField(30);

  @$pb.TagNumber(31)
  $core.String get p2pId => $_getSZ(30);
  @$pb.TagNumber(31)
  set p2pId($core.String v) { $_setString(30, v); }
  @$pb.TagNumber(31)
  $core.bool hasP2pId() => $_has(30);
  @$pb.TagNumber(31)
  void clearP2pId() => clearField(31);

  @$pb.TagNumber(32)
  $core.List<$4.Detection> get deepweedDetections => $_getList(31);

  @$pb.TagNumber(33)
  $core.double get segmentationThreshold => $_getN(32);
  @$pb.TagNumber(33)
  set segmentationThreshold($core.double v) { $_setDouble(32, v); }
  @$pb.TagNumber(33)
  $core.bool hasSegmentationThreshold() => $_has(32);
  @$pb.TagNumber(33)
  void clearSegmentationThreshold() => clearField(33);

  @$pb.TagNumber(34)
  $core.bool get simulatorGenerated => $_getBF(33);
  @$pb.TagNumber(34)
  set simulatorGenerated($core.bool v) { $_setBool(33, v); }
  @$pb.TagNumber(34)
  $core.bool hasSimulatorGenerated() => $_has(33);
  @$pb.TagNumber(34)
  void clearSimulatorGenerated() => clearField(34);
}

class P2PImageAndMetadataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PImageAndMetadataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetBytes', $pb.PbFieldType.OY)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetWidth', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetHeight', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'perspectiveBytes', $pb.PbFieldType.OY)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'perspectiveWidth', $pb.PbFieldType.O3)
    ..a<$core.int>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'perspectiveHeight', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'annotatedTargetBytes', $pb.PbFieldType.OY)
    ..a<$core.int>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'annotatedTargetWidth', $pb.PbFieldType.O3)
    ..a<$core.int>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'annotatedTargetHeight', $pb.PbFieldType.O3)
    ..aInt64(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..a<$core.double>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OD)
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isoFormattedTime')
    ..a<$core.double>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaLat', $pb.PbFieldType.OD)
    ..a<$core.double>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaLng', $pb.PbFieldType.OD)
    ..a<$core.double>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaAlt', $pb.PbFieldType.OD)
    ..aInt64(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'llaTimestampMs')
    ..a<$core.double>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefX', $pb.PbFieldType.OD)
    ..a<$core.double>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefY', $pb.PbFieldType.OD)
    ..a<$core.double>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefZ', $pb.PbFieldType.OD)
    ..aInt64(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ecefTimestampMs')
    ..a<$core.double>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ppi', $pb.PbFieldType.OF)
    ..a<$core.double>(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'perspectivePpi', $pb.PbFieldType.OF)
    ..aOS(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageType')
    ..aOS(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelUrl')
    ..aOS(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..a<$core.double>(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'focusMetric', $pb.PbFieldType.OD)
    ..a<$core.double>(29, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'exposureUs', $pb.PbFieldType.OD)
    ..aOB(30, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEnabled')
    ..aOB(31, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningEnabled')
    ..aOS(32, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deepweedId')
    ..aOS(33, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2pId')
    ..hasRequiredFields = false
  ;

  P2PImageAndMetadataResponse._() : super();
  factory P2PImageAndMetadataResponse({
    $core.List<$core.int>? targetBytes,
    $core.int? targetWidth,
    $core.int? targetHeight,
    $core.List<$core.int>? perspectiveBytes,
    $core.int? perspectiveWidth,
    $core.int? perspectiveHeight,
    $core.List<$core.int>? annotatedTargetBytes,
    $core.int? annotatedTargetWidth,
    $core.int? annotatedTargetHeight,
    $fixnum.Int64? timestampMs,
    $core.double? score,
    $core.String? camId,
    $core.String? isoFormattedTime,
    $core.double? llaLat,
    $core.double? llaLng,
    $core.double? llaAlt,
    $fixnum.Int64? llaTimestampMs,
    $core.double? ecefX,
    $core.double? ecefY,
    $core.double? ecefZ,
    $fixnum.Int64? ecefTimestampMs,
    $core.double? ppi,
    $core.double? perspectivePpi,
    $core.String? imageType,
    $core.String? modelUrl,
    $core.String? crop,
    $core.double? focusMetric,
    $core.double? exposureUs,
    $core.bool? weedingEnabled,
    $core.bool? thinningEnabled,
    $core.String? deepweedId,
    $core.String? p2pId,
  }) {
    final _result = create();
    if (targetBytes != null) {
      _result.targetBytes = targetBytes;
    }
    if (targetWidth != null) {
      _result.targetWidth = targetWidth;
    }
    if (targetHeight != null) {
      _result.targetHeight = targetHeight;
    }
    if (perspectiveBytes != null) {
      _result.perspectiveBytes = perspectiveBytes;
    }
    if (perspectiveWidth != null) {
      _result.perspectiveWidth = perspectiveWidth;
    }
    if (perspectiveHeight != null) {
      _result.perspectiveHeight = perspectiveHeight;
    }
    if (annotatedTargetBytes != null) {
      _result.annotatedTargetBytes = annotatedTargetBytes;
    }
    if (annotatedTargetWidth != null) {
      _result.annotatedTargetWidth = annotatedTargetWidth;
    }
    if (annotatedTargetHeight != null) {
      _result.annotatedTargetHeight = annotatedTargetHeight;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (score != null) {
      _result.score = score;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (isoFormattedTime != null) {
      _result.isoFormattedTime = isoFormattedTime;
    }
    if (llaLat != null) {
      _result.llaLat = llaLat;
    }
    if (llaLng != null) {
      _result.llaLng = llaLng;
    }
    if (llaAlt != null) {
      _result.llaAlt = llaAlt;
    }
    if (llaTimestampMs != null) {
      _result.llaTimestampMs = llaTimestampMs;
    }
    if (ecefX != null) {
      _result.ecefX = ecefX;
    }
    if (ecefY != null) {
      _result.ecefY = ecefY;
    }
    if (ecefZ != null) {
      _result.ecefZ = ecefZ;
    }
    if (ecefTimestampMs != null) {
      _result.ecefTimestampMs = ecefTimestampMs;
    }
    if (ppi != null) {
      _result.ppi = ppi;
    }
    if (perspectivePpi != null) {
      _result.perspectivePpi = perspectivePpi;
    }
    if (imageType != null) {
      _result.imageType = imageType;
    }
    if (modelUrl != null) {
      _result.modelUrl = modelUrl;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (focusMetric != null) {
      _result.focusMetric = focusMetric;
    }
    if (exposureUs != null) {
      _result.exposureUs = exposureUs;
    }
    if (weedingEnabled != null) {
      _result.weedingEnabled = weedingEnabled;
    }
    if (thinningEnabled != null) {
      _result.thinningEnabled = thinningEnabled;
    }
    if (deepweedId != null) {
      _result.deepweedId = deepweedId;
    }
    if (p2pId != null) {
      _result.p2pId = p2pId;
    }
    return _result;
  }
  factory P2PImageAndMetadataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PImageAndMetadataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PImageAndMetadataResponse clone() => P2PImageAndMetadataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PImageAndMetadataResponse copyWith(void Function(P2PImageAndMetadataResponse) updates) => super.copyWith((message) => updates(message as P2PImageAndMetadataResponse)) as P2PImageAndMetadataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PImageAndMetadataResponse create() => P2PImageAndMetadataResponse._();
  P2PImageAndMetadataResponse createEmptyInstance() => create();
  static $pb.PbList<P2PImageAndMetadataResponse> createRepeated() => $pb.PbList<P2PImageAndMetadataResponse>();
  @$core.pragma('dart2js:noInline')
  static P2PImageAndMetadataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PImageAndMetadataResponse>(create);
  static P2PImageAndMetadataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get targetBytes => $_getN(0);
  @$pb.TagNumber(1)
  set targetBytes($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetBytes() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetBytes() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get targetWidth => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetWidth($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetWidth() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetWidth() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get targetHeight => $_getIZ(2);
  @$pb.TagNumber(3)
  set targetHeight($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTargetHeight() => $_has(2);
  @$pb.TagNumber(3)
  void clearTargetHeight() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<$core.int> get perspectiveBytes => $_getN(3);
  @$pb.TagNumber(4)
  set perspectiveBytes($core.List<$core.int> v) { $_setBytes(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPerspectiveBytes() => $_has(3);
  @$pb.TagNumber(4)
  void clearPerspectiveBytes() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get perspectiveWidth => $_getIZ(4);
  @$pb.TagNumber(5)
  set perspectiveWidth($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPerspectiveWidth() => $_has(4);
  @$pb.TagNumber(5)
  void clearPerspectiveWidth() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get perspectiveHeight => $_getIZ(5);
  @$pb.TagNumber(6)
  set perspectiveHeight($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPerspectiveHeight() => $_has(5);
  @$pb.TagNumber(6)
  void clearPerspectiveHeight() => clearField(6);

  @$pb.TagNumber(7)
  $core.List<$core.int> get annotatedTargetBytes => $_getN(6);
  @$pb.TagNumber(7)
  set annotatedTargetBytes($core.List<$core.int> v) { $_setBytes(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasAnnotatedTargetBytes() => $_has(6);
  @$pb.TagNumber(7)
  void clearAnnotatedTargetBytes() => clearField(7);

  @$pb.TagNumber(8)
  $core.int get annotatedTargetWidth => $_getIZ(7);
  @$pb.TagNumber(8)
  set annotatedTargetWidth($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasAnnotatedTargetWidth() => $_has(7);
  @$pb.TagNumber(8)
  void clearAnnotatedTargetWidth() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get annotatedTargetHeight => $_getIZ(8);
  @$pb.TagNumber(9)
  set annotatedTargetHeight($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasAnnotatedTargetHeight() => $_has(8);
  @$pb.TagNumber(9)
  void clearAnnotatedTargetHeight() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get timestampMs => $_getI64(9);
  @$pb.TagNumber(10)
  set timestampMs($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasTimestampMs() => $_has(9);
  @$pb.TagNumber(10)
  void clearTimestampMs() => clearField(10);

  @$pb.TagNumber(11)
  $core.double get score => $_getN(10);
  @$pb.TagNumber(11)
  set score($core.double v) { $_setDouble(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasScore() => $_has(10);
  @$pb.TagNumber(11)
  void clearScore() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get camId => $_getSZ(11);
  @$pb.TagNumber(12)
  set camId($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasCamId() => $_has(11);
  @$pb.TagNumber(12)
  void clearCamId() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get isoFormattedTime => $_getSZ(12);
  @$pb.TagNumber(13)
  set isoFormattedTime($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasIsoFormattedTime() => $_has(12);
  @$pb.TagNumber(13)
  void clearIsoFormattedTime() => clearField(13);

  @$pb.TagNumber(14)
  $core.double get llaLat => $_getN(13);
  @$pb.TagNumber(14)
  set llaLat($core.double v) { $_setDouble(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasLlaLat() => $_has(13);
  @$pb.TagNumber(14)
  void clearLlaLat() => clearField(14);

  @$pb.TagNumber(15)
  $core.double get llaLng => $_getN(14);
  @$pb.TagNumber(15)
  set llaLng($core.double v) { $_setDouble(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasLlaLng() => $_has(14);
  @$pb.TagNumber(15)
  void clearLlaLng() => clearField(15);

  @$pb.TagNumber(16)
  $core.double get llaAlt => $_getN(15);
  @$pb.TagNumber(16)
  set llaAlt($core.double v) { $_setDouble(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasLlaAlt() => $_has(15);
  @$pb.TagNumber(16)
  void clearLlaAlt() => clearField(16);

  @$pb.TagNumber(17)
  $fixnum.Int64 get llaTimestampMs => $_getI64(16);
  @$pb.TagNumber(17)
  set llaTimestampMs($fixnum.Int64 v) { $_setInt64(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasLlaTimestampMs() => $_has(16);
  @$pb.TagNumber(17)
  void clearLlaTimestampMs() => clearField(17);

  @$pb.TagNumber(18)
  $core.double get ecefX => $_getN(17);
  @$pb.TagNumber(18)
  set ecefX($core.double v) { $_setDouble(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasEcefX() => $_has(17);
  @$pb.TagNumber(18)
  void clearEcefX() => clearField(18);

  @$pb.TagNumber(19)
  $core.double get ecefY => $_getN(18);
  @$pb.TagNumber(19)
  set ecefY($core.double v) { $_setDouble(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasEcefY() => $_has(18);
  @$pb.TagNumber(19)
  void clearEcefY() => clearField(19);

  @$pb.TagNumber(20)
  $core.double get ecefZ => $_getN(19);
  @$pb.TagNumber(20)
  set ecefZ($core.double v) { $_setDouble(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasEcefZ() => $_has(19);
  @$pb.TagNumber(20)
  void clearEcefZ() => clearField(20);

  @$pb.TagNumber(21)
  $fixnum.Int64 get ecefTimestampMs => $_getI64(20);
  @$pb.TagNumber(21)
  set ecefTimestampMs($fixnum.Int64 v) { $_setInt64(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasEcefTimestampMs() => $_has(20);
  @$pb.TagNumber(21)
  void clearEcefTimestampMs() => clearField(21);

  @$pb.TagNumber(22)
  $core.double get ppi => $_getN(21);
  @$pb.TagNumber(22)
  set ppi($core.double v) { $_setFloat(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasPpi() => $_has(21);
  @$pb.TagNumber(22)
  void clearPpi() => clearField(22);

  @$pb.TagNumber(23)
  $core.double get perspectivePpi => $_getN(22);
  @$pb.TagNumber(23)
  set perspectivePpi($core.double v) { $_setFloat(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasPerspectivePpi() => $_has(22);
  @$pb.TagNumber(23)
  void clearPerspectivePpi() => clearField(23);

  @$pb.TagNumber(25)
  $core.String get imageType => $_getSZ(23);
  @$pb.TagNumber(25)
  set imageType($core.String v) { $_setString(23, v); }
  @$pb.TagNumber(25)
  $core.bool hasImageType() => $_has(23);
  @$pb.TagNumber(25)
  void clearImageType() => clearField(25);

  @$pb.TagNumber(26)
  $core.String get modelUrl => $_getSZ(24);
  @$pb.TagNumber(26)
  set modelUrl($core.String v) { $_setString(24, v); }
  @$pb.TagNumber(26)
  $core.bool hasModelUrl() => $_has(24);
  @$pb.TagNumber(26)
  void clearModelUrl() => clearField(26);

  @$pb.TagNumber(27)
  $core.String get crop => $_getSZ(25);
  @$pb.TagNumber(27)
  set crop($core.String v) { $_setString(25, v); }
  @$pb.TagNumber(27)
  $core.bool hasCrop() => $_has(25);
  @$pb.TagNumber(27)
  void clearCrop() => clearField(27);

  @$pb.TagNumber(28)
  $core.double get focusMetric => $_getN(26);
  @$pb.TagNumber(28)
  set focusMetric($core.double v) { $_setDouble(26, v); }
  @$pb.TagNumber(28)
  $core.bool hasFocusMetric() => $_has(26);
  @$pb.TagNumber(28)
  void clearFocusMetric() => clearField(28);

  @$pb.TagNumber(29)
  $core.double get exposureUs => $_getN(27);
  @$pb.TagNumber(29)
  set exposureUs($core.double v) { $_setDouble(27, v); }
  @$pb.TagNumber(29)
  $core.bool hasExposureUs() => $_has(27);
  @$pb.TagNumber(29)
  void clearExposureUs() => clearField(29);

  @$pb.TagNumber(30)
  $core.bool get weedingEnabled => $_getBF(28);
  @$pb.TagNumber(30)
  set weedingEnabled($core.bool v) { $_setBool(28, v); }
  @$pb.TagNumber(30)
  $core.bool hasWeedingEnabled() => $_has(28);
  @$pb.TagNumber(30)
  void clearWeedingEnabled() => clearField(30);

  @$pb.TagNumber(31)
  $core.bool get thinningEnabled => $_getBF(29);
  @$pb.TagNumber(31)
  set thinningEnabled($core.bool v) { $_setBool(29, v); }
  @$pb.TagNumber(31)
  $core.bool hasThinningEnabled() => $_has(29);
  @$pb.TagNumber(31)
  void clearThinningEnabled() => clearField(31);

  @$pb.TagNumber(32)
  $core.String get deepweedId => $_getSZ(30);
  @$pb.TagNumber(32)
  set deepweedId($core.String v) { $_setString(30, v); }
  @$pb.TagNumber(32)
  $core.bool hasDeepweedId() => $_has(30);
  @$pb.TagNumber(32)
  void clearDeepweedId() => clearField(32);

  @$pb.TagNumber(33)
  $core.String get p2pId => $_getSZ(31);
  @$pb.TagNumber(33)
  set p2pId($core.String v) { $_setString(31, v); }
  @$pb.TagNumber(33)
  $core.bool hasP2pId() => $_has(31);
  @$pb.TagNumber(33)
  void clearP2pId() => clearField(33);
}

class GetCameraInfoRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraInfoRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetCameraInfoRequest._() : super();
  factory GetCameraInfoRequest() => create();
  factory GetCameraInfoRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraInfoRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraInfoRequest clone() => GetCameraInfoRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraInfoRequest copyWith(void Function(GetCameraInfoRequest) updates) => super.copyWith((message) => updates(message as GetCameraInfoRequest)) as GetCameraInfoRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraInfoRequest create() => GetCameraInfoRequest._();
  GetCameraInfoRequest createEmptyInstance() => create();
  static $pb.PbList<GetCameraInfoRequest> createRepeated() => $pb.PbList<GetCameraInfoRequest>();
  @$core.pragma('dart2js:noInline')
  static GetCameraInfoRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraInfoRequest>(create);
  static GetCameraInfoRequest? _defaultInstance;
}

class CameraInfo extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CameraInfo', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ipAddress')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serialNumber')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'model')
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width', $pb.PbFieldType.OU3)
    ..a<$core.int>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'height', $pb.PbFieldType.OU3)
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'connected')
    ..a<$fixnum.Int64>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'linkSpeed', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  CameraInfo._() : super();
  factory CameraInfo({
    $core.String? camId,
    $core.String? ipAddress,
    $core.String? serialNumber,
    $core.String? model,
    $core.int? width,
    $core.int? height,
    $core.bool? connected,
    $fixnum.Int64? linkSpeed,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (ipAddress != null) {
      _result.ipAddress = ipAddress;
    }
    if (serialNumber != null) {
      _result.serialNumber = serialNumber;
    }
    if (model != null) {
      _result.model = model;
    }
    if (width != null) {
      _result.width = width;
    }
    if (height != null) {
      _result.height = height;
    }
    if (connected != null) {
      _result.connected = connected;
    }
    if (linkSpeed != null) {
      _result.linkSpeed = linkSpeed;
    }
    return _result;
  }
  factory CameraInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CameraInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CameraInfo clone() => CameraInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CameraInfo copyWith(void Function(CameraInfo) updates) => super.copyWith((message) => updates(message as CameraInfo)) as CameraInfo; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CameraInfo create() => CameraInfo._();
  CameraInfo createEmptyInstance() => create();
  static $pb.PbList<CameraInfo> createRepeated() => $pb.PbList<CameraInfo>();
  @$core.pragma('dart2js:noInline')
  static CameraInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CameraInfo>(create);
  static CameraInfo? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get ipAddress => $_getSZ(1);
  @$pb.TagNumber(2)
  set ipAddress($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIpAddress() => $_has(1);
  @$pb.TagNumber(2)
  void clearIpAddress() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get serialNumber => $_getSZ(2);
  @$pb.TagNumber(3)
  set serialNumber($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSerialNumber() => $_has(2);
  @$pb.TagNumber(3)
  void clearSerialNumber() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get model => $_getSZ(3);
  @$pb.TagNumber(4)
  set model($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasModel() => $_has(3);
  @$pb.TagNumber(4)
  void clearModel() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get width => $_getIZ(4);
  @$pb.TagNumber(5)
  set width($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasWidth() => $_has(4);
  @$pb.TagNumber(5)
  void clearWidth() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get height => $_getIZ(5);
  @$pb.TagNumber(6)
  set height($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasHeight() => $_has(5);
  @$pb.TagNumber(6)
  void clearHeight() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get connected => $_getBF(6);
  @$pb.TagNumber(7)
  set connected($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasConnected() => $_has(6);
  @$pb.TagNumber(7)
  void clearConnected() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get linkSpeed => $_getI64(7);
  @$pb.TagNumber(8)
  set linkSpeed($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLinkSpeed() => $_has(7);
  @$pb.TagNumber(8)
  void clearLinkSpeed() => clearField(8);
}

class GetCameraInfoResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetCameraInfoResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<CameraInfo>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cameraInfo', $pb.PbFieldType.PM, subBuilder: CameraInfo.create)
    ..hasRequiredFields = false
  ;

  GetCameraInfoResponse._() : super();
  factory GetCameraInfoResponse({
    $core.Iterable<CameraInfo>? cameraInfo,
  }) {
    final _result = create();
    if (cameraInfo != null) {
      _result.cameraInfo.addAll(cameraInfo);
    }
    return _result;
  }
  factory GetCameraInfoResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetCameraInfoResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetCameraInfoResponse clone() => GetCameraInfoResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetCameraInfoResponse copyWith(void Function(GetCameraInfoResponse) updates) => super.copyWith((message) => updates(message as GetCameraInfoResponse)) as GetCameraInfoResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetCameraInfoResponse create() => GetCameraInfoResponse._();
  GetCameraInfoResponse createEmptyInstance() => create();
  static $pb.PbList<GetCameraInfoResponse> createRepeated() => $pb.PbList<GetCameraInfoResponse>();
  @$core.pragma('dart2js:noInline')
  static GetCameraInfoResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetCameraInfoResponse>(create);
  static GetCameraInfoResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<CameraInfo> get cameraInfo => $_getList(0);
}

class GetLightweightBurstRecordRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetLightweightBurstRecordRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetLightweightBurstRecordRequest._() : super();
  factory GetLightweightBurstRecordRequest() => create();
  factory GetLightweightBurstRecordRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetLightweightBurstRecordRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetLightweightBurstRecordRequest clone() => GetLightweightBurstRecordRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetLightweightBurstRecordRequest copyWith(void Function(GetLightweightBurstRecordRequest) updates) => super.copyWith((message) => updates(message as GetLightweightBurstRecordRequest)) as GetLightweightBurstRecordRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetLightweightBurstRecordRequest create() => GetLightweightBurstRecordRequest._();
  GetLightweightBurstRecordRequest createEmptyInstance() => create();
  static $pb.PbList<GetLightweightBurstRecordRequest> createRepeated() => $pb.PbList<GetLightweightBurstRecordRequest>();
  @$core.pragma('dart2js:noInline')
  static GetLightweightBurstRecordRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetLightweightBurstRecordRequest>(create);
  static GetLightweightBurstRecordRequest? _defaultInstance;
}

class GetLightweightBurstRecordResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetLightweightBurstRecordResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'zipFile', $pb.PbFieldType.OY)
    ..a<$core.List<$core.int>>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadataFile', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  GetLightweightBurstRecordResponse._() : super();
  factory GetLightweightBurstRecordResponse({
    $core.List<$core.int>? zipFile,
    $core.List<$core.int>? metadataFile,
  }) {
    final _result = create();
    if (zipFile != null) {
      _result.zipFile = zipFile;
    }
    if (metadataFile != null) {
      _result.metadataFile = metadataFile;
    }
    return _result;
  }
  factory GetLightweightBurstRecordResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetLightweightBurstRecordResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetLightweightBurstRecordResponse clone() => GetLightweightBurstRecordResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetLightweightBurstRecordResponse copyWith(void Function(GetLightweightBurstRecordResponse) updates) => super.copyWith((message) => updates(message as GetLightweightBurstRecordResponse)) as GetLightweightBurstRecordResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetLightweightBurstRecordResponse create() => GetLightweightBurstRecordResponse._();
  GetLightweightBurstRecordResponse createEmptyInstance() => create();
  static $pb.PbList<GetLightweightBurstRecordResponse> createRepeated() => $pb.PbList<GetLightweightBurstRecordResponse>();
  @$core.pragma('dart2js:noInline')
  static GetLightweightBurstRecordResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetLightweightBurstRecordResponse>(create);
  static GetLightweightBurstRecordResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get zipFile => $_getN(0);
  @$pb.TagNumber(1)
  set zipFile($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasZipFile() => $_has(0);
  @$pb.TagNumber(1)
  void clearZipFile() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get metadataFile => $_getN(1);
  @$pb.TagNumber(2)
  set metadataFile($core.List<$core.int> v) { $_setBytes(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadataFile() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadataFile() => clearField(2);
}

class GetBootedRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetBootedRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetBootedRequest._() : super();
  factory GetBootedRequest() => create();
  factory GetBootedRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetBootedRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetBootedRequest clone() => GetBootedRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetBootedRequest copyWith(void Function(GetBootedRequest) updates) => super.copyWith((message) => updates(message as GetBootedRequest)) as GetBootedRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetBootedRequest create() => GetBootedRequest._();
  GetBootedRequest createEmptyInstance() => create();
  static $pb.PbList<GetBootedRequest> createRepeated() => $pb.PbList<GetBootedRequest>();
  @$core.pragma('dart2js:noInline')
  static GetBootedRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetBootedRequest>(create);
  static GetBootedRequest? _defaultInstance;
}

class GetBootedResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetBootedResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'booted')
    ..hasRequiredFields = false
  ;

  GetBootedResponse._() : super();
  factory GetBootedResponse({
    $core.bool? booted,
  }) {
    final _result = create();
    if (booted != null) {
      _result.booted = booted;
    }
    return _result;
  }
  factory GetBootedResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetBootedResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetBootedResponse clone() => GetBootedResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetBootedResponse copyWith(void Function(GetBootedResponse) updates) => super.copyWith((message) => updates(message as GetBootedResponse)) as GetBootedResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetBootedResponse create() => GetBootedResponse._();
  GetBootedResponse createEmptyInstance() => create();
  static $pb.PbList<GetBootedResponse> createRepeated() => $pb.PbList<GetBootedResponse>();
  @$core.pragma('dart2js:noInline')
  static GetBootedResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetBootedResponse>(create);
  static GetBootedResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get booted => $_getBF(0);
  @$pb.TagNumber(1)
  set booted($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBooted() => $_has(0);
  @$pb.TagNumber(1)
  void clearBooted() => clearField(1);
}

class GetReadyRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetReadyRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetReadyRequest._() : super();
  factory GetReadyRequest() => create();
  factory GetReadyRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetReadyRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetReadyRequest clone() => GetReadyRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetReadyRequest copyWith(void Function(GetReadyRequest) updates) => super.copyWith((message) => updates(message as GetReadyRequest)) as GetReadyRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetReadyRequest create() => GetReadyRequest._();
  GetReadyRequest createEmptyInstance() => create();
  static $pb.PbList<GetReadyRequest> createRepeated() => $pb.PbList<GetReadyRequest>();
  @$core.pragma('dart2js:noInline')
  static GetReadyRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetReadyRequest>(create);
  static GetReadyRequest? _defaultInstance;
}

class GetReadyResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetReadyResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ready')
    ..hasRequiredFields = false
  ;

  GetReadyResponse._() : super();
  factory GetReadyResponse({
    $core.bool? ready,
  }) {
    final _result = create();
    if (ready != null) {
      _result.ready = ready;
    }
    return _result;
  }
  factory GetReadyResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetReadyResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetReadyResponse clone() => GetReadyResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetReadyResponse copyWith(void Function(GetReadyResponse) updates) => super.copyWith((message) => updates(message as GetReadyResponse)) as GetReadyResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetReadyResponse create() => GetReadyResponse._();
  GetReadyResponse createEmptyInstance() => create();
  static $pb.PbList<GetReadyResponse> createRepeated() => $pb.PbList<GetReadyResponse>();
  @$core.pragma('dart2js:noInline')
  static GetReadyResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetReadyResponse>(create);
  static GetReadyResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get ready => $_getBF(0);
  @$pb.TagNumber(1)
  set ready($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasReady() => $_has(0);
  @$pb.TagNumber(1)
  void clearReady() => clearField(1);
}

class DeepweedDetection extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeepweedDetection', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OF)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OF)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'size', $pb.PbFieldType.OF)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'score', $pb.PbFieldType.OF)
    ..m<$core.String, $core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detectionClassScores', entryClassName: 'DeepweedDetection.DetectionClassScoresEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OF, packageName: const $pb.PackageName('cv.runtime.proto'))
    ..e<HitClass>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hitClass', $pb.PbFieldType.OE, defaultOrMaker: HitClass.WEED, valueOf: HitClass.valueOf, enumValues: HitClass.values)
    ..pPS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maskIntersections')
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedScore', $pb.PbFieldType.OF)
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropScore', $pb.PbFieldType.OF)
    ..hasRequiredFields = false
  ;

  DeepweedDetection._() : super();
  factory DeepweedDetection({
    $core.double? x,
    $core.double? y,
    $core.double? size,
    $core.double? score,
    $core.Map<$core.String, $core.double>? detectionClassScores,
    HitClass? hitClass,
    $core.Iterable<$core.String>? maskIntersections,
    $core.double? weedScore,
    $core.double? cropScore,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    if (size != null) {
      _result.size = size;
    }
    if (score != null) {
      _result.score = score;
    }
    if (detectionClassScores != null) {
      _result.detectionClassScores.addAll(detectionClassScores);
    }
    if (hitClass != null) {
      _result.hitClass = hitClass;
    }
    if (maskIntersections != null) {
      _result.maskIntersections.addAll(maskIntersections);
    }
    if (weedScore != null) {
      _result.weedScore = weedScore;
    }
    if (cropScore != null) {
      _result.cropScore = cropScore;
    }
    return _result;
  }
  factory DeepweedDetection.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeepweedDetection.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeepweedDetection clone() => DeepweedDetection()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeepweedDetection copyWith(void Function(DeepweedDetection) updates) => super.copyWith((message) => updates(message as DeepweedDetection)) as DeepweedDetection; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeepweedDetection create() => DeepweedDetection._();
  DeepweedDetection createEmptyInstance() => create();
  static $pb.PbList<DeepweedDetection> createRepeated() => $pb.PbList<DeepweedDetection>();
  @$core.pragma('dart2js:noInline')
  static DeepweedDetection getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeepweedDetection>(create);
  static DeepweedDetection? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get x => $_getN(0);
  @$pb.TagNumber(1)
  set x($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get y => $_getN(1);
  @$pb.TagNumber(2)
  set y($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasY() => $_has(1);
  @$pb.TagNumber(2)
  void clearY() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get size => $_getN(2);
  @$pb.TagNumber(3)
  set size($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSize() => $_has(2);
  @$pb.TagNumber(3)
  void clearSize() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get score => $_getN(3);
  @$pb.TagNumber(4)
  set score($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasScore() => $_has(3);
  @$pb.TagNumber(4)
  void clearScore() => clearField(4);

  @$pb.TagNumber(5)
  $core.Map<$core.String, $core.double> get detectionClassScores => $_getMap(4);

  @$pb.TagNumber(6)
  HitClass get hitClass => $_getN(5);
  @$pb.TagNumber(6)
  set hitClass(HitClass v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasHitClass() => $_has(5);
  @$pb.TagNumber(6)
  void clearHitClass() => clearField(6);

  @$pb.TagNumber(7)
  $core.List<$core.String> get maskIntersections => $_getList(6);

  @$pb.TagNumber(8)
  $core.double get weedScore => $_getN(7);
  @$pb.TagNumber(8)
  set weedScore($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasWeedScore() => $_has(7);
  @$pb.TagNumber(8)
  void clearWeedScore() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get cropScore => $_getN(8);
  @$pb.TagNumber(9)
  set cropScore($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasCropScore() => $_has(8);
  @$pb.TagNumber(9)
  void clearCropScore() => clearField(9);
}

class DeepweedOutput extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeepweedOutput', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..pc<DeepweedDetection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detections', $pb.PbFieldType.PM, subBuilder: DeepweedDetection.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maskWidth', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maskHeight', $pb.PbFieldType.OU3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maskChannels', $pb.PbFieldType.OU3)
    ..p<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'mask', $pb.PbFieldType.K3)
    ..pPS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maskChannelClasses')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'predictInDistanceBuffer')
    ..aInt64(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  DeepweedOutput._() : super();
  factory DeepweedOutput({
    $core.Iterable<DeepweedDetection>? detections,
    $core.int? maskWidth,
    $core.int? maskHeight,
    $core.int? maskChannels,
    $core.Iterable<$core.int>? mask,
    $core.Iterable<$core.String>? maskChannelClasses,
    $core.bool? predictInDistanceBuffer,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (detections != null) {
      _result.detections.addAll(detections);
    }
    if (maskWidth != null) {
      _result.maskWidth = maskWidth;
    }
    if (maskHeight != null) {
      _result.maskHeight = maskHeight;
    }
    if (maskChannels != null) {
      _result.maskChannels = maskChannels;
    }
    if (mask != null) {
      _result.mask.addAll(mask);
    }
    if (maskChannelClasses != null) {
      _result.maskChannelClasses.addAll(maskChannelClasses);
    }
    if (predictInDistanceBuffer != null) {
      _result.predictInDistanceBuffer = predictInDistanceBuffer;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory DeepweedOutput.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeepweedOutput.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeepweedOutput clone() => DeepweedOutput()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeepweedOutput copyWith(void Function(DeepweedOutput) updates) => super.copyWith((message) => updates(message as DeepweedOutput)) as DeepweedOutput; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeepweedOutput create() => DeepweedOutput._();
  DeepweedOutput createEmptyInstance() => create();
  static $pb.PbList<DeepweedOutput> createRepeated() => $pb.PbList<DeepweedOutput>();
  @$core.pragma('dart2js:noInline')
  static DeepweedOutput getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeepweedOutput>(create);
  static DeepweedOutput? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<DeepweedDetection> get detections => $_getList(0);

  @$pb.TagNumber(2)
  $core.int get maskWidth => $_getIZ(1);
  @$pb.TagNumber(2)
  set maskWidth($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMaskWidth() => $_has(1);
  @$pb.TagNumber(2)
  void clearMaskWidth() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get maskHeight => $_getIZ(2);
  @$pb.TagNumber(3)
  set maskHeight($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMaskHeight() => $_has(2);
  @$pb.TagNumber(3)
  void clearMaskHeight() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get maskChannels => $_getIZ(3);
  @$pb.TagNumber(4)
  set maskChannels($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasMaskChannels() => $_has(3);
  @$pb.TagNumber(4)
  void clearMaskChannels() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<$core.int> get mask => $_getList(4);

  @$pb.TagNumber(6)
  $core.List<$core.String> get maskChannelClasses => $_getList(5);

  @$pb.TagNumber(7)
  $core.bool get predictInDistanceBuffer => $_getBF(6);
  @$pb.TagNumber(7)
  set predictInDistanceBuffer($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasPredictInDistanceBuffer() => $_has(6);
  @$pb.TagNumber(7)
  void clearPredictInDistanceBuffer() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get timestampMs => $_getI64(7);
  @$pb.TagNumber(8)
  set timestampMs($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasTimestampMs() => $_has(7);
  @$pb.TagNumber(8)
  void clearTimestampMs() => clearField(8);
}

class GetDeepweedOutputByTimestampRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetDeepweedOutputByTimestampRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..hasRequiredFields = false
  ;

  GetDeepweedOutputByTimestampRequest._() : super();
  factory GetDeepweedOutputByTimestampRequest({
    $core.String? camId,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory GetDeepweedOutputByTimestampRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetDeepweedOutputByTimestampRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetDeepweedOutputByTimestampRequest clone() => GetDeepweedOutputByTimestampRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetDeepweedOutputByTimestampRequest copyWith(void Function(GetDeepweedOutputByTimestampRequest) updates) => super.copyWith((message) => updates(message as GetDeepweedOutputByTimestampRequest)) as GetDeepweedOutputByTimestampRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetDeepweedOutputByTimestampRequest create() => GetDeepweedOutputByTimestampRequest._();
  GetDeepweedOutputByTimestampRequest createEmptyInstance() => create();
  static $pb.PbList<GetDeepweedOutputByTimestampRequest> createRepeated() => $pb.PbList<GetDeepweedOutputByTimestampRequest>();
  @$core.pragma('dart2js:noInline')
  static GetDeepweedOutputByTimestampRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetDeepweedOutputByTimestampRequest>(create);
  static GetDeepweedOutputByTimestampRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestampMs => $_getI64(1);
  @$pb.TagNumber(2)
  set timestampMs($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestampMs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestampMs() => clearField(2);
}

class GetRecommendedStrobeSettingsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRecommendedStrobeSettingsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  GetRecommendedStrobeSettingsRequest._() : super();
  factory GetRecommendedStrobeSettingsRequest() => create();
  factory GetRecommendedStrobeSettingsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRecommendedStrobeSettingsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRecommendedStrobeSettingsRequest clone() => GetRecommendedStrobeSettingsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRecommendedStrobeSettingsRequest copyWith(void Function(GetRecommendedStrobeSettingsRequest) updates) => super.copyWith((message) => updates(message as GetRecommendedStrobeSettingsRequest)) as GetRecommendedStrobeSettingsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRecommendedStrobeSettingsRequest create() => GetRecommendedStrobeSettingsRequest._();
  GetRecommendedStrobeSettingsRequest createEmptyInstance() => create();
  static $pb.PbList<GetRecommendedStrobeSettingsRequest> createRepeated() => $pb.PbList<GetRecommendedStrobeSettingsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetRecommendedStrobeSettingsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRecommendedStrobeSettingsRequest>(create);
  static GetRecommendedStrobeSettingsRequest? _defaultInstance;
}

class GetRecommendedStrobeSettingsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetRecommendedStrobeSettingsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetCameraFps', $pb.PbFieldType.OF)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetsPerPredictRatio', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  GetRecommendedStrobeSettingsResponse._() : super();
  factory GetRecommendedStrobeSettingsResponse({
    $core.double? targetCameraFps,
    $core.int? targetsPerPredictRatio,
  }) {
    final _result = create();
    if (targetCameraFps != null) {
      _result.targetCameraFps = targetCameraFps;
    }
    if (targetsPerPredictRatio != null) {
      _result.targetsPerPredictRatio = targetsPerPredictRatio;
    }
    return _result;
  }
  factory GetRecommendedStrobeSettingsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetRecommendedStrobeSettingsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetRecommendedStrobeSettingsResponse clone() => GetRecommendedStrobeSettingsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetRecommendedStrobeSettingsResponse copyWith(void Function(GetRecommendedStrobeSettingsResponse) updates) => super.copyWith((message) => updates(message as GetRecommendedStrobeSettingsResponse)) as GetRecommendedStrobeSettingsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetRecommendedStrobeSettingsResponse create() => GetRecommendedStrobeSettingsResponse._();
  GetRecommendedStrobeSettingsResponse createEmptyInstance() => create();
  static $pb.PbList<GetRecommendedStrobeSettingsResponse> createRepeated() => $pb.PbList<GetRecommendedStrobeSettingsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetRecommendedStrobeSettingsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetRecommendedStrobeSettingsResponse>(create);
  static GetRecommendedStrobeSettingsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get targetCameraFps => $_getN(0);
  @$pb.TagNumber(1)
  set targetCameraFps($core.double v) { $_setFloat(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetCameraFps() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetCameraFps() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get targetsPerPredictRatio => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetsPerPredictRatio($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetsPerPredictRatio() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetsPerPredictRatio() => clearField(2);
}

class StartP2PBufferringRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartP2PBufferringRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..hasRequiredFields = false
  ;

  StartP2PBufferringRequest._() : super();
  factory StartP2PBufferringRequest({
    $core.String? camId,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    return _result;
  }
  factory StartP2PBufferringRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartP2PBufferringRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartP2PBufferringRequest clone() => StartP2PBufferringRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartP2PBufferringRequest copyWith(void Function(StartP2PBufferringRequest) updates) => super.copyWith((message) => updates(message as StartP2PBufferringRequest)) as StartP2PBufferringRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartP2PBufferringRequest create() => StartP2PBufferringRequest._();
  StartP2PBufferringRequest createEmptyInstance() => create();
  static $pb.PbList<StartP2PBufferringRequest> createRepeated() => $pb.PbList<StartP2PBufferringRequest>();
  @$core.pragma('dart2js:noInline')
  static StartP2PBufferringRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartP2PBufferringRequest>(create);
  static StartP2PBufferringRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);
}

class StartP2PBufferringResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartP2PBufferringResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StartP2PBufferringResponse._() : super();
  factory StartP2PBufferringResponse() => create();
  factory StartP2PBufferringResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartP2PBufferringResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartP2PBufferringResponse clone() => StartP2PBufferringResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartP2PBufferringResponse copyWith(void Function(StartP2PBufferringResponse) updates) => super.copyWith((message) => updates(message as StartP2PBufferringResponse)) as StartP2PBufferringResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartP2PBufferringResponse create() => StartP2PBufferringResponse._();
  StartP2PBufferringResponse createEmptyInstance() => create();
  static $pb.PbList<StartP2PBufferringResponse> createRepeated() => $pb.PbList<StartP2PBufferringResponse>();
  @$core.pragma('dart2js:noInline')
  static StartP2PBufferringResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartP2PBufferringResponse>(create);
  static StartP2PBufferringResponse? _defaultInstance;
}

class StopP2PBufferringRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopP2PBufferringRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'saveBurst')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'path')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dontCapturePredictImage')
    ..aInt64(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startTimestampMs')
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endTimestampMs')
    ..hasRequiredFields = false
  ;

  StopP2PBufferringRequest._() : super();
  factory StopP2PBufferringRequest({
    $core.bool? saveBurst,
    $core.String? camId,
    $core.String? path,
    $core.bool? dontCapturePredictImage,
    $fixnum.Int64? startTimestampMs,
    $fixnum.Int64? endTimestampMs,
  }) {
    final _result = create();
    if (saveBurst != null) {
      _result.saveBurst = saveBurst;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (path != null) {
      _result.path = path;
    }
    if (dontCapturePredictImage != null) {
      _result.dontCapturePredictImage = dontCapturePredictImage;
    }
    if (startTimestampMs != null) {
      _result.startTimestampMs = startTimestampMs;
    }
    if (endTimestampMs != null) {
      _result.endTimestampMs = endTimestampMs;
    }
    return _result;
  }
  factory StopP2PBufferringRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopP2PBufferringRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopP2PBufferringRequest clone() => StopP2PBufferringRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopP2PBufferringRequest copyWith(void Function(StopP2PBufferringRequest) updates) => super.copyWith((message) => updates(message as StopP2PBufferringRequest)) as StopP2PBufferringRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopP2PBufferringRequest create() => StopP2PBufferringRequest._();
  StopP2PBufferringRequest createEmptyInstance() => create();
  static $pb.PbList<StopP2PBufferringRequest> createRepeated() => $pb.PbList<StopP2PBufferringRequest>();
  @$core.pragma('dart2js:noInline')
  static StopP2PBufferringRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopP2PBufferringRequest>(create);
  static StopP2PBufferringRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get saveBurst => $_getBF(0);
  @$pb.TagNumber(1)
  set saveBurst($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSaveBurst() => $_has(0);
  @$pb.TagNumber(1)
  void clearSaveBurst() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get camId => $_getSZ(1);
  @$pb.TagNumber(2)
  set camId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCamId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCamId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get path => $_getSZ(2);
  @$pb.TagNumber(3)
  set path($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPath() => $_has(2);
  @$pb.TagNumber(3)
  void clearPath() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get dontCapturePredictImage => $_getBF(3);
  @$pb.TagNumber(4)
  set dontCapturePredictImage($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDontCapturePredictImage() => $_has(3);
  @$pb.TagNumber(4)
  void clearDontCapturePredictImage() => clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get startTimestampMs => $_getI64(4);
  @$pb.TagNumber(5)
  set startTimestampMs($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasStartTimestampMs() => $_has(4);
  @$pb.TagNumber(5)
  void clearStartTimestampMs() => clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get endTimestampMs => $_getI64(5);
  @$pb.TagNumber(6)
  set endTimestampMs($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasEndTimestampMs() => $_has(5);
  @$pb.TagNumber(6)
  void clearEndTimestampMs() => clearField(6);
}

class StopP2PBufferringResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StopP2PBufferringResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StopP2PBufferringResponse._() : super();
  factory StopP2PBufferringResponse() => create();
  factory StopP2PBufferringResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StopP2PBufferringResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StopP2PBufferringResponse clone() => StopP2PBufferringResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StopP2PBufferringResponse copyWith(void Function(StopP2PBufferringResponse) updates) => super.copyWith((message) => updates(message as StopP2PBufferringResponse)) as StopP2PBufferringResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StopP2PBufferringResponse create() => StopP2PBufferringResponse._();
  StopP2PBufferringResponse createEmptyInstance() => create();
  static $pb.PbList<StopP2PBufferringResponse> createRepeated() => $pb.PbList<StopP2PBufferringResponse>();
  @$core.pragma('dart2js:noInline')
  static StopP2PBufferringResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StopP2PBufferringResponse>(create);
  static StopP2PBufferringResponse? _defaultInstance;
}

class P2PCaptureRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PCaptureRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'writeToDisk')
    ..e<$61.P2PCaptureReason>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reason', $pb.PbFieldType.OE, defaultOrMaker: $61.P2PCaptureReason.P2PCaptureReason_MISS, valueOf: $61.P2PCaptureReason.valueOf, enumValues: $61.P2PCaptureReason.values)
    ..hasRequiredFields = false
  ;

  P2PCaptureRequest._() : super();
  factory P2PCaptureRequest({
    $core.String? camId,
    $core.String? name,
    $fixnum.Int64? timestampMs,
    $core.bool? writeToDisk,
    $61.P2PCaptureReason? reason,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (name != null) {
      _result.name = name;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (writeToDisk != null) {
      _result.writeToDisk = writeToDisk;
    }
    if (reason != null) {
      _result.reason = reason;
    }
    return _result;
  }
  factory P2PCaptureRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PCaptureRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PCaptureRequest clone() => P2PCaptureRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PCaptureRequest copyWith(void Function(P2PCaptureRequest) updates) => super.copyWith((message) => updates(message as P2PCaptureRequest)) as P2PCaptureRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PCaptureRequest create() => P2PCaptureRequest._();
  P2PCaptureRequest createEmptyInstance() => create();
  static $pb.PbList<P2PCaptureRequest> createRepeated() => $pb.PbList<P2PCaptureRequest>();
  @$core.pragma('dart2js:noInline')
  static P2PCaptureRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PCaptureRequest>(create);
  static P2PCaptureRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(1)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCamId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timestampMs => $_getI64(2);
  @$pb.TagNumber(3)
  set timestampMs($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimestampMs() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimestampMs() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get writeToDisk => $_getBF(3);
  @$pb.TagNumber(4)
  set writeToDisk($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasWriteToDisk() => $_has(3);
  @$pb.TagNumber(4)
  void clearWriteToDisk() => clearField(4);

  @$pb.TagNumber(5)
  $61.P2PCaptureReason get reason => $_getN(4);
  @$pb.TagNumber(5)
  set reason($61.P2PCaptureReason v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasReason() => $_has(4);
  @$pb.TagNumber(5)
  void clearReason() => clearField(5);
}

class P2PCaptureResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PCaptureResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  P2PCaptureResponse._() : super();
  factory P2PCaptureResponse() => create();
  factory P2PCaptureResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PCaptureResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PCaptureResponse clone() => P2PCaptureResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PCaptureResponse copyWith(void Function(P2PCaptureResponse) updates) => super.copyWith((message) => updates(message as P2PCaptureResponse)) as P2PCaptureResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PCaptureResponse create() => P2PCaptureResponse._();
  P2PCaptureResponse createEmptyInstance() => create();
  static $pb.PbList<P2PCaptureResponse> createRepeated() => $pb.PbList<P2PCaptureResponse>();
  @$core.pragma('dart2js:noInline')
  static P2PCaptureResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PCaptureResponse>(create);
  static P2PCaptureResponse? _defaultInstance;
}

class P2PBufferringBurstCaptureRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PBufferringBurstCaptureRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'path')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dontCapturePredictImage')
    ..aInt64(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startTimestampMs')
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endTimestampMs')
    ..hasRequiredFields = false
  ;

  P2PBufferringBurstCaptureRequest._() : super();
  factory P2PBufferringBurstCaptureRequest({
    $core.String? camId,
    $core.String? path,
    $core.bool? dontCapturePredictImage,
    $fixnum.Int64? startTimestampMs,
    $fixnum.Int64? endTimestampMs,
  }) {
    final _result = create();
    if (camId != null) {
      _result.camId = camId;
    }
    if (path != null) {
      _result.path = path;
    }
    if (dontCapturePredictImage != null) {
      _result.dontCapturePredictImage = dontCapturePredictImage;
    }
    if (startTimestampMs != null) {
      _result.startTimestampMs = startTimestampMs;
    }
    if (endTimestampMs != null) {
      _result.endTimestampMs = endTimestampMs;
    }
    return _result;
  }
  factory P2PBufferringBurstCaptureRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PBufferringBurstCaptureRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PBufferringBurstCaptureRequest clone() => P2PBufferringBurstCaptureRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PBufferringBurstCaptureRequest copyWith(void Function(P2PBufferringBurstCaptureRequest) updates) => super.copyWith((message) => updates(message as P2PBufferringBurstCaptureRequest)) as P2PBufferringBurstCaptureRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PBufferringBurstCaptureRequest create() => P2PBufferringBurstCaptureRequest._();
  P2PBufferringBurstCaptureRequest createEmptyInstance() => create();
  static $pb.PbList<P2PBufferringBurstCaptureRequest> createRepeated() => $pb.PbList<P2PBufferringBurstCaptureRequest>();
  @$core.pragma('dart2js:noInline')
  static P2PBufferringBurstCaptureRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PBufferringBurstCaptureRequest>(create);
  static P2PBufferringBurstCaptureRequest? _defaultInstance;

  @$pb.TagNumber(2)
  $core.String get camId => $_getSZ(0);
  @$pb.TagNumber(2)
  set camId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(2)
  $core.bool hasCamId() => $_has(0);
  @$pb.TagNumber(2)
  void clearCamId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get path => $_getSZ(1);
  @$pb.TagNumber(3)
  set path($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasPath() => $_has(1);
  @$pb.TagNumber(3)
  void clearPath() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get dontCapturePredictImage => $_getBF(2);
  @$pb.TagNumber(4)
  set dontCapturePredictImage($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasDontCapturePredictImage() => $_has(2);
  @$pb.TagNumber(4)
  void clearDontCapturePredictImage() => clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get startTimestampMs => $_getI64(3);
  @$pb.TagNumber(5)
  set startTimestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasStartTimestampMs() => $_has(3);
  @$pb.TagNumber(5)
  void clearStartTimestampMs() => clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get endTimestampMs => $_getI64(4);
  @$pb.TagNumber(6)
  set endTimestampMs($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasEndTimestampMs() => $_has(4);
  @$pb.TagNumber(6)
  void clearEndTimestampMs() => clearField(6);
}

class P2PBufferringBurstCaptureResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'P2PBufferringBurstCaptureResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  P2PBufferringBurstCaptureResponse._() : super();
  factory P2PBufferringBurstCaptureResponse() => create();
  factory P2PBufferringBurstCaptureResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory P2PBufferringBurstCaptureResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  P2PBufferringBurstCaptureResponse clone() => P2PBufferringBurstCaptureResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  P2PBufferringBurstCaptureResponse copyWith(void Function(P2PBufferringBurstCaptureResponse) updates) => super.copyWith((message) => updates(message as P2PBufferringBurstCaptureResponse)) as P2PBufferringBurstCaptureResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static P2PBufferringBurstCaptureResponse create() => P2PBufferringBurstCaptureResponse._();
  P2PBufferringBurstCaptureResponse createEmptyInstance() => create();
  static $pb.PbList<P2PBufferringBurstCaptureResponse> createRepeated() => $pb.PbList<P2PBufferringBurstCaptureResponse>();
  @$core.pragma('dart2js:noInline')
  static P2PBufferringBurstCaptureResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<P2PBufferringBurstCaptureResponse>(create);
  static P2PBufferringBurstCaptureResponse? _defaultInstance;
}

class GetNextDeepweedOutputRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextDeepweedOutputRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bufferName')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timeoutMs')
    ..hasRequiredFields = false
  ;

  GetNextDeepweedOutputRequest._() : super();
  factory GetNextDeepweedOutputRequest({
    $fixnum.Int64? timestampMs,
    $core.String? bufferName,
    $fixnum.Int64? timeoutMs,
  }) {
    final _result = create();
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (bufferName != null) {
      _result.bufferName = bufferName;
    }
    if (timeoutMs != null) {
      _result.timeoutMs = timeoutMs;
    }
    return _result;
  }
  factory GetNextDeepweedOutputRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextDeepweedOutputRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextDeepweedOutputRequest clone() => GetNextDeepweedOutputRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextDeepweedOutputRequest copyWith(void Function(GetNextDeepweedOutputRequest) updates) => super.copyWith((message) => updates(message as GetNextDeepweedOutputRequest)) as GetNextDeepweedOutputRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextDeepweedOutputRequest create() => GetNextDeepweedOutputRequest._();
  GetNextDeepweedOutputRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextDeepweedOutputRequest> createRepeated() => $pb.PbList<GetNextDeepweedOutputRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextDeepweedOutputRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextDeepweedOutputRequest>(create);
  static GetNextDeepweedOutputRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get timestampMs => $_getI64(0);
  @$pb.TagNumber(1)
  set timestampMs($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestampMs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestampMs() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get bufferName => $_getSZ(1);
  @$pb.TagNumber(2)
  set bufferName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBufferName() => $_has(1);
  @$pb.TagNumber(2)
  void clearBufferName() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timeoutMs => $_getI64(2);
  @$pb.TagNumber(3)
  set timeoutMs($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimeoutMs() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimeoutMs() => clearField(3);
}

class SetTargetingStateRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetTargetingStateRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEnabled')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningEnabled')
    ..hasRequiredFields = false
  ;

  SetTargetingStateRequest._() : super();
  factory SetTargetingStateRequest({
    $core.bool? weedingEnabled,
    $core.bool? thinningEnabled,
  }) {
    final _result = create();
    if (weedingEnabled != null) {
      _result.weedingEnabled = weedingEnabled;
    }
    if (thinningEnabled != null) {
      _result.thinningEnabled = thinningEnabled;
    }
    return _result;
  }
  factory SetTargetingStateRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetTargetingStateRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetTargetingStateRequest clone() => SetTargetingStateRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetTargetingStateRequest copyWith(void Function(SetTargetingStateRequest) updates) => super.copyWith((message) => updates(message as SetTargetingStateRequest)) as SetTargetingStateRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetTargetingStateRequest create() => SetTargetingStateRequest._();
  SetTargetingStateRequest createEmptyInstance() => create();
  static $pb.PbList<SetTargetingStateRequest> createRepeated() => $pb.PbList<SetTargetingStateRequest>();
  @$core.pragma('dart2js:noInline')
  static SetTargetingStateRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetTargetingStateRequest>(create);
  static SetTargetingStateRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get weedingEnabled => $_getBF(0);
  @$pb.TagNumber(1)
  set weedingEnabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeedingEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeedingEnabled() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get thinningEnabled => $_getBF(1);
  @$pb.TagNumber(2)
  set thinningEnabled($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasThinningEnabled() => $_has(1);
  @$pb.TagNumber(2)
  void clearThinningEnabled() => clearField(2);
}

class SetTargetingStateResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetTargetingStateResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'cv.runtime.proto'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetTargetingStateResponse._() : super();
  factory SetTargetingStateResponse() => create();
  factory SetTargetingStateResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetTargetingStateResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetTargetingStateResponse clone() => SetTargetingStateResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetTargetingStateResponse copyWith(void Function(SetTargetingStateResponse) updates) => super.copyWith((message) => updates(message as SetTargetingStateResponse)) as SetTargetingStateResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetTargetingStateResponse create() => SetTargetingStateResponse._();
  SetTargetingStateResponse createEmptyInstance() => create();
  static $pb.PbList<SetTargetingStateResponse> createRepeated() => $pb.PbList<SetTargetingStateResponse>();
  @$core.pragma('dart2js:noInline')
  static SetTargetingStateResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetTargetingStateResponse>(create);
  static SetTargetingStateResponse? _defaultInstance;
}

