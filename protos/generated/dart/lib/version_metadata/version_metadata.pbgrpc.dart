///
//  Generated code. Do not modify.
//  source: version_metadata/version_metadata.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'version_metadata.pb.dart' as $58;
import '../util/util.pb.dart' as $1;
export 'version_metadata.pb.dart';

class VersionMetadataServiceClient extends $grpc.Client {
  static final _$getVersionMetadata =
      $grpc.ClientMethod<$58.GetVersionMetadataRequest, $58.VersionMetadata>(
          '/carbon.version_metadata.VersionMetadataService/GetVersionMetadata',
          ($58.GetVersionMetadataRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $58.VersionMetadata.fromBuffer(value));
  static final _$uploadVersionMetadata = $grpc.ClientMethod<
          $58.UploadVersionMetadataRequest, $1.Empty>(
      '/carbon.version_metadata.VersionMetadataService/UploadVersionMetadata',
      ($58.UploadVersionMetadataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  VersionMetadataServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$58.VersionMetadata> getVersionMetadata(
      $58.GetVersionMetadataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getVersionMetadata, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadVersionMetadata(
      $58.UploadVersionMetadataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadVersionMetadata, request, options: options);
  }
}

abstract class VersionMetadataServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.version_metadata.VersionMetadataService';

  VersionMetadataServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$58.GetVersionMetadataRequest, $58.VersionMetadata>(
            'GetVersionMetadata',
            getVersionMetadata_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $58.GetVersionMetadataRequest.fromBuffer(value),
            ($58.VersionMetadata value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$58.UploadVersionMetadataRequest, $1.Empty>(
        'UploadVersionMetadata',
        uploadVersionMetadata_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $58.UploadVersionMetadataRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$58.VersionMetadata> getVersionMetadata_Pre(
      $grpc.ServiceCall call,
      $async.Future<$58.GetVersionMetadataRequest> request) async {
    return getVersionMetadata(call, await request);
  }

  $async.Future<$1.Empty> uploadVersionMetadata_Pre($grpc.ServiceCall call,
      $async.Future<$58.UploadVersionMetadataRequest> request) async {
    return uploadVersionMetadata(call, await request);
  }

  $async.Future<$58.VersionMetadata> getVersionMetadata(
      $grpc.ServiceCall call, $58.GetVersionMetadataRequest request);
  $async.Future<$1.Empty> uploadVersionMetadata(
      $grpc.ServiceCall call, $58.UploadVersionMetadataRequest request);
}
