///
//  Generated code. Do not modify.
//  source: frontend/category_collection.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;
import '../category_profile/category_profile.pb.dart' as $64;

class GetNextCategoryCollectionsDataRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextCategoryCollectionsDataRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.category_collection'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextCategoryCollectionsDataRequest._() : super();
  factory GetNextCategoryCollectionsDataRequest({
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextCategoryCollectionsDataRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextCategoryCollectionsDataRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextCategoryCollectionsDataRequest clone() => GetNextCategoryCollectionsDataRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextCategoryCollectionsDataRequest copyWith(void Function(GetNextCategoryCollectionsDataRequest) updates) => super.copyWith((message) => updates(message as GetNextCategoryCollectionsDataRequest)) as GetNextCategoryCollectionsDataRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextCategoryCollectionsDataRequest create() => GetNextCategoryCollectionsDataRequest._();
  GetNextCategoryCollectionsDataRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextCategoryCollectionsDataRequest> createRepeated() => $pb.PbList<GetNextCategoryCollectionsDataRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextCategoryCollectionsDataRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextCategoryCollectionsDataRequest>(create);
  static GetNextCategoryCollectionsDataRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);
}

class GetNextCategoryCollectionsDataResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextCategoryCollectionsDataResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.category_collection'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<$64.CategoryCollection>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollections', $pb.PbFieldType.PM, subBuilder: $64.CategoryCollection.create)
    ..hasRequiredFields = false
  ;

  GetNextCategoryCollectionsDataResponse._() : super();
  factory GetNextCategoryCollectionsDataResponse({
    $1.Timestamp? ts,
    $core.Iterable<$64.CategoryCollection>? categoryCollections,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (categoryCollections != null) {
      _result.categoryCollections.addAll(categoryCollections);
    }
    return _result;
  }
  factory GetNextCategoryCollectionsDataResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextCategoryCollectionsDataResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextCategoryCollectionsDataResponse clone() => GetNextCategoryCollectionsDataResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextCategoryCollectionsDataResponse copyWith(void Function(GetNextCategoryCollectionsDataResponse) updates) => super.copyWith((message) => updates(message as GetNextCategoryCollectionsDataResponse)) as GetNextCategoryCollectionsDataResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextCategoryCollectionsDataResponse create() => GetNextCategoryCollectionsDataResponse._();
  GetNextCategoryCollectionsDataResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextCategoryCollectionsDataResponse> createRepeated() => $pb.PbList<GetNextCategoryCollectionsDataResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextCategoryCollectionsDataResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextCategoryCollectionsDataResponse>(create);
  static GetNextCategoryCollectionsDataResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.List<$64.CategoryCollection> get categoryCollections => $_getList(1);
}

class GetNextActiveCategoryCollectionIdRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveCategoryCollectionIdRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.category_collection'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextActiveCategoryCollectionIdRequest._() : super();
  factory GetNextActiveCategoryCollectionIdRequest({
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextActiveCategoryCollectionIdRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveCategoryCollectionIdRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveCategoryCollectionIdRequest clone() => GetNextActiveCategoryCollectionIdRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveCategoryCollectionIdRequest copyWith(void Function(GetNextActiveCategoryCollectionIdRequest) updates) => super.copyWith((message) => updates(message as GetNextActiveCategoryCollectionIdRequest)) as GetNextActiveCategoryCollectionIdRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveCategoryCollectionIdRequest create() => GetNextActiveCategoryCollectionIdRequest._();
  GetNextActiveCategoryCollectionIdRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveCategoryCollectionIdRequest> createRepeated() => $pb.PbList<GetNextActiveCategoryCollectionIdRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveCategoryCollectionIdRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveCategoryCollectionIdRequest>(create);
  static GetNextActiveCategoryCollectionIdRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);
}

class GetNextActiveCategoryCollectionIdResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveCategoryCollectionIdResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.category_collection'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reloadRequired')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastUpdatedTimestampMs')
    ..hasRequiredFields = false
  ;

  GetNextActiveCategoryCollectionIdResponse._() : super();
  factory GetNextActiveCategoryCollectionIdResponse({
    $core.String? uuid,
    $1.Timestamp? ts,
    $core.bool? reloadRequired,
    $fixnum.Int64? lastUpdatedTimestampMs,
  }) {
    final _result = create();
    if (uuid != null) {
      _result.uuid = uuid;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    if (reloadRequired != null) {
      _result.reloadRequired = reloadRequired;
    }
    if (lastUpdatedTimestampMs != null) {
      _result.lastUpdatedTimestampMs = lastUpdatedTimestampMs;
    }
    return _result;
  }
  factory GetNextActiveCategoryCollectionIdResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveCategoryCollectionIdResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveCategoryCollectionIdResponse clone() => GetNextActiveCategoryCollectionIdResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveCategoryCollectionIdResponse copyWith(void Function(GetNextActiveCategoryCollectionIdResponse) updates) => super.copyWith((message) => updates(message as GetNextActiveCategoryCollectionIdResponse)) as GetNextActiveCategoryCollectionIdResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveCategoryCollectionIdResponse create() => GetNextActiveCategoryCollectionIdResponse._();
  GetNextActiveCategoryCollectionIdResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveCategoryCollectionIdResponse> createRepeated() => $pb.PbList<GetNextActiveCategoryCollectionIdResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveCategoryCollectionIdResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveCategoryCollectionIdResponse>(create);
  static GetNextActiveCategoryCollectionIdResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get uuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set uuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearUuid() => clearField(1);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.bool get reloadRequired => $_getBF(2);
  @$pb.TagNumber(3)
  set reloadRequired($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasReloadRequired() => $_has(2);
  @$pb.TagNumber(3)
  void clearReloadRequired() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get lastUpdatedTimestampMs => $_getI64(3);
  @$pb.TagNumber(4)
  set lastUpdatedTimestampMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastUpdatedTimestampMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastUpdatedTimestampMs() => clearField(4);
}

class SetActiveCategoryCollectionIdRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveCategoryCollectionIdRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.category_collection'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uuid')
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  SetActiveCategoryCollectionIdRequest._() : super();
  factory SetActiveCategoryCollectionIdRequest({
    $core.String? uuid,
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (uuid != null) {
      _result.uuid = uuid;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory SetActiveCategoryCollectionIdRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveCategoryCollectionIdRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveCategoryCollectionIdRequest clone() => SetActiveCategoryCollectionIdRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveCategoryCollectionIdRequest copyWith(void Function(SetActiveCategoryCollectionIdRequest) updates) => super.copyWith((message) => updates(message as SetActiveCategoryCollectionIdRequest)) as SetActiveCategoryCollectionIdRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveCategoryCollectionIdRequest create() => SetActiveCategoryCollectionIdRequest._();
  SetActiveCategoryCollectionIdRequest createEmptyInstance() => create();
  static $pb.PbList<SetActiveCategoryCollectionIdRequest> createRepeated() => $pb.PbList<SetActiveCategoryCollectionIdRequest>();
  @$core.pragma('dart2js:noInline')
  static SetActiveCategoryCollectionIdRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveCategoryCollectionIdRequest>(create);
  static SetActiveCategoryCollectionIdRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get uuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set uuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearUuid() => clearField(1);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);
}

