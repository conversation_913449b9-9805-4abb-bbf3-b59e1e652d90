///
//  Generated code. Do not modify.
//  source: frontend/plant_captcha.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;
import '../weed_tracking/weed_tracking.pb.dart' as $4;
import '../almanac/almanac.pb.dart' as $63;

import '../weed_tracking/weed_tracking.pbenum.dart' as $4;
import 'plant_captcha.pbenum.dart';

export 'plant_captcha.pbenum.dart';

class PlantCaptcha extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptcha', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropName')
    ..aInt64(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startTimeMs')
    ..p<$core.int>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowsUsed', $pb.PbFieldType.K3)
    ..hasRequiredFields = false
  ;

  PlantCaptcha._() : super();
  factory PlantCaptcha({
    $core.String? name,
    $core.String? modelId,
    $core.String? cropId,
    $core.String? cropName,
    $fixnum.Int64? startTimeMs,
    $core.Iterable<$core.int>? rowsUsed,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (modelId != null) {
      _result.modelId = modelId;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (cropName != null) {
      _result.cropName = cropName;
    }
    if (startTimeMs != null) {
      _result.startTimeMs = startTimeMs;
    }
    if (rowsUsed != null) {
      _result.rowsUsed.addAll(rowsUsed);
    }
    return _result;
  }
  factory PlantCaptcha.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptcha.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptcha clone() => PlantCaptcha()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptcha copyWith(void Function(PlantCaptcha) updates) => super.copyWith((message) => updates(message as PlantCaptcha)) as PlantCaptcha; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptcha create() => PlantCaptcha._();
  PlantCaptcha createEmptyInstance() => create();
  static $pb.PbList<PlantCaptcha> createRepeated() => $pb.PbList<PlantCaptcha>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptcha getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptcha>(create);
  static PlantCaptcha? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get modelId => $_getSZ(1);
  @$pb.TagNumber(2)
  set modelId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasModelId() => $_has(1);
  @$pb.TagNumber(2)
  void clearModelId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get cropId => $_getSZ(2);
  @$pb.TagNumber(3)
  set cropId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCropId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCropId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get cropName => $_getSZ(3);
  @$pb.TagNumber(4)
  set cropName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCropName() => $_has(3);
  @$pb.TagNumber(4)
  void clearCropName() => clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get startTimeMs => $_getI64(4);
  @$pb.TagNumber(5)
  set startTimeMs($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasStartTimeMs() => $_has(4);
  @$pb.TagNumber(5)
  void clearStartTimeMs() => clearField(5);

  @$pb.TagNumber(6)
  $core.List<$core.int> get rowsUsed => $_getList(5);
}

class StartPlantCaptchaRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartPlantCaptchaRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<PlantCaptcha>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantCaptcha', subBuilder: PlantCaptcha.create)
    ..hasRequiredFields = false
  ;

  StartPlantCaptchaRequest._() : super();
  factory StartPlantCaptchaRequest({
    PlantCaptcha? plantCaptcha,
  }) {
    final _result = create();
    if (plantCaptcha != null) {
      _result.plantCaptcha = plantCaptcha;
    }
    return _result;
  }
  factory StartPlantCaptchaRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartPlantCaptchaRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartPlantCaptchaRequest clone() => StartPlantCaptchaRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartPlantCaptchaRequest copyWith(void Function(StartPlantCaptchaRequest) updates) => super.copyWith((message) => updates(message as StartPlantCaptchaRequest)) as StartPlantCaptchaRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartPlantCaptchaRequest create() => StartPlantCaptchaRequest._();
  StartPlantCaptchaRequest createEmptyInstance() => create();
  static $pb.PbList<StartPlantCaptchaRequest> createRepeated() => $pb.PbList<StartPlantCaptchaRequest>();
  @$core.pragma('dart2js:noInline')
  static StartPlantCaptchaRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartPlantCaptchaRequest>(create);
  static StartPlantCaptchaRequest? _defaultInstance;

  @$pb.TagNumber(1)
  PlantCaptcha get plantCaptcha => $_getN(0);
  @$pb.TagNumber(1)
  set plantCaptcha(PlantCaptcha v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPlantCaptcha() => $_has(0);
  @$pb.TagNumber(1)
  void clearPlantCaptcha() => clearField(1);
  @$pb.TagNumber(1)
  PlantCaptcha ensurePlantCaptcha() => $_ensure(0);
}

class StartPlantCaptchaResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartPlantCaptchaResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  StartPlantCaptchaResponse._() : super();
  factory StartPlantCaptchaResponse() => create();
  factory StartPlantCaptchaResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartPlantCaptchaResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartPlantCaptchaResponse clone() => StartPlantCaptchaResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartPlantCaptchaResponse copyWith(void Function(StartPlantCaptchaResponse) updates) => super.copyWith((message) => updates(message as StartPlantCaptchaResponse)) as StartPlantCaptchaResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartPlantCaptchaResponse create() => StartPlantCaptchaResponse._();
  StartPlantCaptchaResponse createEmptyInstance() => create();
  static $pb.PbList<StartPlantCaptchaResponse> createRepeated() => $pb.PbList<StartPlantCaptchaResponse>();
  @$core.pragma('dart2js:noInline')
  static StartPlantCaptchaResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartPlantCaptchaResponse>(create);
  static StartPlantCaptchaResponse? _defaultInstance;
}

class GetNextPlantCaptchaStatusRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextPlantCaptchaStatusRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextPlantCaptchaStatusRequest._() : super();
  factory GetNextPlantCaptchaStatusRequest({
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextPlantCaptchaStatusRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextPlantCaptchaStatusRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaStatusRequest clone() => GetNextPlantCaptchaStatusRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaStatusRequest copyWith(void Function(GetNextPlantCaptchaStatusRequest) updates) => super.copyWith((message) => updates(message as GetNextPlantCaptchaStatusRequest)) as GetNextPlantCaptchaStatusRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaStatusRequest create() => GetNextPlantCaptchaStatusRequest._();
  GetNextPlantCaptchaStatusRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextPlantCaptchaStatusRequest> createRepeated() => $pb.PbList<GetNextPlantCaptchaStatusRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaStatusRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextPlantCaptchaStatusRequest>(create);
  static GetNextPlantCaptchaStatusRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);
}

class GetNextPlantCaptchaStatusResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextPlantCaptchaStatusResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..e<$4.PlantCaptchaStatus>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: $4.PlantCaptchaStatus.NOT_STARTED, valueOf: $4.PlantCaptchaStatus.valueOf, enumValues: $4.PlantCaptchaStatus.values)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'totalImages', $pb.PbFieldType.O3)
    ..a<$core.int>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesTaken', $pb.PbFieldType.O3)
    ..a<$core.int>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadataTaken', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  GetNextPlantCaptchaStatusResponse._() : super();
  factory GetNextPlantCaptchaStatusResponse({
    $1.Timestamp? ts,
    $4.PlantCaptchaStatus? status,
    $core.int? totalImages,
    $core.int? imagesTaken,
    $core.int? metadataTaken,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (status != null) {
      _result.status = status;
    }
    if (totalImages != null) {
      _result.totalImages = totalImages;
    }
    if (imagesTaken != null) {
      _result.imagesTaken = imagesTaken;
    }
    if (metadataTaken != null) {
      _result.metadataTaken = metadataTaken;
    }
    return _result;
  }
  factory GetNextPlantCaptchaStatusResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextPlantCaptchaStatusResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaStatusResponse clone() => GetNextPlantCaptchaStatusResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaStatusResponse copyWith(void Function(GetNextPlantCaptchaStatusResponse) updates) => super.copyWith((message) => updates(message as GetNextPlantCaptchaStatusResponse)) as GetNextPlantCaptchaStatusResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaStatusResponse create() => GetNextPlantCaptchaStatusResponse._();
  GetNextPlantCaptchaStatusResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextPlantCaptchaStatusResponse> createRepeated() => $pb.PbList<GetNextPlantCaptchaStatusResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaStatusResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextPlantCaptchaStatusResponse>(create);
  static GetNextPlantCaptchaStatusResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $4.PlantCaptchaStatus get status => $_getN(1);
  @$pb.TagNumber(2)
  set status($4.PlantCaptchaStatus v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearStatus() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get totalImages => $_getIZ(2);
  @$pb.TagNumber(3)
  set totalImages($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTotalImages() => $_has(2);
  @$pb.TagNumber(3)
  void clearTotalImages() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get imagesTaken => $_getIZ(3);
  @$pb.TagNumber(4)
  set imagesTaken($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasImagesTaken() => $_has(3);
  @$pb.TagNumber(4)
  void clearImagesTaken() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get metadataTaken => $_getIZ(4);
  @$pb.TagNumber(5)
  set metadataTaken($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasMetadataTaken() => $_has(4);
  @$pb.TagNumber(5)
  void clearMetadataTaken() => clearField(5);
}

class GetNextPlantCaptchasListRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextPlantCaptchasListRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextPlantCaptchasListRequest._() : super();
  factory GetNextPlantCaptchasListRequest({
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextPlantCaptchasListRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextPlantCaptchasListRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchasListRequest clone() => GetNextPlantCaptchasListRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchasListRequest copyWith(void Function(GetNextPlantCaptchasListRequest) updates) => super.copyWith((message) => updates(message as GetNextPlantCaptchasListRequest)) as GetNextPlantCaptchasListRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchasListRequest create() => GetNextPlantCaptchasListRequest._();
  GetNextPlantCaptchasListRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextPlantCaptchasListRequest> createRepeated() => $pb.PbList<GetNextPlantCaptchasListRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchasListRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextPlantCaptchasListRequest>(create);
  static GetNextPlantCaptchasListRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);
}

class PlantCaptchaListItem extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaListItem', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<PlantCaptcha>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantCaptcha', subBuilder: PlantCaptcha.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesTaken', $pb.PbFieldType.O3)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imagesProcessed', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  PlantCaptchaListItem._() : super();
  factory PlantCaptchaListItem({
    PlantCaptcha? plantCaptcha,
    $core.int? imagesTaken,
    $core.int? imagesProcessed,
  }) {
    final _result = create();
    if (plantCaptcha != null) {
      _result.plantCaptcha = plantCaptcha;
    }
    if (imagesTaken != null) {
      _result.imagesTaken = imagesTaken;
    }
    if (imagesProcessed != null) {
      _result.imagesProcessed = imagesProcessed;
    }
    return _result;
  }
  factory PlantCaptchaListItem.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaListItem.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaListItem clone() => PlantCaptchaListItem()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaListItem copyWith(void Function(PlantCaptchaListItem) updates) => super.copyWith((message) => updates(message as PlantCaptchaListItem)) as PlantCaptchaListItem; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaListItem create() => PlantCaptchaListItem._();
  PlantCaptchaListItem createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaListItem> createRepeated() => $pb.PbList<PlantCaptchaListItem>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaListItem getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaListItem>(create);
  static PlantCaptchaListItem? _defaultInstance;

  @$pb.TagNumber(1)
  PlantCaptcha get plantCaptcha => $_getN(0);
  @$pb.TagNumber(1)
  set plantCaptcha(PlantCaptcha v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPlantCaptcha() => $_has(0);
  @$pb.TagNumber(1)
  void clearPlantCaptcha() => clearField(1);
  @$pb.TagNumber(1)
  PlantCaptcha ensurePlantCaptcha() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get imagesTaken => $_getIZ(1);
  @$pb.TagNumber(2)
  set imagesTaken($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasImagesTaken() => $_has(1);
  @$pb.TagNumber(2)
  void clearImagesTaken() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get imagesProcessed => $_getIZ(2);
  @$pb.TagNumber(3)
  set imagesProcessed($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasImagesProcessed() => $_has(2);
  @$pb.TagNumber(3)
  void clearImagesProcessed() => clearField(3);
}

class GetNextPlantCaptchasListResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextPlantCaptchasListResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<PlantCaptchaListItem>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantCaptchas', $pb.PbFieldType.PM, subBuilder: PlantCaptchaListItem.create)
    ..hasRequiredFields = false
  ;

  GetNextPlantCaptchasListResponse._() : super();
  factory GetNextPlantCaptchasListResponse({
    $1.Timestamp? ts,
    $core.Iterable<PlantCaptchaListItem>? plantCaptchas,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (plantCaptchas != null) {
      _result.plantCaptchas.addAll(plantCaptchas);
    }
    return _result;
  }
  factory GetNextPlantCaptchasListResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextPlantCaptchasListResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchasListResponse clone() => GetNextPlantCaptchasListResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchasListResponse copyWith(void Function(GetNextPlantCaptchasListResponse) updates) => super.copyWith((message) => updates(message as GetNextPlantCaptchasListResponse)) as GetNextPlantCaptchasListResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchasListResponse create() => GetNextPlantCaptchasListResponse._();
  GetNextPlantCaptchasListResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextPlantCaptchasListResponse> createRepeated() => $pb.PbList<GetNextPlantCaptchasListResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchasListResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextPlantCaptchasListResponse>(create);
  static GetNextPlantCaptchasListResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<PlantCaptchaListItem> get plantCaptchas => $_getList(1);
}

class DeletePlantCaptchaRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeletePlantCaptchaRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  DeletePlantCaptchaRequest._() : super();
  factory DeletePlantCaptchaRequest({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory DeletePlantCaptchaRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeletePlantCaptchaRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeletePlantCaptchaRequest clone() => DeletePlantCaptchaRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeletePlantCaptchaRequest copyWith(void Function(DeletePlantCaptchaRequest) updates) => super.copyWith((message) => updates(message as DeletePlantCaptchaRequest)) as DeletePlantCaptchaRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeletePlantCaptchaRequest create() => DeletePlantCaptchaRequest._();
  DeletePlantCaptchaRequest createEmptyInstance() => create();
  static $pb.PbList<DeletePlantCaptchaRequest> createRepeated() => $pb.PbList<DeletePlantCaptchaRequest>();
  @$core.pragma('dart2js:noInline')
  static DeletePlantCaptchaRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeletePlantCaptchaRequest>(create);
  static DeletePlantCaptchaRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class GetPlantCaptchaRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPlantCaptchaRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  GetPlantCaptchaRequest._() : super();
  factory GetPlantCaptchaRequest({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory GetPlantCaptchaRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPlantCaptchaRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaRequest clone() => GetPlantCaptchaRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaRequest copyWith(void Function(GetPlantCaptchaRequest) updates) => super.copyWith((message) => updates(message as GetPlantCaptchaRequest)) as GetPlantCaptchaRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaRequest create() => GetPlantCaptchaRequest._();
  GetPlantCaptchaRequest createEmptyInstance() => create();
  static $pb.PbList<GetPlantCaptchaRequest> createRepeated() => $pb.PbList<GetPlantCaptchaRequest>();
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPlantCaptchaRequest>(create);
  static GetPlantCaptchaRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class PlantCaptchaItem extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaItem', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageUrl')
    ..aOM<$4.PlantCaptchaItemMetadata>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', subBuilder: $4.PlantCaptchaItemMetadata.create)
    ..pPS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'additionalImageUrls')
    ..pc<$4.PlantCaptchaItemMetadata>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'additionalMetadatas', $pb.PbFieldType.PM, subBuilder: $4.PlantCaptchaItemMetadata.create)
    ..hasRequiredFields = false
  ;

  PlantCaptchaItem._() : super();
  factory PlantCaptchaItem({
    $core.String? imageUrl,
    $4.PlantCaptchaItemMetadata? metadata,
    $core.Iterable<$core.String>? additionalImageUrls,
    $core.Iterable<$4.PlantCaptchaItemMetadata>? additionalMetadatas,
  }) {
    final _result = create();
    if (imageUrl != null) {
      _result.imageUrl = imageUrl;
    }
    if (metadata != null) {
      _result.metadata = metadata;
    }
    if (additionalImageUrls != null) {
      _result.additionalImageUrls.addAll(additionalImageUrls);
    }
    if (additionalMetadatas != null) {
      _result.additionalMetadatas.addAll(additionalMetadatas);
    }
    return _result;
  }
  factory PlantCaptchaItem.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaItem.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaItem clone() => PlantCaptchaItem()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaItem copyWith(void Function(PlantCaptchaItem) updates) => super.copyWith((message) => updates(message as PlantCaptchaItem)) as PlantCaptchaItem; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaItem create() => PlantCaptchaItem._();
  PlantCaptchaItem createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaItem> createRepeated() => $pb.PbList<PlantCaptchaItem>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaItem getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaItem>(create);
  static PlantCaptchaItem? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get imageUrl => $_getSZ(0);
  @$pb.TagNumber(1)
  set imageUrl($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasImageUrl() => $_has(0);
  @$pb.TagNumber(1)
  void clearImageUrl() => clearField(1);

  @$pb.TagNumber(2)
  $4.PlantCaptchaItemMetadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata($4.PlantCaptchaItemMetadata v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => clearField(2);
  @$pb.TagNumber(2)
  $4.PlantCaptchaItemMetadata ensureMetadata() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.List<$core.String> get additionalImageUrls => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<$4.PlantCaptchaItemMetadata> get additionalMetadatas => $_getList(3);
}

class GetPlantCaptchaResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPlantCaptchaResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<PlantCaptcha>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantCaptcha', subBuilder: PlantCaptcha.create)
    ..pc<PlantCaptchaItem>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'items', $pb.PbFieldType.PM, subBuilder: PlantCaptchaItem.create)
    ..hasRequiredFields = false
  ;

  GetPlantCaptchaResponse._() : super();
  factory GetPlantCaptchaResponse({
    PlantCaptcha? plantCaptcha,
    $core.Iterable<PlantCaptchaItem>? items,
  }) {
    final _result = create();
    if (plantCaptcha != null) {
      _result.plantCaptcha = plantCaptcha;
    }
    if (items != null) {
      _result.items.addAll(items);
    }
    return _result;
  }
  factory GetPlantCaptchaResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPlantCaptchaResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaResponse clone() => GetPlantCaptchaResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaResponse copyWith(void Function(GetPlantCaptchaResponse) updates) => super.copyWith((message) => updates(message as GetPlantCaptchaResponse)) as GetPlantCaptchaResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaResponse create() => GetPlantCaptchaResponse._();
  GetPlantCaptchaResponse createEmptyInstance() => create();
  static $pb.PbList<GetPlantCaptchaResponse> createRepeated() => $pb.PbList<GetPlantCaptchaResponse>();
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPlantCaptchaResponse>(create);
  static GetPlantCaptchaResponse? _defaultInstance;

  @$pb.TagNumber(1)
  PlantCaptcha get plantCaptcha => $_getN(0);
  @$pb.TagNumber(1)
  set plantCaptcha(PlantCaptcha v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPlantCaptcha() => $_has(0);
  @$pb.TagNumber(1)
  void clearPlantCaptcha() => clearField(1);
  @$pb.TagNumber(1)
  PlantCaptcha ensurePlantCaptcha() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<PlantCaptchaItem> get items => $_getList(1);
}

class StartPlantCaptchaUploadRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartPlantCaptchaUploadRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  StartPlantCaptchaUploadRequest._() : super();
  factory StartPlantCaptchaUploadRequest({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory StartPlantCaptchaUploadRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartPlantCaptchaUploadRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartPlantCaptchaUploadRequest clone() => StartPlantCaptchaUploadRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartPlantCaptchaUploadRequest copyWith(void Function(StartPlantCaptchaUploadRequest) updates) => super.copyWith((message) => updates(message as StartPlantCaptchaUploadRequest)) as StartPlantCaptchaUploadRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartPlantCaptchaUploadRequest create() => StartPlantCaptchaUploadRequest._();
  StartPlantCaptchaUploadRequest createEmptyInstance() => create();
  static $pb.PbList<StartPlantCaptchaUploadRequest> createRepeated() => $pb.PbList<StartPlantCaptchaUploadRequest>();
  @$core.pragma('dart2js:noInline')
  static StartPlantCaptchaUploadRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartPlantCaptchaUploadRequest>(create);
  static StartPlantCaptchaUploadRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class GetNextPlantCaptchaUploadStateRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextPlantCaptchaUploadStateRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  GetNextPlantCaptchaUploadStateRequest._() : super();
  factory GetNextPlantCaptchaUploadStateRequest({
    $1.Timestamp? ts,
    $core.String? name,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory GetNextPlantCaptchaUploadStateRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextPlantCaptchaUploadStateRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaUploadStateRequest clone() => GetNextPlantCaptchaUploadStateRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaUploadStateRequest copyWith(void Function(GetNextPlantCaptchaUploadStateRequest) updates) => super.copyWith((message) => updates(message as GetNextPlantCaptchaUploadStateRequest)) as GetNextPlantCaptchaUploadStateRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaUploadStateRequest create() => GetNextPlantCaptchaUploadStateRequest._();
  GetNextPlantCaptchaUploadStateRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextPlantCaptchaUploadStateRequest> createRepeated() => $pb.PbList<GetNextPlantCaptchaUploadStateRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaUploadStateRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextPlantCaptchaUploadStateRequest>(create);
  static GetNextPlantCaptchaUploadStateRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);
}

class GetNextPlantCaptchaUploadStateResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextPlantCaptchaUploadStateResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..e<PlantCaptchaUploadState>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uploadState', $pb.PbFieldType.OE, defaultOrMaker: PlantCaptchaUploadState.NONE, valueOf: PlantCaptchaUploadState.valueOf, enumValues: PlantCaptchaUploadState.values)
    ..a<$core.int>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'percent', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  GetNextPlantCaptchaUploadStateResponse._() : super();
  factory GetNextPlantCaptchaUploadStateResponse({
    $1.Timestamp? ts,
    PlantCaptchaUploadState? uploadState,
    $core.int? percent,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (uploadState != null) {
      _result.uploadState = uploadState;
    }
    if (percent != null) {
      _result.percent = percent;
    }
    return _result;
  }
  factory GetNextPlantCaptchaUploadStateResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextPlantCaptchaUploadStateResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaUploadStateResponse clone() => GetNextPlantCaptchaUploadStateResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextPlantCaptchaUploadStateResponse copyWith(void Function(GetNextPlantCaptchaUploadStateResponse) updates) => super.copyWith((message) => updates(message as GetNextPlantCaptchaUploadStateResponse)) as GetNextPlantCaptchaUploadStateResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaUploadStateResponse create() => GetNextPlantCaptchaUploadStateResponse._();
  GetNextPlantCaptchaUploadStateResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextPlantCaptchaUploadStateResponse> createRepeated() => $pb.PbList<GetNextPlantCaptchaUploadStateResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextPlantCaptchaUploadStateResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextPlantCaptchaUploadStateResponse>(create);
  static GetNextPlantCaptchaUploadStateResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  PlantCaptchaUploadState get uploadState => $_getN(1);
  @$pb.TagNumber(2)
  set uploadState(PlantCaptchaUploadState v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUploadState() => $_has(1);
  @$pb.TagNumber(2)
  void clearUploadState() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get percent => $_getIZ(2);
  @$pb.TagNumber(3)
  set percent($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPercent() => $_has(2);
  @$pb.TagNumber(3)
  void clearPercent() => clearField(3);
}

class PlantCaptchaItemResult extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaItemResult', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..e<$4.PlantCaptchaUserPrediction>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userPrediction', $pb.PbFieldType.OE, defaultOrMaker: $4.PlantCaptchaUserPrediction.WEED, valueOf: $4.PlantCaptchaUserPrediction.valueOf, enumValues: $4.PlantCaptchaUserPrediction.values)
    ..hasRequiredFields = false
  ;

  PlantCaptchaItemResult._() : super();
  factory PlantCaptchaItemResult({
    $core.String? id,
    $4.PlantCaptchaUserPrediction? userPrediction,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (userPrediction != null) {
      _result.userPrediction = userPrediction;
    }
    return _result;
  }
  factory PlantCaptchaItemResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaItemResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaItemResult clone() => PlantCaptchaItemResult()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaItemResult copyWith(void Function(PlantCaptchaItemResult) updates) => super.copyWith((message) => updates(message as PlantCaptchaItemResult)) as PlantCaptchaItemResult; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaItemResult create() => PlantCaptchaItemResult._();
  PlantCaptchaItemResult createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaItemResult> createRepeated() => $pb.PbList<PlantCaptchaItemResult>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaItemResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaItemResult>(create);
  static PlantCaptchaItemResult? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $4.PlantCaptchaUserPrediction get userPrediction => $_getN(1);
  @$pb.TagNumber(2)
  set userPrediction($4.PlantCaptchaUserPrediction v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUserPrediction() => $_has(1);
  @$pb.TagNumber(2)
  void clearUserPrediction() => clearField(2);
}

class SubmitPlantCaptchaResultsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SubmitPlantCaptchaResultsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..pc<PlantCaptchaItemResult>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'results', $pb.PbFieldType.PM, subBuilder: PlantCaptchaItemResult.create)
    ..hasRequiredFields = false
  ;

  SubmitPlantCaptchaResultsRequest._() : super();
  factory SubmitPlantCaptchaResultsRequest({
    $core.String? name,
    $core.Iterable<PlantCaptchaItemResult>? results,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (results != null) {
      _result.results.addAll(results);
    }
    return _result;
  }
  factory SubmitPlantCaptchaResultsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SubmitPlantCaptchaResultsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SubmitPlantCaptchaResultsRequest clone() => SubmitPlantCaptchaResultsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SubmitPlantCaptchaResultsRequest copyWith(void Function(SubmitPlantCaptchaResultsRequest) updates) => super.copyWith((message) => updates(message as SubmitPlantCaptchaResultsRequest)) as SubmitPlantCaptchaResultsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SubmitPlantCaptchaResultsRequest create() => SubmitPlantCaptchaResultsRequest._();
  SubmitPlantCaptchaResultsRequest createEmptyInstance() => create();
  static $pb.PbList<SubmitPlantCaptchaResultsRequest> createRepeated() => $pb.PbList<SubmitPlantCaptchaResultsRequest>();
  @$core.pragma('dart2js:noInline')
  static SubmitPlantCaptchaResultsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SubmitPlantCaptchaResultsRequest>(create);
  static SubmitPlantCaptchaResultsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<PlantCaptchaItemResult> get results => $_getList(1);
}

class GetPlantCaptchaItemResultsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPlantCaptchaItemResultsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..pPS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  GetPlantCaptchaItemResultsRequest._() : super();
  factory GetPlantCaptchaItemResultsRequest({
    $core.String? name,
    $core.Iterable<$core.String>? id,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (id != null) {
      _result.id.addAll(id);
    }
    return _result;
  }
  factory GetPlantCaptchaItemResultsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPlantCaptchaItemResultsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaItemResultsRequest clone() => GetPlantCaptchaItemResultsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaItemResultsRequest copyWith(void Function(GetPlantCaptchaItemResultsRequest) updates) => super.copyWith((message) => updates(message as GetPlantCaptchaItemResultsRequest)) as GetPlantCaptchaItemResultsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaItemResultsRequest create() => GetPlantCaptchaItemResultsRequest._();
  GetPlantCaptchaItemResultsRequest createEmptyInstance() => create();
  static $pb.PbList<GetPlantCaptchaItemResultsRequest> createRepeated() => $pb.PbList<GetPlantCaptchaItemResultsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaItemResultsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPlantCaptchaItemResultsRequest>(create);
  static GetPlantCaptchaItemResultsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get id => $_getList(1);
}

class GetPlantCaptchaItemResultsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetPlantCaptchaItemResultsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..pc<PlantCaptchaItemResult>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'results', $pb.PbFieldType.PM, subBuilder: PlantCaptchaItemResult.create)
    ..hasRequiredFields = false
  ;

  GetPlantCaptchaItemResultsResponse._() : super();
  factory GetPlantCaptchaItemResultsResponse({
    $core.Iterable<PlantCaptchaItemResult>? results,
  }) {
    final _result = create();
    if (results != null) {
      _result.results.addAll(results);
    }
    return _result;
  }
  factory GetPlantCaptchaItemResultsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetPlantCaptchaItemResultsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaItemResultsResponse clone() => GetPlantCaptchaItemResultsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetPlantCaptchaItemResultsResponse copyWith(void Function(GetPlantCaptchaItemResultsResponse) updates) => super.copyWith((message) => updates(message as GetPlantCaptchaItemResultsResponse)) as GetPlantCaptchaItemResultsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaItemResultsResponse create() => GetPlantCaptchaItemResultsResponse._();
  GetPlantCaptchaItemResultsResponse createEmptyInstance() => create();
  static $pb.PbList<GetPlantCaptchaItemResultsResponse> createRepeated() => $pb.PbList<GetPlantCaptchaItemResultsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetPlantCaptchaItemResultsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetPlantCaptchaItemResultsResponse>(create);
  static GetPlantCaptchaItemResultsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<PlantCaptchaItemResult> get results => $_getList(0);
}

class CalculatePlantCaptchaRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CalculatePlantCaptchaRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  CalculatePlantCaptchaRequest._() : super();
  factory CalculatePlantCaptchaRequest({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory CalculatePlantCaptchaRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CalculatePlantCaptchaRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CalculatePlantCaptchaRequest clone() => CalculatePlantCaptchaRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CalculatePlantCaptchaRequest copyWith(void Function(CalculatePlantCaptchaRequest) updates) => super.copyWith((message) => updates(message as CalculatePlantCaptchaRequest)) as CalculatePlantCaptchaRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CalculatePlantCaptchaRequest create() => CalculatePlantCaptchaRequest._();
  CalculatePlantCaptchaRequest createEmptyInstance() => create();
  static $pb.PbList<CalculatePlantCaptchaRequest> createRepeated() => $pb.PbList<CalculatePlantCaptchaRequest>();
  @$core.pragma('dart2js:noInline')
  static CalculatePlantCaptchaRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CalculatePlantCaptchaRequest>(create);
  static CalculatePlantCaptchaRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class CalculatePlantCaptchaResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CalculatePlantCaptchaResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$63.ModelinatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelinatorConfig', subBuilder: $63.ModelinatorConfig.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'succeeded')
    ..e<PlantLabelAlgorithmFailureReason>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'failureReason', $pb.PbFieldType.OE, defaultOrMaker: PlantLabelAlgorithmFailureReason.NO_FAILURE, valueOf: PlantLabelAlgorithmFailureReason.valueOf, enumValues: PlantLabelAlgorithmFailureReason.values)
    ..hasRequiredFields = false
  ;

  CalculatePlantCaptchaResponse._() : super();
  factory CalculatePlantCaptchaResponse({
    $63.ModelinatorConfig? modelinatorConfig,
    $core.bool? succeeded,
    PlantLabelAlgorithmFailureReason? failureReason,
  }) {
    final _result = create();
    if (modelinatorConfig != null) {
      _result.modelinatorConfig = modelinatorConfig;
    }
    if (succeeded != null) {
      _result.succeeded = succeeded;
    }
    if (failureReason != null) {
      _result.failureReason = failureReason;
    }
    return _result;
  }
  factory CalculatePlantCaptchaResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CalculatePlantCaptchaResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CalculatePlantCaptchaResponse clone() => CalculatePlantCaptchaResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CalculatePlantCaptchaResponse copyWith(void Function(CalculatePlantCaptchaResponse) updates) => super.copyWith((message) => updates(message as CalculatePlantCaptchaResponse)) as CalculatePlantCaptchaResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CalculatePlantCaptchaResponse create() => CalculatePlantCaptchaResponse._();
  CalculatePlantCaptchaResponse createEmptyInstance() => create();
  static $pb.PbList<CalculatePlantCaptchaResponse> createRepeated() => $pb.PbList<CalculatePlantCaptchaResponse>();
  @$core.pragma('dart2js:noInline')
  static CalculatePlantCaptchaResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CalculatePlantCaptchaResponse>(create);
  static CalculatePlantCaptchaResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $63.ModelinatorConfig get modelinatorConfig => $_getN(0);
  @$pb.TagNumber(1)
  set modelinatorConfig($63.ModelinatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasModelinatorConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearModelinatorConfig() => clearField(1);
  @$pb.TagNumber(1)
  $63.ModelinatorConfig ensureModelinatorConfig() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get succeeded => $_getBF(1);
  @$pb.TagNumber(2)
  set succeeded($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSucceeded() => $_has(1);
  @$pb.TagNumber(2)
  void clearSucceeded() => clearField(2);

  @$pb.TagNumber(3)
  PlantLabelAlgorithmFailureReason get failureReason => $_getN(2);
  @$pb.TagNumber(3)
  set failureReason(PlantLabelAlgorithmFailureReason v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasFailureReason() => $_has(2);
  @$pb.TagNumber(3)
  void clearFailureReason() => clearField(3);
}

class PlantCaptchaResult extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaResult', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..e<$4.PlantCaptchaUserPrediction>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'label', $pb.PbFieldType.OE, defaultOrMaker: $4.PlantCaptchaUserPrediction.WEED, valueOf: $4.PlantCaptchaUserPrediction.valueOf, enumValues: $4.PlantCaptchaUserPrediction.values)
    ..aOM<$4.PlantCaptchaItemMetadata>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', subBuilder: $4.PlantCaptchaItemMetadata.create)
    ..hasRequiredFields = false
  ;

  PlantCaptchaResult._() : super();
  factory PlantCaptchaResult({
    $4.PlantCaptchaUserPrediction? label,
    $4.PlantCaptchaItemMetadata? metadata,
  }) {
    final _result = create();
    if (label != null) {
      _result.label = label;
    }
    if (metadata != null) {
      _result.metadata = metadata;
    }
    return _result;
  }
  factory PlantCaptchaResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaResult clone() => PlantCaptchaResult()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaResult copyWith(void Function(PlantCaptchaResult) updates) => super.copyWith((message) => updates(message as PlantCaptchaResult)) as PlantCaptchaResult; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaResult create() => PlantCaptchaResult._();
  PlantCaptchaResult createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaResult> createRepeated() => $pb.PbList<PlantCaptchaResult>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaResult>(create);
  static PlantCaptchaResult? _defaultInstance;

  @$pb.TagNumber(1)
  $4.PlantCaptchaUserPrediction get label => $_getN(0);
  @$pb.TagNumber(1)
  set label($4.PlantCaptchaUserPrediction v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasLabel() => $_has(0);
  @$pb.TagNumber(1)
  void clearLabel() => clearField(1);

  @$pb.TagNumber(2)
  $4.PlantCaptchaItemMetadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata($4.PlantCaptchaItemMetadata v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => clearField(2);
  @$pb.TagNumber(2)
  $4.PlantCaptchaItemMetadata ensureMetadata() => $_ensure(1);
}

class PlantCaptchaResults extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantCaptchaResults', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$63.ModelinatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'currentParameters', subBuilder: $63.ModelinatorConfig.create)
    ..pc<PlantCaptchaResult>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'captchaResults', $pb.PbFieldType.PM, subBuilder: PlantCaptchaResult.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'algorithm')
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'goalCropsTargeted', $pb.PbFieldType.OF)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'goalWeedsTargeted', $pb.PbFieldType.OF)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'goalUnknownTargeted', $pb.PbFieldType.OF)
    ..aOM<$63.AlmanacConfig>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanac', subBuilder: $63.AlmanacConfig.create)
    ..a<$core.double>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maxRecommendedMindoo', $pb.PbFieldType.OF)
    ..a<$core.int>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minItemsForRecommendation', $pb.PbFieldType.O3)
    ..aOB(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'useWeedCategoriesForWeedLabels')
    ..a<$core.double>(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minRecommendedMindoo', $pb.PbFieldType.OF)
    ..a<$core.double>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minRecommendedWeedThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maxRecommendedWeedThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minRecommendedCropThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'maxRecommendedCropThreshold', $pb.PbFieldType.OF)
    ..a<$core.double>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'minDooForRecommendation', $pb.PbFieldType.OF)
    ..aOB(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'useOtherAsTiebreaker')
    ..aOB(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'limitByCropsMissed')
    ..a<$core.int>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numberOfCropConfigurations', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  PlantCaptchaResults._() : super();
  factory PlantCaptchaResults({
    $63.ModelinatorConfig? currentParameters,
    $core.Iterable<PlantCaptchaResult>? captchaResults,
    $core.String? algorithm,
    $core.double? goalCropsTargeted,
    $core.double? goalWeedsTargeted,
    $core.double? goalUnknownTargeted,
    $63.AlmanacConfig? almanac,
    $core.double? maxRecommendedMindoo,
    $core.int? minItemsForRecommendation,
    $core.bool? useWeedCategoriesForWeedLabels,
    $core.double? minRecommendedMindoo,
    $core.double? minRecommendedWeedThreshold,
    $core.double? maxRecommendedWeedThreshold,
    $core.double? minRecommendedCropThreshold,
    $core.double? maxRecommendedCropThreshold,
    $core.double? minDooForRecommendation,
    $core.bool? useOtherAsTiebreaker,
    $core.bool? limitByCropsMissed,
    $core.int? numberOfCropConfigurations,
  }) {
    final _result = create();
    if (currentParameters != null) {
      _result.currentParameters = currentParameters;
    }
    if (captchaResults != null) {
      _result.captchaResults.addAll(captchaResults);
    }
    if (algorithm != null) {
      _result.algorithm = algorithm;
    }
    if (goalCropsTargeted != null) {
      _result.goalCropsTargeted = goalCropsTargeted;
    }
    if (goalWeedsTargeted != null) {
      _result.goalWeedsTargeted = goalWeedsTargeted;
    }
    if (goalUnknownTargeted != null) {
      _result.goalUnknownTargeted = goalUnknownTargeted;
    }
    if (almanac != null) {
      _result.almanac = almanac;
    }
    if (maxRecommendedMindoo != null) {
      _result.maxRecommendedMindoo = maxRecommendedMindoo;
    }
    if (minItemsForRecommendation != null) {
      _result.minItemsForRecommendation = minItemsForRecommendation;
    }
    if (useWeedCategoriesForWeedLabels != null) {
      _result.useWeedCategoriesForWeedLabels = useWeedCategoriesForWeedLabels;
    }
    if (minRecommendedMindoo != null) {
      _result.minRecommendedMindoo = minRecommendedMindoo;
    }
    if (minRecommendedWeedThreshold != null) {
      _result.minRecommendedWeedThreshold = minRecommendedWeedThreshold;
    }
    if (maxRecommendedWeedThreshold != null) {
      _result.maxRecommendedWeedThreshold = maxRecommendedWeedThreshold;
    }
    if (minRecommendedCropThreshold != null) {
      _result.minRecommendedCropThreshold = minRecommendedCropThreshold;
    }
    if (maxRecommendedCropThreshold != null) {
      _result.maxRecommendedCropThreshold = maxRecommendedCropThreshold;
    }
    if (minDooForRecommendation != null) {
      _result.minDooForRecommendation = minDooForRecommendation;
    }
    if (useOtherAsTiebreaker != null) {
      _result.useOtherAsTiebreaker = useOtherAsTiebreaker;
    }
    if (limitByCropsMissed != null) {
      _result.limitByCropsMissed = limitByCropsMissed;
    }
    if (numberOfCropConfigurations != null) {
      _result.numberOfCropConfigurations = numberOfCropConfigurations;
    }
    return _result;
  }
  factory PlantCaptchaResults.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantCaptchaResults.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantCaptchaResults clone() => PlantCaptchaResults()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantCaptchaResults copyWith(void Function(PlantCaptchaResults) updates) => super.copyWith((message) => updates(message as PlantCaptchaResults)) as PlantCaptchaResults; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaResults create() => PlantCaptchaResults._();
  PlantCaptchaResults createEmptyInstance() => create();
  static $pb.PbList<PlantCaptchaResults> createRepeated() => $pb.PbList<PlantCaptchaResults>();
  @$core.pragma('dart2js:noInline')
  static PlantCaptchaResults getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantCaptchaResults>(create);
  static PlantCaptchaResults? _defaultInstance;

  @$pb.TagNumber(1)
  $63.ModelinatorConfig get currentParameters => $_getN(0);
  @$pb.TagNumber(1)
  set currentParameters($63.ModelinatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCurrentParameters() => $_has(0);
  @$pb.TagNumber(1)
  void clearCurrentParameters() => clearField(1);
  @$pb.TagNumber(1)
  $63.ModelinatorConfig ensureCurrentParameters() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<PlantCaptchaResult> get captchaResults => $_getList(1);

  @$pb.TagNumber(3)
  $core.String get algorithm => $_getSZ(2);
  @$pb.TagNumber(3)
  set algorithm($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAlgorithm() => $_has(2);
  @$pb.TagNumber(3)
  void clearAlgorithm() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get goalCropsTargeted => $_getN(3);
  @$pb.TagNumber(4)
  set goalCropsTargeted($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGoalCropsTargeted() => $_has(3);
  @$pb.TagNumber(4)
  void clearGoalCropsTargeted() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get goalWeedsTargeted => $_getN(4);
  @$pb.TagNumber(5)
  set goalWeedsTargeted($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGoalWeedsTargeted() => $_has(4);
  @$pb.TagNumber(5)
  void clearGoalWeedsTargeted() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get goalUnknownTargeted => $_getN(5);
  @$pb.TagNumber(6)
  set goalUnknownTargeted($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasGoalUnknownTargeted() => $_has(5);
  @$pb.TagNumber(6)
  void clearGoalUnknownTargeted() => clearField(6);

  @$pb.TagNumber(7)
  $63.AlmanacConfig get almanac => $_getN(6);
  @$pb.TagNumber(7)
  set almanac($63.AlmanacConfig v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasAlmanac() => $_has(6);
  @$pb.TagNumber(7)
  void clearAlmanac() => clearField(7);
  @$pb.TagNumber(7)
  $63.AlmanacConfig ensureAlmanac() => $_ensure(6);

  @$pb.TagNumber(8)
  $core.double get maxRecommendedMindoo => $_getN(7);
  @$pb.TagNumber(8)
  set maxRecommendedMindoo($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasMaxRecommendedMindoo() => $_has(7);
  @$pb.TagNumber(8)
  void clearMaxRecommendedMindoo() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get minItemsForRecommendation => $_getIZ(8);
  @$pb.TagNumber(9)
  set minItemsForRecommendation($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasMinItemsForRecommendation() => $_has(8);
  @$pb.TagNumber(9)
  void clearMinItemsForRecommendation() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get useWeedCategoriesForWeedLabels => $_getBF(9);
  @$pb.TagNumber(10)
  set useWeedCategoriesForWeedLabels($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasUseWeedCategoriesForWeedLabels() => $_has(9);
  @$pb.TagNumber(10)
  void clearUseWeedCategoriesForWeedLabels() => clearField(10);

  @$pb.TagNumber(11)
  $core.double get minRecommendedMindoo => $_getN(10);
  @$pb.TagNumber(11)
  set minRecommendedMindoo($core.double v) { $_setFloat(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasMinRecommendedMindoo() => $_has(10);
  @$pb.TagNumber(11)
  void clearMinRecommendedMindoo() => clearField(11);

  @$pb.TagNumber(12)
  $core.double get minRecommendedWeedThreshold => $_getN(11);
  @$pb.TagNumber(12)
  set minRecommendedWeedThreshold($core.double v) { $_setFloat(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasMinRecommendedWeedThreshold() => $_has(11);
  @$pb.TagNumber(12)
  void clearMinRecommendedWeedThreshold() => clearField(12);

  @$pb.TagNumber(13)
  $core.double get maxRecommendedWeedThreshold => $_getN(12);
  @$pb.TagNumber(13)
  set maxRecommendedWeedThreshold($core.double v) { $_setFloat(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasMaxRecommendedWeedThreshold() => $_has(12);
  @$pb.TagNumber(13)
  void clearMaxRecommendedWeedThreshold() => clearField(13);

  @$pb.TagNumber(14)
  $core.double get minRecommendedCropThreshold => $_getN(13);
  @$pb.TagNumber(14)
  set minRecommendedCropThreshold($core.double v) { $_setFloat(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasMinRecommendedCropThreshold() => $_has(13);
  @$pb.TagNumber(14)
  void clearMinRecommendedCropThreshold() => clearField(14);

  @$pb.TagNumber(15)
  $core.double get maxRecommendedCropThreshold => $_getN(14);
  @$pb.TagNumber(15)
  set maxRecommendedCropThreshold($core.double v) { $_setFloat(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasMaxRecommendedCropThreshold() => $_has(14);
  @$pb.TagNumber(15)
  void clearMaxRecommendedCropThreshold() => clearField(15);

  @$pb.TagNumber(16)
  $core.double get minDooForRecommendation => $_getN(15);
  @$pb.TagNumber(16)
  set minDooForRecommendation($core.double v) { $_setFloat(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasMinDooForRecommendation() => $_has(15);
  @$pb.TagNumber(16)
  void clearMinDooForRecommendation() => clearField(16);

  @$pb.TagNumber(17)
  $core.bool get useOtherAsTiebreaker => $_getBF(16);
  @$pb.TagNumber(17)
  set useOtherAsTiebreaker($core.bool v) { $_setBool(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasUseOtherAsTiebreaker() => $_has(16);
  @$pb.TagNumber(17)
  void clearUseOtherAsTiebreaker() => clearField(17);

  @$pb.TagNumber(18)
  $core.bool get limitByCropsMissed => $_getBF(17);
  @$pb.TagNumber(18)
  set limitByCropsMissed($core.bool v) { $_setBool(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasLimitByCropsMissed() => $_has(17);
  @$pb.TagNumber(18)
  void clearLimitByCropsMissed() => clearField(18);

  @$pb.TagNumber(19)
  $core.int get numberOfCropConfigurations => $_getIZ(18);
  @$pb.TagNumber(19)
  set numberOfCropConfigurations($core.int v) { $_setSignedInt32(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasNumberOfCropConfigurations() => $_has(18);
  @$pb.TagNumber(19)
  void clearNumberOfCropConfigurations() => clearField(19);
}

class VeselkaPlantCaptchaResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'VeselkaPlantCaptchaResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$63.ModelinatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'newModelParameters', subBuilder: $63.ModelinatorConfig.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'succeeded')
    ..hasRequiredFields = false
  ;

  VeselkaPlantCaptchaResponse._() : super();
  factory VeselkaPlantCaptchaResponse({
    $63.ModelinatorConfig? newModelParameters,
    $core.bool? succeeded,
  }) {
    final _result = create();
    if (newModelParameters != null) {
      _result.newModelParameters = newModelParameters;
    }
    if (succeeded != null) {
      _result.succeeded = succeeded;
    }
    return _result;
  }
  factory VeselkaPlantCaptchaResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory VeselkaPlantCaptchaResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  VeselkaPlantCaptchaResponse clone() => VeselkaPlantCaptchaResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  VeselkaPlantCaptchaResponse copyWith(void Function(VeselkaPlantCaptchaResponse) updates) => super.copyWith((message) => updates(message as VeselkaPlantCaptchaResponse)) as VeselkaPlantCaptchaResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static VeselkaPlantCaptchaResponse create() => VeselkaPlantCaptchaResponse._();
  VeselkaPlantCaptchaResponse createEmptyInstance() => create();
  static $pb.PbList<VeselkaPlantCaptchaResponse> createRepeated() => $pb.PbList<VeselkaPlantCaptchaResponse>();
  @$core.pragma('dart2js:noInline')
  static VeselkaPlantCaptchaResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<VeselkaPlantCaptchaResponse>(create);
  static VeselkaPlantCaptchaResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $63.ModelinatorConfig get newModelParameters => $_getN(0);
  @$pb.TagNumber(1)
  set newModelParameters($63.ModelinatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasNewModelParameters() => $_has(0);
  @$pb.TagNumber(1)
  void clearNewModelParameters() => clearField(1);
  @$pb.TagNumber(1)
  $63.ModelinatorConfig ensureNewModelParameters() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get succeeded => $_getBF(1);
  @$pb.TagNumber(2)
  set succeeded($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSucceeded() => $_has(1);
  @$pb.TagNumber(2)
  void clearSucceeded() => clearField(2);
}

class GetOriginalModelinatorConfigRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetOriginalModelinatorConfigRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  GetOriginalModelinatorConfigRequest._() : super();
  factory GetOriginalModelinatorConfigRequest({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory GetOriginalModelinatorConfigRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetOriginalModelinatorConfigRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetOriginalModelinatorConfigRequest clone() => GetOriginalModelinatorConfigRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetOriginalModelinatorConfigRequest copyWith(void Function(GetOriginalModelinatorConfigRequest) updates) => super.copyWith((message) => updates(message as GetOriginalModelinatorConfigRequest)) as GetOriginalModelinatorConfigRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetOriginalModelinatorConfigRequest create() => GetOriginalModelinatorConfigRequest._();
  GetOriginalModelinatorConfigRequest createEmptyInstance() => create();
  static $pb.PbList<GetOriginalModelinatorConfigRequest> createRepeated() => $pb.PbList<GetOriginalModelinatorConfigRequest>();
  @$core.pragma('dart2js:noInline')
  static GetOriginalModelinatorConfigRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetOriginalModelinatorConfigRequest>(create);
  static GetOriginalModelinatorConfigRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class GetOriginalModelinatorConfigResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetOriginalModelinatorConfigResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.plant_captcha'), createEmptyInstance: create)
    ..aOM<$63.ModelinatorConfig>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelinatorConfig', subBuilder: $63.ModelinatorConfig.create)
    ..hasRequiredFields = false
  ;

  GetOriginalModelinatorConfigResponse._() : super();
  factory GetOriginalModelinatorConfigResponse({
    $63.ModelinatorConfig? modelinatorConfig,
  }) {
    final _result = create();
    if (modelinatorConfig != null) {
      _result.modelinatorConfig = modelinatorConfig;
    }
    return _result;
  }
  factory GetOriginalModelinatorConfigResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetOriginalModelinatorConfigResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetOriginalModelinatorConfigResponse clone() => GetOriginalModelinatorConfigResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetOriginalModelinatorConfigResponse copyWith(void Function(GetOriginalModelinatorConfigResponse) updates) => super.copyWith((message) => updates(message as GetOriginalModelinatorConfigResponse)) as GetOriginalModelinatorConfigResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetOriginalModelinatorConfigResponse create() => GetOriginalModelinatorConfigResponse._();
  GetOriginalModelinatorConfigResponse createEmptyInstance() => create();
  static $pb.PbList<GetOriginalModelinatorConfigResponse> createRepeated() => $pb.PbList<GetOriginalModelinatorConfigResponse>();
  @$core.pragma('dart2js:noInline')
  static GetOriginalModelinatorConfigResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetOriginalModelinatorConfigResponse>(create);
  static GetOriginalModelinatorConfigResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $63.ModelinatorConfig get modelinatorConfig => $_getN(0);
  @$pb.TagNumber(1)
  set modelinatorConfig($63.ModelinatorConfig v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasModelinatorConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearModelinatorConfig() => clearField(1);
  @$pb.TagNumber(1)
  $63.ModelinatorConfig ensureModelinatorConfig() => $_ensure(0);
}

