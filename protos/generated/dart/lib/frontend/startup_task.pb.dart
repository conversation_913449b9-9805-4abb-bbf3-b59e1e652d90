///
//  Generated code. Do not modify.
//  source: frontend/startup_task.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;
import '../startup_task/startup_task.pb.dart' as $68;

class GetNextTasksResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextTasksResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.startup_task'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<$68.Task>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'tasks', $pb.PbFieldType.PM, subBuilder: $68.Task.create)
    ..hasRequiredFields = false
  ;

  GetNextTasksResponse._() : super();
  factory GetNextTasksResponse({
    $1.Timestamp? ts,
    $core.Iterable<$68.Task>? tasks,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (tasks != null) {
      _result.tasks.addAll(tasks);
    }
    return _result;
  }
  factory GetNextTasksResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextTasksResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextTasksResponse clone() => GetNextTasksResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextTasksResponse copyWith(void Function(GetNextTasksResponse) updates) => super.copyWith((message) => updates(message as GetNextTasksResponse)) as GetNextTasksResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextTasksResponse create() => GetNextTasksResponse._();
  GetNextTasksResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextTasksResponse> createRepeated() => $pb.PbList<GetNextTasksResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextTasksResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextTasksResponse>(create);
  static GetNextTasksResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<$68.Task> get tasks => $_getList(1);
}

class MarkTaskCompleteRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MarkTaskCompleteRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.startup_task'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'taskId')
    ..hasRequiredFields = false
  ;

  MarkTaskCompleteRequest._() : super();
  factory MarkTaskCompleteRequest({
    $core.String? taskId,
  }) {
    final _result = create();
    if (taskId != null) {
      _result.taskId = taskId;
    }
    return _result;
  }
  factory MarkTaskCompleteRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MarkTaskCompleteRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MarkTaskCompleteRequest clone() => MarkTaskCompleteRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MarkTaskCompleteRequest copyWith(void Function(MarkTaskCompleteRequest) updates) => super.copyWith((message) => updates(message as MarkTaskCompleteRequest)) as MarkTaskCompleteRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MarkTaskCompleteRequest create() => MarkTaskCompleteRequest._();
  MarkTaskCompleteRequest createEmptyInstance() => create();
  static $pb.PbList<MarkTaskCompleteRequest> createRepeated() => $pb.PbList<MarkTaskCompleteRequest>();
  @$core.pragma('dart2js:noInline')
  static MarkTaskCompleteRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MarkTaskCompleteRequest>(create);
  static MarkTaskCompleteRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get taskId => $_getSZ(0);
  @$pb.TagNumber(1)
  set taskId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTaskId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTaskId() => clearField(1);
}

class MarkTaskCompleteResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MarkTaskCompleteResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.startup_task'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  MarkTaskCompleteResponse._() : super();
  factory MarkTaskCompleteResponse() => create();
  factory MarkTaskCompleteResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MarkTaskCompleteResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MarkTaskCompleteResponse clone() => MarkTaskCompleteResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MarkTaskCompleteResponse copyWith(void Function(MarkTaskCompleteResponse) updates) => super.copyWith((message) => updates(message as MarkTaskCompleteResponse)) as MarkTaskCompleteResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MarkTaskCompleteResponse create() => MarkTaskCompleteResponse._();
  MarkTaskCompleteResponse createEmptyInstance() => create();
  static $pb.PbList<MarkTaskCompleteResponse> createRepeated() => $pb.PbList<MarkTaskCompleteResponse>();
  @$core.pragma('dart2js:noInline')
  static MarkTaskCompleteResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MarkTaskCompleteResponse>(create);
  static MarkTaskCompleteResponse? _defaultInstance;
}

