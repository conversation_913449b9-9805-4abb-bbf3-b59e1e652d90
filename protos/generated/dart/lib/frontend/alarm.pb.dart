///
//  Generated code. Do not modify.
//  source: frontend/alarm.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'translation.pb.dart' as $62;
import '../util/util.pb.dart' as $1;

import 'alarm.pbenum.dart';

export 'alarm.pbenum.dart';

class AlarmRow extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AlarmRow', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarmCode')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'subsystem')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..e<AlarmLevel>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'level', $pb.PbFieldType.OE, defaultOrMaker: AlarmLevel.AL_UNKNOWN, valueOf: AlarmLevel.valueOf, enumValues: AlarmLevel.values)
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'identifier')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'acknowledged')
    ..e<AlarmImpact>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'impact', $pb.PbFieldType.OE, defaultOrMaker: AlarmImpact.AI_UNKNOWN, valueOf: AlarmImpact.valueOf, enumValues: AlarmImpact.values)
    ..aInt64(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'stopTimestampMs')
    ..aOB(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'autofixAvailable')
    ..aOB(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'autofixAttempted')
    ..a<$core.int>(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'autofixDurationSec', $pb.PbFieldType.OU3)
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'descriptionKey')
    ..pc<$62.TranslationParameter>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'translationParameters', $pb.PbFieldType.PM, subBuilder: $62.TranslationParameter.create)
    ..hasRequiredFields = false
  ;

  AlarmRow._() : super();
  factory AlarmRow({
    $fixnum.Int64? timestampMs,
    $core.String? alarmCode,
    $core.String? subsystem,
    $core.String? description,
    AlarmLevel? level,
    $core.String? identifier,
    $core.bool? acknowledged,
    AlarmImpact? impact,
    $fixnum.Int64? stopTimestampMs,
    $core.bool? autofixAvailable,
    $core.bool? autofixAttempted,
    $core.int? autofixDurationSec,
    $core.String? descriptionKey,
    $core.Iterable<$62.TranslationParameter>? translationParameters,
  }) {
    final _result = create();
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (alarmCode != null) {
      _result.alarmCode = alarmCode;
    }
    if (subsystem != null) {
      _result.subsystem = subsystem;
    }
    if (description != null) {
      _result.description = description;
    }
    if (level != null) {
      _result.level = level;
    }
    if (identifier != null) {
      _result.identifier = identifier;
    }
    if (acknowledged != null) {
      _result.acknowledged = acknowledged;
    }
    if (impact != null) {
      _result.impact = impact;
    }
    if (stopTimestampMs != null) {
      _result.stopTimestampMs = stopTimestampMs;
    }
    if (autofixAvailable != null) {
      _result.autofixAvailable = autofixAvailable;
    }
    if (autofixAttempted != null) {
      _result.autofixAttempted = autofixAttempted;
    }
    if (autofixDurationSec != null) {
      _result.autofixDurationSec = autofixDurationSec;
    }
    if (descriptionKey != null) {
      _result.descriptionKey = descriptionKey;
    }
    if (translationParameters != null) {
      _result.translationParameters.addAll(translationParameters);
    }
    return _result;
  }
  factory AlarmRow.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AlarmRow.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AlarmRow clone() => AlarmRow()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AlarmRow copyWith(void Function(AlarmRow) updates) => super.copyWith((message) => updates(message as AlarmRow)) as AlarmRow; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AlarmRow create() => AlarmRow._();
  AlarmRow createEmptyInstance() => create();
  static $pb.PbList<AlarmRow> createRepeated() => $pb.PbList<AlarmRow>();
  @$core.pragma('dart2js:noInline')
  static AlarmRow getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AlarmRow>(create);
  static AlarmRow? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get timestampMs => $_getI64(0);
  @$pb.TagNumber(1)
  set timestampMs($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestampMs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestampMs() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get alarmCode => $_getSZ(1);
  @$pb.TagNumber(2)
  set alarmCode($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAlarmCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearAlarmCode() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get subsystem => $_getSZ(2);
  @$pb.TagNumber(3)
  set subsystem($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSubsystem() => $_has(2);
  @$pb.TagNumber(3)
  void clearSubsystem() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get description => $_getSZ(3);
  @$pb.TagNumber(4)
  set description($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDescription() => $_has(3);
  @$pb.TagNumber(4)
  void clearDescription() => clearField(4);

  @$pb.TagNumber(5)
  AlarmLevel get level => $_getN(4);
  @$pb.TagNumber(5)
  set level(AlarmLevel v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasLevel() => $_has(4);
  @$pb.TagNumber(5)
  void clearLevel() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get identifier => $_getSZ(5);
  @$pb.TagNumber(6)
  set identifier($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasIdentifier() => $_has(5);
  @$pb.TagNumber(6)
  void clearIdentifier() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get acknowledged => $_getBF(6);
  @$pb.TagNumber(7)
  set acknowledged($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasAcknowledged() => $_has(6);
  @$pb.TagNumber(7)
  void clearAcknowledged() => clearField(7);

  @$pb.TagNumber(8)
  AlarmImpact get impact => $_getN(7);
  @$pb.TagNumber(8)
  set impact(AlarmImpact v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasImpact() => $_has(7);
  @$pb.TagNumber(8)
  void clearImpact() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get stopTimestampMs => $_getI64(8);
  @$pb.TagNumber(9)
  set stopTimestampMs($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasStopTimestampMs() => $_has(8);
  @$pb.TagNumber(9)
  void clearStopTimestampMs() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get autofixAvailable => $_getBF(9);
  @$pb.TagNumber(10)
  set autofixAvailable($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasAutofixAvailable() => $_has(9);
  @$pb.TagNumber(10)
  void clearAutofixAvailable() => clearField(10);

  @$pb.TagNumber(11)
  $core.bool get autofixAttempted => $_getBF(10);
  @$pb.TagNumber(11)
  set autofixAttempted($core.bool v) { $_setBool(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasAutofixAttempted() => $_has(10);
  @$pb.TagNumber(11)
  void clearAutofixAttempted() => clearField(11);

  @$pb.TagNumber(12)
  $core.int get autofixDurationSec => $_getIZ(11);
  @$pb.TagNumber(12)
  set autofixDurationSec($core.int v) { $_setUnsignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasAutofixDurationSec() => $_has(11);
  @$pb.TagNumber(12)
  void clearAutofixDurationSec() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get descriptionKey => $_getSZ(12);
  @$pb.TagNumber(13)
  set descriptionKey($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDescriptionKey() => $_has(12);
  @$pb.TagNumber(13)
  void clearDescriptionKey() => clearField(13);

  @$pb.TagNumber(14)
  $core.List<$62.TranslationParameter> get translationParameters => $_getList(13);
}

class AlarmTable extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AlarmTable', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<AlarmRow>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: AlarmRow.create)
    ..hasRequiredFields = false
  ;

  AlarmTable._() : super();
  factory AlarmTable({
    $1.Timestamp? ts,
    $core.Iterable<AlarmRow>? alarms,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (alarms != null) {
      _result.alarms.addAll(alarms);
    }
    return _result;
  }
  factory AlarmTable.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AlarmTable.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AlarmTable clone() => AlarmTable()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AlarmTable copyWith(void Function(AlarmTable) updates) => super.copyWith((message) => updates(message as AlarmTable)) as AlarmTable; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AlarmTable create() => AlarmTable._();
  AlarmTable createEmptyInstance() => create();
  static $pb.PbList<AlarmTable> createRepeated() => $pb.PbList<AlarmTable>();
  @$core.pragma('dart2js:noInline')
  static AlarmTable getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AlarmTable>(create);
  static AlarmTable? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<AlarmRow> get alarms => $_getList(1);
}

class AlarmCount extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AlarmCount', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'count', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  AlarmCount._() : super();
  factory AlarmCount({
    $1.Timestamp? ts,
    $core.int? count,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (count != null) {
      _result.count = count;
    }
    return _result;
  }
  factory AlarmCount.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AlarmCount.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AlarmCount clone() => AlarmCount()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AlarmCount copyWith(void Function(AlarmCount) updates) => super.copyWith((message) => updates(message as AlarmCount)) as AlarmCount; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AlarmCount create() => AlarmCount._();
  AlarmCount createEmptyInstance() => create();
  static $pb.PbList<AlarmCount> createRepeated() => $pb.PbList<AlarmCount>();
  @$core.pragma('dart2js:noInline')
  static AlarmCount getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AlarmCount>(create);
  static AlarmCount? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get count => $_getIZ(1);
  @$pb.TagNumber(2)
  set count($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCount() => $_has(1);
  @$pb.TagNumber(2)
  void clearCount() => clearField(2);
}

class AcknowledgeRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AcknowledgeRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'identifier')
    ..hasRequiredFields = false
  ;

  AcknowledgeRequest._() : super();
  factory AcknowledgeRequest({
    $core.String? identifier,
  }) {
    final _result = create();
    if (identifier != null) {
      _result.identifier = identifier;
    }
    return _result;
  }
  factory AcknowledgeRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AcknowledgeRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AcknowledgeRequest clone() => AcknowledgeRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AcknowledgeRequest copyWith(void Function(AcknowledgeRequest) updates) => super.copyWith((message) => updates(message as AcknowledgeRequest)) as AcknowledgeRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AcknowledgeRequest create() => AcknowledgeRequest._();
  AcknowledgeRequest createEmptyInstance() => create();
  static $pb.PbList<AcknowledgeRequest> createRepeated() => $pb.PbList<AcknowledgeRequest>();
  @$core.pragma('dart2js:noInline')
  static AcknowledgeRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AcknowledgeRequest>(create);
  static AcknowledgeRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get identifier => $_getSZ(0);
  @$pb.TagNumber(1)
  set identifier($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasIdentifier() => $_has(0);
  @$pb.TagNumber(1)
  void clearIdentifier() => clearField(1);
}

class GetNextAlarmLogRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAlarmLogRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fromIdx', $pb.PbFieldType.O3)
    ..a<$core.int>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'toIdx', $pb.PbFieldType.O3)
    ..aOM<$1.Timestamp>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'visibleOnly')
    ..hasRequiredFields = false
  ;

  GetNextAlarmLogRequest._() : super();
  factory GetNextAlarmLogRequest({
    $core.int? fromIdx,
    $core.int? toIdx,
    $1.Timestamp? ts,
    $core.bool? visibleOnly,
  }) {
    final _result = create();
    if (fromIdx != null) {
      _result.fromIdx = fromIdx;
    }
    if (toIdx != null) {
      _result.toIdx = toIdx;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    if (visibleOnly != null) {
      _result.visibleOnly = visibleOnly;
    }
    return _result;
  }
  factory GetNextAlarmLogRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAlarmLogRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogRequest clone() => GetNextAlarmLogRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogRequest copyWith(void Function(GetNextAlarmLogRequest) updates) => super.copyWith((message) => updates(message as GetNextAlarmLogRequest)) as GetNextAlarmLogRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogRequest create() => GetNextAlarmLogRequest._();
  GetNextAlarmLogRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextAlarmLogRequest> createRepeated() => $pb.PbList<GetNextAlarmLogRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAlarmLogRequest>(create);
  static GetNextAlarmLogRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get fromIdx => $_getIZ(0);
  @$pb.TagNumber(1)
  set fromIdx($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasFromIdx() => $_has(0);
  @$pb.TagNumber(1)
  void clearFromIdx() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get toIdx => $_getIZ(1);
  @$pb.TagNumber(2)
  set toIdx($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasToIdx() => $_has(1);
  @$pb.TagNumber(2)
  void clearToIdx() => clearField(2);

  @$pb.TagNumber(3)
  $1.Timestamp get ts => $_getN(2);
  @$pb.TagNumber(3)
  set ts($1.Timestamp v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasTs() => $_has(2);
  @$pb.TagNumber(3)
  void clearTs() => clearField(3);
  @$pb.TagNumber(3)
  $1.Timestamp ensureTs() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.bool get visibleOnly => $_getBF(3);
  @$pb.TagNumber(4)
  set visibleOnly($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasVisibleOnly() => $_has(3);
  @$pb.TagNumber(4)
  void clearVisibleOnly() => clearField(4);
}

class GetNextAlarmLogResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAlarmLogResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..pc<AlarmRow>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: AlarmRow.create)
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextAlarmLogResponse._() : super();
  factory GetNextAlarmLogResponse({
    $core.Iterable<AlarmRow>? alarms,
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (alarms != null) {
      _result.alarms.addAll(alarms);
    }
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextAlarmLogResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAlarmLogResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogResponse clone() => GetNextAlarmLogResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogResponse copyWith(void Function(GetNextAlarmLogResponse) updates) => super.copyWith((message) => updates(message as GetNextAlarmLogResponse)) as GetNextAlarmLogResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogResponse create() => GetNextAlarmLogResponse._();
  GetNextAlarmLogResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextAlarmLogResponse> createRepeated() => $pb.PbList<GetNextAlarmLogResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAlarmLogResponse>(create);
  static GetNextAlarmLogResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<AlarmRow> get alarms => $_getList(0);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);
}

class GetNextAlarmLogCountRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAlarmLogCountRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'visibleOnly')
    ..hasRequiredFields = false
  ;

  GetNextAlarmLogCountRequest._() : super();
  factory GetNextAlarmLogCountRequest({
    $1.Timestamp? ts,
    $core.bool? visibleOnly,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (visibleOnly != null) {
      _result.visibleOnly = visibleOnly;
    }
    return _result;
  }
  factory GetNextAlarmLogCountRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAlarmLogCountRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogCountRequest clone() => GetNextAlarmLogCountRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogCountRequest copyWith(void Function(GetNextAlarmLogCountRequest) updates) => super.copyWith((message) => updates(message as GetNextAlarmLogCountRequest)) as GetNextAlarmLogCountRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogCountRequest create() => GetNextAlarmLogCountRequest._();
  GetNextAlarmLogCountRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextAlarmLogCountRequest> createRepeated() => $pb.PbList<GetNextAlarmLogCountRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogCountRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAlarmLogCountRequest>(create);
  static GetNextAlarmLogCountRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get visibleOnly => $_getBF(1);
  @$pb.TagNumber(2)
  set visibleOnly($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasVisibleOnly() => $_has(1);
  @$pb.TagNumber(2)
  void clearVisibleOnly() => clearField(2);
}

class GetNextAlarmLogCountResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAlarmLogCountResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..a<$core.int>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numAlarms', $pb.PbFieldType.O3)
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextAlarmLogCountResponse._() : super();
  factory GetNextAlarmLogCountResponse({
    $core.int? numAlarms,
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (numAlarms != null) {
      _result.numAlarms = numAlarms;
    }
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextAlarmLogCountResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAlarmLogCountResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogCountResponse clone() => GetNextAlarmLogCountResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAlarmLogCountResponse copyWith(void Function(GetNextAlarmLogCountResponse) updates) => super.copyWith((message) => updates(message as GetNextAlarmLogCountResponse)) as GetNextAlarmLogCountResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogCountResponse create() => GetNextAlarmLogCountResponse._();
  GetNextAlarmLogCountResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextAlarmLogCountResponse> createRepeated() => $pb.PbList<GetNextAlarmLogCountResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextAlarmLogCountResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAlarmLogCountResponse>(create);
  static GetNextAlarmLogCountResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get numAlarms => $_getIZ(0);
  @$pb.TagNumber(1)
  set numAlarms($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNumAlarms() => $_has(0);
  @$pb.TagNumber(1)
  void clearNumAlarms() => clearField(1);

  @$pb.TagNumber(2)
  $1.Timestamp get ts => $_getN(1);
  @$pb.TagNumber(2)
  set ts($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTs() => $_has(1);
  @$pb.TagNumber(2)
  void clearTs() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTs() => $_ensure(1);
}

class AttemptAutofixAlarmRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AttemptAutofixAlarmRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'identifier')
    ..hasRequiredFields = false
  ;

  AttemptAutofixAlarmRequest._() : super();
  factory AttemptAutofixAlarmRequest({
    $core.String? identifier,
  }) {
    final _result = create();
    if (identifier != null) {
      _result.identifier = identifier;
    }
    return _result;
  }
  factory AttemptAutofixAlarmRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AttemptAutofixAlarmRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AttemptAutofixAlarmRequest clone() => AttemptAutofixAlarmRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AttemptAutofixAlarmRequest copyWith(void Function(AttemptAutofixAlarmRequest) updates) => super.copyWith((message) => updates(message as AttemptAutofixAlarmRequest)) as AttemptAutofixAlarmRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AttemptAutofixAlarmRequest create() => AttemptAutofixAlarmRequest._();
  AttemptAutofixAlarmRequest createEmptyInstance() => create();
  static $pb.PbList<AttemptAutofixAlarmRequest> createRepeated() => $pb.PbList<AttemptAutofixAlarmRequest>();
  @$core.pragma('dart2js:noInline')
  static AttemptAutofixAlarmRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AttemptAutofixAlarmRequest>(create);
  static AttemptAutofixAlarmRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get identifier => $_getSZ(0);
  @$pb.TagNumber(1)
  set identifier($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasIdentifier() => $_has(0);
  @$pb.TagNumber(1)
  void clearIdentifier() => clearField(1);
}

class GetNextAutofixAlarmStatusRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAutofixAlarmStatusRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextAutofixAlarmStatusRequest._() : super();
  factory GetNextAutofixAlarmStatusRequest({
    $1.Timestamp? ts,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    return _result;
  }
  factory GetNextAutofixAlarmStatusRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAutofixAlarmStatusRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAutofixAlarmStatusRequest clone() => GetNextAutofixAlarmStatusRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAutofixAlarmStatusRequest copyWith(void Function(GetNextAutofixAlarmStatusRequest) updates) => super.copyWith((message) => updates(message as GetNextAutofixAlarmStatusRequest)) as GetNextAutofixAlarmStatusRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAutofixAlarmStatusRequest create() => GetNextAutofixAlarmStatusRequest._();
  GetNextAutofixAlarmStatusRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextAutofixAlarmStatusRequest> createRepeated() => $pb.PbList<GetNextAutofixAlarmStatusRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextAutofixAlarmStatusRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAutofixAlarmStatusRequest>(create);
  static GetNextAutofixAlarmStatusRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);
}

class GetNextAutofixAlarmStatusResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAutofixAlarmStatusResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.alarm'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'completed')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'errorMessage')
    ..hasRequiredFields = false
  ;

  GetNextAutofixAlarmStatusResponse._() : super();
  factory GetNextAutofixAlarmStatusResponse({
    $1.Timestamp? ts,
    $core.bool? completed,
    $core.String? errorMessage,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (completed != null) {
      _result.completed = completed;
    }
    if (errorMessage != null) {
      _result.errorMessage = errorMessage;
    }
    return _result;
  }
  factory GetNextAutofixAlarmStatusResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAutofixAlarmStatusResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAutofixAlarmStatusResponse clone() => GetNextAutofixAlarmStatusResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAutofixAlarmStatusResponse copyWith(void Function(GetNextAutofixAlarmStatusResponse) updates) => super.copyWith((message) => updates(message as GetNextAutofixAlarmStatusResponse)) as GetNextAutofixAlarmStatusResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAutofixAlarmStatusResponse create() => GetNextAutofixAlarmStatusResponse._();
  GetNextAutofixAlarmStatusResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextAutofixAlarmStatusResponse> createRepeated() => $pb.PbList<GetNextAutofixAlarmStatusResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextAutofixAlarmStatusResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAutofixAlarmStatusResponse>(create);
  static GetNextAutofixAlarmStatusResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get completed => $_getBF(1);
  @$pb.TagNumber(2)
  set completed($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCompleted() => $_has(1);
  @$pb.TagNumber(2)
  void clearCompleted() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get errorMessage => $_getSZ(2);
  @$pb.TagNumber(3)
  set errorMessage($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasErrorMessage() => $_has(2);
  @$pb.TagNumber(3)
  void clearErrorMessage() => clearField(3);
}

