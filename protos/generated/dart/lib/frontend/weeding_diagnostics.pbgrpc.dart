///
//  Generated code. Do not modify.
//  source: frontend/weeding_diagnostics.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'weeding_diagnostics.pb.dart' as $22;
import '../util/util.pb.dart' as $1;
import '../weed_tracking/weed_tracking.pb.dart' as $4;
export 'weeding_diagnostics.pb.dart';

class WeedingDiagnosticsServiceClient extends $grpc.Client {
  static final _$recordWeedingDiagnostics = $grpc.ClientMethod<
          $22.RecordWeedingDiagnosticsRequest, $1.Empty>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/RecordWeedingDiagnostics',
      ($22.RecordWeedingDiagnosticsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getCurrentTrajectories = $grpc.ClientMethod<
          $22.GetCurrentTrajectoriesRequest, $4.DiagnosticsSnapshot>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetCurrentTrajectories',
      ($22.GetCurrentTrajectoriesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $4.DiagnosticsSnapshot.fromBuffer(value));
  static final _$getRecordingsList = $grpc.ClientMethod<
          $22.GetRecordingsListRequest, $22.GetRecordingsListResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRecordingsList',
      ($22.GetRecordingsListRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetRecordingsListResponse.fromBuffer(value));
  static final _$openRecording = $grpc.ClientMethod<$22.OpenRecordingRequest,
          $22.OpenRecordingResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/OpenRecording',
      ($22.OpenRecordingRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.OpenRecordingResponse.fromBuffer(value));
  static final _$getSnapshot = $grpc.ClientMethod<$22.GetSnapshotRequest,
          $22.GetSnapshotResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetSnapshot',
      ($22.GetSnapshotRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetSnapshotResponse.fromBuffer(value));
  static final _$deleteRecording = $grpc.ClientMethod<
          $22.DeleteRecordingRequest, $1.Empty>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/DeleteRecording',
      ($22.DeleteRecordingRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getTrajectoryData = $grpc.ClientMethod<
          $22.GetTrajectoryDataRequest, $22.TrajectoryData>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryData',
      ($22.GetTrajectoryDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $22.TrajectoryData.fromBuffer(value));
  static final _$getTrajectoryPredictImage = $grpc.ClientMethod<
          $22.GetTrajectoryPredictImageRequest, $22.ImageChunk>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryPredictImage',
      ($22.GetTrajectoryPredictImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $22.ImageChunk.fromBuffer(value));
  static final _$getTrajectoryTargetImage = $grpc.ClientMethod<
          $22.GetTrajectoryTargetImageRequest, $22.ImageChunk>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetTrajectoryTargetImage',
      ($22.GetTrajectoryTargetImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $22.ImageChunk.fromBuffer(value));
  static final _$getPredictImageMetadata = $grpc.ClientMethod<
          $22.GetPredictImageMetadataRequest,
          $22.GetPredictImageMetadataResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImageMetadata',
      ($22.GetPredictImageMetadataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetPredictImageMetadataResponse.fromBuffer(value));
  static final _$getPredictImage = $grpc.ClientMethod<
          $22.GetPredictImageRequest, $22.ImageChunk>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetPredictImage',
      ($22.GetPredictImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $22.ImageChunk.fromBuffer(value));
  static final _$startUpload = $grpc.ClientMethod<$22.StartUploadRequest,
          $1.Empty>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/StartUpload',
      ($22.StartUploadRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getNextUploadState = $grpc.ClientMethod<
          $22.GetNextUploadStateRequest, $22.GetNextUploadStateResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetNextUploadState',
      ($22.GetNextUploadStateRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetNextUploadStateResponse.fromBuffer(value));
  static final _$getDeepweedPredictionsCount = $grpc.ClientMethod<
          $22.GetDeepweedPredictionsCountRequest,
          $22.GetDeepweedPredictionsCountResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictionsCount',
      ($22.GetDeepweedPredictionsCountRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetDeepweedPredictionsCountResponse.fromBuffer(value));
  static final _$getDeepweedPredictions = $grpc.ClientMethod<
          $22.GetDeepweedPredictionsRequest,
          $22.GetDeepweedPredictionsResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetDeepweedPredictions',
      ($22.GetDeepweedPredictionsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetDeepweedPredictionsResponse.fromBuffer(value));
  static final _$findTrajectory = $grpc.ClientMethod<$22.FindTrajectoryRequest,
          $22.FindTrajectoryResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/FindTrajectory',
      ($22.FindTrajectoryRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.FindTrajectoryResponse.fromBuffer(value));
  static final _$getRotaryTicks = $grpc.ClientMethod<$22.GetRotaryTicksRequest,
          $22.GetRotaryTicksResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetRotaryTicks',
      ($22.GetRotaryTicksRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetRotaryTicksResponse.fromBuffer(value));
  static final _$snapshotPredictImages = $grpc.ClientMethod<
          $22.SnapshotPredictImagesRequest, $22.SnapshotPredictImagesResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/SnapshotPredictImages',
      ($22.SnapshotPredictImagesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.SnapshotPredictImagesResponse.fromBuffer(value));
  static final _$getChipForPredictImage = $grpc.ClientMethod<
          $22.GetChipForPredictImageRequest,
          $22.GetChipForPredictImageResponse>(
      '/carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService/GetChipForPredictImage',
      ($22.GetChipForPredictImageRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $22.GetChipForPredictImageResponse.fromBuffer(value));

  WeedingDiagnosticsServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> recordWeedingDiagnostics(
      $22.RecordWeedingDiagnosticsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$recordWeedingDiagnostics, request,
        options: options);
  }

  $grpc.ResponseFuture<$4.DiagnosticsSnapshot> getCurrentTrajectories(
      $22.GetCurrentTrajectoriesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getCurrentTrajectories, request,
        options: options);
  }

  $grpc.ResponseFuture<$22.GetRecordingsListResponse> getRecordingsList(
      $22.GetRecordingsListRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getRecordingsList, request, options: options);
  }

  $grpc.ResponseFuture<$22.OpenRecordingResponse> openRecording(
      $22.OpenRecordingRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$openRecording, request, options: options);
  }

  $grpc.ResponseFuture<$22.GetSnapshotResponse> getSnapshot(
      $22.GetSnapshotRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getSnapshot, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> deleteRecording(
      $22.DeleteRecordingRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteRecording, request, options: options);
  }

  $grpc.ResponseFuture<$22.TrajectoryData> getTrajectoryData(
      $22.GetTrajectoryDataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTrajectoryData, request, options: options);
  }

  $grpc.ResponseStream<$22.ImageChunk> getTrajectoryPredictImage(
      $22.GetTrajectoryPredictImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(
        _$getTrajectoryPredictImage, $async.Stream.fromIterable([request]),
        options: options);
  }

  $grpc.ResponseStream<$22.ImageChunk> getTrajectoryTargetImage(
      $22.GetTrajectoryTargetImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(
        _$getTrajectoryTargetImage, $async.Stream.fromIterable([request]),
        options: options);
  }

  $grpc.ResponseFuture<$22.GetPredictImageMetadataResponse>
      getPredictImageMetadata($22.GetPredictImageMetadataRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getPredictImageMetadata, request,
        options: options);
  }

  $grpc.ResponseStream<$22.ImageChunk> getPredictImage(
      $22.GetPredictImageRequest request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(
        _$getPredictImage, $async.Stream.fromIterable([request]),
        options: options);
  }

  $grpc.ResponseFuture<$1.Empty> startUpload($22.StartUploadRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$startUpload, request, options: options);
  }

  $grpc.ResponseFuture<$22.GetNextUploadStateResponse> getNextUploadState(
      $22.GetNextUploadStateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getNextUploadState, request, options: options);
  }

  $grpc.ResponseFuture<$22.GetDeepweedPredictionsCountResponse>
      getDeepweedPredictionsCount(
          $22.GetDeepweedPredictionsCountRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDeepweedPredictionsCount, request,
        options: options);
  }

  $grpc.ResponseFuture<$22.GetDeepweedPredictionsResponse>
      getDeepweedPredictions($22.GetDeepweedPredictionsRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getDeepweedPredictions, request,
        options: options);
  }

  $grpc.ResponseFuture<$22.FindTrajectoryResponse> findTrajectory(
      $22.FindTrajectoryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$findTrajectory, request, options: options);
  }

  $grpc.ResponseFuture<$22.GetRotaryTicksResponse> getRotaryTicks(
      $22.GetRotaryTicksRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getRotaryTicks, request, options: options);
  }

  $grpc.ResponseFuture<$22.SnapshotPredictImagesResponse> snapshotPredictImages(
      $22.SnapshotPredictImagesRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$snapshotPredictImages, request, options: options);
  }

  $grpc.ResponseFuture<$22.GetChipForPredictImageResponse>
      getChipForPredictImage($22.GetChipForPredictImageRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getChipForPredictImage, request,
        options: options);
  }
}

abstract class WeedingDiagnosticsServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.frontend.weeding_diagnostics.WeedingDiagnosticsService';

  WeedingDiagnosticsServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$22.RecordWeedingDiagnosticsRequest, $1.Empty>(
            'RecordWeedingDiagnostics',
            recordWeedingDiagnostics_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $22.RecordWeedingDiagnosticsRequest.fromBuffer(value),
            ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetCurrentTrajectoriesRequest,
            $4.DiagnosticsSnapshot>(
        'GetCurrentTrajectories',
        getCurrentTrajectories_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetCurrentTrajectoriesRequest.fromBuffer(value),
        ($4.DiagnosticsSnapshot value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetRecordingsListRequest,
            $22.GetRecordingsListResponse>(
        'GetRecordingsList',
        getRecordingsList_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetRecordingsListRequest.fromBuffer(value),
        ($22.GetRecordingsListResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.OpenRecordingRequest,
            $22.OpenRecordingResponse>(
        'OpenRecording',
        openRecording_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.OpenRecordingRequest.fromBuffer(value),
        ($22.OpenRecordingResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$22.GetSnapshotRequest, $22.GetSnapshotResponse>(
            'GetSnapshot',
            getSnapshot_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $22.GetSnapshotRequest.fromBuffer(value),
            ($22.GetSnapshotResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.DeleteRecordingRequest, $1.Empty>(
        'DeleteRecording',
        deleteRecording_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.DeleteRecordingRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$22.GetTrajectoryDataRequest, $22.TrajectoryData>(
            'GetTrajectoryData',
            getTrajectoryData_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $22.GetTrajectoryDataRequest.fromBuffer(value),
            ($22.TrajectoryData value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetTrajectoryPredictImageRequest,
            $22.ImageChunk>(
        'GetTrajectoryPredictImage',
        getTrajectoryPredictImage_Pre,
        false,
        true,
        ($core.List<$core.int> value) =>
            $22.GetTrajectoryPredictImageRequest.fromBuffer(value),
        ($22.ImageChunk value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetTrajectoryTargetImageRequest,
            $22.ImageChunk>(
        'GetTrajectoryTargetImage',
        getTrajectoryTargetImage_Pre,
        false,
        true,
        ($core.List<$core.int> value) =>
            $22.GetTrajectoryTargetImageRequest.fromBuffer(value),
        ($22.ImageChunk value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetPredictImageMetadataRequest,
            $22.GetPredictImageMetadataResponse>(
        'GetPredictImageMetadata',
        getPredictImageMetadata_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetPredictImageMetadataRequest.fromBuffer(value),
        ($22.GetPredictImageMetadataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetPredictImageRequest, $22.ImageChunk>(
        'GetPredictImage',
        getPredictImage_Pre,
        false,
        true,
        ($core.List<$core.int> value) =>
            $22.GetPredictImageRequest.fromBuffer(value),
        ($22.ImageChunk value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.StartUploadRequest, $1.Empty>(
        'StartUpload',
        startUpload_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.StartUploadRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetNextUploadStateRequest,
            $22.GetNextUploadStateResponse>(
        'GetNextUploadState',
        getNextUploadState_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetNextUploadStateRequest.fromBuffer(value),
        ($22.GetNextUploadStateResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetDeepweedPredictionsCountRequest,
            $22.GetDeepweedPredictionsCountResponse>(
        'GetDeepweedPredictionsCount',
        getDeepweedPredictionsCount_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetDeepweedPredictionsCountRequest.fromBuffer(value),
        ($22.GetDeepweedPredictionsCountResponse value) =>
            value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetDeepweedPredictionsRequest,
            $22.GetDeepweedPredictionsResponse>(
        'GetDeepweedPredictions',
        getDeepweedPredictions_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetDeepweedPredictionsRequest.fromBuffer(value),
        ($22.GetDeepweedPredictionsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.FindTrajectoryRequest,
            $22.FindTrajectoryResponse>(
        'FindTrajectory',
        findTrajectory_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.FindTrajectoryRequest.fromBuffer(value),
        ($22.FindTrajectoryResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetRotaryTicksRequest,
            $22.GetRotaryTicksResponse>(
        'GetRotaryTicks',
        getRotaryTicks_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetRotaryTicksRequest.fromBuffer(value),
        ($22.GetRotaryTicksResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.SnapshotPredictImagesRequest,
            $22.SnapshotPredictImagesResponse>(
        'SnapshotPredictImages',
        snapshotPredictImages_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.SnapshotPredictImagesRequest.fromBuffer(value),
        ($22.SnapshotPredictImagesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$22.GetChipForPredictImageRequest,
            $22.GetChipForPredictImageResponse>(
        'GetChipForPredictImage',
        getChipForPredictImage_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $22.GetChipForPredictImageRequest.fromBuffer(value),
        ($22.GetChipForPredictImageResponse value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> recordWeedingDiagnostics_Pre($grpc.ServiceCall call,
      $async.Future<$22.RecordWeedingDiagnosticsRequest> request) async {
    return recordWeedingDiagnostics(call, await request);
  }

  $async.Future<$4.DiagnosticsSnapshot> getCurrentTrajectories_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetCurrentTrajectoriesRequest> request) async {
    return getCurrentTrajectories(call, await request);
  }

  $async.Future<$22.GetRecordingsListResponse> getRecordingsList_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetRecordingsListRequest> request) async {
    return getRecordingsList(call, await request);
  }

  $async.Future<$22.OpenRecordingResponse> openRecording_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.OpenRecordingRequest> request) async {
    return openRecording(call, await request);
  }

  $async.Future<$22.GetSnapshotResponse> getSnapshot_Pre($grpc.ServiceCall call,
      $async.Future<$22.GetSnapshotRequest> request) async {
    return getSnapshot(call, await request);
  }

  $async.Future<$1.Empty> deleteRecording_Pre($grpc.ServiceCall call,
      $async.Future<$22.DeleteRecordingRequest> request) async {
    return deleteRecording(call, await request);
  }

  $async.Future<$22.TrajectoryData> getTrajectoryData_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetTrajectoryDataRequest> request) async {
    return getTrajectoryData(call, await request);
  }

  $async.Stream<$22.ImageChunk> getTrajectoryPredictImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetTrajectoryPredictImageRequest> request) async* {
    yield* getTrajectoryPredictImage(call, await request);
  }

  $async.Stream<$22.ImageChunk> getTrajectoryTargetImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetTrajectoryTargetImageRequest> request) async* {
    yield* getTrajectoryTargetImage(call, await request);
  }

  $async.Future<$22.GetPredictImageMetadataResponse>
      getPredictImageMetadata_Pre($grpc.ServiceCall call,
          $async.Future<$22.GetPredictImageMetadataRequest> request) async {
    return getPredictImageMetadata(call, await request);
  }

  $async.Stream<$22.ImageChunk> getPredictImage_Pre($grpc.ServiceCall call,
      $async.Future<$22.GetPredictImageRequest> request) async* {
    yield* getPredictImage(call, await request);
  }

  $async.Future<$1.Empty> startUpload_Pre($grpc.ServiceCall call,
      $async.Future<$22.StartUploadRequest> request) async {
    return startUpload(call, await request);
  }

  $async.Future<$22.GetNextUploadStateResponse> getNextUploadState_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetNextUploadStateRequest> request) async {
    return getNextUploadState(call, await request);
  }

  $async.Future<$22.GetDeepweedPredictionsCountResponse>
      getDeepweedPredictionsCount_Pre($grpc.ServiceCall call,
          $async.Future<$22.GetDeepweedPredictionsCountRequest> request) async {
    return getDeepweedPredictionsCount(call, await request);
  }

  $async.Future<$22.GetDeepweedPredictionsResponse> getDeepweedPredictions_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetDeepweedPredictionsRequest> request) async {
    return getDeepweedPredictions(call, await request);
  }

  $async.Future<$22.FindTrajectoryResponse> findTrajectory_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.FindTrajectoryRequest> request) async {
    return findTrajectory(call, await request);
  }

  $async.Future<$22.GetRotaryTicksResponse> getRotaryTicks_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetRotaryTicksRequest> request) async {
    return getRotaryTicks(call, await request);
  }

  $async.Future<$22.SnapshotPredictImagesResponse> snapshotPredictImages_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.SnapshotPredictImagesRequest> request) async {
    return snapshotPredictImages(call, await request);
  }

  $async.Future<$22.GetChipForPredictImageResponse> getChipForPredictImage_Pre(
      $grpc.ServiceCall call,
      $async.Future<$22.GetChipForPredictImageRequest> request) async {
    return getChipForPredictImage(call, await request);
  }

  $async.Future<$1.Empty> recordWeedingDiagnostics(
      $grpc.ServiceCall call, $22.RecordWeedingDiagnosticsRequest request);
  $async.Future<$4.DiagnosticsSnapshot> getCurrentTrajectories(
      $grpc.ServiceCall call, $22.GetCurrentTrajectoriesRequest request);
  $async.Future<$22.GetRecordingsListResponse> getRecordingsList(
      $grpc.ServiceCall call, $22.GetRecordingsListRequest request);
  $async.Future<$22.OpenRecordingResponse> openRecording(
      $grpc.ServiceCall call, $22.OpenRecordingRequest request);
  $async.Future<$22.GetSnapshotResponse> getSnapshot(
      $grpc.ServiceCall call, $22.GetSnapshotRequest request);
  $async.Future<$1.Empty> deleteRecording(
      $grpc.ServiceCall call, $22.DeleteRecordingRequest request);
  $async.Future<$22.TrajectoryData> getTrajectoryData(
      $grpc.ServiceCall call, $22.GetTrajectoryDataRequest request);
  $async.Stream<$22.ImageChunk> getTrajectoryPredictImage(
      $grpc.ServiceCall call, $22.GetTrajectoryPredictImageRequest request);
  $async.Stream<$22.ImageChunk> getTrajectoryTargetImage(
      $grpc.ServiceCall call, $22.GetTrajectoryTargetImageRequest request);
  $async.Future<$22.GetPredictImageMetadataResponse> getPredictImageMetadata(
      $grpc.ServiceCall call, $22.GetPredictImageMetadataRequest request);
  $async.Stream<$22.ImageChunk> getPredictImage(
      $grpc.ServiceCall call, $22.GetPredictImageRequest request);
  $async.Future<$1.Empty> startUpload(
      $grpc.ServiceCall call, $22.StartUploadRequest request);
  $async.Future<$22.GetNextUploadStateResponse> getNextUploadState(
      $grpc.ServiceCall call, $22.GetNextUploadStateRequest request);
  $async.Future<$22.GetDeepweedPredictionsCountResponse>
      getDeepweedPredictionsCount($grpc.ServiceCall call,
          $22.GetDeepweedPredictionsCountRequest request);
  $async.Future<$22.GetDeepweedPredictionsResponse> getDeepweedPredictions(
      $grpc.ServiceCall call, $22.GetDeepweedPredictionsRequest request);
  $async.Future<$22.FindTrajectoryResponse> findTrajectory(
      $grpc.ServiceCall call, $22.FindTrajectoryRequest request);
  $async.Future<$22.GetRotaryTicksResponse> getRotaryTicks(
      $grpc.ServiceCall call, $22.GetRotaryTicksRequest request);
  $async.Future<$22.SnapshotPredictImagesResponse> snapshotPredictImages(
      $grpc.ServiceCall call, $22.SnapshotPredictImagesRequest request);
  $async.Future<$22.GetChipForPredictImageResponse> getChipForPredictImage(
      $grpc.ServiceCall call, $22.GetChipForPredictImageRequest request);
}
