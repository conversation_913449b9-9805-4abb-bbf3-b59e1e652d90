///
//  Generated code. Do not modify.
//  source: frontend/weeding_diagnostics.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use uploadStateDescriptor instead')
const UploadState$json = const {
  '1': 'UploadState',
  '2': const [
    const {'1': 'NONE', '2': 0},
    const {'1': 'IN_PROGRESS', '2': 1},
    const {'1': 'DONE', '2': 2},
  ],
};

/// Descriptor for `UploadState`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List uploadStateDescriptor = $convert.base64Decode('CgtVcGxvYWRTdGF0ZRIICgROT05FEAASDwoLSU5fUFJPR1JFU1MQARIICgRET05FEAI=');
@$core.Deprecated('Use recordWeedingDiagnosticsRequestDescriptor instead')
const RecordWeedingDiagnosticsRequest$json = const {
  '1': 'RecordWeedingDiagnosticsRequest',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'ttl_sec', '3': 2, '4': 1, '5': 13, '10': 'ttlSec'},
    const {'1': 'crop_images_per_sec', '3': 3, '4': 1, '5': 2, '10': 'cropImagesPerSec'},
    const {'1': 'weed_images_per_sec', '3': 4, '4': 1, '5': 2, '10': 'weedImagesPerSec'},
  ],
};

/// Descriptor for `RecordWeedingDiagnosticsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List recordWeedingDiagnosticsRequestDescriptor = $convert.base64Decode('Ch9SZWNvcmRXZWVkaW5nRGlhZ25vc3RpY3NSZXF1ZXN0EhIKBG5hbWUYASABKAlSBG5hbWUSFwoHdHRsX3NlYxgCIAEoDVIGdHRsU2VjEi0KE2Nyb3BfaW1hZ2VzX3Blcl9zZWMYAyABKAJSEGNyb3BJbWFnZXNQZXJTZWMSLQoTd2VlZF9pbWFnZXNfcGVyX3NlYxgEIAEoAlIQd2VlZEltYWdlc1BlclNlYw==');
@$core.Deprecated('Use getCurrentTrajectoriesRequestDescriptor instead')
const GetCurrentTrajectoriesRequest$json = const {
  '1': 'GetCurrentTrajectoriesRequest',
  '2': const [
    const {'1': 'row_id', '3': 1, '4': 1, '5': 13, '10': 'rowId'},
  ],
};

/// Descriptor for `GetCurrentTrajectoriesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getCurrentTrajectoriesRequestDescriptor = $convert.base64Decode('Ch1HZXRDdXJyZW50VHJhamVjdG9yaWVzUmVxdWVzdBIVCgZyb3dfaWQYASABKA1SBXJvd0lk');
@$core.Deprecated('Use getRecordingsListRequestDescriptor instead')
const GetRecordingsListRequest$json = const {
  '1': 'GetRecordingsListRequest',
};

/// Descriptor for `GetRecordingsListRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRecordingsListRequestDescriptor = $convert.base64Decode('ChhHZXRSZWNvcmRpbmdzTGlzdFJlcXVlc3Q=');
@$core.Deprecated('Use getRecordingsListResponseDescriptor instead')
const GetRecordingsListResponse$json = const {
  '1': 'GetRecordingsListResponse',
  '2': const [
    const {'1': 'name', '3': 1, '4': 3, '5': 9, '10': 'name'},
  ],
};

/// Descriptor for `GetRecordingsListResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRecordingsListResponseDescriptor = $convert.base64Decode('ChlHZXRSZWNvcmRpbmdzTGlzdFJlc3BvbnNlEhIKBG5hbWUYASADKAlSBG5hbWU=');
@$core.Deprecated('Use getSnapshotRequestDescriptor instead')
const GetSnapshotRequest$json = const {
  '1': 'GetSnapshotRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'snapshot_number', '3': 3, '4': 1, '5': 13, '10': 'snapshotNumber'},
  ],
};

/// Descriptor for `GetSnapshotRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getSnapshotRequestDescriptor = $convert.base64Decode('ChJHZXRTbmFwc2hvdFJlcXVlc3QSJQoOcmVjb3JkaW5nX25hbWUYASABKAlSDXJlY29yZGluZ05hbWUSFQoGcm93X2lkGAIgASgNUgVyb3dJZBInCg9zbmFwc2hvdF9udW1iZXIYAyABKA1SDnNuYXBzaG90TnVtYmVy');
@$core.Deprecated('Use getSnapshotResponseDescriptor instead')
const GetSnapshotResponse$json = const {
  '1': 'GetSnapshotResponse',
  '2': const [
    const {'1': 'snapshot', '3': 1, '4': 1, '5': 11, '6': '.weed_tracking.DiagnosticsSnapshot', '10': 'snapshot'},
  ],
};

/// Descriptor for `GetSnapshotResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getSnapshotResponseDescriptor = $convert.base64Decode('ChNHZXRTbmFwc2hvdFJlc3BvbnNlEj4KCHNuYXBzaG90GAEgASgLMiIud2VlZF90cmFja2luZy5EaWFnbm9zdGljc1NuYXBzaG90UghzbmFwc2hvdA==');
@$core.Deprecated('Use openRecordingRequestDescriptor instead')
const OpenRecordingRequest$json = const {
  '1': 'OpenRecordingRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
  ],
};

/// Descriptor for `OpenRecordingRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List openRecordingRequestDescriptor = $convert.base64Decode('ChRPcGVuUmVjb3JkaW5nUmVxdWVzdBIlCg5yZWNvcmRpbmdfbmFtZRgBIAEoCVINcmVjb3JkaW5nTmFtZQ==');
@$core.Deprecated('Use trajectoriesWithImagesDescriptor instead')
const TrajectoriesWithImages$json = const {
  '1': 'TrajectoriesWithImages',
  '2': const [
    const {'1': 'ids', '3': 1, '4': 3, '5': 13, '10': 'ids'},
  ],
};

/// Descriptor for `TrajectoriesWithImages`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectoriesWithImagesDescriptor = $convert.base64Decode('ChZUcmFqZWN0b3JpZXNXaXRoSW1hZ2VzEhAKA2lkcxgBIAMoDVIDaWRz');
@$core.Deprecated('Use predictImagesDescriptor instead')
const PredictImages$json = const {
  '1': 'PredictImages',
  '2': const [
    const {'1': 'names', '3': 1, '4': 3, '5': 9, '10': 'names'},
  ],
};

/// Descriptor for `PredictImages`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictImagesDescriptor = $convert.base64Decode('Cg1QcmVkaWN0SW1hZ2VzEhQKBW5hbWVzGAEgAygJUgVuYW1lcw==');
@$core.Deprecated('Use predictImagesPerCamDescriptor instead')
const PredictImagesPerCam$json = const {
  '1': 'PredictImagesPerCam',
  '2': const [
    const {'1': 'images', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.PredictImagesPerCam.ImagesEntry', '10': 'images'},
  ],
  '3': const [PredictImagesPerCam_ImagesEntry$json],
};

@$core.Deprecated('Use predictImagesPerCamDescriptor instead')
const PredictImagesPerCam_ImagesEntry$json = const {
  '1': 'ImagesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 5, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.PredictImages', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `PredictImagesPerCam`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List predictImagesPerCamDescriptor = $convert.base64Decode('ChNQcmVkaWN0SW1hZ2VzUGVyQ2FtElwKBmltYWdlcxgBIAMoCzJELmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLlByZWRpY3RJbWFnZXNQZXJDYW0uSW1hZ2VzRW50cnlSBmltYWdlcxptCgtJbWFnZXNFbnRyeRIQCgNrZXkYASABKAVSA2tleRJICgV2YWx1ZRgCIAEoCzIyLmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLlByZWRpY3RJbWFnZXNSBXZhbHVlOgI4AQ==');
@$core.Deprecated('Use openRecordingResponseDescriptor instead')
const OpenRecordingResponse$json = const {
  '1': 'OpenRecordingResponse',
  '2': const [
    const {'1': 'num_snapshots_per_row', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.NumSnapshotsPerRowEntry', '10': 'numSnapshotsPerRow'},
    const {'1': 'recording_data', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.StaticRecordingData', '10': 'recordingData'},
    const {'1': 'trajectory_images_per_row', '3': 3, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.TrajectoryImagesPerRowEntry', '10': 'trajectoryImagesPerRow'},
    const {'1': 'predict_images_per_row', '3': 4, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.OpenRecordingResponse.PredictImagesPerRowEntry', '10': 'predictImagesPerRow'},
  ],
  '3': const [OpenRecordingResponse_NumSnapshotsPerRowEntry$json, OpenRecordingResponse_TrajectoryImagesPerRowEntry$json, OpenRecordingResponse_PredictImagesPerRowEntry$json],
};

@$core.Deprecated('Use openRecordingResponseDescriptor instead')
const OpenRecordingResponse_NumSnapshotsPerRowEntry$json = const {
  '1': 'NumSnapshotsPerRowEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 13, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 13, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use openRecordingResponseDescriptor instead')
const OpenRecordingResponse_TrajectoryImagesPerRowEntry$json = const {
  '1': 'TrajectoryImagesPerRowEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 13, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.TrajectoriesWithImages', '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use openRecordingResponseDescriptor instead')
const OpenRecordingResponse_PredictImagesPerRowEntry$json = const {
  '1': 'PredictImagesPerRowEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 13, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.PredictImagesPerCam', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `OpenRecordingResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List openRecordingResponseDescriptor = $convert.base64Decode('ChVPcGVuUmVjb3JkaW5nUmVzcG9uc2UShQEKFW51bV9zbmFwc2hvdHNfcGVyX3JvdxgBIAMoCzJSLmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLk9wZW5SZWNvcmRpbmdSZXNwb25zZS5OdW1TbmFwc2hvdHNQZXJSb3dFbnRyeVISbnVtU25hcHNob3RzUGVyUm93El8KDnJlY29yZGluZ19kYXRhGAIgASgLMjguY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuU3RhdGljUmVjb3JkaW5nRGF0YVINcmVjb3JkaW5nRGF0YRKRAQoZdHJhamVjdG9yeV9pbWFnZXNfcGVyX3JvdxgDIAMoCzJWLmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLk9wZW5SZWNvcmRpbmdSZXNwb25zZS5UcmFqZWN0b3J5SW1hZ2VzUGVyUm93RW50cnlSFnRyYWplY3RvcnlJbWFnZXNQZXJSb3cSiAEKFnByZWRpY3RfaW1hZ2VzX3Blcl9yb3cYBCADKAsyUy5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5PcGVuUmVjb3JkaW5nUmVzcG9uc2UuUHJlZGljdEltYWdlc1BlclJvd0VudHJ5UhNwcmVkaWN0SW1hZ2VzUGVyUm93GkUKF051bVNuYXBzaG90c1BlclJvd0VudHJ5EhAKA2tleRgBIAEoDVIDa2V5EhQKBXZhbHVlGAIgASgNUgV2YWx1ZToCOAEahgEKG1RyYWplY3RvcnlJbWFnZXNQZXJSb3dFbnRyeRIQCgNrZXkYASABKA1SA2tleRJRCgV2YWx1ZRgCIAEoCzI7LmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLlRyYWplY3Rvcmllc1dpdGhJbWFnZXNSBXZhbHVlOgI4ARqAAQoYUHJlZGljdEltYWdlc1BlclJvd0VudHJ5EhAKA2tleRgBIAEoDVIDa2V5Ek4KBXZhbHVlGAIgASgLMjguY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuUHJlZGljdEltYWdlc1BlckNhbVIFdmFsdWU6AjgB');
@$core.Deprecated('Use deleteRecordingRequestDescriptor instead')
const DeleteRecordingRequest$json = const {
  '1': 'DeleteRecordingRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
  ],
};

/// Descriptor for `DeleteRecordingRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deleteRecordingRequestDescriptor = $convert.base64Decode('ChZEZWxldGVSZWNvcmRpbmdSZXF1ZXN0EiUKDnJlY29yZGluZ19uYW1lGAEgASgJUg1yZWNvcmRpbmdOYW1l');
@$core.Deprecated('Use configNodeSnapshotDescriptor instead')
const ConfigNodeSnapshot$json = const {
  '1': 'ConfigNodeSnapshot',
  '2': const [
    const {'1': 'values', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ValuesEntry', '10': 'values'},
    const {'1': 'child_nodes', '3': 2, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot.ChildNodesEntry', '10': 'childNodes'},
  ],
  '3': const [ConfigNodeSnapshot_ValuesEntry$json, ConfigNodeSnapshot_ChildNodesEntry$json],
};

@$core.Deprecated('Use configNodeSnapshotDescriptor instead')
const ConfigNodeSnapshot_ValuesEntry$json = const {
  '1': 'ValuesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use configNodeSnapshotDescriptor instead')
const ConfigNodeSnapshot_ChildNodesEntry$json = const {
  '1': 'ChildNodesEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `ConfigNodeSnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List configNodeSnapshotDescriptor = $convert.base64Decode('ChJDb25maWdOb2RlU25hcHNob3QSWwoGdmFsdWVzGAEgAygLMkMuY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuQ29uZmlnTm9kZVNuYXBzaG90LlZhbHVlc0VudHJ5UgZ2YWx1ZXMSaAoLY2hpbGRfbm9kZXMYAiADKAsyRy5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5Db25maWdOb2RlU25hcHNob3QuQ2hpbGROb2Rlc0VudHJ5UgpjaGlsZE5vZGVzGjkKC1ZhbHVlc0VudHJ5EhAKA2tleRgBIAEoCVIDa2V5EhQKBXZhbHVlGAIgASgJUgV2YWx1ZToCOAEadgoPQ2hpbGROb2Rlc0VudHJ5EhAKA2tleRgBIAEoCVIDa2V5Ek0KBXZhbHVlGAIgASgLMjcuY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuQ29uZmlnTm9kZVNuYXBzaG90UgV2YWx1ZToCOAE=');
@$core.Deprecated('Use cameraDimensionsDescriptor instead')
const CameraDimensions$json = const {
  '1': 'CameraDimensions',
  '2': const [
    const {'1': 'width', '3': 1, '4': 1, '5': 13, '10': 'width'},
    const {'1': 'height', '3': 2, '4': 1, '5': 13, '10': 'height'},
  ],
};

/// Descriptor for `CameraDimensions`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cameraDimensionsDescriptor = $convert.base64Decode('ChBDYW1lcmFEaW1lbnNpb25zEhQKBXdpZHRoGAEgASgNUgV3aWR0aBIWCgZoZWlnaHQYAiABKA1SBmhlaWdodA==');
@$core.Deprecated('Use rowCamerasDescriptor instead')
const RowCameras$json = const {
  '1': 'RowCameras',
  '2': const [
    const {'1': 'cams', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.RowCameras.CamsEntry', '10': 'cams'},
  ],
  '3': const [RowCameras_CamsEntry$json],
};

@$core.Deprecated('Use rowCamerasDescriptor instead')
const RowCameras_CamsEntry$json = const {
  '1': 'CamsEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.CameraDimensions', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `RowCameras`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List rowCamerasDescriptor = $convert.base64Decode('CgpSb3dDYW1lcmFzEk0KBGNhbXMYASADKAsyOS5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5Sb3dDYW1lcmFzLkNhbXNFbnRyeVIEY2FtcxpuCglDYW1zRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSSwoFdmFsdWUYAiABKAsyNS5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5DYW1lcmFEaW1lbnNpb25zUgV2YWx1ZToCOAE=');
@$core.Deprecated('Use staticRecordingDataDescriptor instead')
const StaticRecordingData$json = const {
  '1': 'StaticRecordingData',
  '2': const [
    const {'1': 'lasers_enabled', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.StaticRecordingData.LasersEnabledEntry', '10': 'lasersEnabled'},
    const {'1': 'root_config', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot', '10': 'rootConfig'},
    const {'1': 'rows_recorded', '3': 3, '4': 3, '5': 5, '10': 'rowsRecorded'},
    const {'1': 'row_dimensions', '3': 4, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.StaticRecordingData.RowDimensionsEntry', '10': 'rowDimensions'},
    const {'1': 'crop_safety_radius_mm_per_row', '3': 5, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.StaticRecordingData.CropSafetyRadiusMmPerRowEntry', '10': 'cropSafetyRadiusMmPerRow'},
    const {'1': 'thinning_config', '3': 6, '4': 1, '5': 11, '6': '.carbon.thinning.ConfigDefinition', '10': 'thinningConfig'},
    const {'1': 'recording_timestamp', '3': 7, '4': 1, '5': 3, '10': 'recordingTimestamp'},
    const {'1': 'row_cameras', '3': 8, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.StaticRecordingData.RowCamerasEntry', '10': 'rowCameras'},
    const {'1': 'weed_point_threshold', '3': 9, '4': 1, '5': 2, '10': 'weedPointThreshold'},
    const {'1': 'crop_point_threshold', '3': 10, '4': 1, '5': 2, '10': 'cropPointThreshold'},
    const {'1': 'wheel_diameter_back_left_in', '3': 11, '4': 1, '5': 2, '10': 'wheelDiameterBackLeftIn'},
    const {'1': 'wheel_diameter_back_right_in', '3': 12, '4': 1, '5': 2, '10': 'wheelDiameterBackRightIn'},
    const {'1': 'wheel_diameter_front_left_in', '3': 13, '4': 1, '5': 2, '10': 'wheelDiameterFrontLeftIn'},
    const {'1': 'wheel_diameter_front_right_in', '3': 14, '4': 1, '5': 2, '10': 'wheelDiameterFrontRightIn'},
  ],
  '3': const [StaticRecordingData_LasersEnabledEntry$json, StaticRecordingData_RowDimensionsEntry$json, StaticRecordingData_CropSafetyRadiusMmPerRowEntry$json, StaticRecordingData_RowCamerasEntry$json],
};

@$core.Deprecated('Use staticRecordingDataDescriptor instead')
const StaticRecordingData_LasersEnabledEntry$json = const {
  '1': 'LasersEnabledEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 8, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use staticRecordingDataDescriptor instead')
const StaticRecordingData_RowDimensionsEntry$json = const {
  '1': 'RowDimensionsEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 13, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.aimbot.GetDimensionsResponse', '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use staticRecordingDataDescriptor instead')
const StaticRecordingData_CropSafetyRadiusMmPerRowEntry$json = const {
  '1': 'CropSafetyRadiusMmPerRowEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 13, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 2, '10': 'value'},
  ],
  '7': const {'7': true},
};

@$core.Deprecated('Use staticRecordingDataDescriptor instead')
const StaticRecordingData_RowCamerasEntry$json = const {
  '1': 'RowCamerasEntry',
  '2': const [
    const {'1': 'key', '3': 1, '4': 1, '5': 13, '10': 'key'},
    const {'1': 'value', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.RowCameras', '10': 'value'},
  ],
  '7': const {'7': true},
};

/// Descriptor for `StaticRecordingData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List staticRecordingDataDescriptor = $convert.base64Decode('ChNTdGF0aWNSZWNvcmRpbmdEYXRhEnIKDmxhc2Vyc19lbmFibGVkGAEgAygLMksuY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuU3RhdGljUmVjb3JkaW5nRGF0YS5MYXNlcnNFbmFibGVkRW50cnlSDWxhc2Vyc0VuYWJsZWQSWAoLcm9vdF9jb25maWcYAiABKAsyNy5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5Db25maWdOb2RlU25hcHNob3RSCnJvb3RDb25maWcSIwoNcm93c19yZWNvcmRlZBgDIAMoBVIMcm93c1JlY29yZGVkEnIKDnJvd19kaW1lbnNpb25zGAQgAygLMksuY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuU3RhdGljUmVjb3JkaW5nRGF0YS5Sb3dEaW1lbnNpb25zRW50cnlSDXJvd0RpbWVuc2lvbnMSlwEKHWNyb3Bfc2FmZXR5X3JhZGl1c19tbV9wZXJfcm93GAUgAygLMlYuY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuU3RhdGljUmVjb3JkaW5nRGF0YS5Dcm9wU2FmZXR5UmFkaXVzTW1QZXJSb3dFbnRyeVIYY3JvcFNhZmV0eVJhZGl1c01tUGVyUm93EkoKD3RoaW5uaW5nX2NvbmZpZxgGIAEoCzIhLmNhcmJvbi50aGlubmluZy5Db25maWdEZWZpbml0aW9uUg50aGlubmluZ0NvbmZpZxIvChNyZWNvcmRpbmdfdGltZXN0YW1wGAcgASgDUhJyZWNvcmRpbmdUaW1lc3RhbXASaQoLcm93X2NhbWVyYXMYCCADKAsySC5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5TdGF0aWNSZWNvcmRpbmdEYXRhLlJvd0NhbWVyYXNFbnRyeVIKcm93Q2FtZXJhcxIwChR3ZWVkX3BvaW50X3RocmVzaG9sZBgJIAEoAlISd2VlZFBvaW50VGhyZXNob2xkEjAKFGNyb3BfcG9pbnRfdGhyZXNob2xkGAogASgCUhJjcm9wUG9pbnRUaHJlc2hvbGQSPAobd2hlZWxfZGlhbWV0ZXJfYmFja19sZWZ0X2luGAsgASgCUhd3aGVlbERpYW1ldGVyQmFja0xlZnRJbhI+Chx3aGVlbF9kaWFtZXRlcl9iYWNrX3JpZ2h0X2luGAwgASgCUhh3aGVlbERpYW1ldGVyQmFja1JpZ2h0SW4SPgocd2hlZWxfZGlhbWV0ZXJfZnJvbnRfbGVmdF9pbhgNIAEoAlIYd2hlZWxEaWFtZXRlckZyb250TGVmdEluEkAKHXdoZWVsX2RpYW1ldGVyX2Zyb250X3JpZ2h0X2luGA4gASgCUhl3aGVlbERpYW1ldGVyRnJvbnRSaWdodEluGkAKEkxhc2Vyc0VuYWJsZWRFbnRyeRIQCgNrZXkYASABKAlSA2tleRIUCgV2YWx1ZRgCIAEoCFIFdmFsdWU6AjgBGl8KElJvd0RpbWVuc2lvbnNFbnRyeRIQCgNrZXkYASABKA1SA2tleRIzCgV2YWx1ZRgCIAEoCzIdLmFpbWJvdC5HZXREaW1lbnNpb25zUmVzcG9uc2VSBXZhbHVlOgI4ARpLCh1Dcm9wU2FmZXR5UmFkaXVzTW1QZXJSb3dFbnRyeRIQCgNrZXkYASABKA1SA2tleRIUCgV2YWx1ZRgCIAEoAlIFdmFsdWU6AjgBGm4KD1Jvd0NhbWVyYXNFbnRyeRIQCgNrZXkYASABKA1SA2tleRJFCgV2YWx1ZRgCIAEoCzIvLmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLlJvd0NhbWVyYXNSBXZhbHVlOgI4AQ==');
@$core.Deprecated('Use getTrajectoryDataRequestDescriptor instead')
const GetTrajectoryDataRequest$json = const {
  '1': 'GetTrajectoryDataRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'trajectory_id', '3': 3, '4': 1, '5': 13, '10': 'trajectoryId'},
  ],
};

/// Descriptor for `GetTrajectoryDataRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrajectoryDataRequestDescriptor = $convert.base64Decode('ChhHZXRUcmFqZWN0b3J5RGF0YVJlcXVlc3QSJQoOcmVjb3JkaW5nX25hbWUYASABKAlSDXJlY29yZGluZ05hbWUSFQoGcm93X2lkGAIgASgNUgVyb3dJZBIjCg10cmFqZWN0b3J5X2lkGAMgASgNUgx0cmFqZWN0b3J5SWQ=');
@$core.Deprecated('Use lastSnapshotDescriptor instead')
const LastSnapshot$json = const {
  '1': 'LastSnapshot',
  '2': const [
    const {'1': 'trajectory', '3': 1, '4': 1, '5': 11, '6': '.weed_tracking.TrajectorySnapshot', '10': 'trajectory'},
    const {'1': 'diagnostics_snapshot_number', '3': 2, '4': 1, '5': 13, '10': 'diagnosticsSnapshotNumber'},
  ],
};

/// Descriptor for `LastSnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List lastSnapshotDescriptor = $convert.base64Decode('CgxMYXN0U25hcHNob3QSQQoKdHJhamVjdG9yeRgBIAEoCzIhLndlZWRfdHJhY2tpbmcuVHJhamVjdG9yeVNuYXBzaG90Ugp0cmFqZWN0b3J5Ej4KG2RpYWdub3N0aWNzX3NuYXBzaG90X251bWJlchgCIAEoDVIZZGlhZ25vc3RpY3NTbmFwc2hvdE51bWJlcg==');
@$core.Deprecated('Use extraTrajectoryFieldDescriptor instead')
const ExtraTrajectoryField$json = const {
  '1': 'ExtraTrajectoryField',
  '2': const [
    const {'1': 'label', '3': 1, '4': 1, '5': 9, '10': 'label'},
    const {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
    const {'1': 'color', '3': 3, '4': 1, '5': 9, '10': 'color'},
  ],
};

/// Descriptor for `ExtraTrajectoryField`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List extraTrajectoryFieldDescriptor = $convert.base64Decode('ChRFeHRyYVRyYWplY3RvcnlGaWVsZBIUCgVsYWJlbBgBIAEoCVIFbGFiZWwSFAoFdmFsdWUYAiABKAlSBXZhbHVlEhQKBWNvbG9yGAMgASgJUgVjb2xvcg==');
@$core.Deprecated('Use targetImageDescriptor instead')
const TargetImage$json = const {
  '1': 'TargetImage',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'timestamp', '3': 2, '4': 1, '5': 4, '10': 'timestamp'},
    const {'1': 'p2p_predict_x', '3': 3, '4': 1, '5': 13, '10': 'p2pPredictX'},
    const {'1': 'p2p_predict_y', '3': 4, '4': 1, '5': 13, '10': 'p2pPredictY'},
  ],
};

/// Descriptor for `TargetImage`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List targetImageDescriptor = $convert.base64Decode('CgtUYXJnZXRJbWFnZRISCgRuYW1lGAEgASgJUgRuYW1lEhwKCXRpbWVzdGFtcBgCIAEoBFIJdGltZXN0YW1wEiIKDXAycF9wcmVkaWN0X3gYAyABKA1SC3AycFByZWRpY3RYEiIKDXAycF9wcmVkaWN0X3kYBCABKA1SC3AycFByZWRpY3RZ');
@$core.Deprecated('Use p2PPredictImageDescriptor instead')
const P2PPredictImage$json = const {
  '1': 'P2PPredictImage',
  '2': const [
    const {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    const {'1': 'center_x_px', '3': 2, '4': 1, '5': 5, '10': 'centerXPx'},
    const {'1': 'center_y_px', '3': 3, '4': 1, '5': 5, '10': 'centerYPx'},
    const {'1': 'radius_px', '3': 4, '4': 1, '5': 5, '10': 'radiusPx'},
    const {'1': 'ts', '3': 5, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'pcam_id', '3': 6, '4': 1, '5': 9, '10': 'pcamId'},
  ],
};

/// Descriptor for `P2PPredictImage`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List p2PPredictImageDescriptor = $convert.base64Decode('Cg9QMlBQcmVkaWN0SW1hZ2USEgoEbmFtZRgBIAEoCVIEbmFtZRIeCgtjZW50ZXJfeF9weBgCIAEoBVIJY2VudGVyWFB4Eh4KC2NlbnRlcl95X3B4GAMgASgFUgljZW50ZXJZUHgSGwoJcmFkaXVzX3B4GAQgASgFUghyYWRpdXNQeBImCgJ0cxgFIAEoCzIWLmNhcmJvbi51dGlsLlRpbWVzdGFtcFICdHMSFwoHcGNhbV9pZBgGIAEoCVIGcGNhbUlk');
@$core.Deprecated('Use trajectoryPredictImageMetadataDescriptor instead')
const TrajectoryPredictImageMetadata$json = const {
  '1': 'TrajectoryPredictImageMetadata',
  '2': const [
    const {'1': 'center_x_px', '3': 1, '4': 1, '5': 5, '10': 'centerXPx'},
    const {'1': 'center_y_px', '3': 2, '4': 1, '5': 5, '10': 'centerYPx'},
    const {'1': 'radius_px', '3': 3, '4': 1, '5': 5, '10': 'radiusPx'},
    const {'1': 'ts', '3': 4, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'pcam_id', '3': 5, '4': 1, '5': 9, '10': 'pcamId'},
  ],
};

/// Descriptor for `TrajectoryPredictImageMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectoryPredictImageMetadataDescriptor = $convert.base64Decode('Ch5UcmFqZWN0b3J5UHJlZGljdEltYWdlTWV0YWRhdGESHgoLY2VudGVyX3hfcHgYASABKAVSCWNlbnRlclhQeBIeCgtjZW50ZXJfeV9weBgCIAEoBVIJY2VudGVyWVB4EhsKCXJhZGl1c19weBgDIAEoBVIIcmFkaXVzUHgSJgoCdHMYBCABKAsyFi5jYXJib24udXRpbC5UaW1lc3RhbXBSAnRzEhcKB3BjYW1faWQYBSABKAlSBnBjYW1JZA==');
@$core.Deprecated('Use trajectoryDataDescriptor instead')
const TrajectoryData$json = const {
  '1': 'TrajectoryData',
  '2': const [
    const {'1': 'predict_image', '3': 1, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.TrajectoryPredictImageMetadata', '10': 'predictImage'},
    const {'1': 'target_images', '3': 2, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.TargetImage', '10': 'targetImages'},
    const {'1': 'last_snapshot', '3': 3, '4': 1, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.LastSnapshot', '10': 'lastSnapshot'},
    const {'1': 'extra_fields', '3': 4, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.ExtraTrajectoryField', '10': 'extraFields'},
    const {'1': 'crosshair_x', '3': 5, '4': 1, '5': 13, '10': 'crosshairX'},
    const {'1': 'crosshair_y', '3': 6, '4': 1, '5': 13, '10': 'crosshairY'},
    const {'1': 'p2p_predict_images', '3': 7, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.P2PPredictImage', '10': 'p2pPredictImages'},
  ],
};

/// Descriptor for `TrajectoryData`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trajectoryDataDescriptor = $convert.base64Decode('Cg5UcmFqZWN0b3J5RGF0YRJoCg1wcmVkaWN0X2ltYWdlGAEgASgLMkMuY2FyYm9uLmZyb250ZW5kLndlZWRpbmdfZGlhZ25vc3RpY3MuVHJhamVjdG9yeVByZWRpY3RJbWFnZU1ldGFkYXRhUgxwcmVkaWN0SW1hZ2USVQoNdGFyZ2V0X2ltYWdlcxgCIAMoCzIwLmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLlRhcmdldEltYWdlUgx0YXJnZXRJbWFnZXMSVgoNbGFzdF9zbmFwc2hvdBgDIAEoCzIxLmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLkxhc3RTbmFwc2hvdFIMbGFzdFNuYXBzaG90ElwKDGV4dHJhX2ZpZWxkcxgEIAMoCzI5LmNhcmJvbi5mcm9udGVuZC53ZWVkaW5nX2RpYWdub3N0aWNzLkV4dHJhVHJhamVjdG9yeUZpZWxkUgtleHRyYUZpZWxkcxIfCgtjcm9zc2hhaXJfeBgFIAEoDVIKY3Jvc3NoYWlyWBIfCgtjcm9zc2hhaXJfeRgGIAEoDVIKY3Jvc3NoYWlyWRJiChJwMnBfcHJlZGljdF9pbWFnZXMYByADKAsyNC5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5QMlBQcmVkaWN0SW1hZ2VSEHAycFByZWRpY3RJbWFnZXM=');
@$core.Deprecated('Use getTrajectoryPredictImageRequestDescriptor instead')
const GetTrajectoryPredictImageRequest$json = const {
  '1': 'GetTrajectoryPredictImageRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'trajectory_id', '3': 3, '4': 1, '5': 13, '10': 'trajectoryId'},
  ],
};

/// Descriptor for `GetTrajectoryPredictImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrajectoryPredictImageRequestDescriptor = $convert.base64Decode('CiBHZXRUcmFqZWN0b3J5UHJlZGljdEltYWdlUmVxdWVzdBIlCg5yZWNvcmRpbmdfbmFtZRgBIAEoCVINcmVjb3JkaW5nTmFtZRIVCgZyb3dfaWQYAiABKA1SBXJvd0lkEiMKDXRyYWplY3RvcnlfaWQYAyABKA1SDHRyYWplY3RvcnlJZA==');
@$core.Deprecated('Use getTrajectoryTargetImageRequestDescriptor instead')
const GetTrajectoryTargetImageRequest$json = const {
  '1': 'GetTrajectoryTargetImageRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'trajectory_id', '3': 3, '4': 1, '5': 13, '10': 'trajectoryId'},
    const {'1': 'image_name', '3': 4, '4': 1, '5': 9, '10': 'imageName'},
  ],
};

/// Descriptor for `GetTrajectoryTargetImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrajectoryTargetImageRequestDescriptor = $convert.base64Decode('Ch9HZXRUcmFqZWN0b3J5VGFyZ2V0SW1hZ2VSZXF1ZXN0EiUKDnJlY29yZGluZ19uYW1lGAEgASgJUg1yZWNvcmRpbmdOYW1lEhUKBnJvd19pZBgCIAEoDVIFcm93SWQSIwoNdHJhamVjdG9yeV9pZBgDIAEoDVIMdHJhamVjdG9yeUlkEh0KCmltYWdlX25hbWUYBCABKAlSCWltYWdlTmFtZQ==');
@$core.Deprecated('Use imageChunkDescriptor instead')
const ImageChunk$json = const {
  '1': 'ImageChunk',
  '2': const [
    const {'1': 'image_chunk', '3': 1, '4': 1, '5': 12, '10': 'imageChunk'},
  ],
};

/// Descriptor for `ImageChunk`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List imageChunkDescriptor = $convert.base64Decode('CgpJbWFnZUNodW5rEh8KC2ltYWdlX2NodW5rGAEgASgMUgppbWFnZUNodW5r');
@$core.Deprecated('Use getPredictImageMetadataRequestDescriptor instead')
const GetPredictImageMetadataRequest$json = const {
  '1': 'GetPredictImageMetadataRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'image_name', '3': 3, '4': 1, '5': 9, '10': 'imageName'},
  ],
};

/// Descriptor for `GetPredictImageMetadataRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getPredictImageMetadataRequestDescriptor = $convert.base64Decode('Ch5HZXRQcmVkaWN0SW1hZ2VNZXRhZGF0YVJlcXVlc3QSJQoOcmVjb3JkaW5nX25hbWUYASABKAlSDXJlY29yZGluZ05hbWUSFQoGcm93X2lkGAIgASgNUgVyb3dJZBIdCgppbWFnZV9uYW1lGAMgASgJUglpbWFnZU5hbWU=');
@$core.Deprecated('Use getPredictImageMetadataResponseDescriptor instead')
const GetPredictImageMetadataResponse$json = const {
  '1': 'GetPredictImageMetadataResponse',
  '2': const [
    const {'1': 'ts', '3': 1, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'annotations', '3': 2, '4': 1, '5': 11, '6': '.carbon.frontend.image_stream.Annotations', '10': 'annotations'},
    const {'1': 'deepweed_output', '3': 3, '4': 1, '5': 11, '6': '.cv.runtime.proto.DeepweedOutput', '10': 'deepweedOutput'},
    const {'1': 'deepweed_output_below_threshold', '3': 4, '4': 1, '5': 11, '6': '.cv.runtime.proto.DeepweedOutput', '10': 'deepweedOutputBelowThreshold'},
  ],
};

/// Descriptor for `GetPredictImageMetadataResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getPredictImageMetadataResponseDescriptor = $convert.base64Decode('Ch9HZXRQcmVkaWN0SW1hZ2VNZXRhZGF0YVJlc3BvbnNlEiYKAnRzGAEgASgLMhYuY2FyYm9uLnV0aWwuVGltZXN0YW1wUgJ0cxJLCgthbm5vdGF0aW9ucxgCIAEoCzIpLmNhcmJvbi5mcm9udGVuZC5pbWFnZV9zdHJlYW0uQW5ub3RhdGlvbnNSC2Fubm90YXRpb25zEkkKD2RlZXB3ZWVkX291dHB1dBgDIAEoCzIgLmN2LnJ1bnRpbWUucHJvdG8uRGVlcHdlZWRPdXRwdXRSDmRlZXB3ZWVkT3V0cHV0EmcKH2RlZXB3ZWVkX291dHB1dF9iZWxvd190aHJlc2hvbGQYBCABKAsyIC5jdi5ydW50aW1lLnByb3RvLkRlZXB3ZWVkT3V0cHV0UhxkZWVwd2VlZE91dHB1dEJlbG93VGhyZXNob2xk');
@$core.Deprecated('Use getPredictImageRequestDescriptor instead')
const GetPredictImageRequest$json = const {
  '1': 'GetPredictImageRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'image_name', '3': 3, '4': 1, '5': 9, '10': 'imageName'},
  ],
};

/// Descriptor for `GetPredictImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getPredictImageRequestDescriptor = $convert.base64Decode('ChZHZXRQcmVkaWN0SW1hZ2VSZXF1ZXN0EiUKDnJlY29yZGluZ19uYW1lGAEgASgJUg1yZWNvcmRpbmdOYW1lEhUKBnJvd19pZBgCIAEoDVIFcm93SWQSHQoKaW1hZ2VfbmFtZRgDIAEoCVIJaW1hZ2VOYW1l');
@$core.Deprecated('Use startUploadRequestDescriptor instead')
const StartUploadRequest$json = const {
  '1': 'StartUploadRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
  ],
};

/// Descriptor for `StartUploadRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List startUploadRequestDescriptor = $convert.base64Decode('ChJTdGFydFVwbG9hZFJlcXVlc3QSJQoOcmVjb3JkaW5nX25hbWUYASABKAlSDXJlY29yZGluZ05hbWU=');
@$core.Deprecated('Use getNextUploadStateRequestDescriptor instead')
const GetNextUploadStateRequest$json = const {
  '1': 'GetNextUploadStateRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'ts', '3': 2, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
  ],
};

/// Descriptor for `GetNextUploadStateRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextUploadStateRequestDescriptor = $convert.base64Decode('ChlHZXROZXh0VXBsb2FkU3RhdGVSZXF1ZXN0EiUKDnJlY29yZGluZ19uYW1lGAEgASgJUg1yZWNvcmRpbmdOYW1lEiYKAnRzGAIgASgLMhYuY2FyYm9uLnV0aWwuVGltZXN0YW1wUgJ0cw==');
@$core.Deprecated('Use getNextUploadStateResponseDescriptor instead')
const GetNextUploadStateResponse$json = const {
  '1': 'GetNextUploadStateResponse',
  '2': const [
    const {'1': 'upload_state', '3': 1, '4': 1, '5': 14, '6': '.carbon.frontend.weeding_diagnostics.UploadState', '10': 'uploadState'},
    const {'1': 'ts', '3': 2, '4': 1, '5': 11, '6': '.carbon.util.Timestamp', '10': 'ts'},
    const {'1': 'percent_uploaded', '3': 3, '4': 1, '5': 13, '10': 'percentUploaded'},
  ],
};

/// Descriptor for `GetNextUploadStateResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getNextUploadStateResponseDescriptor = $convert.base64Decode('ChpHZXROZXh0VXBsb2FkU3RhdGVSZXNwb25zZRJTCgx1cGxvYWRfc3RhdGUYASABKA4yMC5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5VcGxvYWRTdGF0ZVILdXBsb2FkU3RhdGUSJgoCdHMYAiABKAsyFi5jYXJib24udXRpbC5UaW1lc3RhbXBSAnRzEikKEHBlcmNlbnRfdXBsb2FkZWQYAyABKA1SD3BlcmNlbnRVcGxvYWRlZA==');
@$core.Deprecated('Use getDeepweedPredictionsCountRequestDescriptor instead')
const GetDeepweedPredictionsCountRequest$json = const {
  '1': 'GetDeepweedPredictionsCountRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row', '3': 2, '4': 1, '5': 13, '10': 'row'},
    const {'1': 'cam_id', '3': 3, '4': 1, '5': 13, '10': 'camId'},
  ],
};

/// Descriptor for `GetDeepweedPredictionsCountRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedPredictionsCountRequestDescriptor = $convert.base64Decode('CiJHZXREZWVwd2VlZFByZWRpY3Rpb25zQ291bnRSZXF1ZXN0EiUKDnJlY29yZGluZ19uYW1lGAEgASgJUg1yZWNvcmRpbmdOYW1lEhAKA3JvdxgCIAEoDVIDcm93EhUKBmNhbV9pZBgDIAEoDVIFY2FtSWQ=');
@$core.Deprecated('Use getDeepweedPredictionsCountResponseDescriptor instead')
const GetDeepweedPredictionsCountResponse$json = const {
  '1': 'GetDeepweedPredictionsCountResponse',
  '2': const [
    const {'1': 'count', '3': 1, '4': 1, '5': 13, '10': 'count'},
  ],
};

/// Descriptor for `GetDeepweedPredictionsCountResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedPredictionsCountResponseDescriptor = $convert.base64Decode('CiNHZXREZWVwd2VlZFByZWRpY3Rpb25zQ291bnRSZXNwb25zZRIUCgVjb3VudBgBIAEoDVIFY291bnQ=');
@$core.Deprecated('Use getDeepweedPredictionsRequestDescriptor instead')
const GetDeepweedPredictionsRequest$json = const {
  '1': 'GetDeepweedPredictionsRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row', '3': 2, '4': 1, '5': 13, '10': 'row'},
    const {'1': 'cam_id', '3': 3, '4': 1, '5': 13, '10': 'camId'},
    const {'1': 'idx', '3': 4, '4': 1, '5': 13, '10': 'idx'},
  ],
};

/// Descriptor for `GetDeepweedPredictionsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedPredictionsRequestDescriptor = $convert.base64Decode('Ch1HZXREZWVwd2VlZFByZWRpY3Rpb25zUmVxdWVzdBIlCg5yZWNvcmRpbmdfbmFtZRgBIAEoCVINcmVjb3JkaW5nTmFtZRIQCgNyb3cYAiABKA1SA3JvdxIVCgZjYW1faWQYAyABKA1SBWNhbUlkEhAKA2lkeBgEIAEoDVIDaWR4');
@$core.Deprecated('Use getDeepweedPredictionsResponseDescriptor instead')
const GetDeepweedPredictionsResponse$json = const {
  '1': 'GetDeepweedPredictionsResponse',
  '2': const [
    const {'1': 'predictions', '3': 1, '4': 1, '5': 11, '6': '.recorder.DeepweedPredictionRecord', '10': 'predictions'},
  ],
};

/// Descriptor for `GetDeepweedPredictionsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getDeepweedPredictionsResponseDescriptor = $convert.base64Decode('Ch5HZXREZWVwd2VlZFByZWRpY3Rpb25zUmVzcG9uc2USRAoLcHJlZGljdGlvbnMYASABKAsyIi5yZWNvcmRlci5EZWVwd2VlZFByZWRpY3Rpb25SZWNvcmRSC3ByZWRpY3Rpb25z');
@$core.Deprecated('Use findTrajectoryRequestDescriptor instead')
const FindTrajectoryRequest$json = const {
  '1': 'FindTrajectoryRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
    const {'1': 'trajectory_id', '3': 3, '4': 1, '5': 13, '10': 'trajectoryId'},
  ],
};

/// Descriptor for `FindTrajectoryRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List findTrajectoryRequestDescriptor = $convert.base64Decode('ChVGaW5kVHJhamVjdG9yeVJlcXVlc3QSJQoOcmVjb3JkaW5nX25hbWUYASABKAlSDXJlY29yZGluZ05hbWUSFQoGcm93X2lkGAIgASgNUgVyb3dJZBIjCg10cmFqZWN0b3J5X2lkGAMgASgNUgx0cmFqZWN0b3J5SWQ=');
@$core.Deprecated('Use findTrajectoryResponseDescriptor instead')
const FindTrajectoryResponse$json = const {
  '1': 'FindTrajectoryResponse',
  '2': const [
    const {'1': 'snapshot_id', '3': 1, '4': 1, '5': 13, '10': 'snapshotId'},
    const {'1': 'trajectory', '3': 2, '4': 1, '5': 11, '6': '.weed_tracking.TrajectorySnapshot', '10': 'trajectory'},
  ],
};

/// Descriptor for `FindTrajectoryResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List findTrajectoryResponseDescriptor = $convert.base64Decode('ChZGaW5kVHJhamVjdG9yeVJlc3BvbnNlEh8KC3NuYXBzaG90X2lkGAEgASgNUgpzbmFwc2hvdElkEkEKCnRyYWplY3RvcnkYAiABKAsyIS53ZWVkX3RyYWNraW5nLlRyYWplY3RvcnlTbmFwc2hvdFIKdHJhamVjdG9yeQ==');
@$core.Deprecated('Use getRotaryTicksRequestDescriptor instead')
const GetRotaryTicksRequest$json = const {
  '1': 'GetRotaryTicksRequest',
  '2': const [
    const {'1': 'recording_name', '3': 1, '4': 1, '5': 9, '10': 'recordingName'},
    const {'1': 'row_id', '3': 2, '4': 1, '5': 13, '10': 'rowId'},
  ],
};

/// Descriptor for `GetRotaryTicksRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRotaryTicksRequestDescriptor = $convert.base64Decode('ChVHZXRSb3RhcnlUaWNrc1JlcXVlc3QSJQoOcmVjb3JkaW5nX25hbWUYASABKAlSDXJlY29yZGluZ05hbWUSFQoGcm93X2lkGAIgASgNUgVyb3dJZA==');
@$core.Deprecated('Use getRotaryTicksResponseDescriptor instead')
const GetRotaryTicksResponse$json = const {
  '1': 'GetRotaryTicksResponse',
  '2': const [
    const {'1': 'records', '3': 1, '4': 3, '5': 11, '6': '.recorder.RotaryTicksRecord', '10': 'records'},
    const {'1': 'wheel_encoder_resolution', '3': 2, '4': 1, '5': 13, '10': 'wheelEncoderResolution'},
  ],
};

/// Descriptor for `GetRotaryTicksResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getRotaryTicksResponseDescriptor = $convert.base64Decode('ChZHZXRSb3RhcnlUaWNrc1Jlc3BvbnNlEjUKB3JlY29yZHMYASADKAsyGy5yZWNvcmRlci5Sb3RhcnlUaWNrc1JlY29yZFIHcmVjb3JkcxI4Chh3aGVlbF9lbmNvZGVyX3Jlc29sdXRpb24YAiABKA1SFndoZWVsRW5jb2RlclJlc29sdXRpb24=');
@$core.Deprecated('Use snapshotPredictImagesRequestDescriptor instead')
const SnapshotPredictImagesRequest$json = const {
  '1': 'SnapshotPredictImagesRequest',
  '2': const [
    const {'1': 'row_id', '3': 1, '4': 1, '5': 13, '10': 'rowId'},
  ],
};

/// Descriptor for `SnapshotPredictImagesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List snapshotPredictImagesRequestDescriptor = $convert.base64Decode('ChxTbmFwc2hvdFByZWRpY3RJbWFnZXNSZXF1ZXN0EhUKBnJvd19pZBgBIAEoDVIFcm93SWQ=');
@$core.Deprecated('Use pcamSnapshotDescriptor instead')
const PcamSnapshot$json = const {
  '1': 'PcamSnapshot',
  '2': const [
    const {'1': 'pcam_id', '3': 1, '4': 1, '5': 9, '10': 'pcamId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
  ],
};

/// Descriptor for `PcamSnapshot`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pcamSnapshotDescriptor = $convert.base64Decode('CgxQY2FtU25hcHNob3QSFwoHcGNhbV9pZBgBIAEoCVIGcGNhbUlkEiEKDHRpbWVzdGFtcF9tcxgCIAEoA1ILdGltZXN0YW1wTXM=');
@$core.Deprecated('Use snapshotPredictImagesResponseDescriptor instead')
const SnapshotPredictImagesResponse$json = const {
  '1': 'SnapshotPredictImagesResponse',
  '2': const [
    const {'1': 'snapshots', '3': 1, '4': 3, '5': 11, '6': '.carbon.frontend.weeding_diagnostics.PcamSnapshot', '10': 'snapshots'},
  ],
};

/// Descriptor for `SnapshotPredictImagesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List snapshotPredictImagesResponseDescriptor = $convert.base64Decode('Ch1TbmFwc2hvdFByZWRpY3RJbWFnZXNSZXNwb25zZRJPCglzbmFwc2hvdHMYASADKAsyMS5jYXJib24uZnJvbnRlbmQud2VlZGluZ19kaWFnbm9zdGljcy5QY2FtU25hcHNob3RSCXNuYXBzaG90cw==');
@$core.Deprecated('Use getChipForPredictImageRequestDescriptor instead')
const GetChipForPredictImageRequest$json = const {
  '1': 'GetChipForPredictImageRequest',
  '2': const [
    const {'1': 'pcam_id', '3': 1, '4': 1, '5': 9, '10': 'pcamId'},
    const {'1': 'timestamp_ms', '3': 2, '4': 1, '5': 3, '10': 'timestampMs'},
    const {'1': 'center_x_px', '3': 3, '4': 1, '5': 5, '10': 'centerXPx'},
    const {'1': 'center_y_px', '3': 4, '4': 1, '5': 5, '10': 'centerYPx'},
    const {'1': 'row_id', '3': 5, '4': 1, '5': 13, '10': 'rowId'},
  ],
};

/// Descriptor for `GetChipForPredictImageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getChipForPredictImageRequestDescriptor = $convert.base64Decode('Ch1HZXRDaGlwRm9yUHJlZGljdEltYWdlUmVxdWVzdBIXCgdwY2FtX2lkGAEgASgJUgZwY2FtSWQSIQoMdGltZXN0YW1wX21zGAIgASgDUgt0aW1lc3RhbXBNcxIeCgtjZW50ZXJfeF9weBgDIAEoBVIJY2VudGVyWFB4Eh4KC2NlbnRlcl95X3B4GAQgASgFUgljZW50ZXJZUHgSFQoGcm93X2lkGAUgASgNUgVyb3dJZA==');
@$core.Deprecated('Use getChipForPredictImageResponseDescriptor instead')
const GetChipForPredictImageResponse$json = const {
  '1': 'GetChipForPredictImageResponse',
  '2': const [
    const {'1': 'chip_image', '3': 1, '4': 1, '5': 12, '10': 'chipImage'},
  ],
};

/// Descriptor for `GetChipForPredictImageResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getChipForPredictImageResponseDescriptor = $convert.base64Decode('Ch5HZXRDaGlwRm9yUHJlZGljdEltYWdlUmVzcG9uc2USHQoKY2hpcF9pbWFnZRgBIAEoDFIJY2hpcEltYWdl');
