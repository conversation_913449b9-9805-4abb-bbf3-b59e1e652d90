///
//  Generated code. Do not modify.
//  source: frontend/target_velocity_estimator.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;
import '../target_velocity_estimator/target_velocity_estimator.pb.dart' as $69;

class GetNextAvailableTVEProfilesResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextAvailableTVEProfilesResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..pc<$69.ProfileDetails>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profiles', $pb.PbFieldType.PM, subBuilder: $69.ProfileDetails.create)
    ..hasRequiredFields = false
  ;

  GetNextAvailableTVEProfilesResponse._() : super();
  factory GetNextAvailableTVEProfilesResponse({
    $1.Timestamp? ts,
    $core.Iterable<$69.ProfileDetails>? profiles,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (profiles != null) {
      _result.profiles.addAll(profiles);
    }
    return _result;
  }
  factory GetNextAvailableTVEProfilesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextAvailableTVEProfilesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextAvailableTVEProfilesResponse clone() => GetNextAvailableTVEProfilesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextAvailableTVEProfilesResponse copyWith(void Function(GetNextAvailableTVEProfilesResponse) updates) => super.copyWith((message) => updates(message as GetNextAvailableTVEProfilesResponse)) as GetNextAvailableTVEProfilesResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextAvailableTVEProfilesResponse create() => GetNextAvailableTVEProfilesResponse._();
  GetNextAvailableTVEProfilesResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextAvailableTVEProfilesResponse> createRepeated() => $pb.PbList<GetNextAvailableTVEProfilesResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextAvailableTVEProfilesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextAvailableTVEProfilesResponse>(create);
  static GetNextAvailableTVEProfilesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<$69.ProfileDetails> get profiles => $_getList(1);
}

class GetNextActiveTVEProfileResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveTVEProfileResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOM<$69.TVEProfile>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $69.TVEProfile.create)
    ..hasRequiredFields = false
  ;

  GetNextActiveTVEProfileResponse._() : super();
  factory GetNextActiveTVEProfileResponse({
    $1.Timestamp? ts,
    $69.TVEProfile? profile,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (profile != null) {
      _result.profile = profile;
    }
    return _result;
  }
  factory GetNextActiveTVEProfileResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveTVEProfileResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveTVEProfileResponse clone() => GetNextActiveTVEProfileResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveTVEProfileResponse copyWith(void Function(GetNextActiveTVEProfileResponse) updates) => super.copyWith((message) => updates(message as GetNextActiveTVEProfileResponse)) as GetNextActiveTVEProfileResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveTVEProfileResponse create() => GetNextActiveTVEProfileResponse._();
  GetNextActiveTVEProfileResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveTVEProfileResponse> createRepeated() => $pb.PbList<GetNextActiveTVEProfileResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveTVEProfileResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveTVEProfileResponse>(create);
  static GetNextActiveTVEProfileResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $69.TVEProfile get profile => $_getN(1);
  @$pb.TagNumber(2)
  set profile($69.TVEProfile v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasProfile() => $_has(1);
  @$pb.TagNumber(2)
  void clearProfile() => clearField(2);
  @$pb.TagNumber(2)
  $69.TVEProfile ensureProfile() => $_ensure(1);
}

class LoadTVEProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadTVEProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  LoadTVEProfileRequest._() : super();
  factory LoadTVEProfileRequest({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory LoadTVEProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadTVEProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadTVEProfileRequest clone() => LoadTVEProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadTVEProfileRequest copyWith(void Function(LoadTVEProfileRequest) updates) => super.copyWith((message) => updates(message as LoadTVEProfileRequest)) as LoadTVEProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadTVEProfileRequest create() => LoadTVEProfileRequest._();
  LoadTVEProfileRequest createEmptyInstance() => create();
  static $pb.PbList<LoadTVEProfileRequest> createRepeated() => $pb.PbList<LoadTVEProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static LoadTVEProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadTVEProfileRequest>(create);
  static LoadTVEProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class LoadTVEProfileResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'LoadTVEProfileResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOM<$69.TVEProfile>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $69.TVEProfile.create)
    ..hasRequiredFields = false
  ;

  LoadTVEProfileResponse._() : super();
  factory LoadTVEProfileResponse({
    $69.TVEProfile? profile,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    return _result;
  }
  factory LoadTVEProfileResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory LoadTVEProfileResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  LoadTVEProfileResponse clone() => LoadTVEProfileResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  LoadTVEProfileResponse copyWith(void Function(LoadTVEProfileResponse) updates) => super.copyWith((message) => updates(message as LoadTVEProfileResponse)) as LoadTVEProfileResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static LoadTVEProfileResponse create() => LoadTVEProfileResponse._();
  LoadTVEProfileResponse createEmptyInstance() => create();
  static $pb.PbList<LoadTVEProfileResponse> createRepeated() => $pb.PbList<LoadTVEProfileResponse>();
  @$core.pragma('dart2js:noInline')
  static LoadTVEProfileResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<LoadTVEProfileResponse>(create);
  static LoadTVEProfileResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $69.TVEProfile get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($69.TVEProfile v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $69.TVEProfile ensureProfile() => $_ensure(0);
}

class SaveTVEProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveTVEProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOM<$69.TVEProfile>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $69.TVEProfile.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'setActive')
    ..hasRequiredFields = false
  ;

  SaveTVEProfileRequest._() : super();
  factory SaveTVEProfileRequest({
    $69.TVEProfile? profile,
    $core.bool? setActive,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (setActive != null) {
      _result.setActive = setActive;
    }
    return _result;
  }
  factory SaveTVEProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveTVEProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveTVEProfileRequest clone() => SaveTVEProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveTVEProfileRequest copyWith(void Function(SaveTVEProfileRequest) updates) => super.copyWith((message) => updates(message as SaveTVEProfileRequest)) as SaveTVEProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveTVEProfileRequest create() => SaveTVEProfileRequest._();
  SaveTVEProfileRequest createEmptyInstance() => create();
  static $pb.PbList<SaveTVEProfileRequest> createRepeated() => $pb.PbList<SaveTVEProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static SaveTVEProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveTVEProfileRequest>(create);
  static SaveTVEProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $69.TVEProfile get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($69.TVEProfile v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $69.TVEProfile ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.bool get setActive => $_getBF(1);
  @$pb.TagNumber(2)
  set setActive($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSetActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearSetActive() => clearField(2);
}

class SaveTVEProfileResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SaveTVEProfileResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  SaveTVEProfileResponse._() : super();
  factory SaveTVEProfileResponse({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory SaveTVEProfileResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SaveTVEProfileResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SaveTVEProfileResponse clone() => SaveTVEProfileResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SaveTVEProfileResponse copyWith(void Function(SaveTVEProfileResponse) updates) => super.copyWith((message) => updates(message as SaveTVEProfileResponse)) as SaveTVEProfileResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SaveTVEProfileResponse create() => SaveTVEProfileResponse._();
  SaveTVEProfileResponse createEmptyInstance() => create();
  static $pb.PbList<SaveTVEProfileResponse> createRepeated() => $pb.PbList<SaveTVEProfileResponse>();
  @$core.pragma('dart2js:noInline')
  static SaveTVEProfileResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SaveTVEProfileResponse>(create);
  static SaveTVEProfileResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class SetActiveTVEProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveTVEProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..hasRequiredFields = false
  ;

  SetActiveTVEProfileRequest._() : super();
  factory SetActiveTVEProfileRequest({
    $core.String? id,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    return _result;
  }
  factory SetActiveTVEProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveTVEProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveTVEProfileRequest clone() => SetActiveTVEProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveTVEProfileRequest copyWith(void Function(SetActiveTVEProfileRequest) updates) => super.copyWith((message) => updates(message as SetActiveTVEProfileRequest)) as SetActiveTVEProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveTVEProfileRequest create() => SetActiveTVEProfileRequest._();
  SetActiveTVEProfileRequest createEmptyInstance() => create();
  static $pb.PbList<SetActiveTVEProfileRequest> createRepeated() => $pb.PbList<SetActiveTVEProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static SetActiveTVEProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveTVEProfileRequest>(create);
  static SetActiveTVEProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
}

class SetActiveTVEProfileResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetActiveTVEProfileResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  SetActiveTVEProfileResponse._() : super();
  factory SetActiveTVEProfileResponse() => create();
  factory SetActiveTVEProfileResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetActiveTVEProfileResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetActiveTVEProfileResponse clone() => SetActiveTVEProfileResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetActiveTVEProfileResponse copyWith(void Function(SetActiveTVEProfileResponse) updates) => super.copyWith((message) => updates(message as SetActiveTVEProfileResponse)) as SetActiveTVEProfileResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetActiveTVEProfileResponse create() => SetActiveTVEProfileResponse._();
  SetActiveTVEProfileResponse createEmptyInstance() => create();
  static $pb.PbList<SetActiveTVEProfileResponse> createRepeated() => $pb.PbList<SetActiveTVEProfileResponse>();
  @$core.pragma('dart2js:noInline')
  static SetActiveTVEProfileResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetActiveTVEProfileResponse>(create);
  static SetActiveTVEProfileResponse? _defaultInstance;
}

class DeleteTVEProfileRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteTVEProfileRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'newActiveId')
    ..hasRequiredFields = false
  ;

  DeleteTVEProfileRequest._() : super();
  factory DeleteTVEProfileRequest({
    $core.String? id,
    $core.String? newActiveId,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (newActiveId != null) {
      _result.newActiveId = newActiveId;
    }
    return _result;
  }
  factory DeleteTVEProfileRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteTVEProfileRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteTVEProfileRequest clone() => DeleteTVEProfileRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteTVEProfileRequest copyWith(void Function(DeleteTVEProfileRequest) updates) => super.copyWith((message) => updates(message as DeleteTVEProfileRequest)) as DeleteTVEProfileRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteTVEProfileRequest create() => DeleteTVEProfileRequest._();
  DeleteTVEProfileRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteTVEProfileRequest> createRepeated() => $pb.PbList<DeleteTVEProfileRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteTVEProfileRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteTVEProfileRequest>(create);
  static DeleteTVEProfileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get newActiveId => $_getSZ(1);
  @$pb.TagNumber(2)
  set newActiveId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasNewActiveId() => $_has(1);
  @$pb.TagNumber(2)
  void clearNewActiveId() => clearField(2);
}

class DeleteTVEProfileResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteTVEProfileResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.target_velocity_estimator'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  DeleteTVEProfileResponse._() : super();
  factory DeleteTVEProfileResponse() => create();
  factory DeleteTVEProfileResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteTVEProfileResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteTVEProfileResponse clone() => DeleteTVEProfileResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteTVEProfileResponse copyWith(void Function(DeleteTVEProfileResponse) updates) => super.copyWith((message) => updates(message as DeleteTVEProfileResponse)) as DeleteTVEProfileResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteTVEProfileResponse create() => DeleteTVEProfileResponse._();
  DeleteTVEProfileResponse createEmptyInstance() => create();
  static $pb.PbList<DeleteTVEProfileResponse> createRepeated() => $pb.PbList<DeleteTVEProfileResponse>();
  @$core.pragma('dart2js:noInline')
  static DeleteTVEProfileResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteTVEProfileResponse>(create);
  static DeleteTVEProfileResponse? _defaultInstance;
}

