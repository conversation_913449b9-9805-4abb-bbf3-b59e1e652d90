///
//  Generated code. Do not modify.
//  source: frontend/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../util/util.pb.dart' as $1;
import '../metrics/metrics_aggregator_service.pb.dart' as $23;
import 'weeding_diagnostics.pb.dart' as $22;

import 'profile_sync.pbenum.dart' as $67;

class JobDescription extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'JobDescription', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs', protoName: 'timestampMs')
    ..hasRequiredFields = false
  ;

  JobDescription._() : super();
  factory JobDescription({
    $core.String? jobId,
    $core.String? name,
    $fixnum.Int64? timestampMs,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    if (name != null) {
      _result.name = name;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    return _result;
  }
  factory JobDescription.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory JobDescription.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  JobDescription clone() => JobDescription()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  JobDescription copyWith(void Function(JobDescription) updates) => super.copyWith((message) => updates(message as JobDescription)) as JobDescription; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static JobDescription create() => JobDescription._();
  JobDescription createEmptyInstance() => create();
  static $pb.PbList<JobDescription> createRepeated() => $pb.PbList<JobDescription>();
  @$core.pragma('dart2js:noInline')
  static JobDescription getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<JobDescription>(create);
  static JobDescription? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timestampMs => $_getI64(2);
  @$pb.TagNumber(3)
  set timestampMs($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimestampMs() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimestampMs() => clearField(3);
}

class ActiveProfile extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ActiveProfile', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..e<$67.ProfileType>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profileType', $pb.PbFieldType.OE, defaultOrMaker: $67.ProfileType.ALMANAC, valueOf: $67.ProfileType.valueOf, enumValues: $67.ProfileType.values)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  ActiveProfile._() : super();
  factory ActiveProfile({
    $67.ProfileType? profileType,
    $core.String? id,
    $core.String? name,
  }) {
    final _result = create();
    if (profileType != null) {
      _result.profileType = profileType;
    }
    if (id != null) {
      _result.id = id;
    }
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory ActiveProfile.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ActiveProfile.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ActiveProfile clone() => ActiveProfile()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ActiveProfile copyWith(void Function(ActiveProfile) updates) => super.copyWith((message) => updates(message as ActiveProfile)) as ActiveProfile; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ActiveProfile create() => ActiveProfile._();
  ActiveProfile createEmptyInstance() => create();
  static $pb.PbList<ActiveProfile> createRepeated() => $pb.PbList<ActiveProfile>();
  @$core.pragma('dart2js:noInline')
  static ActiveProfile getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ActiveProfile>(create);
  static ActiveProfile? _defaultInstance;

  @$pb.TagNumber(1)
  $67.ProfileType get profileType => $_getN(0);
  @$pb.TagNumber(1)
  set profileType($67.ProfileType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfileType() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfileType() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get id => $_getSZ(1);
  @$pb.TagNumber(2)
  set id($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasId() => $_has(1);
  @$pb.TagNumber(2)
  void clearId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);
}

class Job extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Job', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<JobDescription>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobDescription', protoName: 'jobDescription', subBuilder: JobDescription.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingProfile', protoName: 'bandingProfile')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningProfile', protoName: 'thinningProfile')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'stopTimeMs', protoName: 'stopTimeMs')
    ..aInt64(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastUpdateTimeMs', protoName: 'lastUpdateTimeMs')
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expectedAcreage', $pb.PbFieldType.OF, protoName: 'expectedAcreage')
    ..aOB(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'completed')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanac')
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'discriminator')
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingProfileUUID', protoName: 'bandingProfileUUID')
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningProfileUUID', protoName: 'thinningProfileUUID')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanacProfileUUID', protoName: 'almanacProfileUUID')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'discriminatorProfileUUID', protoName: 'discriminatorProfileUUID')
    ..m<$core.int, ActiveProfile>(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeProfiles', entryClassName: 'Job.ActiveProfilesEntry', keyFieldType: $pb.PbFieldType.O3, valueFieldType: $pb.PbFieldType.OM, valueCreator: ActiveProfile.create, packageName: const $pb.PackageName('carbon.frontend.jobs'))
    ..aInt64(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lastUsedTimeMs', protoName: 'lastUsedTimeMs')
    ..hasRequiredFields = false
  ;

  Job._() : super();
  factory Job({
    JobDescription? jobDescription,
    $core.String? bandingProfile,
    $core.String? thinningProfile,
    $fixnum.Int64? stopTimeMs,
    $fixnum.Int64? lastUpdateTimeMs,
    $core.double? expectedAcreage,
    $core.bool? completed,
    $core.String? almanac,
    $core.String? discriminator,
    $core.String? cropId,
    $core.String? bandingProfileUUID,
    $core.String? thinningProfileUUID,
    $core.String? almanacProfileUUID,
    $core.String? discriminatorProfileUUID,
    $core.Map<$core.int, ActiveProfile>? activeProfiles,
    $fixnum.Int64? lastUsedTimeMs,
  }) {
    final _result = create();
    if (jobDescription != null) {
      _result.jobDescription = jobDescription;
    }
    if (bandingProfile != null) {
      _result.bandingProfile = bandingProfile;
    }
    if (thinningProfile != null) {
      _result.thinningProfile = thinningProfile;
    }
    if (stopTimeMs != null) {
      _result.stopTimeMs = stopTimeMs;
    }
    if (lastUpdateTimeMs != null) {
      _result.lastUpdateTimeMs = lastUpdateTimeMs;
    }
    if (expectedAcreage != null) {
      _result.expectedAcreage = expectedAcreage;
    }
    if (completed != null) {
      _result.completed = completed;
    }
    if (almanac != null) {
      _result.almanac = almanac;
    }
    if (discriminator != null) {
      _result.discriminator = discriminator;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (bandingProfileUUID != null) {
      _result.bandingProfileUUID = bandingProfileUUID;
    }
    if (thinningProfileUUID != null) {
      _result.thinningProfileUUID = thinningProfileUUID;
    }
    if (almanacProfileUUID != null) {
      _result.almanacProfileUUID = almanacProfileUUID;
    }
    if (discriminatorProfileUUID != null) {
      _result.discriminatorProfileUUID = discriminatorProfileUUID;
    }
    if (activeProfiles != null) {
      _result.activeProfiles.addAll(activeProfiles);
    }
    if (lastUsedTimeMs != null) {
      _result.lastUsedTimeMs = lastUsedTimeMs;
    }
    return _result;
  }
  factory Job.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Job.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Job clone() => Job()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Job copyWith(void Function(Job) updates) => super.copyWith((message) => updates(message as Job)) as Job; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Job create() => Job._();
  Job createEmptyInstance() => create();
  static $pb.PbList<Job> createRepeated() => $pb.PbList<Job>();
  @$core.pragma('dart2js:noInline')
  static Job getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Job>(create);
  static Job? _defaultInstance;

  @$pb.TagNumber(1)
  JobDescription get jobDescription => $_getN(0);
  @$pb.TagNumber(1)
  set jobDescription(JobDescription v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobDescription() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobDescription() => clearField(1);
  @$pb.TagNumber(1)
  JobDescription ensureJobDescription() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get bandingProfile => $_getSZ(1);
  @$pb.TagNumber(2)
  set bandingProfile($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBandingProfile() => $_has(1);
  @$pb.TagNumber(2)
  void clearBandingProfile() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get thinningProfile => $_getSZ(2);
  @$pb.TagNumber(3)
  set thinningProfile($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasThinningProfile() => $_has(2);
  @$pb.TagNumber(3)
  void clearThinningProfile() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get stopTimeMs => $_getI64(3);
  @$pb.TagNumber(4)
  set stopTimeMs($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasStopTimeMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearStopTimeMs() => clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get lastUpdateTimeMs => $_getI64(4);
  @$pb.TagNumber(5)
  set lastUpdateTimeMs($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLastUpdateTimeMs() => $_has(4);
  @$pb.TagNumber(5)
  void clearLastUpdateTimeMs() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get expectedAcreage => $_getN(5);
  @$pb.TagNumber(6)
  set expectedAcreage($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasExpectedAcreage() => $_has(5);
  @$pb.TagNumber(6)
  void clearExpectedAcreage() => clearField(6);

  @$pb.TagNumber(7)
  $core.bool get completed => $_getBF(6);
  @$pb.TagNumber(7)
  set completed($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasCompleted() => $_has(6);
  @$pb.TagNumber(7)
  void clearCompleted() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get almanac => $_getSZ(7);
  @$pb.TagNumber(8)
  set almanac($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasAlmanac() => $_has(7);
  @$pb.TagNumber(8)
  void clearAlmanac() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get discriminator => $_getSZ(8);
  @$pb.TagNumber(9)
  set discriminator($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDiscriminator() => $_has(8);
  @$pb.TagNumber(9)
  void clearDiscriminator() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get cropId => $_getSZ(9);
  @$pb.TagNumber(10)
  set cropId($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasCropId() => $_has(9);
  @$pb.TagNumber(10)
  void clearCropId() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get bandingProfileUUID => $_getSZ(10);
  @$pb.TagNumber(11)
  set bandingProfileUUID($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasBandingProfileUUID() => $_has(10);
  @$pb.TagNumber(11)
  void clearBandingProfileUUID() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get thinningProfileUUID => $_getSZ(11);
  @$pb.TagNumber(12)
  set thinningProfileUUID($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasThinningProfileUUID() => $_has(11);
  @$pb.TagNumber(12)
  void clearThinningProfileUUID() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get almanacProfileUUID => $_getSZ(12);
  @$pb.TagNumber(13)
  set almanacProfileUUID($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasAlmanacProfileUUID() => $_has(12);
  @$pb.TagNumber(13)
  void clearAlmanacProfileUUID() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get discriminatorProfileUUID => $_getSZ(13);
  @$pb.TagNumber(14)
  set discriminatorProfileUUID($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasDiscriminatorProfileUUID() => $_has(13);
  @$pb.TagNumber(14)
  void clearDiscriminatorProfileUUID() => clearField(14);

  @$pb.TagNumber(15)
  $core.Map<$core.int, ActiveProfile> get activeProfiles => $_getMap(14);

  @$pb.TagNumber(16)
  $fixnum.Int64 get lastUsedTimeMs => $_getI64(15);
  @$pb.TagNumber(16)
  set lastUsedTimeMs($fixnum.Int64 v) { $_setInt64(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasLastUsedTimeMs() => $_has(15);
  @$pb.TagNumber(16)
  void clearLastUsedTimeMs() => clearField(16);
}

class CreateJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreateJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'active')
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expectedAcreage', $pb.PbFieldType.OF, protoName: 'expectedAcreage')
    ..hasRequiredFields = false
  ;

  CreateJobRequest._() : super();
  factory CreateJobRequest({
    $core.String? name,
    $core.bool? active,
    $core.double? expectedAcreage,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    if (active != null) {
      _result.active = active;
    }
    if (expectedAcreage != null) {
      _result.expectedAcreage = expectedAcreage;
    }
    return _result;
  }
  factory CreateJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateJobRequest clone() => CreateJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateJobRequest copyWith(void Function(CreateJobRequest) updates) => super.copyWith((message) => updates(message as CreateJobRequest)) as CreateJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreateJobRequest create() => CreateJobRequest._();
  CreateJobRequest createEmptyInstance() => create();
  static $pb.PbList<CreateJobRequest> createRepeated() => $pb.PbList<CreateJobRequest>();
  @$core.pragma('dart2js:noInline')
  static CreateJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateJobRequest>(create);
  static CreateJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get active => $_getBF(1);
  @$pb.TagNumber(2)
  set active($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasActive() => $_has(1);
  @$pb.TagNumber(2)
  void clearActive() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get expectedAcreage => $_getN(2);
  @$pb.TagNumber(3)
  set expectedAcreage($core.double v) { $_setFloat(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasExpectedAcreage() => $_has(2);
  @$pb.TagNumber(3)
  void clearExpectedAcreage() => clearField(3);
}

class CreateJobResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreateJobResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  CreateJobResponse._() : super();
  factory CreateJobResponse({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory CreateJobResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateJobResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateJobResponse clone() => CreateJobResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateJobResponse copyWith(void Function(CreateJobResponse) updates) => super.copyWith((message) => updates(message as CreateJobResponse)) as CreateJobResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreateJobResponse create() => CreateJobResponse._();
  CreateJobResponse createEmptyInstance() => create();
  static $pb.PbList<CreateJobResponse> createRepeated() => $pb.PbList<CreateJobResponse>();
  @$core.pragma('dart2js:noInline')
  static CreateJobResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateJobResponse>(create);
  static CreateJobResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class UpdateJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UpdateJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<JobDescription>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobDescription', protoName: 'jobDescription', subBuilder: JobDescription.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'expectedAcreage', $pb.PbFieldType.OF, protoName: 'expectedAcreage')
    ..hasRequiredFields = false
  ;

  UpdateJobRequest._() : super();
  factory UpdateJobRequest({
    JobDescription? jobDescription,
    $core.double? expectedAcreage,
  }) {
    final _result = create();
    if (jobDescription != null) {
      _result.jobDescription = jobDescription;
    }
    if (expectedAcreage != null) {
      _result.expectedAcreage = expectedAcreage;
    }
    return _result;
  }
  factory UpdateJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UpdateJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UpdateJobRequest clone() => UpdateJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UpdateJobRequest copyWith(void Function(UpdateJobRequest) updates) => super.copyWith((message) => updates(message as UpdateJobRequest)) as UpdateJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UpdateJobRequest create() => UpdateJobRequest._();
  UpdateJobRequest createEmptyInstance() => create();
  static $pb.PbList<UpdateJobRequest> createRepeated() => $pb.PbList<UpdateJobRequest>();
  @$core.pragma('dart2js:noInline')
  static UpdateJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UpdateJobRequest>(create);
  static UpdateJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  JobDescription get jobDescription => $_getN(0);
  @$pb.TagNumber(1)
  set jobDescription(JobDescription v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobDescription() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobDescription() => clearField(1);
  @$pb.TagNumber(1)
  JobDescription ensureJobDescription() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get expectedAcreage => $_getN(1);
  @$pb.TagNumber(2)
  set expectedAcreage($core.double v) { $_setFloat(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasExpectedAcreage() => $_has(1);
  @$pb.TagNumber(2)
  void clearExpectedAcreage() => clearField(2);
}

class GetNextJobsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextJobsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextJobsRequest._() : super();
  factory GetNextJobsRequest({
    $1.Timestamp? timestamp,
  }) {
    final _result = create();
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    return _result;
  }
  factory GetNextJobsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextJobsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextJobsRequest clone() => GetNextJobsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextJobsRequest copyWith(void Function(GetNextJobsRequest) updates) => super.copyWith((message) => updates(message as GetNextJobsRequest)) as GetNextJobsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextJobsRequest create() => GetNextJobsRequest._();
  GetNextJobsRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextJobsRequest> createRepeated() => $pb.PbList<GetNextJobsRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextJobsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextJobsRequest>(create);
  static GetNextJobsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get timestamp => $_getN(0);
  @$pb.TagNumber(1)
  set timestamp($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestamp() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestamp() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTimestamp() => $_ensure(0);
}

class JobWithMetrics extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'JobWithMetrics', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<Job>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'job', subBuilder: Job.create)
    ..aOM<$23.Metrics>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metrics', subBuilder: $23.Metrics.create)
    ..hasRequiredFields = false
  ;

  JobWithMetrics._() : super();
  factory JobWithMetrics({
    Job? job,
    $23.Metrics? metrics,
  }) {
    final _result = create();
    if (job != null) {
      _result.job = job;
    }
    if (metrics != null) {
      _result.metrics = metrics;
    }
    return _result;
  }
  factory JobWithMetrics.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory JobWithMetrics.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  JobWithMetrics clone() => JobWithMetrics()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  JobWithMetrics copyWith(void Function(JobWithMetrics) updates) => super.copyWith((message) => updates(message as JobWithMetrics)) as JobWithMetrics; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static JobWithMetrics create() => JobWithMetrics._();
  JobWithMetrics createEmptyInstance() => create();
  static $pb.PbList<JobWithMetrics> createRepeated() => $pb.PbList<JobWithMetrics>();
  @$core.pragma('dart2js:noInline')
  static JobWithMetrics getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<JobWithMetrics>(create);
  static JobWithMetrics? _defaultInstance;

  @$pb.TagNumber(1)
  Job get job => $_getN(0);
  @$pb.TagNumber(1)
  set job(Job v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasJob() => $_has(0);
  @$pb.TagNumber(1)
  void clearJob() => clearField(1);
  @$pb.TagNumber(1)
  Job ensureJob() => $_ensure(0);

  @$pb.TagNumber(2)
  $23.Metrics get metrics => $_getN(1);
  @$pb.TagNumber(2)
  set metrics($23.Metrics v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetrics() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetrics() => clearField(2);
  @$pb.TagNumber(2)
  $23.Metrics ensureMetrics() => $_ensure(1);
}

class GetNextJobsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextJobsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..pc<JobWithMetrics>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobs', $pb.PbFieldType.PM, subBuilder: JobWithMetrics.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeJobId', protoName: 'activeJobId')
    ..aOM<$1.Timestamp>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextJobsResponse._() : super();
  factory GetNextJobsResponse({
    $core.Iterable<JobWithMetrics>? jobs,
    $core.String? activeJobId,
    $1.Timestamp? timestamp,
  }) {
    final _result = create();
    if (jobs != null) {
      _result.jobs.addAll(jobs);
    }
    if (activeJobId != null) {
      _result.activeJobId = activeJobId;
    }
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    return _result;
  }
  factory GetNextJobsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextJobsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextJobsResponse clone() => GetNextJobsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextJobsResponse copyWith(void Function(GetNextJobsResponse) updates) => super.copyWith((message) => updates(message as GetNextJobsResponse)) as GetNextJobsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextJobsResponse create() => GetNextJobsResponse._();
  GetNextJobsResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextJobsResponse> createRepeated() => $pb.PbList<GetNextJobsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextJobsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextJobsResponse>(create);
  static GetNextJobsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<JobWithMetrics> get jobs => $_getList(0);

  @$pb.TagNumber(2)
  $core.String get activeJobId => $_getSZ(1);
  @$pb.TagNumber(2)
  set activeJobId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasActiveJobId() => $_has(1);
  @$pb.TagNumber(2)
  void clearActiveJobId() => clearField(2);

  @$pb.TagNumber(3)
  $1.Timestamp get timestamp => $_getN(2);
  @$pb.TagNumber(3)
  set timestamp($1.Timestamp v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimestamp() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimestamp() => clearField(3);
  @$pb.TagNumber(3)
  $1.Timestamp ensureTimestamp() => $_ensure(2);
}

class GetNextActiveJobIdRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveJobIdRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextActiveJobIdRequest._() : super();
  factory GetNextActiveJobIdRequest({
    $1.Timestamp? timestamp,
  }) {
    final _result = create();
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    return _result;
  }
  factory GetNextActiveJobIdRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveJobIdRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveJobIdRequest clone() => GetNextActiveJobIdRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveJobIdRequest copyWith(void Function(GetNextActiveJobIdRequest) updates) => super.copyWith((message) => updates(message as GetNextActiveJobIdRequest)) as GetNextActiveJobIdRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveJobIdRequest create() => GetNextActiveJobIdRequest._();
  GetNextActiveJobIdRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveJobIdRequest> createRepeated() => $pb.PbList<GetNextActiveJobIdRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveJobIdRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveJobIdRequest>(create);
  static GetNextActiveJobIdRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get timestamp => $_getN(0);
  @$pb.TagNumber(1)
  set timestamp($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestamp() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestamp() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTimestamp() => $_ensure(0);
}

class GetNextActiveJobIdResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextActiveJobIdResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeJobId', protoName: 'activeJobId')
    ..aOM<$1.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $1.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetNextActiveJobIdResponse._() : super();
  factory GetNextActiveJobIdResponse({
    $core.String? activeJobId,
    $1.Timestamp? timestamp,
  }) {
    final _result = create();
    if (activeJobId != null) {
      _result.activeJobId = activeJobId;
    }
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    return _result;
  }
  factory GetNextActiveJobIdResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextActiveJobIdResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextActiveJobIdResponse clone() => GetNextActiveJobIdResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextActiveJobIdResponse copyWith(void Function(GetNextActiveJobIdResponse) updates) => super.copyWith((message) => updates(message as GetNextActiveJobIdResponse)) as GetNextActiveJobIdResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextActiveJobIdResponse create() => GetNextActiveJobIdResponse._();
  GetNextActiveJobIdResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextActiveJobIdResponse> createRepeated() => $pb.PbList<GetNextActiveJobIdResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextActiveJobIdResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextActiveJobIdResponse>(create);
  static GetNextActiveJobIdResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get activeJobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set activeJobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasActiveJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearActiveJobId() => clearField(1);

  @$pb.TagNumber(2)
  $1.Timestamp get timestamp => $_getN(1);
  @$pb.TagNumber(2)
  set timestamp($1.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => clearField(2);
  @$pb.TagNumber(2)
  $1.Timestamp ensureTimestamp() => $_ensure(1);
}

class GetJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  GetJobRequest._() : super();
  factory GetJobRequest({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory GetJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetJobRequest clone() => GetJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetJobRequest copyWith(void Function(GetJobRequest) updates) => super.copyWith((message) => updates(message as GetJobRequest)) as GetJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetJobRequest create() => GetJobRequest._();
  GetJobRequest createEmptyInstance() => create();
  static $pb.PbList<GetJobRequest> createRepeated() => $pb.PbList<GetJobRequest>();
  @$core.pragma('dart2js:noInline')
  static GetJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetJobRequest>(create);
  static GetJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class GetJobResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetJobResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<Job>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'job', subBuilder: Job.create)
    ..hasRequiredFields = false
  ;

  GetJobResponse._() : super();
  factory GetJobResponse({
    Job? job,
  }) {
    final _result = create();
    if (job != null) {
      _result.job = job;
    }
    return _result;
  }
  factory GetJobResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetJobResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetJobResponse clone() => GetJobResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetJobResponse copyWith(void Function(GetJobResponse) updates) => super.copyWith((message) => updates(message as GetJobResponse)) as GetJobResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetJobResponse create() => GetJobResponse._();
  GetJobResponse createEmptyInstance() => create();
  static $pb.PbList<GetJobResponse> createRepeated() => $pb.PbList<GetJobResponse>();
  @$core.pragma('dart2js:noInline')
  static GetJobResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetJobResponse>(create);
  static GetJobResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Job get job => $_getN(0);
  @$pb.TagNumber(1)
  set job(Job v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasJob() => $_has(0);
  @$pb.TagNumber(1)
  void clearJob() => clearField(1);
  @$pb.TagNumber(1)
  Job ensureJob() => $_ensure(0);
}

class StartJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'StartJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  StartJobRequest._() : super();
  factory StartJobRequest({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory StartJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory StartJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  StartJobRequest clone() => StartJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  StartJobRequest copyWith(void Function(StartJobRequest) updates) => super.copyWith((message) => updates(message as StartJobRequest)) as StartJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static StartJobRequest create() => StartJobRequest._();
  StartJobRequest createEmptyInstance() => create();
  static $pb.PbList<StartJobRequest> createRepeated() => $pb.PbList<StartJobRequest>();
  @$core.pragma('dart2js:noInline')
  static StartJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StartJobRequest>(create);
  static StartJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class GetConfigDumpRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetConfigDumpRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  GetConfigDumpRequest._() : super();
  factory GetConfigDumpRequest({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory GetConfigDumpRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetConfigDumpRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetConfigDumpRequest clone() => GetConfigDumpRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetConfigDumpRequest copyWith(void Function(GetConfigDumpRequest) updates) => super.copyWith((message) => updates(message as GetConfigDumpRequest)) as GetConfigDumpRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetConfigDumpRequest create() => GetConfigDumpRequest._();
  GetConfigDumpRequest createEmptyInstance() => create();
  static $pb.PbList<GetConfigDumpRequest> createRepeated() => $pb.PbList<GetConfigDumpRequest>();
  @$core.pragma('dart2js:noInline')
  static GetConfigDumpRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetConfigDumpRequest>(create);
  static GetConfigDumpRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class GetConfigDumpResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetConfigDumpResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<$22.ConfigNodeSnapshot>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rootConfig', protoName: 'rootConfig', subBuilder: $22.ConfigNodeSnapshot.create)
    ..hasRequiredFields = false
  ;

  GetConfigDumpResponse._() : super();
  factory GetConfigDumpResponse({
    $22.ConfigNodeSnapshot? rootConfig,
  }) {
    final _result = create();
    if (rootConfig != null) {
      _result.rootConfig = rootConfig;
    }
    return _result;
  }
  factory GetConfigDumpResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetConfigDumpResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetConfigDumpResponse clone() => GetConfigDumpResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetConfigDumpResponse copyWith(void Function(GetConfigDumpResponse) updates) => super.copyWith((message) => updates(message as GetConfigDumpResponse)) as GetConfigDumpResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetConfigDumpResponse create() => GetConfigDumpResponse._();
  GetConfigDumpResponse createEmptyInstance() => create();
  static $pb.PbList<GetConfigDumpResponse> createRepeated() => $pb.PbList<GetConfigDumpResponse>();
  @$core.pragma('dart2js:noInline')
  static GetConfigDumpResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetConfigDumpResponse>(create);
  static GetConfigDumpResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $22.ConfigNodeSnapshot get rootConfig => $_getN(0);
  @$pb.TagNumber(1)
  set rootConfig($22.ConfigNodeSnapshot v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasRootConfig() => $_has(0);
  @$pb.TagNumber(1)
  void clearRootConfig() => clearField(1);
  @$pb.TagNumber(1)
  $22.ConfigNodeSnapshot ensureRootConfig() => $_ensure(0);
}

class GetActiveJobMetricsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetActiveJobMetricsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<$23.Metrics>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobMetrics', protoName: 'jobMetrics', subBuilder: $23.Metrics.create)
    ..hasRequiredFields = false
  ;

  GetActiveJobMetricsResponse._() : super();
  factory GetActiveJobMetricsResponse({
    $23.Metrics? jobMetrics,
  }) {
    final _result = create();
    if (jobMetrics != null) {
      _result.jobMetrics = jobMetrics;
    }
    return _result;
  }
  factory GetActiveJobMetricsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetActiveJobMetricsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetActiveJobMetricsResponse clone() => GetActiveJobMetricsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetActiveJobMetricsResponse copyWith(void Function(GetActiveJobMetricsResponse) updates) => super.copyWith((message) => updates(message as GetActiveJobMetricsResponse)) as GetActiveJobMetricsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetActiveJobMetricsResponse create() => GetActiveJobMetricsResponse._();
  GetActiveJobMetricsResponse createEmptyInstance() => create();
  static $pb.PbList<GetActiveJobMetricsResponse> createRepeated() => $pb.PbList<GetActiveJobMetricsResponse>();
  @$core.pragma('dart2js:noInline')
  static GetActiveJobMetricsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetActiveJobMetricsResponse>(create);
  static GetActiveJobMetricsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $23.Metrics get jobMetrics => $_getN(0);
  @$pb.TagNumber(1)
  set jobMetrics($23.Metrics v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobMetrics() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobMetrics() => clearField(1);
  @$pb.TagNumber(1)
  $23.Metrics ensureJobMetrics() => $_ensure(0);
}

class DeleteJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DeleteJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  DeleteJobRequest._() : super();
  factory DeleteJobRequest({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory DeleteJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteJobRequest clone() => DeleteJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteJobRequest copyWith(void Function(DeleteJobRequest) updates) => super.copyWith((message) => updates(message as DeleteJobRequest)) as DeleteJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DeleteJobRequest create() => DeleteJobRequest._();
  DeleteJobRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteJobRequest> createRepeated() => $pb.PbList<DeleteJobRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteJobRequest>(create);
  static DeleteJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class MarkJobCompletedRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MarkJobCompletedRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  MarkJobCompletedRequest._() : super();
  factory MarkJobCompletedRequest({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory MarkJobCompletedRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MarkJobCompletedRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MarkJobCompletedRequest clone() => MarkJobCompletedRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MarkJobCompletedRequest copyWith(void Function(MarkJobCompletedRequest) updates) => super.copyWith((message) => updates(message as MarkJobCompletedRequest)) as MarkJobCompletedRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MarkJobCompletedRequest create() => MarkJobCompletedRequest._();
  MarkJobCompletedRequest createEmptyInstance() => create();
  static $pb.PbList<MarkJobCompletedRequest> createRepeated() => $pb.PbList<MarkJobCompletedRequest>();
  @$core.pragma('dart2js:noInline')
  static MarkJobCompletedRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MarkJobCompletedRequest>(create);
  static MarkJobCompletedRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class MarkJobIncompleteRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'MarkJobIncompleteRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  MarkJobIncompleteRequest._() : super();
  factory MarkJobIncompleteRequest({
    $core.String? jobId,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory MarkJobIncompleteRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MarkJobIncompleteRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MarkJobIncompleteRequest clone() => MarkJobIncompleteRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MarkJobIncompleteRequest copyWith(void Function(MarkJobIncompleteRequest) updates) => super.copyWith((message) => updates(message as MarkJobIncompleteRequest)) as MarkJobIncompleteRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static MarkJobIncompleteRequest create() => MarkJobIncompleteRequest._();
  MarkJobIncompleteRequest createEmptyInstance() => create();
  static $pb.PbList<MarkJobIncompleteRequest> createRepeated() => $pb.PbList<MarkJobIncompleteRequest>();
  @$core.pragma('dart2js:noInline')
  static MarkJobIncompleteRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MarkJobIncompleteRequest>(create);
  static MarkJobIncompleteRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);
}

class GetNextJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..hasRequiredFields = false
  ;

  GetNextJobRequest._() : super();
  factory GetNextJobRequest({
    $1.Timestamp? ts,
    $core.String? jobId,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (jobId != null) {
      _result.jobId = jobId;
    }
    return _result;
  }
  factory GetNextJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextJobRequest clone() => GetNextJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextJobRequest copyWith(void Function(GetNextJobRequest) updates) => super.copyWith((message) => updates(message as GetNextJobRequest)) as GetNextJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextJobRequest create() => GetNextJobRequest._();
  GetNextJobRequest createEmptyInstance() => create();
  static $pb.PbList<GetNextJobRequest> createRepeated() => $pb.PbList<GetNextJobRequest>();
  @$core.pragma('dart2js:noInline')
  static GetNextJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextJobRequest>(create);
  static GetNextJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get jobId => $_getSZ(1);
  @$pb.TagNumber(2)
  set jobId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasJobId() => $_has(1);
  @$pb.TagNumber(2)
  void clearJobId() => clearField(2);
}

class GetNextJobResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetNextJobResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.frontend.jobs'), createEmptyInstance: create)
    ..aOM<$1.Timestamp>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ts', subBuilder: $1.Timestamp.create)
    ..aOM<JobWithMetrics>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'job', subBuilder: JobWithMetrics.create)
    ..hasRequiredFields = false
  ;

  GetNextJobResponse._() : super();
  factory GetNextJobResponse({
    $1.Timestamp? ts,
    JobWithMetrics? job,
  }) {
    final _result = create();
    if (ts != null) {
      _result.ts = ts;
    }
    if (job != null) {
      _result.job = job;
    }
    return _result;
  }
  factory GetNextJobResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetNextJobResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetNextJobResponse clone() => GetNextJobResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetNextJobResponse copyWith(void Function(GetNextJobResponse) updates) => super.copyWith((message) => updates(message as GetNextJobResponse)) as GetNextJobResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetNextJobResponse create() => GetNextJobResponse._();
  GetNextJobResponse createEmptyInstance() => create();
  static $pb.PbList<GetNextJobResponse> createRepeated() => $pb.PbList<GetNextJobResponse>();
  @$core.pragma('dart2js:noInline')
  static GetNextJobResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetNextJobResponse>(create);
  static GetNextJobResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $1.Timestamp get ts => $_getN(0);
  @$pb.TagNumber(1)
  set ts($1.Timestamp v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTs() => clearField(1);
  @$pb.TagNumber(1)
  $1.Timestamp ensureTs() => $_ensure(0);

  @$pb.TagNumber(2)
  JobWithMetrics get job => $_getN(1);
  @$pb.TagNumber(2)
  set job(JobWithMetrics v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasJob() => $_has(1);
  @$pb.TagNumber(2)
  void clearJob() => clearField(2);
  @$pb.TagNumber(2)
  JobWithMetrics ensureJob() => $_ensure(1);
}

