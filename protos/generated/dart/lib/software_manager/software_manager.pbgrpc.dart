///
//  Generated code. Do not modify.
//  source: software_manager/software_manager.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'software_manager.pb.dart' as $57;
export 'software_manager.pb.dart';

class SoftwareManagerServiceClient extends $grpc.Client {
  static final _$getSoftwareVersionMetadata = $grpc.ClientMethod<
          $57.SoftwareVersionMetadataRequest, $57.SoftwareVersionMetadata>(
      '/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata',
      ($57.SoftwareVersionMetadataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $57.SoftwareVersionMetadata.fromBuffer(value));
  static final _$getVersionsSummary =
      $grpc.ClientMethod<$57.VersionSummaryRequest, $57.VersionSummaryReply>(
          '/carbon.software_manager.SoftwareManagerService/GetVersionsSummary',
          ($57.VersionSummaryRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $57.VersionSummaryReply.fromBuffer(value));
  static final _$triggerUpdate =
      $grpc.ClientMethod<$57.TriggerUpdateRequest, $57.TriggerUpdateReply>(
          '/carbon.software_manager.SoftwareManagerService/TriggerUpdate',
          ($57.TriggerUpdateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $57.TriggerUpdateReply.fromBuffer(value));
  static final _$getIdentity =
      $grpc.ClientMethod<$57.GetIdentityRequest, $57.IdentityInfo>(
          '/carbon.software_manager.SoftwareManagerService/GetIdentity',
          ($57.GetIdentityRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $57.IdentityInfo.fromBuffer(value));
  static final _$clearPackagesCache = $grpc.ClientMethod<
          $57.ClearPackagesCacheRequest, $57.ClearPackagesCacheResponse>(
      '/carbon.software_manager.SoftwareManagerService/ClearPackagesCache',
      ($57.ClearPackagesCacheRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $57.ClearPackagesCacheResponse.fromBuffer(value));
  static final _$prepareUpdate =
      $grpc.ClientMethod<$57.PrepareUpdateRequest, $57.PrepareUpdateResponse>(
          '/carbon.software_manager.SoftwareManagerService/PrepareUpdate',
          ($57.PrepareUpdateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $57.PrepareUpdateResponse.fromBuffer(value));
  static final _$abortUpdate =
      $grpc.ClientMethod<$57.AbortUpdateRequest, $57.AbortUpdateResponse>(
          '/carbon.software_manager.SoftwareManagerService/AbortUpdate',
          ($57.AbortUpdateRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $57.AbortUpdateResponse.fromBuffer(value));

  SoftwareManagerServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$57.SoftwareVersionMetadata> getSoftwareVersionMetadata(
      $57.SoftwareVersionMetadataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getSoftwareVersionMetadata, request,
        options: options);
  }

  $grpc.ResponseFuture<$57.VersionSummaryReply> getVersionsSummary(
      $57.VersionSummaryRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getVersionsSummary, request, options: options);
  }

  $grpc.ResponseFuture<$57.TriggerUpdateReply> triggerUpdate(
      $57.TriggerUpdateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$triggerUpdate, request, options: options);
  }

  $grpc.ResponseFuture<$57.IdentityInfo> getIdentity(
      $57.GetIdentityRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getIdentity, request, options: options);
  }

  $grpc.ResponseFuture<$57.ClearPackagesCacheResponse> clearPackagesCache(
      $57.ClearPackagesCacheRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$clearPackagesCache, request, options: options);
  }

  $grpc.ResponseFuture<$57.PrepareUpdateResponse> prepareUpdate(
      $57.PrepareUpdateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$prepareUpdate, request, options: options);
  }

  $grpc.ResponseFuture<$57.AbortUpdateResponse> abortUpdate(
      $57.AbortUpdateRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$abortUpdate, request, options: options);
  }
}

abstract class SoftwareManagerServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.software_manager.SoftwareManagerService';

  SoftwareManagerServiceBase() {
    $addMethod($grpc.ServiceMethod<$57.SoftwareVersionMetadataRequest,
            $57.SoftwareVersionMetadata>(
        'GetSoftwareVersionMetadata',
        getSoftwareVersionMetadata_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $57.SoftwareVersionMetadataRequest.fromBuffer(value),
        ($57.SoftwareVersionMetadata value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$57.VersionSummaryRequest, $57.VersionSummaryReply>(
            'GetVersionsSummary',
            getVersionsSummary_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $57.VersionSummaryRequest.fromBuffer(value),
            ($57.VersionSummaryReply value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$57.TriggerUpdateRequest, $57.TriggerUpdateReply>(
            'TriggerUpdate',
            triggerUpdate_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $57.TriggerUpdateRequest.fromBuffer(value),
            ($57.TriggerUpdateReply value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$57.GetIdentityRequest, $57.IdentityInfo>(
        'GetIdentity',
        getIdentity_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $57.GetIdentityRequest.fromBuffer(value),
        ($57.IdentityInfo value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$57.ClearPackagesCacheRequest,
            $57.ClearPackagesCacheResponse>(
        'ClearPackagesCache',
        clearPackagesCache_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $57.ClearPackagesCacheRequest.fromBuffer(value),
        ($57.ClearPackagesCacheResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$57.PrepareUpdateRequest,
            $57.PrepareUpdateResponse>(
        'PrepareUpdate',
        prepareUpdate_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $57.PrepareUpdateRequest.fromBuffer(value),
        ($57.PrepareUpdateResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$57.AbortUpdateRequest, $57.AbortUpdateResponse>(
            'AbortUpdate',
            abortUpdate_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $57.AbortUpdateRequest.fromBuffer(value),
            ($57.AbortUpdateResponse value) => value.writeToBuffer()));
  }

  $async.Future<$57.SoftwareVersionMetadata> getSoftwareVersionMetadata_Pre(
      $grpc.ServiceCall call,
      $async.Future<$57.SoftwareVersionMetadataRequest> request) async {
    return getSoftwareVersionMetadata(call, await request);
  }

  $async.Future<$57.VersionSummaryReply> getVersionsSummary_Pre(
      $grpc.ServiceCall call,
      $async.Future<$57.VersionSummaryRequest> request) async {
    return getVersionsSummary(call, await request);
  }

  $async.Future<$57.TriggerUpdateReply> triggerUpdate_Pre(
      $grpc.ServiceCall call,
      $async.Future<$57.TriggerUpdateRequest> request) async {
    return triggerUpdate(call, await request);
  }

  $async.Future<$57.IdentityInfo> getIdentity_Pre($grpc.ServiceCall call,
      $async.Future<$57.GetIdentityRequest> request) async {
    return getIdentity(call, await request);
  }

  $async.Future<$57.ClearPackagesCacheResponse> clearPackagesCache_Pre(
      $grpc.ServiceCall call,
      $async.Future<$57.ClearPackagesCacheRequest> request) async {
    return clearPackagesCache(call, await request);
  }

  $async.Future<$57.PrepareUpdateResponse> prepareUpdate_Pre(
      $grpc.ServiceCall call,
      $async.Future<$57.PrepareUpdateRequest> request) async {
    return prepareUpdate(call, await request);
  }

  $async.Future<$57.AbortUpdateResponse> abortUpdate_Pre($grpc.ServiceCall call,
      $async.Future<$57.AbortUpdateRequest> request) async {
    return abortUpdate(call, await request);
  }

  $async.Future<$57.SoftwareVersionMetadata> getSoftwareVersionMetadata(
      $grpc.ServiceCall call, $57.SoftwareVersionMetadataRequest request);
  $async.Future<$57.VersionSummaryReply> getVersionsSummary(
      $grpc.ServiceCall call, $57.VersionSummaryRequest request);
  $async.Future<$57.TriggerUpdateReply> triggerUpdate(
      $grpc.ServiceCall call, $57.TriggerUpdateRequest request);
  $async.Future<$57.IdentityInfo> getIdentity(
      $grpc.ServiceCall call, $57.GetIdentityRequest request);
  $async.Future<$57.ClearPackagesCacheResponse> clearPackagesCache(
      $grpc.ServiceCall call, $57.ClearPackagesCacheRequest request);
  $async.Future<$57.PrepareUpdateResponse> prepareUpdate(
      $grpc.ServiceCall call, $57.PrepareUpdateRequest request);
  $async.Future<$57.AbortUpdateResponse> abortUpdate(
      $grpc.ServiceCall call, $57.AbortUpdateRequest request);
}
