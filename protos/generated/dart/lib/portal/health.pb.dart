///
//  Generated code. Do not modify.
//  source: portal/health.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../frontend/alarm.pb.dart' as $8;
import '../frontend/laser.pb.dart' as $25;
import '../metrics/metrics.pb.dart' as $77;

import 'health.pbenum.dart';
import '../frontend/status_bar.pbenum.dart' as $34;

export 'health.pbenum.dart';

class AlarmRow extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AlarmRow', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarmCode')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..e<AlarmLevel>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'level', $pb.PbFieldType.OE, defaultOrMaker: AlarmLevel.ALARM_UNKNOWN, valueOf: AlarmLevel.valueOf, enumValues: AlarmLevel.values)
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'identifier')
    ..aOB(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'acknowledged')
    ..e<AlarmImpact>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'impact', $pb.PbFieldType.OE, defaultOrMaker: AlarmImpact.IMPACT_UNDEFINED, valueOf: AlarmImpact.valueOf, enumValues: AlarmImpact.values)
    ..hasRequiredFields = false
  ;

  AlarmRow._() : super();
  factory AlarmRow({
    $fixnum.Int64? timestampMs,
    $core.String? alarmCode,
    $core.String? description,
    AlarmLevel? level,
    $core.String? identifier,
    $core.bool? acknowledged,
    AlarmImpact? impact,
  }) {
    final _result = create();
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (alarmCode != null) {
      _result.alarmCode = alarmCode;
    }
    if (description != null) {
      _result.description = description;
    }
    if (level != null) {
      _result.level = level;
    }
    if (identifier != null) {
      _result.identifier = identifier;
    }
    if (acknowledged != null) {
      _result.acknowledged = acknowledged;
    }
    if (impact != null) {
      _result.impact = impact;
    }
    return _result;
  }
  factory AlarmRow.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AlarmRow.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AlarmRow clone() => AlarmRow()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AlarmRow copyWith(void Function(AlarmRow) updates) => super.copyWith((message) => updates(message as AlarmRow)) as AlarmRow; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AlarmRow create() => AlarmRow._();
  AlarmRow createEmptyInstance() => create();
  static $pb.PbList<AlarmRow> createRepeated() => $pb.PbList<AlarmRow>();
  @$core.pragma('dart2js:noInline')
  static AlarmRow getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AlarmRow>(create);
  static AlarmRow? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get timestampMs => $_getI64(0);
  @$pb.TagNumber(1)
  set timestampMs($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTimestampMs() => $_has(0);
  @$pb.TagNumber(1)
  void clearTimestampMs() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get alarmCode => $_getSZ(1);
  @$pb.TagNumber(2)
  set alarmCode($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAlarmCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearAlarmCode() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get description => $_getSZ(2);
  @$pb.TagNumber(3)
  set description($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDescription() => $_has(2);
  @$pb.TagNumber(3)
  void clearDescription() => clearField(3);

  @$pb.TagNumber(4)
  AlarmLevel get level => $_getN(3);
  @$pb.TagNumber(4)
  set level(AlarmLevel v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasLevel() => $_has(3);
  @$pb.TagNumber(4)
  void clearLevel() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get identifier => $_getSZ(4);
  @$pb.TagNumber(5)
  set identifier($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIdentifier() => $_has(4);
  @$pb.TagNumber(5)
  void clearIdentifier() => clearField(5);

  @$pb.TagNumber(6)
  $core.bool get acknowledged => $_getBF(5);
  @$pb.TagNumber(6)
  set acknowledged($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAcknowledged() => $_has(5);
  @$pb.TagNumber(6)
  void clearAcknowledged() => clearField(6);

  @$pb.TagNumber(7)
  AlarmImpact get impact => $_getN(6);
  @$pb.TagNumber(7)
  set impact(AlarmImpact v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasImpact() => $_has(6);
  @$pb.TagNumber(7)
  void clearImpact() => clearField(7);
}

class Location extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Location', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'x', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'y', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'z', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  Location._() : super();
  factory Location({
    $core.double? x,
    $core.double? y,
    $core.double? z,
  }) {
    final _result = create();
    if (x != null) {
      _result.x = x;
    }
    if (y != null) {
      _result.y = y;
    }
    if (z != null) {
      _result.z = z;
    }
    return _result;
  }
  factory Location.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Location.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Location clone() => Location()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Location copyWith(void Function(Location) updates) => super.copyWith((message) => updates(message as Location)) as Location; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Location create() => Location._();
  Location createEmptyInstance() => create();
  static $pb.PbList<Location> createRepeated() => $pb.PbList<Location>();
  @$core.pragma('dart2js:noInline')
  static Location getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Location>(create);
  static Location? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get x => $_getN(0);
  @$pb.TagNumber(1)
  set x($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasX() => $_has(0);
  @$pb.TagNumber(1)
  void clearX() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get y => $_getN(1);
  @$pb.TagNumber(2)
  set y($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasY() => $_has(1);
  @$pb.TagNumber(2)
  void clearY() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get z => $_getN(2);
  @$pb.TagNumber(3)
  set z($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasZ() => $_has(2);
  @$pb.TagNumber(3)
  void clearZ() => clearField(3);
}

class FieldConfig extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FieldConfig', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingEnabled')
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingDynamic')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeBandConfig')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeThinningConfigId')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeJobId')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeAlmanacId')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeDiscriminatorId')
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isWeeding')
    ..aOB(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isThinning')
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeBandConfigName')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeThinningConfigName')
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeJobName')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeAlmanacName')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeDiscriminatorName')
    ..aOS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeModelinatorId')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeVelocityEstimatorId')
    ..aOS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeVelocityEstimatorName')
    ..aOS(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'activeCategoryCollectionId')
    ..hasRequiredFields = false
  ;

  FieldConfig._() : super();
  factory FieldConfig({
    $core.bool? bandingEnabled,
    $core.bool? bandingDynamic,
    $core.String? activeBandConfig,
    $core.String? activeThinningConfigId,
    $core.String? activeJobId,
    $core.String? activeAlmanacId,
    $core.String? activeDiscriminatorId,
    $core.bool? isWeeding,
    $core.bool? isThinning,
    $core.String? activeBandConfigName,
    $core.String? activeThinningConfigName,
    $core.String? activeJobName,
    $core.String? activeAlmanacName,
    $core.String? activeDiscriminatorName,
    $core.String? activeModelinatorId,
    $core.String? activeVelocityEstimatorId,
    $core.String? activeVelocityEstimatorName,
    $core.String? activeCategoryCollectionId,
  }) {
    final _result = create();
    if (bandingEnabled != null) {
      _result.bandingEnabled = bandingEnabled;
    }
    if (bandingDynamic != null) {
      _result.bandingDynamic = bandingDynamic;
    }
    if (activeBandConfig != null) {
      _result.activeBandConfig = activeBandConfig;
    }
    if (activeThinningConfigId != null) {
      _result.activeThinningConfigId = activeThinningConfigId;
    }
    if (activeJobId != null) {
      _result.activeJobId = activeJobId;
    }
    if (activeAlmanacId != null) {
      _result.activeAlmanacId = activeAlmanacId;
    }
    if (activeDiscriminatorId != null) {
      _result.activeDiscriminatorId = activeDiscriminatorId;
    }
    if (isWeeding != null) {
      _result.isWeeding = isWeeding;
    }
    if (isThinning != null) {
      _result.isThinning = isThinning;
    }
    if (activeBandConfigName != null) {
      _result.activeBandConfigName = activeBandConfigName;
    }
    if (activeThinningConfigName != null) {
      _result.activeThinningConfigName = activeThinningConfigName;
    }
    if (activeJobName != null) {
      _result.activeJobName = activeJobName;
    }
    if (activeAlmanacName != null) {
      _result.activeAlmanacName = activeAlmanacName;
    }
    if (activeDiscriminatorName != null) {
      _result.activeDiscriminatorName = activeDiscriminatorName;
    }
    if (activeModelinatorId != null) {
      _result.activeModelinatorId = activeModelinatorId;
    }
    if (activeVelocityEstimatorId != null) {
      _result.activeVelocityEstimatorId = activeVelocityEstimatorId;
    }
    if (activeVelocityEstimatorName != null) {
      _result.activeVelocityEstimatorName = activeVelocityEstimatorName;
    }
    if (activeCategoryCollectionId != null) {
      _result.activeCategoryCollectionId = activeCategoryCollectionId;
    }
    return _result;
  }
  factory FieldConfig.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FieldConfig.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FieldConfig clone() => FieldConfig()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FieldConfig copyWith(void Function(FieldConfig) updates) => super.copyWith((message) => updates(message as FieldConfig)) as FieldConfig; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FieldConfig create() => FieldConfig._();
  FieldConfig createEmptyInstance() => create();
  static $pb.PbList<FieldConfig> createRepeated() => $pb.PbList<FieldConfig>();
  @$core.pragma('dart2js:noInline')
  static FieldConfig getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FieldConfig>(create);
  static FieldConfig? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get bandingEnabled => $_getBF(0);
  @$pb.TagNumber(1)
  set bandingEnabled($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBandingEnabled() => $_has(0);
  @$pb.TagNumber(1)
  void clearBandingEnabled() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get bandingDynamic => $_getBF(1);
  @$pb.TagNumber(2)
  set bandingDynamic($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBandingDynamic() => $_has(1);
  @$pb.TagNumber(2)
  void clearBandingDynamic() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get activeBandConfig => $_getSZ(2);
  @$pb.TagNumber(3)
  set activeBandConfig($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasActiveBandConfig() => $_has(2);
  @$pb.TagNumber(3)
  void clearActiveBandConfig() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get activeThinningConfigId => $_getSZ(3);
  @$pb.TagNumber(4)
  set activeThinningConfigId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasActiveThinningConfigId() => $_has(3);
  @$pb.TagNumber(4)
  void clearActiveThinningConfigId() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get activeJobId => $_getSZ(4);
  @$pb.TagNumber(5)
  set activeJobId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasActiveJobId() => $_has(4);
  @$pb.TagNumber(5)
  void clearActiveJobId() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get activeAlmanacId => $_getSZ(5);
  @$pb.TagNumber(6)
  set activeAlmanacId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasActiveAlmanacId() => $_has(5);
  @$pb.TagNumber(6)
  void clearActiveAlmanacId() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get activeDiscriminatorId => $_getSZ(6);
  @$pb.TagNumber(7)
  set activeDiscriminatorId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasActiveDiscriminatorId() => $_has(6);
  @$pb.TagNumber(7)
  void clearActiveDiscriminatorId() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get isWeeding => $_getBF(7);
  @$pb.TagNumber(8)
  set isWeeding($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasIsWeeding() => $_has(7);
  @$pb.TagNumber(8)
  void clearIsWeeding() => clearField(8);

  @$pb.TagNumber(9)
  $core.bool get isThinning => $_getBF(8);
  @$pb.TagNumber(9)
  set isThinning($core.bool v) { $_setBool(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasIsThinning() => $_has(8);
  @$pb.TagNumber(9)
  void clearIsThinning() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get activeBandConfigName => $_getSZ(9);
  @$pb.TagNumber(10)
  set activeBandConfigName($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasActiveBandConfigName() => $_has(9);
  @$pb.TagNumber(10)
  void clearActiveBandConfigName() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get activeThinningConfigName => $_getSZ(10);
  @$pb.TagNumber(11)
  set activeThinningConfigName($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasActiveThinningConfigName() => $_has(10);
  @$pb.TagNumber(11)
  void clearActiveThinningConfigName() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get activeJobName => $_getSZ(11);
  @$pb.TagNumber(12)
  set activeJobName($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasActiveJobName() => $_has(11);
  @$pb.TagNumber(12)
  void clearActiveJobName() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get activeAlmanacName => $_getSZ(12);
  @$pb.TagNumber(13)
  set activeAlmanacName($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasActiveAlmanacName() => $_has(12);
  @$pb.TagNumber(13)
  void clearActiveAlmanacName() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get activeDiscriminatorName => $_getSZ(13);
  @$pb.TagNumber(14)
  set activeDiscriminatorName($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasActiveDiscriminatorName() => $_has(13);
  @$pb.TagNumber(14)
  void clearActiveDiscriminatorName() => clearField(14);

  @$pb.TagNumber(15)
  $core.String get activeModelinatorId => $_getSZ(14);
  @$pb.TagNumber(15)
  set activeModelinatorId($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasActiveModelinatorId() => $_has(14);
  @$pb.TagNumber(15)
  void clearActiveModelinatorId() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get activeVelocityEstimatorId => $_getSZ(15);
  @$pb.TagNumber(16)
  set activeVelocityEstimatorId($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasActiveVelocityEstimatorId() => $_has(15);
  @$pb.TagNumber(16)
  void clearActiveVelocityEstimatorId() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get activeVelocityEstimatorName => $_getSZ(16);
  @$pb.TagNumber(17)
  set activeVelocityEstimatorName($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasActiveVelocityEstimatorName() => $_has(16);
  @$pb.TagNumber(17)
  void clearActiveVelocityEstimatorName() => clearField(17);

  @$pb.TagNumber(18)
  $core.String get activeCategoryCollectionId => $_getSZ(17);
  @$pb.TagNumber(18)
  set activeCategoryCollectionId($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasActiveCategoryCollectionId() => $_has(17);
  @$pb.TagNumber(18)
  void clearActiveCategoryCollectionId() => clearField(18);
}

class Versions extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Versions', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'current')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'latest')
    ..hasRequiredFields = false
  ;

  Versions._() : super();
  factory Versions({
    $core.String? current,
    $core.String? latest,
  }) {
    final _result = create();
    if (current != null) {
      _result.current = current;
    }
    if (latest != null) {
      _result.latest = latest;
    }
    return _result;
  }
  factory Versions.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Versions.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Versions clone() => Versions()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Versions copyWith(void Function(Versions) updates) => super.copyWith((message) => updates(message as Versions)) as Versions; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Versions create() => Versions._();
  Versions createEmptyInstance() => create();
  static $pb.PbList<Versions> createRepeated() => $pb.PbList<Versions>();
  @$core.pragma('dart2js:noInline')
  static Versions getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Versions>(create);
  static Versions? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get current => $_getSZ(0);
  @$pb.TagNumber(1)
  set current($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCurrent() => $_has(0);
  @$pb.TagNumber(1)
  void clearCurrent() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get latest => $_getSZ(1);
  @$pb.TagNumber(2)
  set latest($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLatest() => $_has(1);
  @$pb.TagNumber(2)
  void clearLatest() => clearField(2);
}

class WeedingPerformance extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'WeedingPerformance', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'areaWeededTotal', $pb.PbFieldType.OD)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'areaWeededToday', $pb.PbFieldType.OD)
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timeWeededToday')
    ..hasRequiredFields = false
  ;

  WeedingPerformance._() : super();
  factory WeedingPerformance({
    $core.double? areaWeededTotal,
    $core.double? areaWeededToday,
    $fixnum.Int64? timeWeededToday,
  }) {
    final _result = create();
    if (areaWeededTotal != null) {
      _result.areaWeededTotal = areaWeededTotal;
    }
    if (areaWeededToday != null) {
      _result.areaWeededToday = areaWeededToday;
    }
    if (timeWeededToday != null) {
      _result.timeWeededToday = timeWeededToday;
    }
    return _result;
  }
  factory WeedingPerformance.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WeedingPerformance.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  WeedingPerformance clone() => WeedingPerformance()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  WeedingPerformance copyWith(void Function(WeedingPerformance) updates) => super.copyWith((message) => updates(message as WeedingPerformance)) as WeedingPerformance; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static WeedingPerformance create() => WeedingPerformance._();
  WeedingPerformance createEmptyInstance() => create();
  static $pb.PbList<WeedingPerformance> createRepeated() => $pb.PbList<WeedingPerformance>();
  @$core.pragma('dart2js:noInline')
  static WeedingPerformance getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WeedingPerformance>(create);
  static WeedingPerformance? _defaultInstance;

  @$pb.TagNumber(1)
  $core.double get areaWeededTotal => $_getN(0);
  @$pb.TagNumber(1)
  set areaWeededTotal($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasAreaWeededTotal() => $_has(0);
  @$pb.TagNumber(1)
  void clearAreaWeededTotal() => clearField(1);

  @$pb.TagNumber(2)
  $core.double get areaWeededToday => $_getN(1);
  @$pb.TagNumber(2)
  set areaWeededToday($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAreaWeededToday() => $_has(1);
  @$pb.TagNumber(2)
  void clearAreaWeededToday() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timeWeededToday => $_getI64(2);
  @$pb.TagNumber(3)
  set timeWeededToday($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTimeWeededToday() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimeWeededToday() => clearField(3);
}

class Performance extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Performance', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..aOM<WeedingPerformance>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weeding', subBuilder: WeedingPerformance.create)
    ..hasRequiredFields = false
  ;

  Performance._() : super();
  factory Performance({
    WeedingPerformance? weeding,
  }) {
    final _result = create();
    if (weeding != null) {
      _result.weeding = weeding;
    }
    return _result;
  }
  factory Performance.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Performance.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Performance clone() => Performance()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Performance copyWith(void Function(Performance) updates) => super.copyWith((message) => updates(message as Performance)) as Performance; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Performance create() => Performance._();
  Performance createEmptyInstance() => create();
  static $pb.PbList<Performance> createRepeated() => $pb.PbList<Performance>();
  @$core.pragma('dart2js:noInline')
  static Performance getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Performance>(create);
  static Performance? _defaultInstance;

  @$pb.TagNumber(1)
  WeedingPerformance get weeding => $_getN(0);
  @$pb.TagNumber(1)
  set weeding(WeedingPerformance v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasWeeding() => $_has(0);
  @$pb.TagNumber(1)
  void clearWeeding() => clearField(1);
  @$pb.TagNumber(1)
  WeedingPerformance ensureWeeding() => $_ensure(0);
}

class DailyMetrics extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DailyMetrics', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..m<$core.String, $core.String>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metrics', entryClassName: 'DailyMetrics.MetricsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.portal.health'))
    ..hasRequiredFields = false
  ;

  DailyMetrics._() : super();
  factory DailyMetrics({
    $core.Map<$core.String, $core.String>? metrics,
  }) {
    final _result = create();
    if (metrics != null) {
      _result.metrics.addAll(metrics);
    }
    return _result;
  }
  factory DailyMetrics.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DailyMetrics.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DailyMetrics clone() => DailyMetrics()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DailyMetrics copyWith(void Function(DailyMetrics) updates) => super.copyWith((message) => updates(message as DailyMetrics)) as DailyMetrics; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DailyMetrics create() => DailyMetrics._();
  DailyMetrics createEmptyInstance() => create();
  static $pb.PbList<DailyMetrics> createRepeated() => $pb.PbList<DailyMetrics>();
  @$core.pragma('dart2js:noInline')
  static DailyMetrics getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DailyMetrics>(create);
  static DailyMetrics? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, $core.String> get metrics => $_getMap(0);
}

class Metrics extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Metrics', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..m<$core.String, DailyMetrics>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'dailyMetrics', entryClassName: 'Metrics.DailyMetricsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: DailyMetrics.create, packageName: const $pb.PackageName('carbon.portal.health'))
    ..hasRequiredFields = false
  ;

  Metrics._() : super();
  factory Metrics({
    $core.Map<$core.String, DailyMetrics>? dailyMetrics,
  }) {
    final _result = create();
    if (dailyMetrics != null) {
      _result.dailyMetrics.addAll(dailyMetrics);
    }
    return _result;
  }
  factory Metrics.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Metrics.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Metrics clone() => Metrics()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Metrics copyWith(void Function(Metrics) updates) => super.copyWith((message) => updates(message as Metrics)) as Metrics; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Metrics create() => Metrics._();
  Metrics createEmptyInstance() => create();
  static $pb.PbList<Metrics> createRepeated() => $pb.PbList<Metrics>();
  @$core.pragma('dart2js:noInline')
  static Metrics getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Metrics>(create);
  static Metrics? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, DailyMetrics> get dailyMetrics => $_getMap(0);
}

class HealthLog extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'HealthLog', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..pc<AlarmRow>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarms', $pb.PbFieldType.PM, subBuilder: AlarmRow.create)
    ..aOM<Location>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location', subBuilder: Location.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'model')
    ..pPS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'models')
    ..aOM<Performance>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'performance', subBuilder: Performance.create)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reportedAt')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..e<$34.Status>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: $34.Status.STATUS_ERROR, valueOf: $34.Status.valueOf, enumValues: $34.Status.values)
    ..aInt64(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusChangedAt')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'p2p')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'softwareVersion')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVersion')
    ..aOB(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetVersionReady')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'statusMessage')
    ..m<$core.String, $fixnum.Int64>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metricTotals', entryClassName: 'HealthLog.MetricTotalsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OU6, packageName: const $pb.PackageName('carbon.portal.health'))
    ..pc<$8.AlarmRow>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarmList', $pb.PbFieldType.PM, subBuilder: $8.AlarmRow.create)
    ..aOM<FieldConfig>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fieldConfig', subBuilder: FieldConfig.create)
    ..aOM<Metrics>(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metrics', subBuilder: Metrics.create)
    ..aOS(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..a<$core.int>(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotRuntime240v', $pb.PbFieldType.OU3, protoName: 'robot_runtime_240v')
    ..aOM<$25.LaserStateList>(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'laserState', subBuilder: $25.LaserStateList.create)
    ..aOM<$77.LaserChangeTimes>(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'laserChangeTimes', subBuilder: $77.LaserChangeTimes.create)
    ..m<$core.String, $core.String>(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'hostSerials', entryClassName: 'HealthLog.HostSerialsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OS, packageName: const $pb.PackageName('carbon.portal.health'))
    ..m<$core.String, $core.bool>(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'featureFlags', entryClassName: 'HealthLog.FeatureFlagsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OB, packageName: const $pb.PackageName('carbon.portal.health'))
    ..hasRequiredFields = false
  ;

  HealthLog._() : super();
  factory HealthLog({
  @$core.Deprecated('This field is deprecated.')
    $core.Iterable<AlarmRow>? alarms,
    Location? location,
    $core.String? model,
    $core.Iterable<$core.String>? models,
    Performance? performance,
    $fixnum.Int64? reportedAt,
    $core.String? robotSerial,
    $34.Status? status,
    $fixnum.Int64? statusChangedAt,
  @$core.Deprecated('This field is deprecated.')
    $core.String? crop,
    $core.String? p2p,
    $core.String? softwareVersion,
    $core.String? targetVersion,
    $core.bool? targetVersionReady,
    $core.String? statusMessage,
    $core.Map<$core.String, $fixnum.Int64>? metricTotals,
    $core.Iterable<$8.AlarmRow>? alarmList,
    FieldConfig? fieldConfig,
    Metrics? metrics,
    $core.String? cropId,
    $core.int? robotRuntime240v,
    $25.LaserStateList? laserState,
    $77.LaserChangeTimes? laserChangeTimes,
    $core.Map<$core.String, $core.String>? hostSerials,
    $core.Map<$core.String, $core.bool>? featureFlags,
  }) {
    final _result = create();
    if (alarms != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.alarms.addAll(alarms);
    }
    if (location != null) {
      _result.location = location;
    }
    if (model != null) {
      _result.model = model;
    }
    if (models != null) {
      _result.models.addAll(models);
    }
    if (performance != null) {
      _result.performance = performance;
    }
    if (reportedAt != null) {
      _result.reportedAt = reportedAt;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (status != null) {
      _result.status = status;
    }
    if (statusChangedAt != null) {
      _result.statusChangedAt = statusChangedAt;
    }
    if (crop != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.crop = crop;
    }
    if (p2p != null) {
      _result.p2p = p2p;
    }
    if (softwareVersion != null) {
      _result.softwareVersion = softwareVersion;
    }
    if (targetVersion != null) {
      _result.targetVersion = targetVersion;
    }
    if (targetVersionReady != null) {
      _result.targetVersionReady = targetVersionReady;
    }
    if (statusMessage != null) {
      _result.statusMessage = statusMessage;
    }
    if (metricTotals != null) {
      _result.metricTotals.addAll(metricTotals);
    }
    if (alarmList != null) {
      _result.alarmList.addAll(alarmList);
    }
    if (fieldConfig != null) {
      _result.fieldConfig = fieldConfig;
    }
    if (metrics != null) {
      _result.metrics = metrics;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (robotRuntime240v != null) {
      _result.robotRuntime240v = robotRuntime240v;
    }
    if (laserState != null) {
      _result.laserState = laserState;
    }
    if (laserChangeTimes != null) {
      _result.laserChangeTimes = laserChangeTimes;
    }
    if (hostSerials != null) {
      _result.hostSerials.addAll(hostSerials);
    }
    if (featureFlags != null) {
      _result.featureFlags.addAll(featureFlags);
    }
    return _result;
  }
  factory HealthLog.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory HealthLog.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  HealthLog clone() => HealthLog()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  HealthLog copyWith(void Function(HealthLog) updates) => super.copyWith((message) => updates(message as HealthLog)) as HealthLog; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static HealthLog create() => HealthLog._();
  HealthLog createEmptyInstance() => create();
  static $pb.PbList<HealthLog> createRepeated() => $pb.PbList<HealthLog>();
  @$core.pragma('dart2js:noInline')
  static HealthLog getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HealthLog>(create);
  static HealthLog? _defaultInstance;

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(1)
  $core.List<AlarmRow> get alarms => $_getList(0);

  @$pb.TagNumber(2)
  Location get location => $_getN(1);
  @$pb.TagNumber(2)
  set location(Location v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);
  @$pb.TagNumber(2)
  Location ensureLocation() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.String get model => $_getSZ(2);
  @$pb.TagNumber(3)
  set model($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasModel() => $_has(2);
  @$pb.TagNumber(3)
  void clearModel() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<$core.String> get models => $_getList(3);

  @$pb.TagNumber(5)
  Performance get performance => $_getN(4);
  @$pb.TagNumber(5)
  set performance(Performance v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasPerformance() => $_has(4);
  @$pb.TagNumber(5)
  void clearPerformance() => clearField(5);
  @$pb.TagNumber(5)
  Performance ensurePerformance() => $_ensure(4);

  @$pb.TagNumber(6)
  $fixnum.Int64 get reportedAt => $_getI64(5);
  @$pb.TagNumber(6)
  set reportedAt($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasReportedAt() => $_has(5);
  @$pb.TagNumber(6)
  void clearReportedAt() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get robotSerial => $_getSZ(6);
  @$pb.TagNumber(7)
  set robotSerial($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRobotSerial() => $_has(6);
  @$pb.TagNumber(7)
  void clearRobotSerial() => clearField(7);

  @$pb.TagNumber(9)
  $34.Status get status => $_getN(7);
  @$pb.TagNumber(9)
  set status($34.Status v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasStatus() => $_has(7);
  @$pb.TagNumber(9)
  void clearStatus() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get statusChangedAt => $_getI64(8);
  @$pb.TagNumber(10)
  set statusChangedAt($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(10)
  $core.bool hasStatusChangedAt() => $_has(8);
  @$pb.TagNumber(10)
  void clearStatusChangedAt() => clearField(10);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.String get crop => $_getSZ(9);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  set crop($core.String v) { $_setString(9, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  $core.bool hasCrop() => $_has(9);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(11)
  void clearCrop() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get p2p => $_getSZ(10);
  @$pb.TagNumber(12)
  set p2p($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(12)
  $core.bool hasP2p() => $_has(10);
  @$pb.TagNumber(12)
  void clearP2p() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get softwareVersion => $_getSZ(11);
  @$pb.TagNumber(13)
  set softwareVersion($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(13)
  $core.bool hasSoftwareVersion() => $_has(11);
  @$pb.TagNumber(13)
  void clearSoftwareVersion() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get targetVersion => $_getSZ(12);
  @$pb.TagNumber(14)
  set targetVersion($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(14)
  $core.bool hasTargetVersion() => $_has(12);
  @$pb.TagNumber(14)
  void clearTargetVersion() => clearField(14);

  @$pb.TagNumber(15)
  $core.bool get targetVersionReady => $_getBF(13);
  @$pb.TagNumber(15)
  set targetVersionReady($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(15)
  $core.bool hasTargetVersionReady() => $_has(13);
  @$pb.TagNumber(15)
  void clearTargetVersionReady() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get statusMessage => $_getSZ(14);
  @$pb.TagNumber(16)
  set statusMessage($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(16)
  $core.bool hasStatusMessage() => $_has(14);
  @$pb.TagNumber(16)
  void clearStatusMessage() => clearField(16);

  @$pb.TagNumber(17)
  $core.Map<$core.String, $fixnum.Int64> get metricTotals => $_getMap(15);

  @$pb.TagNumber(18)
  $core.List<$8.AlarmRow> get alarmList => $_getList(16);

  @$pb.TagNumber(19)
  FieldConfig get fieldConfig => $_getN(17);
  @$pb.TagNumber(19)
  set fieldConfig(FieldConfig v) { setField(19, v); }
  @$pb.TagNumber(19)
  $core.bool hasFieldConfig() => $_has(17);
  @$pb.TagNumber(19)
  void clearFieldConfig() => clearField(19);
  @$pb.TagNumber(19)
  FieldConfig ensureFieldConfig() => $_ensure(17);

  @$pb.TagNumber(20)
  Metrics get metrics => $_getN(18);
  @$pb.TagNumber(20)
  set metrics(Metrics v) { setField(20, v); }
  @$pb.TagNumber(20)
  $core.bool hasMetrics() => $_has(18);
  @$pb.TagNumber(20)
  void clearMetrics() => clearField(20);
  @$pb.TagNumber(20)
  Metrics ensureMetrics() => $_ensure(18);

  @$pb.TagNumber(21)
  $core.String get cropId => $_getSZ(19);
  @$pb.TagNumber(21)
  set cropId($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(21)
  $core.bool hasCropId() => $_has(19);
  @$pb.TagNumber(21)
  void clearCropId() => clearField(21);

  @$pb.TagNumber(22)
  $core.int get robotRuntime240v => $_getIZ(20);
  @$pb.TagNumber(22)
  set robotRuntime240v($core.int v) { $_setUnsignedInt32(20, v); }
  @$pb.TagNumber(22)
  $core.bool hasRobotRuntime240v() => $_has(20);
  @$pb.TagNumber(22)
  void clearRobotRuntime240v() => clearField(22);

  @$pb.TagNumber(23)
  $25.LaserStateList get laserState => $_getN(21);
  @$pb.TagNumber(23)
  set laserState($25.LaserStateList v) { setField(23, v); }
  @$pb.TagNumber(23)
  $core.bool hasLaserState() => $_has(21);
  @$pb.TagNumber(23)
  void clearLaserState() => clearField(23);
  @$pb.TagNumber(23)
  $25.LaserStateList ensureLaserState() => $_ensure(21);

  @$pb.TagNumber(24)
  $77.LaserChangeTimes get laserChangeTimes => $_getN(22);
  @$pb.TagNumber(24)
  set laserChangeTimes($77.LaserChangeTimes v) { setField(24, v); }
  @$pb.TagNumber(24)
  $core.bool hasLaserChangeTimes() => $_has(22);
  @$pb.TagNumber(24)
  void clearLaserChangeTimes() => clearField(24);
  @$pb.TagNumber(24)
  $77.LaserChangeTimes ensureLaserChangeTimes() => $_ensure(22);

  @$pb.TagNumber(25)
  $core.Map<$core.String, $core.String> get hostSerials => $_getMap(23);

  @$pb.TagNumber(26)
  $core.Map<$core.String, $core.bool> get featureFlags => $_getMap(24);
}

class HistoryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'HistoryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..pc<HealthLog>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'logs', $pb.PbFieldType.PM, subBuilder: HealthLog.create)
    ..aOB(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'queryLimitReached')
    ..hasRequiredFields = false
  ;

  HistoryResponse._() : super();
  factory HistoryResponse({
    $core.Iterable<HealthLog>? logs,
    $core.bool? queryLimitReached,
  }) {
    final _result = create();
    if (logs != null) {
      _result.logs.addAll(logs);
    }
    if (queryLimitReached != null) {
      _result.queryLimitReached = queryLimitReached;
    }
    return _result;
  }
  factory HistoryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory HistoryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  HistoryResponse clone() => HistoryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  HistoryResponse copyWith(void Function(HistoryResponse) updates) => super.copyWith((message) => updates(message as HistoryResponse)) as HistoryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static HistoryResponse create() => HistoryResponse._();
  HistoryResponse createEmptyInstance() => create();
  static $pb.PbList<HistoryResponse> createRepeated() => $pb.PbList<HistoryResponse>();
  @$core.pragma('dart2js:noInline')
  static HistoryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HistoryResponse>(create);
  static HistoryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<HealthLog> get logs => $_getList(0);

  @$pb.TagNumber(2)
  $core.bool get queryLimitReached => $_getBF(1);
  @$pb.TagNumber(2)
  set queryLimitReached($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasQueryLimitReached() => $_has(1);
  @$pb.TagNumber(2)
  void clearQueryLimitReached() => clearField(2);
}

class IssueReport extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'IssueReport', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.health'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'phoneNumber')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotSerial')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reportedAt')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelId')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'softwareVersion')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..hasRequiredFields = false
  ;

  IssueReport._() : super();
  factory IssueReport({
    $core.String? description,
    $core.String? phoneNumber,
    $core.String? robotSerial,
    $fixnum.Int64? reportedAt,
  @$core.Deprecated('This field is deprecated.')
    $core.String? crop,
    $core.String? modelId,
    $core.String? softwareVersion,
    $core.String? cropId,
  }) {
    final _result = create();
    if (description != null) {
      _result.description = description;
    }
    if (phoneNumber != null) {
      _result.phoneNumber = phoneNumber;
    }
    if (robotSerial != null) {
      _result.robotSerial = robotSerial;
    }
    if (reportedAt != null) {
      _result.reportedAt = reportedAt;
    }
    if (crop != null) {
      // ignore: deprecated_member_use_from_same_package
      _result.crop = crop;
    }
    if (modelId != null) {
      _result.modelId = modelId;
    }
    if (softwareVersion != null) {
      _result.softwareVersion = softwareVersion;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    return _result;
  }
  factory IssueReport.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory IssueReport.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  IssueReport clone() => IssueReport()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  IssueReport copyWith(void Function(IssueReport) updates) => super.copyWith((message) => updates(message as IssueReport)) as IssueReport; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static IssueReport create() => IssueReport._();
  IssueReport createEmptyInstance() => create();
  static $pb.PbList<IssueReport> createRepeated() => $pb.PbList<IssueReport>();
  @$core.pragma('dart2js:noInline')
  static IssueReport getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<IssueReport>(create);
  static IssueReport? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get description => $_getSZ(0);
  @$pb.TagNumber(1)
  set description($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDescription() => $_has(0);
  @$pb.TagNumber(1)
  void clearDescription() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get phoneNumber => $_getSZ(1);
  @$pb.TagNumber(2)
  set phoneNumber($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPhoneNumber() => $_has(1);
  @$pb.TagNumber(2)
  void clearPhoneNumber() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get robotSerial => $_getSZ(2);
  @$pb.TagNumber(3)
  set robotSerial($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotSerial() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotSerial() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get reportedAt => $_getI64(3);
  @$pb.TagNumber(4)
  set reportedAt($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasReportedAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearReportedAt() => clearField(4);

  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(5)
  $core.String get crop => $_getSZ(4);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(5)
  set crop($core.String v) { $_setString(4, v); }
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(5)
  $core.bool hasCrop() => $_has(4);
  @$core.Deprecated('This field is deprecated.')
  @$pb.TagNumber(5)
  void clearCrop() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get modelId => $_getSZ(5);
  @$pb.TagNumber(6)
  set modelId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasModelId() => $_has(5);
  @$pb.TagNumber(6)
  void clearModelId() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get softwareVersion => $_getSZ(6);
  @$pb.TagNumber(7)
  set softwareVersion($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSoftwareVersion() => $_has(6);
  @$pb.TagNumber(7)
  void clearSoftwareVersion() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get cropId => $_getSZ(7);
  @$pb.TagNumber(8)
  set cropId($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasCropId() => $_has(7);
  @$pb.TagNumber(8)
  void clearCropId() => clearField(8);
}

