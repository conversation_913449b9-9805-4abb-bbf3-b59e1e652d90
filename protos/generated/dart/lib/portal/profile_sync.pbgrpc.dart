///
//  Generated code. Do not modify.
//  source: portal/profile_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'profile_sync.pb.dart' as $47;
import '../util/util.pb.dart' as $1;
export 'profile_sync.pb.dart';

class PortalProfileSyncServiceClient extends $grpc.Client {
  static final _$getProfilesData = $grpc.ClientMethod<
          $47.GetProfilesDataRequest, $47.GetProfilesDataResponse>(
      '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData',
      ($47.GetProfilesDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $47.GetProfilesDataResponse.fromBuffer(value));
  static final _$uploadProfile =
      $grpc.ClientMethod<$47.UploadProfileRequest, $1.Empty>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile',
          ($47.UploadProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getProfile =
      $grpc.ClientMethod<$47.GetProfileRequest, $47.GetProfileResponse>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile',
          ($47.GetProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $47.GetProfileResponse.fromBuffer(value));
  static final _$deleteProfile =
      $grpc.ClientMethod<$47.DeleteProfileRequest, $1.Empty>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile',
          ($47.DeleteProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$purgeProfile =
      $grpc.ClientMethod<$47.PurgeProfileRequest, $1.Empty>(
          '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile',
          ($47.PurgeProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getSetActiveProfileCommands = $grpc.ClientMethod<
          $47.GetSetActiveProfileCommandsRequest,
          $47.GetSetActiveProfileCommandsResponse>(
      '/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands',
      ($47.GetSetActiveProfileCommandsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $47.GetSetActiveProfileCommandsResponse.fromBuffer(value));
  static final _$purgeSetActiveProfileCommands = $grpc.ClientMethod<
          $47.PurgeSetActiveProfileCommandsRequest, $1.Empty>(
      '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands',
      ($47.PurgeSetActiveProfileCommandsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  PortalProfileSyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$47.GetProfilesDataResponse> getProfilesData(
      $47.GetProfilesDataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfilesData, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadProfile($47.UploadProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadProfile, request, options: options);
  }

  $grpc.ResponseFuture<$47.GetProfileResponse> getProfile(
      $47.GetProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> deleteProfile($47.DeleteProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeProfile($47.PurgeProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeProfile, request, options: options);
  }

  $grpc.ResponseFuture<$47.GetSetActiveProfileCommandsResponse>
      getSetActiveProfileCommands(
          $47.GetSetActiveProfileCommandsRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getSetActiveProfileCommands, request,
        options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeSetActiveProfileCommands(
      $47.PurgeSetActiveProfileCommandsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeSetActiveProfileCommands, request,
        options: options);
  }
}

abstract class PortalProfileSyncServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.portal.profile_sync.PortalProfileSyncService';

  PortalProfileSyncServiceBase() {
    $addMethod($grpc.ServiceMethod<$47.GetProfilesDataRequest,
            $47.GetProfilesDataResponse>(
        'GetProfilesData',
        getProfilesData_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $47.GetProfilesDataRequest.fromBuffer(value),
        ($47.GetProfilesDataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$47.UploadProfileRequest, $1.Empty>(
        'UploadProfile',
        uploadProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $47.UploadProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$47.GetProfileRequest, $47.GetProfileResponse>(
            'GetProfile',
            getProfile_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $47.GetProfileRequest.fromBuffer(value),
            ($47.GetProfileResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$47.DeleteProfileRequest, $1.Empty>(
        'DeleteProfile',
        deleteProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $47.DeleteProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$47.PurgeProfileRequest, $1.Empty>(
        'PurgeProfile',
        purgeProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $47.PurgeProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$47.GetSetActiveProfileCommandsRequest,
            $47.GetSetActiveProfileCommandsResponse>(
        'GetSetActiveProfileCommands',
        getSetActiveProfileCommands_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $47.GetSetActiveProfileCommandsRequest.fromBuffer(value),
        ($47.GetSetActiveProfileCommandsResponse value) =>
            value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$47.PurgeSetActiveProfileCommandsRequest, $1.Empty>(
            'PurgeSetActiveProfileCommands',
            purgeSetActiveProfileCommands_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $47.PurgeSetActiveProfileCommandsRequest.fromBuffer(value),
            ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$47.GetProfilesDataResponse> getProfilesData_Pre(
      $grpc.ServiceCall call,
      $async.Future<$47.GetProfilesDataRequest> request) async {
    return getProfilesData(call, await request);
  }

  $async.Future<$1.Empty> uploadProfile_Pre($grpc.ServiceCall call,
      $async.Future<$47.UploadProfileRequest> request) async {
    return uploadProfile(call, await request);
  }

  $async.Future<$47.GetProfileResponse> getProfile_Pre($grpc.ServiceCall call,
      $async.Future<$47.GetProfileRequest> request) async {
    return getProfile(call, await request);
  }

  $async.Future<$1.Empty> deleteProfile_Pre($grpc.ServiceCall call,
      $async.Future<$47.DeleteProfileRequest> request) async {
    return deleteProfile(call, await request);
  }

  $async.Future<$1.Empty> purgeProfile_Pre($grpc.ServiceCall call,
      $async.Future<$47.PurgeProfileRequest> request) async {
    return purgeProfile(call, await request);
  }

  $async.Future<$47.GetSetActiveProfileCommandsResponse>
      getSetActiveProfileCommands_Pre($grpc.ServiceCall call,
          $async.Future<$47.GetSetActiveProfileCommandsRequest> request) async {
    return getSetActiveProfileCommands(call, await request);
  }

  $async.Future<$1.Empty> purgeSetActiveProfileCommands_Pre(
      $grpc.ServiceCall call,
      $async.Future<$47.PurgeSetActiveProfileCommandsRequest> request) async {
    return purgeSetActiveProfileCommands(call, await request);
  }

  $async.Future<$47.GetProfilesDataResponse> getProfilesData(
      $grpc.ServiceCall call, $47.GetProfilesDataRequest request);
  $async.Future<$1.Empty> uploadProfile(
      $grpc.ServiceCall call, $47.UploadProfileRequest request);
  $async.Future<$47.GetProfileResponse> getProfile(
      $grpc.ServiceCall call, $47.GetProfileRequest request);
  $async.Future<$1.Empty> deleteProfile(
      $grpc.ServiceCall call, $47.DeleteProfileRequest request);
  $async.Future<$1.Empty> purgeProfile(
      $grpc.ServiceCall call, $47.PurgeProfileRequest request);
  $async.Future<$47.GetSetActiveProfileCommandsResponse>
      getSetActiveProfileCommands($grpc.ServiceCall call,
          $47.GetSetActiveProfileCommandsRequest request);
  $async.Future<$1.Empty> purgeSetActiveProfileCommands(
      $grpc.ServiceCall call, $47.PurgeSetActiveProfileCommandsRequest request);
}
