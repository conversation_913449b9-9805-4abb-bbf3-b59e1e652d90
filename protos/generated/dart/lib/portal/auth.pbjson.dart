///
//  Generated code. Do not modify.
//  source: portal/auth.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,deprecated_member_use_from_same_package,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
@$core.Deprecated('Use userDisplayRoleDescriptor instead')
const UserDisplayRole$json = const {
  '1': 'UserDisplayRole',
  '2': const [
    const {'1': 'unknown_role', '2': 0},
    const {'1': 'robot_role', '2': 1},
    const {'1': 'operator_basic', '2': 2},
    const {'1': 'farm_manager', '2': 3},
    const {'1': 'carbon_tech', '2': 4},
    const {'1': 'carbon_basic', '2': 5},
    const {'1': 'operator_advanced', '2': 6},
  ],
};

/// Descriptor for `UserDisplayRole`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List userDisplayRoleDescriptor = $convert.base64Decode('Cg9Vc2VyRGlzcGxheVJvbGUSEAoMdW5rbm93bl9yb2xlEAASDgoKcm9ib3Rfcm9sZRABEhIKDm9wZXJhdG9yX2Jhc2ljEAISEAoMZmFybV9tYW5hZ2VyEAMSDwoLY2FyYm9uX3RlY2gQBBIQCgxjYXJib25fYmFzaWMQBRIVChFvcGVyYXRvcl9hZHZhbmNlZBAG');
@$core.Deprecated('Use permissionActionDescriptor instead')
const PermissionAction$json = const {
  '1': 'PermissionAction',
  '2': const [
    const {'1': 'unknown_action', '2': 0},
    const {'1': 'read', '2': 1},
    const {'1': 'update', '2': 2},
  ],
};

/// Descriptor for `PermissionAction`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List permissionActionDescriptor = $convert.base64Decode('ChBQZXJtaXNzaW9uQWN0aW9uEhIKDnVua25vd25fYWN0aW9uEAASCAoEcmVhZBABEgoKBnVwZGF0ZRAC');
@$core.Deprecated('Use permissionResourceDescriptor instead')
const PermissionResource$json = const {
  '1': 'PermissionResource',
  '2': const [
    const {'1': 'unknown', '2': 0},
    const {'1': 'alarms_customer', '2': 1},
    const {'1': 'alarms_internal', '2': 2},
    const {'1': 'almanacs', '2': 3},
    const {'1': 'operator_settings', '2': 4},
    const {'1': 'banding', '2': 5},
    const {'1': 'banding_basic', '2': 6},
    const {'1': 'banding_advanced', '2': 7},
    const {'1': 'calibration', '2': 8},
    const {'1': 'cameras', '2': 9},
    const {'1': 'capture', '2': 10},
    const {'1': 'chat', '2': 11},
    const {'1': 'configs', '2': 12},
    const {'1': 'crops', '2': 13},
    const {'1': 'models_basic', '2': 14},
    const {'1': 'models_pinned', '2': 15},
    const {'1': 'models_nickname', '2': 16},
    const {'1': 'thresholds', '2': 17},
    const {'1': 'diagnostics', '2': 18},
    const {'1': 'guides', '2': 19},
    const {'1': 'jobs', '2': 20},
    const {'1': 'captcha', '2': 21},
    const {'1': 'lasers_row', '2': 22},
    const {'1': 'lasers_basic', '2': 23},
    const {'1': 'lasers_disable', '2': 24},
    const {'1': 'lasers_enable', '2': 25},
    const {'1': 'lasers_advanced', '2': 26},
    const {'1': 'lasers', '2': 27},
    const {'1': 'feeds', '2': 28},
    const {'1': 'models_advanced', '2': 29},
    const {'1': 'hardware', '2': 30},
    const {'1': 'quick_tune', '2': 31},
    const {'1': 'robot_ui', '2': 32},
    const {'1': 'thinning', '2': 33},
    const {'1': 'thinning_basic', '2': 34},
    const {'1': 'thinning_advanced', '2': 35},
    const {'1': 'software_basic', '2': 36},
    const {'1': 'software_advanced', '2': 37},
    const {'1': 'velocity_estimators', '2': 38},
    const {'1': 'vizualization_basic', '2': 39},
    const {'1': 'vizualization_advanced', '2': 40},
    const {'1': 'metrics', '2': 41},
    const {'1': 'mode', '2': 42},
    const {'1': 'jobs_select', '2': 43},
    const {'1': 'portal_settings', '2': 44},
    const {
      '1': 'portal_labs',
      '2': 45,
      '3': const {'1': true},
    },
    const {'1': 'shortcuts_internal', '2': 46},
    const {'1': 'admin_alarms', '2': 47},
    const {'1': 'admin_cloud', '2': 48},
    const {'1': 'customers', '2': 49},
    const {'1': 'users', '2': 50},
    const {'1': 'users_invite', '2': 51},
    const {'1': 'users_permissions', '2': 52},
    const {'1': 'reports', '2': 53},
    const {'1': 'robots', '2': 54},
    const {'1': 'robots_assign', '2': 55},
    const {'1': 'robots_status', '2': 56},
    const {'1': 'robots_health', '2': 57},
    const {'1': 'veselka', '2': 58},
    const {'1': 'metrics_internal', '2': 59},
    const {'1': 'metrics_customer', '2': 60},
    const {'1': 'crops_basic', '2': 61},
    const {'1': 'crops_advanced', '2': 62},
    const {'1': 'discriminators', '2': 63},
    const {'1': 'operator_map', '2': 64},
    const {'1': 'uploads', '2': 65},
    const {'1': 'farms', '2': 66},
    const {'1': 'images', '2': 67},
    const {'1': 'autotractor_jobs', '2': 68},
    const {'1': 'plant_category_profiles', '2': 69},
    const {'1': 'portal_globals', '2': 70},
    const {'1': 'autotractor_drive', '2': 71},
  ],
};

/// Descriptor for `PermissionResource`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List permissionResourceDescriptor = $convert.base64Decode('ChJQZXJtaXNzaW9uUmVzb3VyY2USCwoHdW5rbm93bhAAEhMKD2FsYXJtc19jdXN0b21lchABEhMKD2FsYXJtc19pbnRlcm5hbBACEgwKCGFsbWFuYWNzEAMSFQoRb3BlcmF0b3Jfc2V0dGluZ3MQBBILCgdiYW5kaW5nEAUSEQoNYmFuZGluZ19iYXNpYxAGEhQKEGJhbmRpbmdfYWR2YW5jZWQQBxIPCgtjYWxpYnJhdGlvbhAIEgsKB2NhbWVyYXMQCRILCgdjYXB0dXJlEAoSCAoEY2hhdBALEgsKB2NvbmZpZ3MQDBIJCgVjcm9wcxANEhAKDG1vZGVsc19iYXNpYxAOEhEKDW1vZGVsc19waW5uZWQQDxITCg9tb2RlbHNfbmlja25hbWUQEBIOCgp0aHJlc2hvbGRzEBESDwoLZGlhZ25vc3RpY3MQEhIKCgZndWlkZXMQExIICgRqb2JzEBQSCwoHY2FwdGNoYRAVEg4KCmxhc2Vyc19yb3cQFhIQCgxsYXNlcnNfYmFzaWMQFxISCg5sYXNlcnNfZGlzYWJsZRAYEhEKDWxhc2Vyc19lbmFibGUQGRITCg9sYXNlcnNfYWR2YW5jZWQQGhIKCgZsYXNlcnMQGxIJCgVmZWVkcxAcEhMKD21vZGVsc19hZHZhbmNlZBAdEgwKCGhhcmR3YXJlEB4SDgoKcXVpY2tfdHVuZRAfEgwKCHJvYm90X3VpECASDAoIdGhpbm5pbmcQIRISCg50aGlubmluZ19iYXNpYxAiEhUKEXRoaW5uaW5nX2FkdmFuY2VkECMSEgoOc29mdHdhcmVfYmFzaWMQJBIVChFzb2Z0d2FyZV9hZHZhbmNlZBAlEhcKE3ZlbG9jaXR5X2VzdGltYXRvcnMQJhIXChN2aXp1YWxpemF0aW9uX2Jhc2ljECcSGgoWdml6dWFsaXphdGlvbl9hZHZhbmNlZBAoEgsKB21ldHJpY3MQKRIICgRtb2RlECoSDwoLam9ic19zZWxlY3QQKxITCg9wb3J0YWxfc2V0dGluZ3MQLBITCgtwb3J0YWxfbGFicxAtGgIIARIWChJzaG9ydGN1dHNfaW50ZXJuYWwQLhIQCgxhZG1pbl9hbGFybXMQLxIPCgthZG1pbl9jbG91ZBAwEg0KCWN1c3RvbWVycxAxEgkKBXVzZXJzEDISEAoMdXNlcnNfaW52aXRlEDMSFQoRdXNlcnNfcGVybWlzc2lvbnMQNBILCgdyZXBvcnRzEDUSCgoGcm9ib3RzEDYSEQoNcm9ib3RzX2Fzc2lnbhA3EhEKDXJvYm90c19zdGF0dXMQOBIRCg1yb2JvdHNfaGVhbHRoEDkSCwoHdmVzZWxrYRA6EhQKEG1ldHJpY3NfaW50ZXJuYWwQOxIUChBtZXRyaWNzX2N1c3RvbWVyEDwSDwoLY3JvcHNfYmFzaWMQPRISCg5jcm9wc19hZHZhbmNlZBA+EhIKDmRpc2NyaW1pbmF0b3JzED8SEAoMb3BlcmF0b3JfbWFwEEASCwoHdXBsb2FkcxBBEgkKBWZhcm1zEEISCgoGaW1hZ2VzEEMSFAoQYXV0b3RyYWN0b3Jfam9icxBEEhsKF3BsYW50X2NhdGVnb3J5X3Byb2ZpbGVzEEUSEgoOcG9ydGFsX2dsb2JhbHMQRhIVChFhdXRvdHJhY3Rvcl9kcml2ZRBH');
@$core.Deprecated('Use permissionDomainDescriptor instead')
const PermissionDomain$json = const {
  '1': 'PermissionDomain',
  '2': const [
    const {'1': 'unknown_domain', '2': 0},
    const {'1': 'none', '2': 1},
    const {'1': 'self', '2': 2},
    const {'1': 'robot', '2': 3},
    const {'1': 'customer', '2': 4},
    const {'1': 'all', '2': 5},
    const {'1': 'templates', '2': 6},
  ],
};

/// Descriptor for `PermissionDomain`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List permissionDomainDescriptor = $convert.base64Decode('ChBQZXJtaXNzaW9uRG9tYWluEhIKDnVua25vd25fZG9tYWluEAASCAoEbm9uZRABEggKBHNlbGYQAhIJCgVyb2JvdBADEgwKCGN1c3RvbWVyEAQSBwoDYWxsEAUSDQoJdGVtcGxhdGVzEAY=');
