///
//  Generated code. Do not modify.
//  source: portal/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'jobs.pb.dart' as $44;
import '../util/util.pb.dart' as $1;
export 'jobs.pb.dart';

class PortalJobsServiceClient extends $grpc.Client {
  static final _$uploadJob = $grpc.ClientMethod<$44.UploadJobRequest, $1.Empty>(
      '/carbon.portal.jobs.PortalJobsService/UploadJob',
      ($44.UploadJobRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$uploadJobConfigDump =
      $grpc.ClientMethod<$44.UploadJobConfigDumpRequest, $1.Empty>(
          '/carbon.portal.jobs.PortalJobsService/UploadJobConfigDump',
          ($44.UploadJobConfigDumpRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$uploadJobMetrics =
      $grpc.ClientMethod<$44.UploadJobMetricsRequest, $1.Empty>(
          '/carbon.portal.jobs.PortalJobsService/UploadJobMetrics',
          ($44.UploadJobMetricsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  PortalJobsServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> uploadJob($44.UploadJobRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadJob, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadJobConfigDump(
      $44.UploadJobConfigDumpRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadJobConfigDump, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadJobMetrics(
      $44.UploadJobMetricsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadJobMetrics, request, options: options);
  }
}

abstract class PortalJobsServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.jobs.PortalJobsService';

  PortalJobsServiceBase() {
    $addMethod($grpc.ServiceMethod<$44.UploadJobRequest, $1.Empty>(
        'UploadJob',
        uploadJob_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $44.UploadJobRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$44.UploadJobConfigDumpRequest, $1.Empty>(
        'UploadJobConfigDump',
        uploadJobConfigDump_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $44.UploadJobConfigDumpRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$44.UploadJobMetricsRequest, $1.Empty>(
        'UploadJobMetrics',
        uploadJobMetrics_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $44.UploadJobMetricsRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> uploadJob_Pre($grpc.ServiceCall call,
      $async.Future<$44.UploadJobRequest> request) async {
    return uploadJob(call, await request);
  }

  $async.Future<$1.Empty> uploadJobConfigDump_Pre($grpc.ServiceCall call,
      $async.Future<$44.UploadJobConfigDumpRequest> request) async {
    return uploadJobConfigDump(call, await request);
  }

  $async.Future<$1.Empty> uploadJobMetrics_Pre($grpc.ServiceCall call,
      $async.Future<$44.UploadJobMetricsRequest> request) async {
    return uploadJobMetrics(call, await request);
  }

  $async.Future<$1.Empty> uploadJob(
      $grpc.ServiceCall call, $44.UploadJobRequest request);
  $async.Future<$1.Empty> uploadJobConfigDump(
      $grpc.ServiceCall call, $44.UploadJobConfigDumpRequest request);
  $async.Future<$1.Empty> uploadJobMetrics(
      $grpc.ServiceCall call, $44.UploadJobMetricsRequest request);
}
