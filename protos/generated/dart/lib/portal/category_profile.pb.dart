///
//  Generated code. Do not modify.
//  source: portal/category_profile.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../category_profile/category_profile.pb.dart' as $64;

class Metadata extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Metadata', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updatedAt')
    ..hasRequiredFields = false
  ;

  Metadata._() : super();
  factory Metadata({
    $fixnum.Int64? updatedAt,
  }) {
    final _result = create();
    if (updatedAt != null) {
      _result.updatedAt = updatedAt;
    }
    return _result;
  }
  factory Metadata.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Metadata.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Metadata clone() => Metadata()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Metadata copyWith(void Function(Metadata) updates) => super.copyWith((message) => updates(message as Metadata)) as Metadata; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Metadata create() => Metadata._();
  Metadata createEmptyInstance() => create();
  static $pb.PbList<Metadata> createRepeated() => $pb.PbList<Metadata>();
  @$core.pragma('dart2js:noInline')
  static Metadata getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Metadata>(create);
  static Metadata? _defaultInstance;

  @$pb.TagNumber(6)
  $fixnum.Int64 get updatedAt => $_getI64(0);
  @$pb.TagNumber(6)
  set updatedAt($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(6)
  $core.bool hasUpdatedAt() => $_has(0);
  @$pb.TagNumber(6)
  void clearUpdatedAt() => clearField(6);
}

class CategoryCollectionResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CategoryCollectionResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<$64.CategoryCollection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $64.CategoryCollection.create)
    ..aOM<Metadata>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', subBuilder: Metadata.create)
    ..hasRequiredFields = false
  ;

  CategoryCollectionResponse._() : super();
  factory CategoryCollectionResponse({
    $64.CategoryCollection? profile,
    Metadata? metadata,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (metadata != null) {
      _result.metadata = metadata;
    }
    return _result;
  }
  factory CategoryCollectionResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CategoryCollectionResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CategoryCollectionResponse clone() => CategoryCollectionResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CategoryCollectionResponse copyWith(void Function(CategoryCollectionResponse) updates) => super.copyWith((message) => updates(message as CategoryCollectionResponse)) as CategoryCollectionResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CategoryCollectionResponse create() => CategoryCollectionResponse._();
  CategoryCollectionResponse createEmptyInstance() => create();
  static $pb.PbList<CategoryCollectionResponse> createRepeated() => $pb.PbList<CategoryCollectionResponse>();
  @$core.pragma('dart2js:noInline')
  static CategoryCollectionResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CategoryCollectionResponse>(create);
  static CategoryCollectionResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $64.CategoryCollection get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($64.CategoryCollection v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $64.CategoryCollection ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  Metadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata(Metadata v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => clearField(2);
  @$pb.TagNumber(2)
  Metadata ensureMetadata() => $_ensure(1);
}

class CategoryResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CategoryResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<$64.Category>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $64.Category.create)
    ..aOM<Metadata>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadata', subBuilder: Metadata.create)
    ..hasRequiredFields = false
  ;

  CategoryResponse._() : super();
  factory CategoryResponse({
    $64.Category? profile,
    Metadata? metadata,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (metadata != null) {
      _result.metadata = metadata;
    }
    return _result;
  }
  factory CategoryResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CategoryResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CategoryResponse clone() => CategoryResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CategoryResponse copyWith(void Function(CategoryResponse) updates) => super.copyWith((message) => updates(message as CategoryResponse)) as CategoryResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CategoryResponse create() => CategoryResponse._();
  CategoryResponse createEmptyInstance() => create();
  static $pb.PbList<CategoryResponse> createRepeated() => $pb.PbList<CategoryResponse>();
  @$core.pragma('dart2js:noInline')
  static CategoryResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CategoryResponse>(create);
  static CategoryResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $64.Category get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($64.Category v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $64.Category ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  Metadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata(Metadata v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => clearField(2);
  @$pb.TagNumber(2)
  Metadata ensureMetadata() => $_ensure(1);
}

class ExpandedCategoryCollectionResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ExpandedCategoryCollectionResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<CategoryCollectionResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: CategoryCollectionResponse.create)
    ..pc<CategoryResponse>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categories', $pb.PbFieldType.PM, subBuilder: CategoryResponse.create)
    ..hasRequiredFields = false
  ;

  ExpandedCategoryCollectionResponse._() : super();
  factory ExpandedCategoryCollectionResponse({
    CategoryCollectionResponse? profile,
    $core.Iterable<CategoryResponse>? categories,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (categories != null) {
      _result.categories.addAll(categories);
    }
    return _result;
  }
  factory ExpandedCategoryCollectionResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ExpandedCategoryCollectionResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ExpandedCategoryCollectionResponse clone() => ExpandedCategoryCollectionResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ExpandedCategoryCollectionResponse copyWith(void Function(ExpandedCategoryCollectionResponse) updates) => super.copyWith((message) => updates(message as ExpandedCategoryCollectionResponse)) as ExpandedCategoryCollectionResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ExpandedCategoryCollectionResponse create() => ExpandedCategoryCollectionResponse._();
  ExpandedCategoryCollectionResponse createEmptyInstance() => create();
  static $pb.PbList<ExpandedCategoryCollectionResponse> createRepeated() => $pb.PbList<ExpandedCategoryCollectionResponse>();
  @$core.pragma('dart2js:noInline')
  static ExpandedCategoryCollectionResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ExpandedCategoryCollectionResponse>(create);
  static ExpandedCategoryCollectionResponse? _defaultInstance;

  @$pb.TagNumber(1)
  CategoryCollectionResponse get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile(CategoryCollectionResponse v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  CategoryCollectionResponse ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<CategoryResponse> get categories => $_getList(1);
}

class ExpandedCategoryCollectionRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ExpandedCategoryCollectionRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.category_profile'), createEmptyInstance: create)
    ..aOM<$64.CategoryCollection>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'profile', subBuilder: $64.CategoryCollection.create)
    ..pc<$64.Category>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categories', $pb.PbFieldType.PM, subBuilder: $64.Category.create)
    ..hasRequiredFields = false
  ;

  ExpandedCategoryCollectionRequest._() : super();
  factory ExpandedCategoryCollectionRequest({
    $64.CategoryCollection? profile,
    $core.Iterable<$64.Category>? categories,
  }) {
    final _result = create();
    if (profile != null) {
      _result.profile = profile;
    }
    if (categories != null) {
      _result.categories.addAll(categories);
    }
    return _result;
  }
  factory ExpandedCategoryCollectionRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ExpandedCategoryCollectionRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ExpandedCategoryCollectionRequest clone() => ExpandedCategoryCollectionRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ExpandedCategoryCollectionRequest copyWith(void Function(ExpandedCategoryCollectionRequest) updates) => super.copyWith((message) => updates(message as ExpandedCategoryCollectionRequest)) as ExpandedCategoryCollectionRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ExpandedCategoryCollectionRequest create() => ExpandedCategoryCollectionRequest._();
  ExpandedCategoryCollectionRequest createEmptyInstance() => create();
  static $pb.PbList<ExpandedCategoryCollectionRequest> createRepeated() => $pb.PbList<ExpandedCategoryCollectionRequest>();
  @$core.pragma('dart2js:noInline')
  static ExpandedCategoryCollectionRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ExpandedCategoryCollectionRequest>(create);
  static ExpandedCategoryCollectionRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $64.CategoryCollection get profile => $_getN(0);
  @$pb.TagNumber(1)
  set profile($64.CategoryCollection v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasProfile() => $_has(0);
  @$pb.TagNumber(1)
  void clearProfile() => clearField(1);
  @$pb.TagNumber(1)
  $64.CategoryCollection ensureProfile() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<$64.Category> get categories => $_getList(1);
}

