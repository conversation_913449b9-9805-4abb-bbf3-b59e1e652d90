///
//  Generated code. Do not modify.
//  source: portal/model_info_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'model_info_sync.pb.dart' as $46;
import '../util/util.pb.dart' as $1;
export 'model_info_sync.pb.dart';

class ModelInfoSyncServiceClient extends $grpc.Client {
  static final _$uploadModelInfos =
      $grpc.ClientMethod<$46.UploadModelInfosRequest, $1.Empty>(
          '/carbon.portal.model_info.ModelInfoSyncService/UploadModelInfos',
          ($46.UploadModelInfosRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getRenameModelCommands = $grpc.ClientMethod<
          $46.GetRenameModelCommandsRequest,
          $46.GetRenameModelCommandsResponse>(
      '/carbon.portal.model_info.ModelInfoSyncService/GetRenameModelCommands',
      ($46.GetRenameModelCommandsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $46.GetRenameModelCommandsResponse.fromBuffer(value));
  static final _$purgeRenameModelCommands = $grpc.ClientMethod<
          $46.PurgeRenameModelCommandsRequest, $1.Empty>(
      '/carbon.portal.model_info.ModelInfoSyncService/PurgeRenameModelCommands',
      ($46.PurgeRenameModelCommandsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  ModelInfoSyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> uploadModelInfos(
      $46.UploadModelInfosRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadModelInfos, request, options: options);
  }

  $grpc.ResponseFuture<$46.GetRenameModelCommandsResponse>
      getRenameModelCommands($46.GetRenameModelCommandsRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getRenameModelCommands, request,
        options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeRenameModelCommands(
      $46.PurgeRenameModelCommandsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeRenameModelCommands, request,
        options: options);
  }
}

abstract class ModelInfoSyncServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.model_info.ModelInfoSyncService';

  ModelInfoSyncServiceBase() {
    $addMethod($grpc.ServiceMethod<$46.UploadModelInfosRequest, $1.Empty>(
        'UploadModelInfos',
        uploadModelInfos_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $46.UploadModelInfosRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$46.GetRenameModelCommandsRequest,
            $46.GetRenameModelCommandsResponse>(
        'GetRenameModelCommands',
        getRenameModelCommands_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $46.GetRenameModelCommandsRequest.fromBuffer(value),
        ($46.GetRenameModelCommandsResponse value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$46.PurgeRenameModelCommandsRequest, $1.Empty>(
            'PurgeRenameModelCommands',
            purgeRenameModelCommands_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $46.PurgeRenameModelCommandsRequest.fromBuffer(value),
            ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> uploadModelInfos_Pre($grpc.ServiceCall call,
      $async.Future<$46.UploadModelInfosRequest> request) async {
    return uploadModelInfos(call, await request);
  }

  $async.Future<$46.GetRenameModelCommandsResponse> getRenameModelCommands_Pre(
      $grpc.ServiceCall call,
      $async.Future<$46.GetRenameModelCommandsRequest> request) async {
    return getRenameModelCommands(call, await request);
  }

  $async.Future<$1.Empty> purgeRenameModelCommands_Pre($grpc.ServiceCall call,
      $async.Future<$46.PurgeRenameModelCommandsRequest> request) async {
    return purgeRenameModelCommands(call, await request);
  }

  $async.Future<$1.Empty> uploadModelInfos(
      $grpc.ServiceCall call, $46.UploadModelInfosRequest request);
  $async.Future<$46.GetRenameModelCommandsResponse> getRenameModelCommands(
      $grpc.ServiceCall call, $46.GetRenameModelCommandsRequest request);
  $async.Future<$1.Empty> purgeRenameModelCommands(
      $grpc.ServiceCall call, $46.PurgeRenameModelCommandsRequest request);
}
