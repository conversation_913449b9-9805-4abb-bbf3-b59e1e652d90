///
//  Generated code. Do not modify.
//  source: portal/spatial_metrics_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../metrics/metrics.pb.dart' as $77;

class SyncSpatialMetricBlocksRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SyncSpatialMetricBlocksRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.spatial_metrics'), createEmptyInstance: create)
    ..pc<$77.SpatialMetricBlock>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'blocks', $pb.PbFieldType.PM, subBuilder: $77.SpatialMetricBlock.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robot')
    ..hasRequiredFields = false
  ;

  SyncSpatialMetricBlocksRequest._() : super();
  factory SyncSpatialMetricBlocksRequest({
    $core.Iterable<$77.SpatialMetricBlock>? blocks,
    $core.String? robot,
  }) {
    final _result = create();
    if (blocks != null) {
      _result.blocks.addAll(blocks);
    }
    if (robot != null) {
      _result.robot = robot;
    }
    return _result;
  }
  factory SyncSpatialMetricBlocksRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SyncSpatialMetricBlocksRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SyncSpatialMetricBlocksRequest clone() => SyncSpatialMetricBlocksRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SyncSpatialMetricBlocksRequest copyWith(void Function(SyncSpatialMetricBlocksRequest) updates) => super.copyWith((message) => updates(message as SyncSpatialMetricBlocksRequest)) as SyncSpatialMetricBlocksRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SyncSpatialMetricBlocksRequest create() => SyncSpatialMetricBlocksRequest._();
  SyncSpatialMetricBlocksRequest createEmptyInstance() => create();
  static $pb.PbList<SyncSpatialMetricBlocksRequest> createRepeated() => $pb.PbList<SyncSpatialMetricBlocksRequest>();
  @$core.pragma('dart2js:noInline')
  static SyncSpatialMetricBlocksRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SyncSpatialMetricBlocksRequest>(create);
  static SyncSpatialMetricBlocksRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$77.SpatialMetricBlock> get blocks => $_getList(0);

  @$pb.TagNumber(2)
  $core.String get robot => $_getSZ(1);
  @$pb.TagNumber(2)
  set robot($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobot() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobot() => clearField(2);
}

