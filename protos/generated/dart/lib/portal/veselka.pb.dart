///
//  Generated code. Do not modify.
//  source: portal/veselka.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $74;
import 'category_profile.pb.dart' as $82;

class Image extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Image', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.veselka'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'useCase')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'role')
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'created')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'url')
    ..aInt64(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'height')
    ..aInt64(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'width')
    ..aInt64(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ppi')
    ..aInt64(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'valid')
    ..aInt64(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'capturedAt')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'detectionJson')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'reasonJson')
    ..aOS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'priority')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'camId')
    ..aOS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rowId')
    ..aOS(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'geoJson')
    ..aOS(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'imageType')
    ..aOS(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'city')
    ..aOB(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'correctedHw')
    ..aOS(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'sessionName')
    ..aOS(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aInt64(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'focusMetric')
    ..aOS(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'geohash')
    ..aOS(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'quarantineReason')
    ..hasRequiredFields = false
  ;

  Image._() : super();
  factory Image({
    $core.String? id,
    $core.String? location,
    $core.String? robotId,
    $core.String? useCase,
    $core.String? role,
    $fixnum.Int64? created,
    $core.String? url,
    $fixnum.Int64? height,
    $fixnum.Int64? width,
    $fixnum.Int64? ppi,
    $fixnum.Int64? valid,
    $fixnum.Int64? capturedAt,
    $core.String? detectionJson,
    $core.String? reasonJson,
    $core.String? priority,
    $core.String? camId,
    $core.String? rowId,
    $core.String? geoJson,
    $core.String? crop,
    $core.String? imageType,
    $core.String? city,
    $core.bool? correctedHw,
    $core.String? sessionName,
    $core.String? cropId,
    $fixnum.Int64? focusMetric,
    $core.String? geohash,
    $core.String? quarantineReason,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (location != null) {
      _result.location = location;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    if (useCase != null) {
      _result.useCase = useCase;
    }
    if (role != null) {
      _result.role = role;
    }
    if (created != null) {
      _result.created = created;
    }
    if (url != null) {
      _result.url = url;
    }
    if (height != null) {
      _result.height = height;
    }
    if (width != null) {
      _result.width = width;
    }
    if (ppi != null) {
      _result.ppi = ppi;
    }
    if (valid != null) {
      _result.valid = valid;
    }
    if (capturedAt != null) {
      _result.capturedAt = capturedAt;
    }
    if (detectionJson != null) {
      _result.detectionJson = detectionJson;
    }
    if (reasonJson != null) {
      _result.reasonJson = reasonJson;
    }
    if (priority != null) {
      _result.priority = priority;
    }
    if (camId != null) {
      _result.camId = camId;
    }
    if (rowId != null) {
      _result.rowId = rowId;
    }
    if (geoJson != null) {
      _result.geoJson = geoJson;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (imageType != null) {
      _result.imageType = imageType;
    }
    if (city != null) {
      _result.city = city;
    }
    if (correctedHw != null) {
      _result.correctedHw = correctedHw;
    }
    if (sessionName != null) {
      _result.sessionName = sessionName;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (focusMetric != null) {
      _result.focusMetric = focusMetric;
    }
    if (geohash != null) {
      _result.geohash = geohash;
    }
    if (quarantineReason != null) {
      _result.quarantineReason = quarantineReason;
    }
    return _result;
  }
  factory Image.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Image.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Image clone() => Image()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Image copyWith(void Function(Image) updates) => super.copyWith((message) => updates(message as Image)) as Image; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Image create() => Image._();
  Image createEmptyInstance() => create();
  static $pb.PbList<Image> createRepeated() => $pb.PbList<Image>();
  @$core.pragma('dart2js:noInline')
  static Image getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Image>(create);
  static Image? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get location => $_getSZ(1);
  @$pb.TagNumber(2)
  set location($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLocation() => $_has(1);
  @$pb.TagNumber(2)
  void clearLocation() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get robotId => $_getSZ(2);
  @$pb.TagNumber(3)
  set robotId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotId() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get useCase => $_getSZ(3);
  @$pb.TagNumber(4)
  set useCase($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUseCase() => $_has(3);
  @$pb.TagNumber(4)
  void clearUseCase() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get role => $_getSZ(4);
  @$pb.TagNumber(5)
  set role($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasRole() => $_has(4);
  @$pb.TagNumber(5)
  void clearRole() => clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get created => $_getI64(5);
  @$pb.TagNumber(6)
  set created($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCreated() => $_has(5);
  @$pb.TagNumber(6)
  void clearCreated() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get url => $_getSZ(6);
  @$pb.TagNumber(7)
  set url($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasUrl() => $_has(6);
  @$pb.TagNumber(7)
  void clearUrl() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get height => $_getI64(7);
  @$pb.TagNumber(8)
  set height($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasHeight() => $_has(7);
  @$pb.TagNumber(8)
  void clearHeight() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get width => $_getI64(8);
  @$pb.TagNumber(9)
  set width($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasWidth() => $_has(8);
  @$pb.TagNumber(9)
  void clearWidth() => clearField(9);

  @$pb.TagNumber(10)
  $fixnum.Int64 get ppi => $_getI64(9);
  @$pb.TagNumber(10)
  set ppi($fixnum.Int64 v) { $_setInt64(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasPpi() => $_has(9);
  @$pb.TagNumber(10)
  void clearPpi() => clearField(10);

  @$pb.TagNumber(11)
  $fixnum.Int64 get valid => $_getI64(10);
  @$pb.TagNumber(11)
  set valid($fixnum.Int64 v) { $_setInt64(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasValid() => $_has(10);
  @$pb.TagNumber(11)
  void clearValid() => clearField(11);

  @$pb.TagNumber(12)
  $fixnum.Int64 get capturedAt => $_getI64(11);
  @$pb.TagNumber(12)
  set capturedAt($fixnum.Int64 v) { $_setInt64(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasCapturedAt() => $_has(11);
  @$pb.TagNumber(12)
  void clearCapturedAt() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get detectionJson => $_getSZ(12);
  @$pb.TagNumber(13)
  set detectionJson($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDetectionJson() => $_has(12);
  @$pb.TagNumber(13)
  void clearDetectionJson() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get reasonJson => $_getSZ(13);
  @$pb.TagNumber(14)
  set reasonJson($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasReasonJson() => $_has(13);
  @$pb.TagNumber(14)
  void clearReasonJson() => clearField(14);

  @$pb.TagNumber(15)
  $core.String get priority => $_getSZ(14);
  @$pb.TagNumber(15)
  set priority($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasPriority() => $_has(14);
  @$pb.TagNumber(15)
  void clearPriority() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get camId => $_getSZ(15);
  @$pb.TagNumber(16)
  set camId($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasCamId() => $_has(15);
  @$pb.TagNumber(16)
  void clearCamId() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get rowId => $_getSZ(16);
  @$pb.TagNumber(17)
  set rowId($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasRowId() => $_has(16);
  @$pb.TagNumber(17)
  void clearRowId() => clearField(17);

  @$pb.TagNumber(18)
  $core.String get geoJson => $_getSZ(17);
  @$pb.TagNumber(18)
  set geoJson($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasGeoJson() => $_has(17);
  @$pb.TagNumber(18)
  void clearGeoJson() => clearField(18);

  @$pb.TagNumber(19)
  $core.String get crop => $_getSZ(18);
  @$pb.TagNumber(19)
  set crop($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasCrop() => $_has(18);
  @$pb.TagNumber(19)
  void clearCrop() => clearField(19);

  @$pb.TagNumber(20)
  $core.String get imageType => $_getSZ(19);
  @$pb.TagNumber(20)
  set imageType($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasImageType() => $_has(19);
  @$pb.TagNumber(20)
  void clearImageType() => clearField(20);

  @$pb.TagNumber(21)
  $core.String get city => $_getSZ(20);
  @$pb.TagNumber(21)
  set city($core.String v) { $_setString(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasCity() => $_has(20);
  @$pb.TagNumber(21)
  void clearCity() => clearField(21);

  @$pb.TagNumber(22)
  $core.bool get correctedHw => $_getBF(21);
  @$pb.TagNumber(22)
  set correctedHw($core.bool v) { $_setBool(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasCorrectedHw() => $_has(21);
  @$pb.TagNumber(22)
  void clearCorrectedHw() => clearField(22);

  @$pb.TagNumber(23)
  $core.String get sessionName => $_getSZ(22);
  @$pb.TagNumber(23)
  set sessionName($core.String v) { $_setString(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasSessionName() => $_has(22);
  @$pb.TagNumber(23)
  void clearSessionName() => clearField(23);

  @$pb.TagNumber(24)
  $core.String get cropId => $_getSZ(23);
  @$pb.TagNumber(24)
  set cropId($core.String v) { $_setString(23, v); }
  @$pb.TagNumber(24)
  $core.bool hasCropId() => $_has(23);
  @$pb.TagNumber(24)
  void clearCropId() => clearField(24);

  @$pb.TagNumber(25)
  $fixnum.Int64 get focusMetric => $_getI64(24);
  @$pb.TagNumber(25)
  set focusMetric($fixnum.Int64 v) { $_setInt64(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasFocusMetric() => $_has(24);
  @$pb.TagNumber(25)
  void clearFocusMetric() => clearField(25);

  @$pb.TagNumber(26)
  $core.String get geohash => $_getSZ(25);
  @$pb.TagNumber(26)
  set geohash($core.String v) { $_setString(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasGeohash() => $_has(25);
  @$pb.TagNumber(26)
  void clearGeohash() => clearField(26);

  @$pb.TagNumber(27)
  $core.String get quarantineReason => $_getSZ(26);
  @$pb.TagNumber(27)
  set quarantineReason($core.String v) { $_setString(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasQuarantineReason() => $_has(26);
  @$pb.TagNumber(27)
  void clearQuarantineReason() => clearField(27);
}

class CropConfidence extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CropConfidence', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.veselka'), createEmptyInstance: create)
    ..aOM<$74.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $74.DB.create)
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'latitude', $pb.PbFieldType.OF)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'longitude', $pb.PbFieldType.OF)
    ..aInt64(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'precision')
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numTotalImages')
    ..aInt64(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'numRegionalImages')
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'confidence')
    ..hasRequiredFields = false
  ;

  CropConfidence._() : super();
  factory CropConfidence({
    $74.DB? db,
    $fixnum.Int64? robotId,
    $core.String? cropId,
    $core.double? latitude,
    $core.double? longitude,
    $fixnum.Int64? precision,
    $fixnum.Int64? numTotalImages,
    $fixnum.Int64? numRegionalImages,
    $core.String? confidence,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (latitude != null) {
      _result.latitude = latitude;
    }
    if (longitude != null) {
      _result.longitude = longitude;
    }
    if (precision != null) {
      _result.precision = precision;
    }
    if (numTotalImages != null) {
      _result.numTotalImages = numTotalImages;
    }
    if (numRegionalImages != null) {
      _result.numRegionalImages = numRegionalImages;
    }
    if (confidence != null) {
      _result.confidence = confidence;
    }
    return _result;
  }
  factory CropConfidence.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CropConfidence.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CropConfidence clone() => CropConfidence()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CropConfidence copyWith(void Function(CropConfidence) updates) => super.copyWith((message) => updates(message as CropConfidence)) as CropConfidence; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CropConfidence create() => CropConfidence._();
  CropConfidence createEmptyInstance() => create();
  static $pb.PbList<CropConfidence> createRepeated() => $pb.PbList<CropConfidence>();
  @$core.pragma('dart2js:noInline')
  static CropConfidence getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CropConfidence>(create);
  static CropConfidence? _defaultInstance;

  @$pb.TagNumber(1)
  $74.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($74.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $74.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $fixnum.Int64 get robotId => $_getI64(1);
  @$pb.TagNumber(2)
  set robotId($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobotId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobotId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get cropId => $_getSZ(2);
  @$pb.TagNumber(3)
  set cropId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCropId() => $_has(2);
  @$pb.TagNumber(3)
  void clearCropId() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get latitude => $_getN(3);
  @$pb.TagNumber(4)
  set latitude($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLatitude() => $_has(3);
  @$pb.TagNumber(4)
  void clearLatitude() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get longitude => $_getN(4);
  @$pb.TagNumber(5)
  set longitude($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLongitude() => $_has(4);
  @$pb.TagNumber(5)
  void clearLongitude() => clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get precision => $_getI64(5);
  @$pb.TagNumber(6)
  set precision($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPrecision() => $_has(5);
  @$pb.TagNumber(6)
  void clearPrecision() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get numTotalImages => $_getI64(6);
  @$pb.TagNumber(7)
  set numTotalImages($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasNumTotalImages() => $_has(6);
  @$pb.TagNumber(7)
  void clearNumTotalImages() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get numRegionalImages => $_getI64(7);
  @$pb.TagNumber(8)
  set numRegionalImages($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasNumRegionalImages() => $_has(7);
  @$pb.TagNumber(8)
  void clearNumRegionalImages() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get confidence => $_getSZ(8);
  @$pb.TagNumber(9)
  set confidence($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasConfidence() => $_has(8);
  @$pb.TagNumber(9)
  void clearConfidence() => clearField(9);
}

class Crop extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Crop', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.veselka'), createEmptyInstance: create)
    ..aOB(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'archived')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'carbonName')
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'commonName')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'created')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'legacyCropName')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'notes')
    ..aInt64(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updated')
    ..hasRequiredFields = false
  ;

  Crop._() : super();
  factory Crop({
    $core.bool? archived,
    $core.String? carbonName,
    $core.String? commonName,
    $fixnum.Int64? created,
    $core.String? description,
    $core.String? id,
    $core.String? legacyCropName,
    $core.String? notes,
    $fixnum.Int64? updated,
  }) {
    final _result = create();
    if (archived != null) {
      _result.archived = archived;
    }
    if (carbonName != null) {
      _result.carbonName = carbonName;
    }
    if (commonName != null) {
      _result.commonName = commonName;
    }
    if (created != null) {
      _result.created = created;
    }
    if (description != null) {
      _result.description = description;
    }
    if (id != null) {
      _result.id = id;
    }
    if (legacyCropName != null) {
      _result.legacyCropName = legacyCropName;
    }
    if (notes != null) {
      _result.notes = notes;
    }
    if (updated != null) {
      _result.updated = updated;
    }
    return _result;
  }
  factory Crop.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Crop.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Crop clone() => Crop()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Crop copyWith(void Function(Crop) updates) => super.copyWith((message) => updates(message as Crop)) as Crop; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Crop create() => Crop._();
  Crop createEmptyInstance() => create();
  static $pb.PbList<Crop> createRepeated() => $pb.PbList<Crop>();
  @$core.pragma('dart2js:noInline')
  static Crop getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Crop>(create);
  static Crop? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get archived => $_getBF(0);
  @$pb.TagNumber(1)
  set archived($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasArchived() => $_has(0);
  @$pb.TagNumber(1)
  void clearArchived() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get carbonName => $_getSZ(1);
  @$pb.TagNumber(2)
  set carbonName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCarbonName() => $_has(1);
  @$pb.TagNumber(2)
  void clearCarbonName() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get commonName => $_getSZ(2);
  @$pb.TagNumber(3)
  set commonName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCommonName() => $_has(2);
  @$pb.TagNumber(3)
  void clearCommonName() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get created => $_getI64(3);
  @$pb.TagNumber(4)
  set created($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCreated() => $_has(3);
  @$pb.TagNumber(4)
  void clearCreated() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get description => $_getSZ(4);
  @$pb.TagNumber(5)
  set description($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDescription() => $_has(4);
  @$pb.TagNumber(5)
  void clearDescription() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get id => $_getSZ(5);
  @$pb.TagNumber(6)
  set id($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasId() => $_has(5);
  @$pb.TagNumber(6)
  void clearId() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get legacyCropName => $_getSZ(6);
  @$pb.TagNumber(7)
  set legacyCropName($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLegacyCropName() => $_has(6);
  @$pb.TagNumber(7)
  void clearLegacyCropName() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get notes => $_getSZ(7);
  @$pb.TagNumber(8)
  set notes($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasNotes() => $_has(7);
  @$pb.TagNumber(8)
  void clearNotes() => clearField(8);

  @$pb.TagNumber(9)
  $fixnum.Int64 get updated => $_getI64(8);
  @$pb.TagNumber(9)
  set updated($fixnum.Int64 v) { $_setInt64(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasUpdated() => $_has(8);
  @$pb.TagNumber(9)
  void clearUpdated() => clearField(9);
}

class RobotCrop extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotCrop', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.veselka'), createEmptyInstance: create)
    ..aOM<Crop>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop', subBuilder: Crop.create)
    ..aOM<CropConfidence>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'confidence', subBuilder: CropConfidence.create)
    ..hasRequiredFields = false
  ;

  RobotCrop._() : super();
  factory RobotCrop({
    Crop? crop,
    CropConfidence? confidence,
  }) {
    final _result = create();
    if (crop != null) {
      _result.crop = crop;
    }
    if (confidence != null) {
      _result.confidence = confidence;
    }
    return _result;
  }
  factory RobotCrop.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotCrop.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotCrop clone() => RobotCrop()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotCrop copyWith(void Function(RobotCrop) updates) => super.copyWith((message) => updates(message as RobotCrop)) as RobotCrop; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotCrop create() => RobotCrop._();
  RobotCrop createEmptyInstance() => create();
  static $pb.PbList<RobotCrop> createRepeated() => $pb.PbList<RobotCrop>();
  @$core.pragma('dart2js:noInline')
  static RobotCrop getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotCrop>(create);
  static RobotCrop? _defaultInstance;

  @$pb.TagNumber(1)
  Crop get crop => $_getN(0);
  @$pb.TagNumber(1)
  set crop(Crop v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCrop() => $_has(0);
  @$pb.TagNumber(1)
  void clearCrop() => clearField(1);
  @$pb.TagNumber(1)
  Crop ensureCrop() => $_ensure(0);

  @$pb.TagNumber(2)
  CropConfidence get confidence => $_getN(1);
  @$pb.TagNumber(2)
  set confidence(CropConfidence v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasConfidence() => $_has(1);
  @$pb.TagNumber(2)
  void clearConfidence() => clearField(2);
  @$pb.TagNumber(2)
  CropConfidence ensureConfidence() => $_ensure(1);
}

class Model extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Model', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.veselka'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id')
    ..aInt64(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'created')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updated')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'url')
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customer')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aInt64(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'version')
    ..aOS(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trainingDockerTag')
    ..aOS(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'gitSha', protoName: 'gitSha')
    ..aOS(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'checksum')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'location')
    ..aInt64(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'trainedAt')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'type')
    ..aOS(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'description')
    ..aOS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metadataJson')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'productionContainerVersion')
    ..aOS(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'testResultsJson')
    ..aOS(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wandbJson')
    ..aOS(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'snapshotJson')
    ..aOB(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isStub')
    ..aOB(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isGoodToDeploy')
    ..aOS(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotName')
    ..aOS(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'environment')
    ..aOB(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deploy')
    ..aOB(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'isPretraining')
    ..aOS(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'subType')
    ..aOS(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'datasetId')
    ..aOS(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'containerVersion')
    ..aOS(29, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'containerId')
    ..aOS(30, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pipelineId')
    ..aOS(31, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'parentModelId')
    ..pPS(32, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'viableCropIds')
    ..hasRequiredFields = false
  ;

  Model._() : super();
  factory Model({
    $core.String? id,
    $fixnum.Int64? created,
    $fixnum.Int64? updated,
    $core.String? url,
    $core.String? customer,
    $core.String? crop,
    $fixnum.Int64? version,
    $core.String? trainingDockerTag,
    $core.String? gitSha,
    $core.String? checksum,
    $core.String? location,
    $fixnum.Int64? trainedAt,
    $core.String? type,
    $core.String? description,
    $core.String? metadataJson,
    $core.String? productionContainerVersion,
    $core.String? testResultsJson,
    $core.String? wandbJson,
    $core.String? snapshotJson,
    $core.bool? isStub,
    $core.bool? isGoodToDeploy,
    $core.String? robotName,
    $core.String? environment,
    $core.bool? deploy,
    $core.bool? isPretraining,
    $core.String? subType,
    $core.String? datasetId,
    $core.String? containerVersion,
    $core.String? containerId,
    $core.String? pipelineId,
    $core.String? parentModelId,
    $core.Iterable<$core.String>? viableCropIds,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (created != null) {
      _result.created = created;
    }
    if (updated != null) {
      _result.updated = updated;
    }
    if (url != null) {
      _result.url = url;
    }
    if (customer != null) {
      _result.customer = customer;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (version != null) {
      _result.version = version;
    }
    if (trainingDockerTag != null) {
      _result.trainingDockerTag = trainingDockerTag;
    }
    if (gitSha != null) {
      _result.gitSha = gitSha;
    }
    if (checksum != null) {
      _result.checksum = checksum;
    }
    if (location != null) {
      _result.location = location;
    }
    if (trainedAt != null) {
      _result.trainedAt = trainedAt;
    }
    if (type != null) {
      _result.type = type;
    }
    if (description != null) {
      _result.description = description;
    }
    if (metadataJson != null) {
      _result.metadataJson = metadataJson;
    }
    if (productionContainerVersion != null) {
      _result.productionContainerVersion = productionContainerVersion;
    }
    if (testResultsJson != null) {
      _result.testResultsJson = testResultsJson;
    }
    if (wandbJson != null) {
      _result.wandbJson = wandbJson;
    }
    if (snapshotJson != null) {
      _result.snapshotJson = snapshotJson;
    }
    if (isStub != null) {
      _result.isStub = isStub;
    }
    if (isGoodToDeploy != null) {
      _result.isGoodToDeploy = isGoodToDeploy;
    }
    if (robotName != null) {
      _result.robotName = robotName;
    }
    if (environment != null) {
      _result.environment = environment;
    }
    if (deploy != null) {
      _result.deploy = deploy;
    }
    if (isPretraining != null) {
      _result.isPretraining = isPretraining;
    }
    if (subType != null) {
      _result.subType = subType;
    }
    if (datasetId != null) {
      _result.datasetId = datasetId;
    }
    if (containerVersion != null) {
      _result.containerVersion = containerVersion;
    }
    if (containerId != null) {
      _result.containerId = containerId;
    }
    if (pipelineId != null) {
      _result.pipelineId = pipelineId;
    }
    if (parentModelId != null) {
      _result.parentModelId = parentModelId;
    }
    if (viableCropIds != null) {
      _result.viableCropIds.addAll(viableCropIds);
    }
    return _result;
  }
  factory Model.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Model.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Model clone() => Model()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Model copyWith(void Function(Model) updates) => super.copyWith((message) => updates(message as Model)) as Model; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Model create() => Model._();
  Model createEmptyInstance() => create();
  static $pb.PbList<Model> createRepeated() => $pb.PbList<Model>();
  @$core.pragma('dart2js:noInline')
  static Model getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Model>(create);
  static Model? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get created => $_getI64(1);
  @$pb.TagNumber(2)
  set created($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCreated() => $_has(1);
  @$pb.TagNumber(2)
  void clearCreated() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get updated => $_getI64(2);
  @$pb.TagNumber(3)
  set updated($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUpdated() => $_has(2);
  @$pb.TagNumber(3)
  void clearUpdated() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get url => $_getSZ(3);
  @$pb.TagNumber(4)
  set url($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUrl() => $_has(3);
  @$pb.TagNumber(4)
  void clearUrl() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get customer => $_getSZ(4);
  @$pb.TagNumber(5)
  set customer($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCustomer() => $_has(4);
  @$pb.TagNumber(5)
  void clearCustomer() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get crop => $_getSZ(5);
  @$pb.TagNumber(6)
  set crop($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCrop() => $_has(5);
  @$pb.TagNumber(6)
  void clearCrop() => clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get version => $_getI64(6);
  @$pb.TagNumber(7)
  set version($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasVersion() => $_has(6);
  @$pb.TagNumber(7)
  void clearVersion() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get trainingDockerTag => $_getSZ(7);
  @$pb.TagNumber(8)
  set trainingDockerTag($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasTrainingDockerTag() => $_has(7);
  @$pb.TagNumber(8)
  void clearTrainingDockerTag() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get gitSha => $_getSZ(8);
  @$pb.TagNumber(9)
  set gitSha($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasGitSha() => $_has(8);
  @$pb.TagNumber(9)
  void clearGitSha() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get checksum => $_getSZ(9);
  @$pb.TagNumber(10)
  set checksum($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasChecksum() => $_has(9);
  @$pb.TagNumber(10)
  void clearChecksum() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get location => $_getSZ(10);
  @$pb.TagNumber(11)
  set location($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasLocation() => $_has(10);
  @$pb.TagNumber(11)
  void clearLocation() => clearField(11);

  @$pb.TagNumber(12)
  $fixnum.Int64 get trainedAt => $_getI64(11);
  @$pb.TagNumber(12)
  set trainedAt($fixnum.Int64 v) { $_setInt64(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasTrainedAt() => $_has(11);
  @$pb.TagNumber(12)
  void clearTrainedAt() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get type => $_getSZ(12);
  @$pb.TagNumber(13)
  set type($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasType() => $_has(12);
  @$pb.TagNumber(13)
  void clearType() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get description => $_getSZ(13);
  @$pb.TagNumber(14)
  set description($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasDescription() => $_has(13);
  @$pb.TagNumber(14)
  void clearDescription() => clearField(14);

  @$pb.TagNumber(15)
  $core.String get metadataJson => $_getSZ(14);
  @$pb.TagNumber(15)
  set metadataJson($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasMetadataJson() => $_has(14);
  @$pb.TagNumber(15)
  void clearMetadataJson() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get productionContainerVersion => $_getSZ(15);
  @$pb.TagNumber(16)
  set productionContainerVersion($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasProductionContainerVersion() => $_has(15);
  @$pb.TagNumber(16)
  void clearProductionContainerVersion() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get testResultsJson => $_getSZ(16);
  @$pb.TagNumber(17)
  set testResultsJson($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasTestResultsJson() => $_has(16);
  @$pb.TagNumber(17)
  void clearTestResultsJson() => clearField(17);

  @$pb.TagNumber(18)
  $core.String get wandbJson => $_getSZ(17);
  @$pb.TagNumber(18)
  set wandbJson($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasWandbJson() => $_has(17);
  @$pb.TagNumber(18)
  void clearWandbJson() => clearField(18);

  @$pb.TagNumber(19)
  $core.String get snapshotJson => $_getSZ(18);
  @$pb.TagNumber(19)
  set snapshotJson($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasSnapshotJson() => $_has(18);
  @$pb.TagNumber(19)
  void clearSnapshotJson() => clearField(19);

  @$pb.TagNumber(20)
  $core.bool get isStub => $_getBF(19);
  @$pb.TagNumber(20)
  set isStub($core.bool v) { $_setBool(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasIsStub() => $_has(19);
  @$pb.TagNumber(20)
  void clearIsStub() => clearField(20);

  @$pb.TagNumber(21)
  $core.bool get isGoodToDeploy => $_getBF(20);
  @$pb.TagNumber(21)
  set isGoodToDeploy($core.bool v) { $_setBool(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasIsGoodToDeploy() => $_has(20);
  @$pb.TagNumber(21)
  void clearIsGoodToDeploy() => clearField(21);

  @$pb.TagNumber(22)
  $core.String get robotName => $_getSZ(21);
  @$pb.TagNumber(22)
  set robotName($core.String v) { $_setString(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasRobotName() => $_has(21);
  @$pb.TagNumber(22)
  void clearRobotName() => clearField(22);

  @$pb.TagNumber(23)
  $core.String get environment => $_getSZ(22);
  @$pb.TagNumber(23)
  set environment($core.String v) { $_setString(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasEnvironment() => $_has(22);
  @$pb.TagNumber(23)
  void clearEnvironment() => clearField(23);

  @$pb.TagNumber(24)
  $core.bool get deploy => $_getBF(23);
  @$pb.TagNumber(24)
  set deploy($core.bool v) { $_setBool(23, v); }
  @$pb.TagNumber(24)
  $core.bool hasDeploy() => $_has(23);
  @$pb.TagNumber(24)
  void clearDeploy() => clearField(24);

  @$pb.TagNumber(25)
  $core.bool get isPretraining => $_getBF(24);
  @$pb.TagNumber(25)
  set isPretraining($core.bool v) { $_setBool(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasIsPretraining() => $_has(24);
  @$pb.TagNumber(25)
  void clearIsPretraining() => clearField(25);

  @$pb.TagNumber(26)
  $core.String get subType => $_getSZ(25);
  @$pb.TagNumber(26)
  set subType($core.String v) { $_setString(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasSubType() => $_has(25);
  @$pb.TagNumber(26)
  void clearSubType() => clearField(26);

  @$pb.TagNumber(27)
  $core.String get datasetId => $_getSZ(26);
  @$pb.TagNumber(27)
  set datasetId($core.String v) { $_setString(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasDatasetId() => $_has(26);
  @$pb.TagNumber(27)
  void clearDatasetId() => clearField(27);

  @$pb.TagNumber(28)
  $core.String get containerVersion => $_getSZ(27);
  @$pb.TagNumber(28)
  set containerVersion($core.String v) { $_setString(27, v); }
  @$pb.TagNumber(28)
  $core.bool hasContainerVersion() => $_has(27);
  @$pb.TagNumber(28)
  void clearContainerVersion() => clearField(28);

  @$pb.TagNumber(29)
  $core.String get containerId => $_getSZ(28);
  @$pb.TagNumber(29)
  set containerId($core.String v) { $_setString(28, v); }
  @$pb.TagNumber(29)
  $core.bool hasContainerId() => $_has(28);
  @$pb.TagNumber(29)
  void clearContainerId() => clearField(29);

  @$pb.TagNumber(30)
  $core.String get pipelineId => $_getSZ(29);
  @$pb.TagNumber(30)
  set pipelineId($core.String v) { $_setString(29, v); }
  @$pb.TagNumber(30)
  $core.bool hasPipelineId() => $_has(29);
  @$pb.TagNumber(30)
  void clearPipelineId() => clearField(30);

  @$pb.TagNumber(31)
  $core.String get parentModelId => $_getSZ(30);
  @$pb.TagNumber(31)
  set parentModelId($core.String v) { $_setString(30, v); }
  @$pb.TagNumber(31)
  $core.bool hasParentModelId() => $_has(30);
  @$pb.TagNumber(31)
  void clearParentModelId() => clearField(31);

  @$pb.TagNumber(32)
  $core.List<$core.String> get viableCropIds => $_getList(31);
}

class CreateCategoryCollectionSessionRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreateCategoryCollectionSessionRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.veselka'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'modelId')
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOM<$82.ExpandedCategoryCollectionRequest>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'categoryCollectionProfile', subBuilder: $82.ExpandedCategoryCollectionRequest.create)
    ..hasRequiredFields = false
  ;

  CreateCategoryCollectionSessionRequest._() : super();
  factory CreateCategoryCollectionSessionRequest({
    $core.String? modelId,
    $fixnum.Int64? customerId,
    $82.ExpandedCategoryCollectionRequest? categoryCollectionProfile,
  }) {
    final _result = create();
    if (modelId != null) {
      _result.modelId = modelId;
    }
    if (customerId != null) {
      _result.customerId = customerId;
    }
    if (categoryCollectionProfile != null) {
      _result.categoryCollectionProfile = categoryCollectionProfile;
    }
    return _result;
  }
  factory CreateCategoryCollectionSessionRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateCategoryCollectionSessionRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateCategoryCollectionSessionRequest clone() => CreateCategoryCollectionSessionRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateCategoryCollectionSessionRequest copyWith(void Function(CreateCategoryCollectionSessionRequest) updates) => super.copyWith((message) => updates(message as CreateCategoryCollectionSessionRequest)) as CreateCategoryCollectionSessionRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreateCategoryCollectionSessionRequest create() => CreateCategoryCollectionSessionRequest._();
  CreateCategoryCollectionSessionRequest createEmptyInstance() => create();
  static $pb.PbList<CreateCategoryCollectionSessionRequest> createRepeated() => $pb.PbList<CreateCategoryCollectionSessionRequest>();
  @$core.pragma('dart2js:noInline')
  static CreateCategoryCollectionSessionRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateCategoryCollectionSessionRequest>(create);
  static CreateCategoryCollectionSessionRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get modelId => $_getSZ(0);
  @$pb.TagNumber(1)
  set modelId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasModelId() => $_has(0);
  @$pb.TagNumber(1)
  void clearModelId() => clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get customerId => $_getI64(1);
  @$pb.TagNumber(2)
  set customerId($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCustomerId() => $_has(1);
  @$pb.TagNumber(2)
  void clearCustomerId() => clearField(2);

  @$pb.TagNumber(3)
  $82.ExpandedCategoryCollectionRequest get categoryCollectionProfile => $_getN(2);
  @$pb.TagNumber(3)
  set categoryCollectionProfile($82.ExpandedCategoryCollectionRequest v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasCategoryCollectionProfile() => $_has(2);
  @$pb.TagNumber(3)
  void clearCategoryCollectionProfile() => clearField(3);
  @$pb.TagNumber(3)
  $82.ExpandedCategoryCollectionRequest ensureCategoryCollectionProfile() => $_ensure(2);
}

