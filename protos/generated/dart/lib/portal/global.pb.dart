///
//  Generated code. Do not modify.
//  source: portal/global.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/field_mask.pb.dart' as $76;

class Globals extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Globals', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.global'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantProfileModelId')
    ..hasRequiredFields = false
  ;

  Globals._() : super();
  factory Globals({
    $core.String? plantProfileModelId,
  }) {
    final _result = create();
    if (plantProfileModelId != null) {
      _result.plantProfileModelId = plantProfileModelId;
    }
    return _result;
  }
  factory Globals.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Globals.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Globals clone() => Globals()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Globals copyWith(void Function(Globals) updates) => super.copyWith((message) => updates(message as Globals)) as Globals; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Globals create() => Globals._();
  Globals createEmptyInstance() => create();
  static $pb.PbList<Globals> createRepeated() => $pb.PbList<Globals>();
  @$core.pragma('dart2js:noInline')
  static Globals getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Globals>(create);
  static Globals? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get plantProfileModelId => $_getSZ(0);
  @$pb.TagNumber(1)
  set plantProfileModelId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPlantProfileModelId() => $_has(0);
  @$pb.TagNumber(1)
  void clearPlantProfileModelId() => clearField(1);
}

class UpdateGlobalsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UpdateGlobalsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.global'), createEmptyInstance: create)
    ..aOM<Globals>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'globals', subBuilder: Globals.create)
    ..aOM<$76.FieldMask>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updateMask', subBuilder: $76.FieldMask.create)
    ..hasRequiredFields = false
  ;

  UpdateGlobalsRequest._() : super();
  factory UpdateGlobalsRequest({
    Globals? globals,
    $76.FieldMask? updateMask,
  }) {
    final _result = create();
    if (globals != null) {
      _result.globals = globals;
    }
    if (updateMask != null) {
      _result.updateMask = updateMask;
    }
    return _result;
  }
  factory UpdateGlobalsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UpdateGlobalsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UpdateGlobalsRequest clone() => UpdateGlobalsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UpdateGlobalsRequest copyWith(void Function(UpdateGlobalsRequest) updates) => super.copyWith((message) => updates(message as UpdateGlobalsRequest)) as UpdateGlobalsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UpdateGlobalsRequest create() => UpdateGlobalsRequest._();
  UpdateGlobalsRequest createEmptyInstance() => create();
  static $pb.PbList<UpdateGlobalsRequest> createRepeated() => $pb.PbList<UpdateGlobalsRequest>();
  @$core.pragma('dart2js:noInline')
  static UpdateGlobalsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UpdateGlobalsRequest>(create);
  static UpdateGlobalsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  Globals get globals => $_getN(0);
  @$pb.TagNumber(1)
  set globals(Globals v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasGlobals() => $_has(0);
  @$pb.TagNumber(1)
  void clearGlobals() => clearField(1);
  @$pb.TagNumber(1)
  Globals ensureGlobals() => $_ensure(0);

  @$pb.TagNumber(2)
  $76.FieldMask get updateMask => $_getN(1);
  @$pb.TagNumber(2)
  set updateMask($76.FieldMask v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateMask() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateMask() => clearField(2);
  @$pb.TagNumber(2)
  $76.FieldMask ensureUpdateMask() => $_ensure(1);
}

