///
//  Generated code. Do not modify.
//  source: portal/geo.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../geo/geo.pb.dart' as $75;
import 'db.pb.dart' as $74;

class GeoJSON extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GeoJSON', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.field'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'feature')
    ..hasRequiredFields = false
  ;

  GeoJSON._() : super();
  factory GeoJSON({
    $core.String? feature,
  }) {
    final _result = create();
    if (feature != null) {
      _result.feature = feature;
    }
    return _result;
  }
  factory GeoJSON.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GeoJSON.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GeoJSON clone() => GeoJSON()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GeoJSON copyWith(void Function(GeoJSON) updates) => super.copyWith((message) => updates(message as GeoJSON)) as GeoJSON; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GeoJSON create() => GeoJSON._();
  GeoJSON createEmptyInstance() => create();
  static $pb.PbList<GeoJSON> createRepeated() => $pb.PbList<GeoJSON>();
  @$core.pragma('dart2js:noInline')
  static GeoJSON getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GeoJSON>(create);
  static GeoJSON? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get feature => $_getSZ(0);
  @$pb.TagNumber(1)
  set feature($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasFeature() => $_has(0);
  @$pb.TagNumber(1)
  void clearFeature() => clearField(1);
}

class PlanPathRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlanPathRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.field'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fieldId')
    ..aOM<$75.Point>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'currentPoint', subBuilder: $75.Point.create)
    ..aOM<$75.LineString>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetLine', subBuilder: $75.LineString.create)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bufferMeters', $pb.PbFieldType.OD)
    ..aOS(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..hasRequiredFields = false
  ;

  PlanPathRequest._() : super();
  factory PlanPathRequest({
    $core.String? fieldId,
    $75.Point? currentPoint,
    $75.LineString? targetLine,
    $core.double? bufferMeters,
    $core.String? serial,
  }) {
    final _result = create();
    if (fieldId != null) {
      _result.fieldId = fieldId;
    }
    if (currentPoint != null) {
      _result.currentPoint = currentPoint;
    }
    if (targetLine != null) {
      _result.targetLine = targetLine;
    }
    if (bufferMeters != null) {
      _result.bufferMeters = bufferMeters;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    return _result;
  }
  factory PlanPathRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlanPathRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlanPathRequest clone() => PlanPathRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlanPathRequest copyWith(void Function(PlanPathRequest) updates) => super.copyWith((message) => updates(message as PlanPathRequest)) as PlanPathRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlanPathRequest create() => PlanPathRequest._();
  PlanPathRequest createEmptyInstance() => create();
  static $pb.PbList<PlanPathRequest> createRepeated() => $pb.PbList<PlanPathRequest>();
  @$core.pragma('dart2js:noInline')
  static PlanPathRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlanPathRequest>(create);
  static PlanPathRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get fieldId => $_getSZ(0);
  @$pb.TagNumber(1)
  set fieldId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasFieldId() => $_has(0);
  @$pb.TagNumber(1)
  void clearFieldId() => clearField(1);

  @$pb.TagNumber(2)
  $75.Point get currentPoint => $_getN(1);
  @$pb.TagNumber(2)
  set currentPoint($75.Point v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasCurrentPoint() => $_has(1);
  @$pb.TagNumber(2)
  void clearCurrentPoint() => clearField(2);
  @$pb.TagNumber(2)
  $75.Point ensureCurrentPoint() => $_ensure(1);

  @$pb.TagNumber(3)
  $75.LineString get targetLine => $_getN(2);
  @$pb.TagNumber(3)
  set targetLine($75.LineString v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasTargetLine() => $_has(2);
  @$pb.TagNumber(3)
  void clearTargetLine() => clearField(3);
  @$pb.TagNumber(3)
  $75.LineString ensureTargetLine() => $_ensure(2);

  @$pb.TagNumber(4)
  $core.double get bufferMeters => $_getN(3);
  @$pb.TagNumber(4)
  set bufferMeters($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasBufferMeters() => $_has(3);
  @$pb.TagNumber(4)
  void clearBufferMeters() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get serial => $_getSZ(4);
  @$pb.TagNumber(5)
  set serial($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSerial() => $_has(4);
  @$pb.TagNumber(5)
  void clearSerial() => clearField(5);
}

class PlanPathResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlanPathResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.field'), createEmptyInstance: create)
    ..aOM<$75.LineString>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'track', subBuilder: $75.LineString.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'distanceMeters', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  PlanPathResponse._() : super();
  factory PlanPathResponse({
    $75.LineString? track,
    $core.double? distanceMeters,
  }) {
    final _result = create();
    if (track != null) {
      _result.track = track;
    }
    if (distanceMeters != null) {
      _result.distanceMeters = distanceMeters;
    }
    return _result;
  }
  factory PlanPathResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlanPathResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlanPathResponse clone() => PlanPathResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlanPathResponse copyWith(void Function(PlanPathResponse) updates) => super.copyWith((message) => updates(message as PlanPathResponse)) as PlanPathResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlanPathResponse create() => PlanPathResponse._();
  PlanPathResponse createEmptyInstance() => create();
  static $pb.PbList<PlanPathResponse> createRepeated() => $pb.PbList<PlanPathResponse>();
  @$core.pragma('dart2js:noInline')
  static PlanPathResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlanPathResponse>(create);
  static PlanPathResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $75.LineString get track => $_getN(0);
  @$pb.TagNumber(1)
  set track($75.LineString v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrack() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrack() => clearField(1);
  @$pb.TagNumber(1)
  $75.LineString ensureTrack() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get distanceMeters => $_getN(1);
  @$pb.TagNumber(2)
  set distanceMeters($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDistanceMeters() => $_has(1);
  @$pb.TagNumber(2)
  void clearDistanceMeters() => clearField(2);
}

class FieldDefinition extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FieldDefinition', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.field'), createEmptyInstance: create)
    ..aOM<$74.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $74.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'fieldId')
    ..aInt64(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aOM<GeoJSON>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'boundary', subBuilder: GeoJSON.create)
    ..aOM<GeoJSON>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantingHeading', subBuilder: GeoJSON.create)
    ..hasRequiredFields = false
  ;

  FieldDefinition._() : super();
  factory FieldDefinition({
    $74.DB? db,
    $core.String? fieldId,
    $fixnum.Int64? robotId,
    $core.String? name,
    GeoJSON? boundary,
    GeoJSON? plantingHeading,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (fieldId != null) {
      _result.fieldId = fieldId;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    if (name != null) {
      _result.name = name;
    }
    if (boundary != null) {
      _result.boundary = boundary;
    }
    if (plantingHeading != null) {
      _result.plantingHeading = plantingHeading;
    }
    return _result;
  }
  factory FieldDefinition.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FieldDefinition.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FieldDefinition clone() => FieldDefinition()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FieldDefinition copyWith(void Function(FieldDefinition) updates) => super.copyWith((message) => updates(message as FieldDefinition)) as FieldDefinition; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FieldDefinition create() => FieldDefinition._();
  FieldDefinition createEmptyInstance() => create();
  static $pb.PbList<FieldDefinition> createRepeated() => $pb.PbList<FieldDefinition>();
  @$core.pragma('dart2js:noInline')
  static FieldDefinition getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FieldDefinition>(create);
  static FieldDefinition? _defaultInstance;

  @$pb.TagNumber(1)
  $74.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($74.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $74.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get fieldId => $_getSZ(1);
  @$pb.TagNumber(2)
  set fieldId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasFieldId() => $_has(1);
  @$pb.TagNumber(2)
  void clearFieldId() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get robotId => $_getI64(2);
  @$pb.TagNumber(3)
  set robotId($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotId() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get name => $_getSZ(3);
  @$pb.TagNumber(4)
  set name($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasName() => $_has(3);
  @$pb.TagNumber(4)
  void clearName() => clearField(4);

  @$pb.TagNumber(5)
  GeoJSON get boundary => $_getN(4);
  @$pb.TagNumber(5)
  set boundary(GeoJSON v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasBoundary() => $_has(4);
  @$pb.TagNumber(5)
  void clearBoundary() => clearField(5);
  @$pb.TagNumber(5)
  GeoJSON ensureBoundary() => $_ensure(4);

  @$pb.TagNumber(6)
  GeoJSON get plantingHeading => $_getN(5);
  @$pb.TagNumber(6)
  set plantingHeading(GeoJSON v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasPlantingHeading() => $_has(5);
  @$pb.TagNumber(6)
  void clearPlantingHeading() => clearField(6);
  @$pb.TagNumber(6)
  GeoJSON ensurePlantingHeading() => $_ensure(5);
}

