///
//  Generated code. Do not modify.
//  source: portal/jobs.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $74;
import 'metrics.pb.dart' as $78;
import '../frontend/jobs.pb.dart' as $24;
import '../frontend/weeding_diagnostics.pb.dart' as $22;
import '../metrics/metrics_aggregator_service.pb.dart' as $23;

class PortalJob extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PortalJob', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.jobs'), createEmptyInstance: create)
    ..aOM<$74.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $74.DB.create)
    ..a<$fixnum.Int64>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId')
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aInt64(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestampMs')
    ..aOS(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingProfile')
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningProfile')
    ..aInt64(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'stopTimestampMs')
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'acreage', $pb.PbFieldType.OF)
    ..aOB(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'completed')
    ..aOS(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aOS(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'almanac')
    ..aOS(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'discriminator')
    ..aOM<$78.DailyMetricResponse>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metrics', subBuilder: $78.DailyMetricResponse.create)
    ..aOS(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..aOS(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..hasRequiredFields = false
  ;

  PortalJob._() : super();
  factory PortalJob({
    $74.DB? db,
    $fixnum.Int64? robotId,
    $core.String? jobId,
    $core.String? name,
    $fixnum.Int64? timestampMs,
    $core.String? bandingProfile,
    $core.String? thinningProfile,
    $fixnum.Int64? stopTimestampMs,
    $core.double? acreage,
    $core.bool? completed,
    $core.String? cropId,
    $core.String? almanac,
    $core.String? discriminator,
    $78.DailyMetricResponse? metrics,
    $core.String? crop,
    $core.String? serial,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    if (jobId != null) {
      _result.jobId = jobId;
    }
    if (name != null) {
      _result.name = name;
    }
    if (timestampMs != null) {
      _result.timestampMs = timestampMs;
    }
    if (bandingProfile != null) {
      _result.bandingProfile = bandingProfile;
    }
    if (thinningProfile != null) {
      _result.thinningProfile = thinningProfile;
    }
    if (stopTimestampMs != null) {
      _result.stopTimestampMs = stopTimestampMs;
    }
    if (acreage != null) {
      _result.acreage = acreage;
    }
    if (completed != null) {
      _result.completed = completed;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (almanac != null) {
      _result.almanac = almanac;
    }
    if (discriminator != null) {
      _result.discriminator = discriminator;
    }
    if (metrics != null) {
      _result.metrics = metrics;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    return _result;
  }
  factory PortalJob.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PortalJob.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PortalJob clone() => PortalJob()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PortalJob copyWith(void Function(PortalJob) updates) => super.copyWith((message) => updates(message as PortalJob)) as PortalJob; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PortalJob create() => PortalJob._();
  PortalJob createEmptyInstance() => create();
  static $pb.PbList<PortalJob> createRepeated() => $pb.PbList<PortalJob>();
  @$core.pragma('dart2js:noInline')
  static PortalJob getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PortalJob>(create);
  static PortalJob? _defaultInstance;

  @$pb.TagNumber(1)
  $74.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($74.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $74.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $fixnum.Int64 get robotId => $_getI64(1);
  @$pb.TagNumber(2)
  set robotId($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobotId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobotId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get jobId => $_getSZ(2);
  @$pb.TagNumber(3)
  set jobId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasJobId() => $_has(2);
  @$pb.TagNumber(3)
  void clearJobId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get name => $_getSZ(3);
  @$pb.TagNumber(4)
  set name($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasName() => $_has(3);
  @$pb.TagNumber(4)
  void clearName() => clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get timestampMs => $_getI64(4);
  @$pb.TagNumber(5)
  set timestampMs($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasTimestampMs() => $_has(4);
  @$pb.TagNumber(5)
  void clearTimestampMs() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get bandingProfile => $_getSZ(5);
  @$pb.TagNumber(6)
  set bandingProfile($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasBandingProfile() => $_has(5);
  @$pb.TagNumber(6)
  void clearBandingProfile() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get thinningProfile => $_getSZ(6);
  @$pb.TagNumber(7)
  set thinningProfile($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasThinningProfile() => $_has(6);
  @$pb.TagNumber(7)
  void clearThinningProfile() => clearField(7);

  @$pb.TagNumber(8)
  $fixnum.Int64 get stopTimestampMs => $_getI64(7);
  @$pb.TagNumber(8)
  set stopTimestampMs($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasStopTimestampMs() => $_has(7);
  @$pb.TagNumber(8)
  void clearStopTimestampMs() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get acreage => $_getN(8);
  @$pb.TagNumber(9)
  set acreage($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasAcreage() => $_has(8);
  @$pb.TagNumber(9)
  void clearAcreage() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get completed => $_getBF(9);
  @$pb.TagNumber(10)
  set completed($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasCompleted() => $_has(9);
  @$pb.TagNumber(10)
  void clearCompleted() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get cropId => $_getSZ(10);
  @$pb.TagNumber(11)
  set cropId($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasCropId() => $_has(10);
  @$pb.TagNumber(11)
  void clearCropId() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get almanac => $_getSZ(11);
  @$pb.TagNumber(12)
  set almanac($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasAlmanac() => $_has(11);
  @$pb.TagNumber(12)
  void clearAlmanac() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get discriminator => $_getSZ(12);
  @$pb.TagNumber(13)
  set discriminator($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDiscriminator() => $_has(12);
  @$pb.TagNumber(13)
  void clearDiscriminator() => clearField(13);

  @$pb.TagNumber(14)
  $78.DailyMetricResponse get metrics => $_getN(13);
  @$pb.TagNumber(14)
  set metrics($78.DailyMetricResponse v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasMetrics() => $_has(13);
  @$pb.TagNumber(14)
  void clearMetrics() => clearField(14);
  @$pb.TagNumber(14)
  $78.DailyMetricResponse ensureMetrics() => $_ensure(13);

  @$pb.TagNumber(15)
  $core.String get crop => $_getSZ(14);
  @$pb.TagNumber(15)
  set crop($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasCrop() => $_has(14);
  @$pb.TagNumber(15)
  void clearCrop() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get serial => $_getSZ(15);
  @$pb.TagNumber(16)
  set serial($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasSerial() => $_has(15);
  @$pb.TagNumber(16)
  void clearSerial() => clearField(16);
}

class UploadJobRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UploadJobRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.jobs'), createEmptyInstance: create)
    ..aOM<$24.Job>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'job', subBuilder: $24.Job.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robot')
    ..hasRequiredFields = false
  ;

  UploadJobRequest._() : super();
  factory UploadJobRequest({
    $24.Job? job,
    $core.String? robot,
  }) {
    final _result = create();
    if (job != null) {
      _result.job = job;
    }
    if (robot != null) {
      _result.robot = robot;
    }
    return _result;
  }
  factory UploadJobRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UploadJobRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UploadJobRequest clone() => UploadJobRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UploadJobRequest copyWith(void Function(UploadJobRequest) updates) => super.copyWith((message) => updates(message as UploadJobRequest)) as UploadJobRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UploadJobRequest create() => UploadJobRequest._();
  UploadJobRequest createEmptyInstance() => create();
  static $pb.PbList<UploadJobRequest> createRepeated() => $pb.PbList<UploadJobRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadJobRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadJobRequest>(create);
  static UploadJobRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $24.Job get job => $_getN(0);
  @$pb.TagNumber(1)
  set job($24.Job v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasJob() => $_has(0);
  @$pb.TagNumber(1)
  void clearJob() => clearField(1);
  @$pb.TagNumber(1)
  $24.Job ensureJob() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get robot => $_getSZ(1);
  @$pb.TagNumber(2)
  set robot($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRobot() => $_has(1);
  @$pb.TagNumber(2)
  void clearRobot() => clearField(2);
}

class UploadJobConfigDumpRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UploadJobConfigDumpRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..aOM<$22.ConfigNodeSnapshot>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'rootConfig', protoName: 'rootConfig', subBuilder: $22.ConfigNodeSnapshot.create)
    ..hasRequiredFields = false
  ;

  UploadJobConfigDumpRequest._() : super();
  factory UploadJobConfigDumpRequest({
    $core.String? jobId,
    $22.ConfigNodeSnapshot? rootConfig,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    if (rootConfig != null) {
      _result.rootConfig = rootConfig;
    }
    return _result;
  }
  factory UploadJobConfigDumpRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UploadJobConfigDumpRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UploadJobConfigDumpRequest clone() => UploadJobConfigDumpRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UploadJobConfigDumpRequest copyWith(void Function(UploadJobConfigDumpRequest) updates) => super.copyWith((message) => updates(message as UploadJobConfigDumpRequest)) as UploadJobConfigDumpRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UploadJobConfigDumpRequest create() => UploadJobConfigDumpRequest._();
  UploadJobConfigDumpRequest createEmptyInstance() => create();
  static $pb.PbList<UploadJobConfigDumpRequest> createRepeated() => $pb.PbList<UploadJobConfigDumpRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadJobConfigDumpRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadJobConfigDumpRequest>(create);
  static UploadJobConfigDumpRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);

  @$pb.TagNumber(2)
  $22.ConfigNodeSnapshot get rootConfig => $_getN(1);
  @$pb.TagNumber(2)
  set rootConfig($22.ConfigNodeSnapshot v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasRootConfig() => $_has(1);
  @$pb.TagNumber(2)
  void clearRootConfig() => clearField(2);
  @$pb.TagNumber(2)
  $22.ConfigNodeSnapshot ensureRootConfig() => $_ensure(1);
}

class UploadJobMetricsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UploadJobMetricsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.jobs'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId', protoName: 'jobId')
    ..aOM<$23.Metrics>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobMetrics', protoName: 'jobMetrics', subBuilder: $23.Metrics.create)
    ..hasRequiredFields = false
  ;

  UploadJobMetricsRequest._() : super();
  factory UploadJobMetricsRequest({
    $core.String? jobId,
    $23.Metrics? jobMetrics,
  }) {
    final _result = create();
    if (jobId != null) {
      _result.jobId = jobId;
    }
    if (jobMetrics != null) {
      _result.jobMetrics = jobMetrics;
    }
    return _result;
  }
  factory UploadJobMetricsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UploadJobMetricsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UploadJobMetricsRequest clone() => UploadJobMetricsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UploadJobMetricsRequest copyWith(void Function(UploadJobMetricsRequest) updates) => super.copyWith((message) => updates(message as UploadJobMetricsRequest)) as UploadJobMetricsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UploadJobMetricsRequest create() => UploadJobMetricsRequest._();
  UploadJobMetricsRequest createEmptyInstance() => create();
  static $pb.PbList<UploadJobMetricsRequest> createRepeated() => $pb.PbList<UploadJobMetricsRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadJobMetricsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadJobMetricsRequest>(create);
  static UploadJobMetricsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => clearField(1);

  @$pb.TagNumber(2)
  $23.Metrics get jobMetrics => $_getN(1);
  @$pb.TagNumber(2)
  set jobMetrics($23.Metrics v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasJobMetrics() => $_has(1);
  @$pb.TagNumber(2)
  void clearJobMetrics() => clearField(2);
  @$pb.TagNumber(2)
  $23.Metrics ensureJobMetrics() => $_ensure(1);
}

