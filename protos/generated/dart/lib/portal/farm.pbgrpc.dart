///
//  Generated code. Do not modify.
//  source: portal/farm.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'farm.pb.dart' as $42;
export 'farm.pb.dart';

class FarmsServiceClient extends $grpc.Client {
  static final _$getFarm =
      $grpc.ClientMethod<$42.GetFarmRequest, $42.GetFarmResponse>(
          '/carbon.portal.farm.FarmsService/GetFarm',
          ($42.GetFarmRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $42.GetFarmResponse.fromBuffer(value));

  FarmsServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$42.GetFarmResponse> getFarm($42.GetFarmRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getFarm, request, options: options);
  }
}

abstract class FarmsServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.farm.FarmsService';

  FarmsServiceBase() {
    $addMethod($grpc.ServiceMethod<$42.GetFarmRequest, $42.GetFarmResponse>(
        'GetFarm',
        getFarm_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $42.GetFarmRequest.fromBuffer(value),
        ($42.GetFarmResponse value) => value.writeToBuffer()));
  }

  $async.Future<$42.GetFarmResponse> getFarm_Pre(
      $grpc.ServiceCall call, $async.Future<$42.GetFarmRequest> request) async {
    return getFarm(call, await request);
  }

  $async.Future<$42.GetFarmResponse> getFarm(
      $grpc.ServiceCall call, $42.GetFarmRequest request);
}
