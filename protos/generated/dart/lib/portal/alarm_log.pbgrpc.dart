///
//  Generated code. Do not modify.
//  source: portal/alarm_log.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'alarm_log.pb.dart' as $41;
import '../util/util.pb.dart' as $1;
export 'alarm_log.pb.dart';

class PortalAlarmLogServiceClient extends $grpc.Client {
  static final _$syncAlarms =
      $grpc.ClientMethod<$41.SyncAlarmsRequest, $1.Empty>(
          '/carbon.portal.alarm_log.PortalAlarmLogService/SyncAlarms',
          ($41.SyncAlarmsRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  PortalAlarmLogServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> syncAlarms($41.SyncAlarmsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$syncAlarms, request, options: options);
  }
}

abstract class PortalAlarmLogServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.alarm_log.PortalAlarmLogService';

  PortalAlarmLogServiceBase() {
    $addMethod($grpc.ServiceMethod<$41.SyncAlarmsRequest, $1.Empty>(
        'SyncAlarms',
        syncAlarms_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $41.SyncAlarmsRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> syncAlarms_Pre($grpc.ServiceCall call,
      $async.Future<$41.SyncAlarmsRequest> request) async {
    return syncAlarms(call, await request);
  }

  $async.Future<$1.Empty> syncAlarms(
      $grpc.ServiceCall call, $41.SyncAlarmsRequest request);
}
