///
//  Generated code. Do not modify.
//  source: portal/farm.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../geo/geo.pb.dart' as $75;
import '../google/protobuf/timestamp.pb.dart' as $70;

class Farm extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Farm', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<$75.Id>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', subBuilder: $75.Id.create)
    ..aOM<VersionInfo>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'version', subBuilder: VersionInfo.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..aInt64(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'customerId')
    ..pc<PointDefinition>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pointDefs', $pb.PbFieldType.PM, subBuilder: PointDefinition.create)
    ..pc<Zone>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'zones', $pb.PbFieldType.PM, subBuilder: Zone.create)
    ..hasRequiredFields = false
  ;

  Farm._() : super();
  factory Farm({
    $75.Id? id,
    VersionInfo? version,
    $core.String? name,
    $fixnum.Int64? customerId,
    $core.Iterable<PointDefinition>? pointDefs,
    $core.Iterable<Zone>? zones,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (version != null) {
      _result.version = version;
    }
    if (name != null) {
      _result.name = name;
    }
    if (customerId != null) {
      _result.customerId = customerId;
    }
    if (pointDefs != null) {
      _result.pointDefs.addAll(pointDefs);
    }
    if (zones != null) {
      _result.zones.addAll(zones);
    }
    return _result;
  }
  factory Farm.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Farm.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Farm clone() => Farm()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Farm copyWith(void Function(Farm) updates) => super.copyWith((message) => updates(message as Farm)) as Farm; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Farm create() => Farm._();
  Farm createEmptyInstance() => create();
  static $pb.PbList<Farm> createRepeated() => $pb.PbList<Farm>();
  @$core.pragma('dart2js:noInline')
  static Farm getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Farm>(create);
  static Farm? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Id get id => $_getN(0);
  @$pb.TagNumber(1)
  set id($75.Id v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
  @$pb.TagNumber(1)
  $75.Id ensureId() => $_ensure(0);

  @$pb.TagNumber(2)
  VersionInfo get version => $_getN(1);
  @$pb.TagNumber(2)
  set version(VersionInfo v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasVersion() => $_has(1);
  @$pb.TagNumber(2)
  void clearVersion() => clearField(2);
  @$pb.TagNumber(2)
  VersionInfo ensureVersion() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get customerId => $_getI64(3);
  @$pb.TagNumber(4)
  set customerId($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCustomerId() => $_has(3);
  @$pb.TagNumber(4)
  void clearCustomerId() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<PointDefinition> get pointDefs => $_getList(4);

  @$pb.TagNumber(6)
  $core.List<Zone> get zones => $_getList(5);
}

class VersionInfo extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'VersionInfo', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aInt64(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ordinal')
    ..aOM<$70.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'updateTime', subBuilder: $70.Timestamp.create)
    ..aOB(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'deleted')
    ..aOB(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'changed')
    ..hasRequiredFields = false
  ;

  VersionInfo._() : super();
  factory VersionInfo({
    $fixnum.Int64? ordinal,
    $70.Timestamp? updateTime,
    $core.bool? deleted,
    $core.bool? changed,
  }) {
    final _result = create();
    if (ordinal != null) {
      _result.ordinal = ordinal;
    }
    if (updateTime != null) {
      _result.updateTime = updateTime;
    }
    if (deleted != null) {
      _result.deleted = deleted;
    }
    if (changed != null) {
      _result.changed = changed;
    }
    return _result;
  }
  factory VersionInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory VersionInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  VersionInfo clone() => VersionInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  VersionInfo copyWith(void Function(VersionInfo) updates) => super.copyWith((message) => updates(message as VersionInfo)) as VersionInfo; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static VersionInfo create() => VersionInfo._();
  VersionInfo createEmptyInstance() => create();
  static $pb.PbList<VersionInfo> createRepeated() => $pb.PbList<VersionInfo>();
  @$core.pragma('dart2js:noInline')
  static VersionInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<VersionInfo>(create);
  static VersionInfo? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get ordinal => $_getI64(0);
  @$pb.TagNumber(1)
  set ordinal($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasOrdinal() => $_has(0);
  @$pb.TagNumber(1)
  void clearOrdinal() => clearField(1);

  @$pb.TagNumber(2)
  $70.Timestamp get updateTime => $_getN(1);
  @$pb.TagNumber(2)
  set updateTime($70.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateTime() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateTime() => clearField(2);
  @$pb.TagNumber(2)
  $70.Timestamp ensureUpdateTime() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.bool get deleted => $_getBF(2);
  @$pb.TagNumber(3)
  set deleted($core.bool v) { $_setBool(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeleted() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeleted() => clearField(3);

  @$pb.TagNumber(4)
  $core.bool get changed => $_getBF(3);
  @$pb.TagNumber(4)
  set changed($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasChanged() => $_has(3);
  @$pb.TagNumber(4)
  void clearChanged() => clearField(4);
}

class PointDefinition extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PointDefinition', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<$75.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'point', subBuilder: $75.Point.create)
    ..aOM<VersionInfo>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'version', subBuilder: VersionInfo.create)
    ..hasRequiredFields = false
  ;

  PointDefinition._() : super();
  factory PointDefinition({
    $75.Point? point,
    VersionInfo? version,
  }) {
    final _result = create();
    if (point != null) {
      _result.point = point;
    }
    if (version != null) {
      _result.version = version;
    }
    return _result;
  }
  factory PointDefinition.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PointDefinition.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PointDefinition clone() => PointDefinition()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PointDefinition copyWith(void Function(PointDefinition) updates) => super.copyWith((message) => updates(message as PointDefinition)) as PointDefinition; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PointDefinition create() => PointDefinition._();
  PointDefinition createEmptyInstance() => create();
  static $pb.PbList<PointDefinition> createRepeated() => $pb.PbList<PointDefinition>();
  @$core.pragma('dart2js:noInline')
  static PointDefinition getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PointDefinition>(create);
  static PointDefinition? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Point get point => $_getN(0);
  @$pb.TagNumber(1)
  set point($75.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPoint() => $_has(0);
  @$pb.TagNumber(1)
  void clearPoint() => clearField(1);
  @$pb.TagNumber(1)
  $75.Point ensurePoint() => $_ensure(0);

  @$pb.TagNumber(2)
  VersionInfo get version => $_getN(1);
  @$pb.TagNumber(2)
  set version(VersionInfo v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasVersion() => $_has(1);
  @$pb.TagNumber(2)
  void clearVersion() => clearField(2);
  @$pb.TagNumber(2)
  VersionInfo ensureVersion() => $_ensure(1);
}

class Zone extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Zone', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<$75.Id>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', subBuilder: $75.Id.create)
    ..aOM<VersionInfo>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'version', subBuilder: VersionInfo.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..pc<Area>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'areas', $pb.PbFieldType.PM, subBuilder: Area.create)
    ..aOM<ZoneContents>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'contents', subBuilder: ZoneContents.create)
    ..hasRequiredFields = false
  ;

  Zone._() : super();
  factory Zone({
    $75.Id? id,
    VersionInfo? version,
    $core.String? name,
    $core.Iterable<Area>? areas,
    ZoneContents? contents,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (version != null) {
      _result.version = version;
    }
    if (name != null) {
      _result.name = name;
    }
    if (areas != null) {
      _result.areas.addAll(areas);
    }
    if (contents != null) {
      _result.contents = contents;
    }
    return _result;
  }
  factory Zone.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Zone.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Zone clone() => Zone()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Zone copyWith(void Function(Zone) updates) => super.copyWith((message) => updates(message as Zone)) as Zone; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Zone create() => Zone._();
  Zone createEmptyInstance() => create();
  static $pb.PbList<Zone> createRepeated() => $pb.PbList<Zone>();
  @$core.pragma('dart2js:noInline')
  static Zone getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Zone>(create);
  static Zone? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Id get id => $_getN(0);
  @$pb.TagNumber(1)
  set id($75.Id v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
  @$pb.TagNumber(1)
  $75.Id ensureId() => $_ensure(0);

  @$pb.TagNumber(2)
  VersionInfo get version => $_getN(1);
  @$pb.TagNumber(2)
  set version(VersionInfo v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasVersion() => $_has(1);
  @$pb.TagNumber(2)
  void clearVersion() => clearField(2);
  @$pb.TagNumber(2)
  VersionInfo ensureVersion() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  @$pb.TagNumber(4)
  $core.List<Area> get areas => $_getList(3);

  @$pb.TagNumber(5)
  ZoneContents get contents => $_getN(4);
  @$pb.TagNumber(5)
  set contents(ZoneContents v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasContents() => $_has(4);
  @$pb.TagNumber(5)
  void clearContents() => clearField(5);
  @$pb.TagNumber(5)
  ZoneContents ensureContents() => $_ensure(4);
}

enum ZoneContents_Data {
  field_5, 
  headland, 
  privateRoad, 
  obstacle, 
  farmBoundary, 
  notSet
}

class ZoneContents extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, ZoneContents_Data> _ZoneContents_DataByTag = {
    5 : ZoneContents_Data.field_5,
    6 : ZoneContents_Data.headland,
    7 : ZoneContents_Data.privateRoad,
    8 : ZoneContents_Data.obstacle,
    9 : ZoneContents_Data.farmBoundary,
    0 : ZoneContents_Data.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ZoneContents', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..oo(0, [5, 6, 7, 8, 9])
    ..aOM<FieldData>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'field', subBuilder: FieldData.create)
    ..aOM<HeadlandData>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'headland', subBuilder: HeadlandData.create)
    ..aOM<PrivateRoadData>(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'privateRoad', subBuilder: PrivateRoadData.create)
    ..aOM<ObstacleData>(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'obstacle', subBuilder: ObstacleData.create)
    ..aOM<FarmBoundaryData>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'farmBoundary', subBuilder: FarmBoundaryData.create)
    ..hasRequiredFields = false
  ;

  ZoneContents._() : super();
  factory ZoneContents({
    FieldData? field_5,
    HeadlandData? headland,
    PrivateRoadData? privateRoad,
    ObstacleData? obstacle,
    FarmBoundaryData? farmBoundary,
  }) {
    final _result = create();
    if (field_5 != null) {
      _result.field_5 = field_5;
    }
    if (headland != null) {
      _result.headland = headland;
    }
    if (privateRoad != null) {
      _result.privateRoad = privateRoad;
    }
    if (obstacle != null) {
      _result.obstacle = obstacle;
    }
    if (farmBoundary != null) {
      _result.farmBoundary = farmBoundary;
    }
    return _result;
  }
  factory ZoneContents.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ZoneContents.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ZoneContents clone() => ZoneContents()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ZoneContents copyWith(void Function(ZoneContents) updates) => super.copyWith((message) => updates(message as ZoneContents)) as ZoneContents; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ZoneContents create() => ZoneContents._();
  ZoneContents createEmptyInstance() => create();
  static $pb.PbList<ZoneContents> createRepeated() => $pb.PbList<ZoneContents>();
  @$core.pragma('dart2js:noInline')
  static ZoneContents getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZoneContents>(create);
  static ZoneContents? _defaultInstance;

  ZoneContents_Data whichData() => _ZoneContents_DataByTag[$_whichOneof(0)]!;
  void clearData() => clearField($_whichOneof(0));

  @$pb.TagNumber(5)
  FieldData get field_5 => $_getN(0);
  @$pb.TagNumber(5)
  set field_5(FieldData v) { setField(5, v); }
  @$pb.TagNumber(5)
  $core.bool hasField_5() => $_has(0);
  @$pb.TagNumber(5)
  void clearField_5() => clearField(5);
  @$pb.TagNumber(5)
  FieldData ensureField_5() => $_ensure(0);

  @$pb.TagNumber(6)
  HeadlandData get headland => $_getN(1);
  @$pb.TagNumber(6)
  set headland(HeadlandData v) { setField(6, v); }
  @$pb.TagNumber(6)
  $core.bool hasHeadland() => $_has(1);
  @$pb.TagNumber(6)
  void clearHeadland() => clearField(6);
  @$pb.TagNumber(6)
  HeadlandData ensureHeadland() => $_ensure(1);

  @$pb.TagNumber(7)
  PrivateRoadData get privateRoad => $_getN(2);
  @$pb.TagNumber(7)
  set privateRoad(PrivateRoadData v) { setField(7, v); }
  @$pb.TagNumber(7)
  $core.bool hasPrivateRoad() => $_has(2);
  @$pb.TagNumber(7)
  void clearPrivateRoad() => clearField(7);
  @$pb.TagNumber(7)
  PrivateRoadData ensurePrivateRoad() => $_ensure(2);

  @$pb.TagNumber(8)
  ObstacleData get obstacle => $_getN(3);
  @$pb.TagNumber(8)
  set obstacle(ObstacleData v) { setField(8, v); }
  @$pb.TagNumber(8)
  $core.bool hasObstacle() => $_has(3);
  @$pb.TagNumber(8)
  void clearObstacle() => clearField(8);
  @$pb.TagNumber(8)
  ObstacleData ensureObstacle() => $_ensure(3);

  @$pb.TagNumber(9)
  FarmBoundaryData get farmBoundary => $_getN(4);
  @$pb.TagNumber(9)
  set farmBoundary(FarmBoundaryData v) { setField(9, v); }
  @$pb.TagNumber(9)
  $core.bool hasFarmBoundary() => $_has(4);
  @$pb.TagNumber(9)
  void clearFarmBoundary() => clearField(9);
  @$pb.TagNumber(9)
  FarmBoundaryData ensureFarmBoundary() => $_ensure(4);
}

enum Area_Geometry {
  point, 
  lineString, 
  polygon, 
  notSet
}

class Area extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, Area_Geometry> _Area_GeometryByTag = {
    2 : Area_Geometry.point,
    3 : Area_Geometry.lineString,
    4 : Area_Geometry.polygon,
    0 : Area_Geometry.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Area', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..oo(0, [2, 3, 4])
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bufferMeters', $pb.PbFieldType.OD)
    ..aOM<$75.Point>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'point', subBuilder: $75.Point.create)
    ..aOM<$75.LineString>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lineString', subBuilder: $75.LineString.create)
    ..aOM<$75.Polygon>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'polygon', subBuilder: $75.Polygon.create)
    ..hasRequiredFields = false
  ;

  Area._() : super();
  factory Area({
    $core.double? bufferMeters,
    $75.Point? point,
    $75.LineString? lineString,
    $75.Polygon? polygon,
  }) {
    final _result = create();
    if (bufferMeters != null) {
      _result.bufferMeters = bufferMeters;
    }
    if (point != null) {
      _result.point = point;
    }
    if (lineString != null) {
      _result.lineString = lineString;
    }
    if (polygon != null) {
      _result.polygon = polygon;
    }
    return _result;
  }
  factory Area.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Area.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Area clone() => Area()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Area copyWith(void Function(Area) updates) => super.copyWith((message) => updates(message as Area)) as Area; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Area create() => Area._();
  Area createEmptyInstance() => create();
  static $pb.PbList<Area> createRepeated() => $pb.PbList<Area>();
  @$core.pragma('dart2js:noInline')
  static Area getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Area>(create);
  static Area? _defaultInstance;

  Area_Geometry whichGeometry() => _Area_GeometryByTag[$_whichOneof(0)]!;
  void clearGeometry() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.double get bufferMeters => $_getN(0);
  @$pb.TagNumber(1)
  set bufferMeters($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBufferMeters() => $_has(0);
  @$pb.TagNumber(1)
  void clearBufferMeters() => clearField(1);

  @$pb.TagNumber(2)
  $75.Point get point => $_getN(1);
  @$pb.TagNumber(2)
  set point($75.Point v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasPoint() => $_has(1);
  @$pb.TagNumber(2)
  void clearPoint() => clearField(2);
  @$pb.TagNumber(2)
  $75.Point ensurePoint() => $_ensure(1);

  @$pb.TagNumber(3)
  $75.LineString get lineString => $_getN(2);
  @$pb.TagNumber(3)
  set lineString($75.LineString v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasLineString() => $_has(2);
  @$pb.TagNumber(3)
  void clearLineString() => clearField(3);
  @$pb.TagNumber(3)
  $75.LineString ensureLineString() => $_ensure(2);

  @$pb.TagNumber(4)
  $75.Polygon get polygon => $_getN(3);
  @$pb.TagNumber(4)
  set polygon($75.Polygon v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasPolygon() => $_has(3);
  @$pb.TagNumber(4)
  void clearPolygon() => clearField(4);
  @$pb.TagNumber(4)
  $75.Polygon ensurePolygon() => $_ensure(3);
}

class FarmBoundaryData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FarmBoundaryData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  FarmBoundaryData._() : super();
  factory FarmBoundaryData() => create();
  factory FarmBoundaryData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FarmBoundaryData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FarmBoundaryData clone() => FarmBoundaryData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FarmBoundaryData copyWith(void Function(FarmBoundaryData) updates) => super.copyWith((message) => updates(message as FarmBoundaryData)) as FarmBoundaryData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FarmBoundaryData create() => FarmBoundaryData._();
  FarmBoundaryData createEmptyInstance() => create();
  static $pb.PbList<FarmBoundaryData> createRepeated() => $pb.PbList<FarmBoundaryData>();
  @$core.pragma('dart2js:noInline')
  static FarmBoundaryData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FarmBoundaryData>(create);
  static FarmBoundaryData? _defaultInstance;
}

class FieldData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'FieldData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<PlantingHeading>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'plantingHeading', subBuilder: PlantingHeading.create)
    ..aOM<CenterPivot>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'centerPivot', subBuilder: CenterPivot.create)
    ..hasRequiredFields = false
  ;

  FieldData._() : super();
  factory FieldData({
    PlantingHeading? plantingHeading,
    CenterPivot? centerPivot,
  }) {
    final _result = create();
    if (plantingHeading != null) {
      _result.plantingHeading = plantingHeading;
    }
    if (centerPivot != null) {
      _result.centerPivot = centerPivot;
    }
    return _result;
  }
  factory FieldData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory FieldData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  FieldData clone() => FieldData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  FieldData copyWith(void Function(FieldData) updates) => super.copyWith((message) => updates(message as FieldData)) as FieldData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static FieldData create() => FieldData._();
  FieldData createEmptyInstance() => create();
  static $pb.PbList<FieldData> createRepeated() => $pb.PbList<FieldData>();
  @$core.pragma('dart2js:noInline')
  static FieldData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FieldData>(create);
  static FieldData? _defaultInstance;

  @$pb.TagNumber(1)
  PlantingHeading get plantingHeading => $_getN(0);
  @$pb.TagNumber(1)
  set plantingHeading(PlantingHeading v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPlantingHeading() => $_has(0);
  @$pb.TagNumber(1)
  void clearPlantingHeading() => clearField(1);
  @$pb.TagNumber(1)
  PlantingHeading ensurePlantingHeading() => $_ensure(0);

  @$pb.TagNumber(2)
  CenterPivot get centerPivot => $_getN(1);
  @$pb.TagNumber(2)
  set centerPivot(CenterPivot v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasCenterPivot() => $_has(1);
  @$pb.TagNumber(2)
  void clearCenterPivot() => clearField(2);
  @$pb.TagNumber(2)
  CenterPivot ensureCenterPivot() => $_ensure(1);
}

class HeadlandData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'HeadlandData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  HeadlandData._() : super();
  factory HeadlandData() => create();
  factory HeadlandData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory HeadlandData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  HeadlandData clone() => HeadlandData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  HeadlandData copyWith(void Function(HeadlandData) updates) => super.copyWith((message) => updates(message as HeadlandData)) as HeadlandData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static HeadlandData create() => HeadlandData._();
  HeadlandData createEmptyInstance() => create();
  static $pb.PbList<HeadlandData> createRepeated() => $pb.PbList<HeadlandData>();
  @$core.pragma('dart2js:noInline')
  static HeadlandData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HeadlandData>(create);
  static HeadlandData? _defaultInstance;
}

class PrivateRoadData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PrivateRoadData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  PrivateRoadData._() : super();
  factory PrivateRoadData() => create();
  factory PrivateRoadData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PrivateRoadData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PrivateRoadData clone() => PrivateRoadData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PrivateRoadData copyWith(void Function(PrivateRoadData) updates) => super.copyWith((message) => updates(message as PrivateRoadData)) as PrivateRoadData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PrivateRoadData create() => PrivateRoadData._();
  PrivateRoadData createEmptyInstance() => create();
  static $pb.PbList<PrivateRoadData> createRepeated() => $pb.PbList<PrivateRoadData>();
  @$core.pragma('dart2js:noInline')
  static PrivateRoadData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PrivateRoadData>(create);
  static PrivateRoadData? _defaultInstance;
}

class ObstacleData extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ObstacleData', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  ObstacleData._() : super();
  factory ObstacleData() => create();
  factory ObstacleData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ObstacleData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ObstacleData clone() => ObstacleData()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ObstacleData copyWith(void Function(ObstacleData) updates) => super.copyWith((message) => updates(message as ObstacleData)) as ObstacleData; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ObstacleData create() => ObstacleData._();
  ObstacleData createEmptyInstance() => create();
  static $pb.PbList<ObstacleData> createRepeated() => $pb.PbList<ObstacleData>();
  @$core.pragma('dart2js:noInline')
  static ObstacleData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ObstacleData>(create);
  static ObstacleData? _defaultInstance;
}

enum PlantingHeading_Heading {
  azimuthDegrees, 
  abLine, 
  notSet
}

class PlantingHeading extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, PlantingHeading_Heading> _PlantingHeading_HeadingByTag = {
    1 : PlantingHeading_Heading.azimuthDegrees,
    2 : PlantingHeading_Heading.abLine,
    0 : PlantingHeading_Heading.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'PlantingHeading', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..a<$core.double>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'azimuthDegrees', $pb.PbFieldType.OD)
    ..aOM<$75.AbLine>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'abLine', subBuilder: $75.AbLine.create)
    ..hasRequiredFields = false
  ;

  PlantingHeading._() : super();
  factory PlantingHeading({
    $core.double? azimuthDegrees,
    $75.AbLine? abLine,
  }) {
    final _result = create();
    if (azimuthDegrees != null) {
      _result.azimuthDegrees = azimuthDegrees;
    }
    if (abLine != null) {
      _result.abLine = abLine;
    }
    return _result;
  }
  factory PlantingHeading.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PlantingHeading.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PlantingHeading clone() => PlantingHeading()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PlantingHeading copyWith(void Function(PlantingHeading) updates) => super.copyWith((message) => updates(message as PlantingHeading)) as PlantingHeading; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static PlantingHeading create() => PlantingHeading._();
  PlantingHeading createEmptyInstance() => create();
  static $pb.PbList<PlantingHeading> createRepeated() => $pb.PbList<PlantingHeading>();
  @$core.pragma('dart2js:noInline')
  static PlantingHeading getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PlantingHeading>(create);
  static PlantingHeading? _defaultInstance;

  PlantingHeading_Heading whichHeading() => _PlantingHeading_HeadingByTag[$_whichOneof(0)]!;
  void clearHeading() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.double get azimuthDegrees => $_getN(0);
  @$pb.TagNumber(1)
  set azimuthDegrees($core.double v) { $_setDouble(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasAzimuthDegrees() => $_has(0);
  @$pb.TagNumber(1)
  void clearAzimuthDegrees() => clearField(1);

  @$pb.TagNumber(2)
  $75.AbLine get abLine => $_getN(1);
  @$pb.TagNumber(2)
  set abLine($75.AbLine v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAbLine() => $_has(1);
  @$pb.TagNumber(2)
  void clearAbLine() => clearField(2);
  @$pb.TagNumber(2)
  $75.AbLine ensureAbLine() => $_ensure(1);
}

class CenterPivot extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CenterPivot', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<$75.Point>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'center', subBuilder: $75.Point.create)
    ..a<$core.double>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'widthMeters', $pb.PbFieldType.OD)
    ..a<$core.double>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'lengthMeters', $pb.PbFieldType.OD)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endpointDeviceId')
    ..hasRequiredFields = false
  ;

  CenterPivot._() : super();
  factory CenterPivot({
    $75.Point? center,
    $core.double? widthMeters,
    $core.double? lengthMeters,
    $core.String? endpointDeviceId,
  }) {
    final _result = create();
    if (center != null) {
      _result.center = center;
    }
    if (widthMeters != null) {
      _result.widthMeters = widthMeters;
    }
    if (lengthMeters != null) {
      _result.lengthMeters = lengthMeters;
    }
    if (endpointDeviceId != null) {
      _result.endpointDeviceId = endpointDeviceId;
    }
    return _result;
  }
  factory CenterPivot.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CenterPivot.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CenterPivot clone() => CenterPivot()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CenterPivot copyWith(void Function(CenterPivot) updates) => super.copyWith((message) => updates(message as CenterPivot)) as CenterPivot; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CenterPivot create() => CenterPivot._();
  CenterPivot createEmptyInstance() => create();
  static $pb.PbList<CenterPivot> createRepeated() => $pb.PbList<CenterPivot>();
  @$core.pragma('dart2js:noInline')
  static CenterPivot getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CenterPivot>(create);
  static CenterPivot? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Point get center => $_getN(0);
  @$pb.TagNumber(1)
  set center($75.Point v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasCenter() => $_has(0);
  @$pb.TagNumber(1)
  void clearCenter() => clearField(1);
  @$pb.TagNumber(1)
  $75.Point ensureCenter() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.double get widthMeters => $_getN(1);
  @$pb.TagNumber(2)
  set widthMeters($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasWidthMeters() => $_has(1);
  @$pb.TagNumber(2)
  void clearWidthMeters() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get lengthMeters => $_getN(2);
  @$pb.TagNumber(3)
  set lengthMeters($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLengthMeters() => $_has(2);
  @$pb.TagNumber(3)
  void clearLengthMeters() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get endpointDeviceId => $_getSZ(3);
  @$pb.TagNumber(4)
  set endpointDeviceId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasEndpointDeviceId() => $_has(3);
  @$pb.TagNumber(4)
  void clearEndpointDeviceId() => clearField(4);
}

class CreateFarmRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'CreateFarmRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<Farm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'farm', subBuilder: Farm.create)
    ..hasRequiredFields = false
  ;

  CreateFarmRequest._() : super();
  factory CreateFarmRequest({
    Farm? farm,
  }) {
    final _result = create();
    if (farm != null) {
      _result.farm = farm;
    }
    return _result;
  }
  factory CreateFarmRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateFarmRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateFarmRequest clone() => CreateFarmRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateFarmRequest copyWith(void Function(CreateFarmRequest) updates) => super.copyWith((message) => updates(message as CreateFarmRequest)) as CreateFarmRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static CreateFarmRequest create() => CreateFarmRequest._();
  CreateFarmRequest createEmptyInstance() => create();
  static $pb.PbList<CreateFarmRequest> createRepeated() => $pb.PbList<CreateFarmRequest>();
  @$core.pragma('dart2js:noInline')
  static CreateFarmRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateFarmRequest>(create);
  static CreateFarmRequest? _defaultInstance;

  @$pb.TagNumber(1)
  Farm get farm => $_getN(0);
  @$pb.TagNumber(1)
  set farm(Farm v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasFarm() => $_has(0);
  @$pb.TagNumber(1)
  void clearFarm() => clearField(1);
  @$pb.TagNumber(1)
  Farm ensureFarm() => $_ensure(0);
}

class UpdateFarmRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'UpdateFarmRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<Farm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'farm', subBuilder: Farm.create)
    ..hasRequiredFields = false
  ;

  UpdateFarmRequest._() : super();
  factory UpdateFarmRequest({
    Farm? farm,
  }) {
    final _result = create();
    if (farm != null) {
      _result.farm = farm;
    }
    return _result;
  }
  factory UpdateFarmRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory UpdateFarmRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  UpdateFarmRequest clone() => UpdateFarmRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  UpdateFarmRequest copyWith(void Function(UpdateFarmRequest) updates) => super.copyWith((message) => updates(message as UpdateFarmRequest)) as UpdateFarmRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static UpdateFarmRequest create() => UpdateFarmRequest._();
  UpdateFarmRequest createEmptyInstance() => create();
  static $pb.PbList<UpdateFarmRequest> createRepeated() => $pb.PbList<UpdateFarmRequest>();
  @$core.pragma('dart2js:noInline')
  static UpdateFarmRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UpdateFarmRequest>(create);
  static UpdateFarmRequest? _defaultInstance;

  @$pb.TagNumber(1)
  Farm get farm => $_getN(0);
  @$pb.TagNumber(1)
  set farm(Farm v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasFarm() => $_has(0);
  @$pb.TagNumber(1)
  void clearFarm() => clearField(1);
  @$pb.TagNumber(1)
  Farm ensureFarm() => $_ensure(0);
}

class ListFarmsRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListFarmsRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'pageToken')
    ..hasRequiredFields = false
  ;

  ListFarmsRequest._() : super();
  factory ListFarmsRequest({
    $core.String? pageToken,
  }) {
    final _result = create();
    if (pageToken != null) {
      _result.pageToken = pageToken;
    }
    return _result;
  }
  factory ListFarmsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListFarmsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListFarmsRequest clone() => ListFarmsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListFarmsRequest copyWith(void Function(ListFarmsRequest) updates) => super.copyWith((message) => updates(message as ListFarmsRequest)) as ListFarmsRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListFarmsRequest create() => ListFarmsRequest._();
  ListFarmsRequest createEmptyInstance() => create();
  static $pb.PbList<ListFarmsRequest> createRepeated() => $pb.PbList<ListFarmsRequest>();
  @$core.pragma('dart2js:noInline')
  static ListFarmsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListFarmsRequest>(create);
  static ListFarmsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get pageToken => $_getSZ(0);
  @$pb.TagNumber(1)
  set pageToken($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPageToken() => $_has(0);
  @$pb.TagNumber(1)
  void clearPageToken() => clearField(1);
}

class ListFarmsResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ListFarmsResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..pc<Farm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'farms', $pb.PbFieldType.PM, subBuilder: Farm.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'nextPageToken')
    ..hasRequiredFields = false
  ;

  ListFarmsResponse._() : super();
  factory ListFarmsResponse({
    $core.Iterable<Farm>? farms,
    $core.String? nextPageToken,
  }) {
    final _result = create();
    if (farms != null) {
      _result.farms.addAll(farms);
    }
    if (nextPageToken != null) {
      _result.nextPageToken = nextPageToken;
    }
    return _result;
  }
  factory ListFarmsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListFarmsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListFarmsResponse clone() => ListFarmsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListFarmsResponse copyWith(void Function(ListFarmsResponse) updates) => super.copyWith((message) => updates(message as ListFarmsResponse)) as ListFarmsResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ListFarmsResponse create() => ListFarmsResponse._();
  ListFarmsResponse createEmptyInstance() => create();
  static $pb.PbList<ListFarmsResponse> createRepeated() => $pb.PbList<ListFarmsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListFarmsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListFarmsResponse>(create);
  static ListFarmsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<Farm> get farms => $_getList(0);

  @$pb.TagNumber(2)
  $core.String get nextPageToken => $_getSZ(1);
  @$pb.TagNumber(2)
  set nextPageToken($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasNextPageToken() => $_has(1);
  @$pb.TagNumber(2)
  void clearNextPageToken() => clearField(2);
}

class GetFarmRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetFarmRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<$75.Id>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'id', subBuilder: $75.Id.create)
    ..aOM<$70.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'ifModifiedSince', subBuilder: $70.Timestamp.create)
    ..hasRequiredFields = false
  ;

  GetFarmRequest._() : super();
  factory GetFarmRequest({
    $75.Id? id,
    $70.Timestamp? ifModifiedSince,
  }) {
    final _result = create();
    if (id != null) {
      _result.id = id;
    }
    if (ifModifiedSince != null) {
      _result.ifModifiedSince = ifModifiedSince;
    }
    return _result;
  }
  factory GetFarmRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetFarmRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetFarmRequest clone() => GetFarmRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetFarmRequest copyWith(void Function(GetFarmRequest) updates) => super.copyWith((message) => updates(message as GetFarmRequest)) as GetFarmRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetFarmRequest create() => GetFarmRequest._();
  GetFarmRequest createEmptyInstance() => create();
  static $pb.PbList<GetFarmRequest> createRepeated() => $pb.PbList<GetFarmRequest>();
  @$core.pragma('dart2js:noInline')
  static GetFarmRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetFarmRequest>(create);
  static GetFarmRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $75.Id get id => $_getN(0);
  @$pb.TagNumber(1)
  set id($75.Id v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => clearField(1);
  @$pb.TagNumber(1)
  $75.Id ensureId() => $_ensure(0);

  @$pb.TagNumber(2)
  $70.Timestamp get ifModifiedSince => $_getN(1);
  @$pb.TagNumber(2)
  set ifModifiedSince($70.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasIfModifiedSince() => $_has(1);
  @$pb.TagNumber(2)
  void clearIfModifiedSince() => clearField(2);
  @$pb.TagNumber(2)
  $70.Timestamp ensureIfModifiedSince() => $_ensure(1);
}

class GetFarmResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'GetFarmResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.farm'), createEmptyInstance: create)
    ..aOM<Farm>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'farm', subBuilder: Farm.create)
    ..hasRequiredFields = false
  ;

  GetFarmResponse._() : super();
  factory GetFarmResponse({
    Farm? farm,
  }) {
    final _result = create();
    if (farm != null) {
      _result.farm = farm;
    }
    return _result;
  }
  factory GetFarmResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetFarmResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetFarmResponse clone() => GetFarmResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetFarmResponse copyWith(void Function(GetFarmResponse) updates) => super.copyWith((message) => updates(message as GetFarmResponse)) as GetFarmResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static GetFarmResponse create() => GetFarmResponse._();
  GetFarmResponse createEmptyInstance() => create();
  static $pb.PbList<GetFarmResponse> createRepeated() => $pb.PbList<GetFarmResponse>();
  @$core.pragma('dart2js:noInline')
  static GetFarmResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetFarmResponse>(create);
  static GetFarmResponse? _defaultInstance;

  @$pb.TagNumber(1)
  Farm get farm => $_getN(0);
  @$pb.TagNumber(1)
  set farm(Farm v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasFarm() => $_has(0);
  @$pb.TagNumber(1)
  void clearFarm() => clearField(1);
  @$pb.TagNumber(1)
  Farm ensureFarm() => $_ensure(0);
}

