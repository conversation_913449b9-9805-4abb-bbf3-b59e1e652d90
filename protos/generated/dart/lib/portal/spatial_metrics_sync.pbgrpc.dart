///
//  Generated code. Do not modify.
//  source: portal/spatial_metrics_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'spatial_metrics_sync.pb.dart' as $50;
import '../util/util.pb.dart' as $1;
export 'spatial_metrics_sync.pb.dart';

class SpatialMetricsSyncServiceClient extends $grpc.Client {
  static final _$syncSpatialMetricBlocks = $grpc.ClientMethod<
          $50.SyncSpatialMetricBlocksRequest, $1.Empty>(
      '/carbon.portal.spatial_metrics.SpatialMetricsSyncService/SyncSpatialMetricBlocks',
      ($50.SyncSpatialMetricBlocksRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  SpatialMetricsSyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> syncSpatialMetricBlocks(
      $50.SyncSpatialMetricBlocksRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$syncSpatialMetricBlocks, request,
        options: options);
  }
}

abstract class SpatialMetricsSyncServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.portal.spatial_metrics.SpatialMetricsSyncService';

  SpatialMetricsSyncServiceBase() {
    $addMethod(
        $grpc.ServiceMethod<$50.SyncSpatialMetricBlocksRequest, $1.Empty>(
            'SyncSpatialMetricBlocks',
            syncSpatialMetricBlocks_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $50.SyncSpatialMetricBlocksRequest.fromBuffer(value),
            ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> syncSpatialMetricBlocks_Pre($grpc.ServiceCall call,
      $async.Future<$50.SyncSpatialMetricBlocksRequest> request) async {
    return syncSpatialMetricBlocks(call, await request);
  }

  $async.Future<$1.Empty> syncSpatialMetricBlocks(
      $grpc.ServiceCall call, $50.SyncSpatialMetricBlocksRequest request);
}
