///
//  Generated code. Do not modify.
//  source: portal/reaper.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'reaper.pb.dart' as $48;
export 'reaper.pb.dart';

class ReaperConfigurationServiceClient extends $grpc.Client {
  static final _$uploadReaperConfiguration = $grpc.ClientMethod<
          $48.UploadReaperConfigurationRequest,
          $48.UploadReaperConfigurationResponse>(
      '/carbon.portal.reaper.ReaperConfigurationService/UploadReaperConfiguration',
      ($48.UploadReaperConfigurationRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $48.UploadReaperConfigurationResponse.fromBuffer(value));

  ReaperConfigurationServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$48.UploadReaperConfigurationResponse>
      uploadReaperConfiguration($48.UploadReaperConfigurationRequest request,
          {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadReaperConfiguration, request,
        options: options);
  }
}

abstract class ReaperConfigurationServiceBase extends $grpc.Service {
  $core.String get $name => 'carbon.portal.reaper.ReaperConfigurationService';

  ReaperConfigurationServiceBase() {
    $addMethod($grpc.ServiceMethod<$48.UploadReaperConfigurationRequest,
            $48.UploadReaperConfigurationResponse>(
        'UploadReaperConfiguration',
        uploadReaperConfiguration_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $48.UploadReaperConfigurationRequest.fromBuffer(value),
        ($48.UploadReaperConfigurationResponse value) =>
            value.writeToBuffer()));
  }

  $async.Future<$48.UploadReaperConfigurationResponse>
      uploadReaperConfiguration_Pre($grpc.ServiceCall call,
          $async.Future<$48.UploadReaperConfigurationRequest> request) async {
    return uploadReaperConfiguration(call, await request);
  }

  $async.Future<$48.UploadReaperConfigurationResponse>
      uploadReaperConfiguration(
          $grpc.ServiceCall call, $48.UploadReaperConfigurationRequest request);
}
