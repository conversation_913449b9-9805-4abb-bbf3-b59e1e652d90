///
//  Generated code. Do not modify.
//  source: portal/model_history_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'model_history_sync.pb.dart' as $45;
import '../util/util.pb.dart' as $1;
export 'model_history_sync.pb.dart';

class ModelHistorySyncServiceClient extends $grpc.Client {
  static final _$uploadModelEvents = $grpc.ClientMethod<
          $45.UploadModelEventsRequest, $1.Empty>(
      '/carbon.portal.model_history.ModelHistorySyncService/UploadModelEvents',
      ($45.UploadModelEventsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  ModelHistorySyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.Empty> uploadModelEvents(
      $45.UploadModelEventsRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadModelEvents, request, options: options);
  }
}

abstract class ModelHistorySyncServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.portal.model_history.ModelHistorySyncService';

  ModelHistorySyncServiceBase() {
    $addMethod($grpc.ServiceMethod<$45.UploadModelEventsRequest, $1.Empty>(
        'UploadModelEvents',
        uploadModelEvents_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $45.UploadModelEventsRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$1.Empty> uploadModelEvents_Pre($grpc.ServiceCall call,
      $async.Future<$45.UploadModelEventsRequest> request) async {
    return uploadModelEvents(call, await request);
  }

  $async.Future<$1.Empty> uploadModelEvents(
      $grpc.ServiceCall call, $45.UploadModelEventsRequest request);
}
