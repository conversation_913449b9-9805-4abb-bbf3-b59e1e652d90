///
//  Generated code. Do not modify.
//  source: portal/metrics.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $74;

class DailyMetricResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DailyMetricResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.metrics'), createEmptyInstance: create)
    ..aOM<$74.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $74.DB.create)
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..a<$fixnum.Int64>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.double>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'acresWeeded', $pb.PbFieldType.OF)
    ..a<$core.double>(5, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'avgSpeedMph', $pb.PbFieldType.OF)
    ..a<$core.double>(6, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'avgWeedSizeMm', $pb.PbFieldType.OF)
    ..aOS(7, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingConfigName')
    ..aOB(8, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingEnabled')
    ..a<$core.double>(9, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'coverageSpeedAcresHr', $pb.PbFieldType.OF)
    ..a<$core.double>(10, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'distanceWeededMeters', $pb.PbFieldType.OF)
    ..aInt64(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'killedWeeds')
    ..aInt64(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'missedWeeds')
    ..aInt64(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'skippedWeeds')
    ..a<$core.double>(14, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timeEfficiency', $pb.PbFieldType.OF)
    ..aInt64(15, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'totalWeeds')
    ..a<$core.double>(16, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'uptimeSeconds', $pb.PbFieldType.OF)
    ..a<$core.double>(17, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedDensitySqFt', $pb.PbFieldType.OF)
    ..a<$core.double>(18, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingUptimeSeconds', $pb.PbFieldType.OF)
    ..a<$core.double>(19, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedingEfficiency', $pb.PbFieldType.OF)
    ..aInt64(20, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedsTypeCountBroadleaf')
    ..aInt64(21, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedsTypeCountGrass')
    ..aInt64(22, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedsTypeCountOffshoot')
    ..aInt64(23, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'weedsTypeCountPurslane')
    ..aInt64(24, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'notWeedingWeeds')
    ..aOS(25, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'date')
    ..aInt64(26, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'keptCrops')
    ..aInt64(27, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'missedCrops')
    ..aInt64(28, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'notThinning')
    ..aInt64(29, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'notWeeding')
    ..aInt64(30, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'skippedCrops')
    ..aInt64(31, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinnedCrops')
    ..aInt64(32, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'totalCrops')
    ..a<$core.double>(33, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'bandingPercentage', $pb.PbFieldType.OF)
    ..a<$core.double>(34, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'thinningEfficiency', $pb.PbFieldType.OF)
    ..aOS(35, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropId')
    ..aOS(36, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'crop')
    ..a<$core.double>(37, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'cropDensitySqFt', $pb.PbFieldType.OF)
    ..a<$core.double>(38, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'avgCropSizeMm', $pb.PbFieldType.OF)
    ..aInt64(39, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'avgTargetableReqLaserTime')
    ..aInt64(40, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'avgUntargetableReqLaserTime')
    ..aInt64(41, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'validCrops')
    ..a<$core.double>(42, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'operatorEffectiveness', $pb.PbFieldType.OF)
    ..aInt64(43, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'targetWeedingTimeSeconds')
    ..aOS(44, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobId')
    ..aOS(45, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'jobName')
    ..hasRequiredFields = false
  ;

  DailyMetricResponse._() : super();
  factory DailyMetricResponse({
    $74.DB? db,
    $core.String? serial,
    $fixnum.Int64? robotId,
    $core.double? acresWeeded,
    $core.double? avgSpeedMph,
    $core.double? avgWeedSizeMm,
    $core.String? bandingConfigName,
    $core.bool? bandingEnabled,
    $core.double? coverageSpeedAcresHr,
    $core.double? distanceWeededMeters,
    $fixnum.Int64? killedWeeds,
    $fixnum.Int64? missedWeeds,
    $fixnum.Int64? skippedWeeds,
    $core.double? timeEfficiency,
    $fixnum.Int64? totalWeeds,
    $core.double? uptimeSeconds,
    $core.double? weedDensitySqFt,
    $core.double? weedingUptimeSeconds,
    $core.double? weedingEfficiency,
    $fixnum.Int64? weedsTypeCountBroadleaf,
    $fixnum.Int64? weedsTypeCountGrass,
    $fixnum.Int64? weedsTypeCountOffshoot,
    $fixnum.Int64? weedsTypeCountPurslane,
    $fixnum.Int64? notWeedingWeeds,
    $core.String? date,
    $fixnum.Int64? keptCrops,
    $fixnum.Int64? missedCrops,
    $fixnum.Int64? notThinning,
    $fixnum.Int64? notWeeding,
    $fixnum.Int64? skippedCrops,
    $fixnum.Int64? thinnedCrops,
    $fixnum.Int64? totalCrops,
    $core.double? bandingPercentage,
    $core.double? thinningEfficiency,
    $core.String? cropId,
    $core.String? crop,
    $core.double? cropDensitySqFt,
    $core.double? avgCropSizeMm,
    $fixnum.Int64? avgTargetableReqLaserTime,
    $fixnum.Int64? avgUntargetableReqLaserTime,
    $fixnum.Int64? validCrops,
    $core.double? operatorEffectiveness,
    $fixnum.Int64? targetWeedingTimeSeconds,
    $core.String? jobId,
    $core.String? jobName,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (serial != null) {
      _result.serial = serial;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    if (acresWeeded != null) {
      _result.acresWeeded = acresWeeded;
    }
    if (avgSpeedMph != null) {
      _result.avgSpeedMph = avgSpeedMph;
    }
    if (avgWeedSizeMm != null) {
      _result.avgWeedSizeMm = avgWeedSizeMm;
    }
    if (bandingConfigName != null) {
      _result.bandingConfigName = bandingConfigName;
    }
    if (bandingEnabled != null) {
      _result.bandingEnabled = bandingEnabled;
    }
    if (coverageSpeedAcresHr != null) {
      _result.coverageSpeedAcresHr = coverageSpeedAcresHr;
    }
    if (distanceWeededMeters != null) {
      _result.distanceWeededMeters = distanceWeededMeters;
    }
    if (killedWeeds != null) {
      _result.killedWeeds = killedWeeds;
    }
    if (missedWeeds != null) {
      _result.missedWeeds = missedWeeds;
    }
    if (skippedWeeds != null) {
      _result.skippedWeeds = skippedWeeds;
    }
    if (timeEfficiency != null) {
      _result.timeEfficiency = timeEfficiency;
    }
    if (totalWeeds != null) {
      _result.totalWeeds = totalWeeds;
    }
    if (uptimeSeconds != null) {
      _result.uptimeSeconds = uptimeSeconds;
    }
    if (weedDensitySqFt != null) {
      _result.weedDensitySqFt = weedDensitySqFt;
    }
    if (weedingUptimeSeconds != null) {
      _result.weedingUptimeSeconds = weedingUptimeSeconds;
    }
    if (weedingEfficiency != null) {
      _result.weedingEfficiency = weedingEfficiency;
    }
    if (weedsTypeCountBroadleaf != null) {
      _result.weedsTypeCountBroadleaf = weedsTypeCountBroadleaf;
    }
    if (weedsTypeCountGrass != null) {
      _result.weedsTypeCountGrass = weedsTypeCountGrass;
    }
    if (weedsTypeCountOffshoot != null) {
      _result.weedsTypeCountOffshoot = weedsTypeCountOffshoot;
    }
    if (weedsTypeCountPurslane != null) {
      _result.weedsTypeCountPurslane = weedsTypeCountPurslane;
    }
    if (notWeedingWeeds != null) {
      _result.notWeedingWeeds = notWeedingWeeds;
    }
    if (date != null) {
      _result.date = date;
    }
    if (keptCrops != null) {
      _result.keptCrops = keptCrops;
    }
    if (missedCrops != null) {
      _result.missedCrops = missedCrops;
    }
    if (notThinning != null) {
      _result.notThinning = notThinning;
    }
    if (notWeeding != null) {
      _result.notWeeding = notWeeding;
    }
    if (skippedCrops != null) {
      _result.skippedCrops = skippedCrops;
    }
    if (thinnedCrops != null) {
      _result.thinnedCrops = thinnedCrops;
    }
    if (totalCrops != null) {
      _result.totalCrops = totalCrops;
    }
    if (bandingPercentage != null) {
      _result.bandingPercentage = bandingPercentage;
    }
    if (thinningEfficiency != null) {
      _result.thinningEfficiency = thinningEfficiency;
    }
    if (cropId != null) {
      _result.cropId = cropId;
    }
    if (crop != null) {
      _result.crop = crop;
    }
    if (cropDensitySqFt != null) {
      _result.cropDensitySqFt = cropDensitySqFt;
    }
    if (avgCropSizeMm != null) {
      _result.avgCropSizeMm = avgCropSizeMm;
    }
    if (avgTargetableReqLaserTime != null) {
      _result.avgTargetableReqLaserTime = avgTargetableReqLaserTime;
    }
    if (avgUntargetableReqLaserTime != null) {
      _result.avgUntargetableReqLaserTime = avgUntargetableReqLaserTime;
    }
    if (validCrops != null) {
      _result.validCrops = validCrops;
    }
    if (operatorEffectiveness != null) {
      _result.operatorEffectiveness = operatorEffectiveness;
    }
    if (targetWeedingTimeSeconds != null) {
      _result.targetWeedingTimeSeconds = targetWeedingTimeSeconds;
    }
    if (jobId != null) {
      _result.jobId = jobId;
    }
    if (jobName != null) {
      _result.jobName = jobName;
    }
    return _result;
  }
  factory DailyMetricResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DailyMetricResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DailyMetricResponse clone() => DailyMetricResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DailyMetricResponse copyWith(void Function(DailyMetricResponse) updates) => super.copyWith((message) => updates(message as DailyMetricResponse)) as DailyMetricResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DailyMetricResponse create() => DailyMetricResponse._();
  DailyMetricResponse createEmptyInstance() => create();
  static $pb.PbList<DailyMetricResponse> createRepeated() => $pb.PbList<DailyMetricResponse>();
  @$core.pragma('dart2js:noInline')
  static DailyMetricResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DailyMetricResponse>(create);
  static DailyMetricResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $74.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($74.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $74.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get serial => $_getSZ(1);
  @$pb.TagNumber(2)
  set serial($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSerial() => $_has(1);
  @$pb.TagNumber(2)
  void clearSerial() => clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get robotId => $_getI64(2);
  @$pb.TagNumber(3)
  set robotId($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRobotId() => $_has(2);
  @$pb.TagNumber(3)
  void clearRobotId() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get acresWeeded => $_getN(3);
  @$pb.TagNumber(4)
  set acresWeeded($core.double v) { $_setFloat(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasAcresWeeded() => $_has(3);
  @$pb.TagNumber(4)
  void clearAcresWeeded() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get avgSpeedMph => $_getN(4);
  @$pb.TagNumber(5)
  set avgSpeedMph($core.double v) { $_setFloat(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasAvgSpeedMph() => $_has(4);
  @$pb.TagNumber(5)
  void clearAvgSpeedMph() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get avgWeedSizeMm => $_getN(5);
  @$pb.TagNumber(6)
  set avgWeedSizeMm($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAvgWeedSizeMm() => $_has(5);
  @$pb.TagNumber(6)
  void clearAvgWeedSizeMm() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get bandingConfigName => $_getSZ(6);
  @$pb.TagNumber(7)
  set bandingConfigName($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasBandingConfigName() => $_has(6);
  @$pb.TagNumber(7)
  void clearBandingConfigName() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get bandingEnabled => $_getBF(7);
  @$pb.TagNumber(8)
  set bandingEnabled($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasBandingEnabled() => $_has(7);
  @$pb.TagNumber(8)
  void clearBandingEnabled() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get coverageSpeedAcresHr => $_getN(8);
  @$pb.TagNumber(9)
  set coverageSpeedAcresHr($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasCoverageSpeedAcresHr() => $_has(8);
  @$pb.TagNumber(9)
  void clearCoverageSpeedAcresHr() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get distanceWeededMeters => $_getN(9);
  @$pb.TagNumber(10)
  set distanceWeededMeters($core.double v) { $_setFloat(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasDistanceWeededMeters() => $_has(9);
  @$pb.TagNumber(10)
  void clearDistanceWeededMeters() => clearField(10);

  @$pb.TagNumber(11)
  $fixnum.Int64 get killedWeeds => $_getI64(10);
  @$pb.TagNumber(11)
  set killedWeeds($fixnum.Int64 v) { $_setInt64(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasKilledWeeds() => $_has(10);
  @$pb.TagNumber(11)
  void clearKilledWeeds() => clearField(11);

  @$pb.TagNumber(12)
  $fixnum.Int64 get missedWeeds => $_getI64(11);
  @$pb.TagNumber(12)
  set missedWeeds($fixnum.Int64 v) { $_setInt64(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasMissedWeeds() => $_has(11);
  @$pb.TagNumber(12)
  void clearMissedWeeds() => clearField(12);

  @$pb.TagNumber(13)
  $fixnum.Int64 get skippedWeeds => $_getI64(12);
  @$pb.TagNumber(13)
  set skippedWeeds($fixnum.Int64 v) { $_setInt64(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasSkippedWeeds() => $_has(12);
  @$pb.TagNumber(13)
  void clearSkippedWeeds() => clearField(13);

  @$pb.TagNumber(14)
  $core.double get timeEfficiency => $_getN(13);
  @$pb.TagNumber(14)
  set timeEfficiency($core.double v) { $_setFloat(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasTimeEfficiency() => $_has(13);
  @$pb.TagNumber(14)
  void clearTimeEfficiency() => clearField(14);

  @$pb.TagNumber(15)
  $fixnum.Int64 get totalWeeds => $_getI64(14);
  @$pb.TagNumber(15)
  set totalWeeds($fixnum.Int64 v) { $_setInt64(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasTotalWeeds() => $_has(14);
  @$pb.TagNumber(15)
  void clearTotalWeeds() => clearField(15);

  @$pb.TagNumber(16)
  $core.double get uptimeSeconds => $_getN(15);
  @$pb.TagNumber(16)
  set uptimeSeconds($core.double v) { $_setFloat(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasUptimeSeconds() => $_has(15);
  @$pb.TagNumber(16)
  void clearUptimeSeconds() => clearField(16);

  @$pb.TagNumber(17)
  $core.double get weedDensitySqFt => $_getN(16);
  @$pb.TagNumber(17)
  set weedDensitySqFt($core.double v) { $_setFloat(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasWeedDensitySqFt() => $_has(16);
  @$pb.TagNumber(17)
  void clearWeedDensitySqFt() => clearField(17);

  @$pb.TagNumber(18)
  $core.double get weedingUptimeSeconds => $_getN(17);
  @$pb.TagNumber(18)
  set weedingUptimeSeconds($core.double v) { $_setFloat(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasWeedingUptimeSeconds() => $_has(17);
  @$pb.TagNumber(18)
  void clearWeedingUptimeSeconds() => clearField(18);

  @$pb.TagNumber(19)
  $core.double get weedingEfficiency => $_getN(18);
  @$pb.TagNumber(19)
  set weedingEfficiency($core.double v) { $_setFloat(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasWeedingEfficiency() => $_has(18);
  @$pb.TagNumber(19)
  void clearWeedingEfficiency() => clearField(19);

  @$pb.TagNumber(20)
  $fixnum.Int64 get weedsTypeCountBroadleaf => $_getI64(19);
  @$pb.TagNumber(20)
  set weedsTypeCountBroadleaf($fixnum.Int64 v) { $_setInt64(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasWeedsTypeCountBroadleaf() => $_has(19);
  @$pb.TagNumber(20)
  void clearWeedsTypeCountBroadleaf() => clearField(20);

  @$pb.TagNumber(21)
  $fixnum.Int64 get weedsTypeCountGrass => $_getI64(20);
  @$pb.TagNumber(21)
  set weedsTypeCountGrass($fixnum.Int64 v) { $_setInt64(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasWeedsTypeCountGrass() => $_has(20);
  @$pb.TagNumber(21)
  void clearWeedsTypeCountGrass() => clearField(21);

  @$pb.TagNumber(22)
  $fixnum.Int64 get weedsTypeCountOffshoot => $_getI64(21);
  @$pb.TagNumber(22)
  set weedsTypeCountOffshoot($fixnum.Int64 v) { $_setInt64(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasWeedsTypeCountOffshoot() => $_has(21);
  @$pb.TagNumber(22)
  void clearWeedsTypeCountOffshoot() => clearField(22);

  @$pb.TagNumber(23)
  $fixnum.Int64 get weedsTypeCountPurslane => $_getI64(22);
  @$pb.TagNumber(23)
  set weedsTypeCountPurslane($fixnum.Int64 v) { $_setInt64(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasWeedsTypeCountPurslane() => $_has(22);
  @$pb.TagNumber(23)
  void clearWeedsTypeCountPurslane() => clearField(23);

  @$pb.TagNumber(24)
  $fixnum.Int64 get notWeedingWeeds => $_getI64(23);
  @$pb.TagNumber(24)
  set notWeedingWeeds($fixnum.Int64 v) { $_setInt64(23, v); }
  @$pb.TagNumber(24)
  $core.bool hasNotWeedingWeeds() => $_has(23);
  @$pb.TagNumber(24)
  void clearNotWeedingWeeds() => clearField(24);

  @$pb.TagNumber(25)
  $core.String get date => $_getSZ(24);
  @$pb.TagNumber(25)
  set date($core.String v) { $_setString(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasDate() => $_has(24);
  @$pb.TagNumber(25)
  void clearDate() => clearField(25);

  @$pb.TagNumber(26)
  $fixnum.Int64 get keptCrops => $_getI64(25);
  @$pb.TagNumber(26)
  set keptCrops($fixnum.Int64 v) { $_setInt64(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasKeptCrops() => $_has(25);
  @$pb.TagNumber(26)
  void clearKeptCrops() => clearField(26);

  @$pb.TagNumber(27)
  $fixnum.Int64 get missedCrops => $_getI64(26);
  @$pb.TagNumber(27)
  set missedCrops($fixnum.Int64 v) { $_setInt64(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasMissedCrops() => $_has(26);
  @$pb.TagNumber(27)
  void clearMissedCrops() => clearField(27);

  @$pb.TagNumber(28)
  $fixnum.Int64 get notThinning => $_getI64(27);
  @$pb.TagNumber(28)
  set notThinning($fixnum.Int64 v) { $_setInt64(27, v); }
  @$pb.TagNumber(28)
  $core.bool hasNotThinning() => $_has(27);
  @$pb.TagNumber(28)
  void clearNotThinning() => clearField(28);

  @$pb.TagNumber(29)
  $fixnum.Int64 get notWeeding => $_getI64(28);
  @$pb.TagNumber(29)
  set notWeeding($fixnum.Int64 v) { $_setInt64(28, v); }
  @$pb.TagNumber(29)
  $core.bool hasNotWeeding() => $_has(28);
  @$pb.TagNumber(29)
  void clearNotWeeding() => clearField(29);

  @$pb.TagNumber(30)
  $fixnum.Int64 get skippedCrops => $_getI64(29);
  @$pb.TagNumber(30)
  set skippedCrops($fixnum.Int64 v) { $_setInt64(29, v); }
  @$pb.TagNumber(30)
  $core.bool hasSkippedCrops() => $_has(29);
  @$pb.TagNumber(30)
  void clearSkippedCrops() => clearField(30);

  @$pb.TagNumber(31)
  $fixnum.Int64 get thinnedCrops => $_getI64(30);
  @$pb.TagNumber(31)
  set thinnedCrops($fixnum.Int64 v) { $_setInt64(30, v); }
  @$pb.TagNumber(31)
  $core.bool hasThinnedCrops() => $_has(30);
  @$pb.TagNumber(31)
  void clearThinnedCrops() => clearField(31);

  @$pb.TagNumber(32)
  $fixnum.Int64 get totalCrops => $_getI64(31);
  @$pb.TagNumber(32)
  set totalCrops($fixnum.Int64 v) { $_setInt64(31, v); }
  @$pb.TagNumber(32)
  $core.bool hasTotalCrops() => $_has(31);
  @$pb.TagNumber(32)
  void clearTotalCrops() => clearField(32);

  @$pb.TagNumber(33)
  $core.double get bandingPercentage => $_getN(32);
  @$pb.TagNumber(33)
  set bandingPercentage($core.double v) { $_setFloat(32, v); }
  @$pb.TagNumber(33)
  $core.bool hasBandingPercentage() => $_has(32);
  @$pb.TagNumber(33)
  void clearBandingPercentage() => clearField(33);

  @$pb.TagNumber(34)
  $core.double get thinningEfficiency => $_getN(33);
  @$pb.TagNumber(34)
  set thinningEfficiency($core.double v) { $_setFloat(33, v); }
  @$pb.TagNumber(34)
  $core.bool hasThinningEfficiency() => $_has(33);
  @$pb.TagNumber(34)
  void clearThinningEfficiency() => clearField(34);

  @$pb.TagNumber(35)
  $core.String get cropId => $_getSZ(34);
  @$pb.TagNumber(35)
  set cropId($core.String v) { $_setString(34, v); }
  @$pb.TagNumber(35)
  $core.bool hasCropId() => $_has(34);
  @$pb.TagNumber(35)
  void clearCropId() => clearField(35);

  @$pb.TagNumber(36)
  $core.String get crop => $_getSZ(35);
  @$pb.TagNumber(36)
  set crop($core.String v) { $_setString(35, v); }
  @$pb.TagNumber(36)
  $core.bool hasCrop() => $_has(35);
  @$pb.TagNumber(36)
  void clearCrop() => clearField(36);

  @$pb.TagNumber(37)
  $core.double get cropDensitySqFt => $_getN(36);
  @$pb.TagNumber(37)
  set cropDensitySqFt($core.double v) { $_setFloat(36, v); }
  @$pb.TagNumber(37)
  $core.bool hasCropDensitySqFt() => $_has(36);
  @$pb.TagNumber(37)
  void clearCropDensitySqFt() => clearField(37);

  @$pb.TagNumber(38)
  $core.double get avgCropSizeMm => $_getN(37);
  @$pb.TagNumber(38)
  set avgCropSizeMm($core.double v) { $_setFloat(37, v); }
  @$pb.TagNumber(38)
  $core.bool hasAvgCropSizeMm() => $_has(37);
  @$pb.TagNumber(38)
  void clearAvgCropSizeMm() => clearField(38);

  @$pb.TagNumber(39)
  $fixnum.Int64 get avgTargetableReqLaserTime => $_getI64(38);
  @$pb.TagNumber(39)
  set avgTargetableReqLaserTime($fixnum.Int64 v) { $_setInt64(38, v); }
  @$pb.TagNumber(39)
  $core.bool hasAvgTargetableReqLaserTime() => $_has(38);
  @$pb.TagNumber(39)
  void clearAvgTargetableReqLaserTime() => clearField(39);

  @$pb.TagNumber(40)
  $fixnum.Int64 get avgUntargetableReqLaserTime => $_getI64(39);
  @$pb.TagNumber(40)
  set avgUntargetableReqLaserTime($fixnum.Int64 v) { $_setInt64(39, v); }
  @$pb.TagNumber(40)
  $core.bool hasAvgUntargetableReqLaserTime() => $_has(39);
  @$pb.TagNumber(40)
  void clearAvgUntargetableReqLaserTime() => clearField(40);

  @$pb.TagNumber(41)
  $fixnum.Int64 get validCrops => $_getI64(40);
  @$pb.TagNumber(41)
  set validCrops($fixnum.Int64 v) { $_setInt64(40, v); }
  @$pb.TagNumber(41)
  $core.bool hasValidCrops() => $_has(40);
  @$pb.TagNumber(41)
  void clearValidCrops() => clearField(41);

  @$pb.TagNumber(42)
  $core.double get operatorEffectiveness => $_getN(41);
  @$pb.TagNumber(42)
  set operatorEffectiveness($core.double v) { $_setFloat(41, v); }
  @$pb.TagNumber(42)
  $core.bool hasOperatorEffectiveness() => $_has(41);
  @$pb.TagNumber(42)
  void clearOperatorEffectiveness() => clearField(42);

  @$pb.TagNumber(43)
  $fixnum.Int64 get targetWeedingTimeSeconds => $_getI64(42);
  @$pb.TagNumber(43)
  set targetWeedingTimeSeconds($fixnum.Int64 v) { $_setInt64(42, v); }
  @$pb.TagNumber(43)
  $core.bool hasTargetWeedingTimeSeconds() => $_has(42);
  @$pb.TagNumber(43)
  void clearTargetWeedingTimeSeconds() => clearField(43);

  @$pb.TagNumber(44)
  $core.String get jobId => $_getSZ(43);
  @$pb.TagNumber(44)
  set jobId($core.String v) { $_setString(43, v); }
  @$pb.TagNumber(44)
  $core.bool hasJobId() => $_has(43);
  @$pb.TagNumber(44)
  void clearJobId() => clearField(44);

  @$pb.TagNumber(45)
  $core.String get jobName => $_getSZ(44);
  @$pb.TagNumber(45)
  set jobName($core.String v) { $_setString(44, v); }
  @$pb.TagNumber(45)
  $core.bool hasJobName() => $_has(44);
  @$pb.TagNumber(45)
  void clearJobName() => clearField(45);
}

class DailyMetricsByDateResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DailyMetricsByDateResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.metrics'), createEmptyInstance: create)
    ..m<$core.String, DailyMetricResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metrics', entryClassName: 'DailyMetricsByDateResponse.MetricsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: DailyMetricResponse.create, packageName: const $pb.PackageName('carbon.portal.metrics'))
    ..hasRequiredFields = false
  ;

  DailyMetricsByDateResponse._() : super();
  factory DailyMetricsByDateResponse({
    $core.Map<$core.String, DailyMetricResponse>? metrics,
  }) {
    final _result = create();
    if (metrics != null) {
      _result.metrics.addAll(metrics);
    }
    return _result;
  }
  factory DailyMetricsByDateResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DailyMetricsByDateResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DailyMetricsByDateResponse clone() => DailyMetricsByDateResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DailyMetricsByDateResponse copyWith(void Function(DailyMetricsByDateResponse) updates) => super.copyWith((message) => updates(message as DailyMetricsByDateResponse)) as DailyMetricsByDateResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DailyMetricsByDateResponse create() => DailyMetricsByDateResponse._();
  DailyMetricsByDateResponse createEmptyInstance() => create();
  static $pb.PbList<DailyMetricsByDateResponse> createRepeated() => $pb.PbList<DailyMetricsByDateResponse>();
  @$core.pragma('dart2js:noInline')
  static DailyMetricsByDateResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DailyMetricsByDateResponse>(create);
  static DailyMetricsByDateResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, DailyMetricResponse> get metrics => $_getMap(0);
}

class DailyMetricsByDateByRobotResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'DailyMetricsByDateByRobotResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.metrics'), createEmptyInstance: create)
    ..m<$core.String, DailyMetricsByDateResponse>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'metrics', entryClassName: 'DailyMetricsByDateByRobotResponse.MetricsEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OM, valueCreator: DailyMetricsByDateResponse.create, packageName: const $pb.PackageName('carbon.portal.metrics'))
    ..hasRequiredFields = false
  ;

  DailyMetricsByDateByRobotResponse._() : super();
  factory DailyMetricsByDateByRobotResponse({
    $core.Map<$core.String, DailyMetricsByDateResponse>? metrics,
  }) {
    final _result = create();
    if (metrics != null) {
      _result.metrics.addAll(metrics);
    }
    return _result;
  }
  factory DailyMetricsByDateByRobotResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DailyMetricsByDateByRobotResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DailyMetricsByDateByRobotResponse clone() => DailyMetricsByDateByRobotResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DailyMetricsByDateByRobotResponse copyWith(void Function(DailyMetricsByDateByRobotResponse) updates) => super.copyWith((message) => updates(message as DailyMetricsByDateByRobotResponse)) as DailyMetricsByDateByRobotResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static DailyMetricsByDateByRobotResponse create() => DailyMetricsByDateByRobotResponse._();
  DailyMetricsByDateByRobotResponse createEmptyInstance() => create();
  static $pb.PbList<DailyMetricsByDateByRobotResponse> createRepeated() => $pb.PbList<DailyMetricsByDateByRobotResponse>();
  @$core.pragma('dart2js:noInline')
  static DailyMetricsByDateByRobotResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DailyMetricsByDateByRobotResponse>(create);
  static DailyMetricsByDateByRobotResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.Map<$core.String, DailyMetricsByDateResponse> get metrics => $_getMap(0);
}

