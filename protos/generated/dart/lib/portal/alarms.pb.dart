///
//  Generated code. Do not modify.
//  source: portal/alarms.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $74;
import '../frontend/alarm.pb.dart' as $8;

class AlarmResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AlarmResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.portal.alarms'), createEmptyInstance: create)
    ..aOM<$74.DB>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'db', subBuilder: $74.DB.create)
    ..aOM<$8.AlarmRow>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'alarm', subBuilder: $8.AlarmRow.create)
    ..aInt64(11, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'startedAt')
    ..aInt64(12, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'endedAt')
    ..aInt64(13, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robotId')
    ..hasRequiredFields = false
  ;

  AlarmResponse._() : super();
  factory AlarmResponse({
    $74.DB? db,
    $8.AlarmRow? alarm,
    $fixnum.Int64? startedAt,
    $fixnum.Int64? endedAt,
    $fixnum.Int64? robotId,
  }) {
    final _result = create();
    if (db != null) {
      _result.db = db;
    }
    if (alarm != null) {
      _result.alarm = alarm;
    }
    if (startedAt != null) {
      _result.startedAt = startedAt;
    }
    if (endedAt != null) {
      _result.endedAt = endedAt;
    }
    if (robotId != null) {
      _result.robotId = robotId;
    }
    return _result;
  }
  factory AlarmResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AlarmResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AlarmResponse clone() => AlarmResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AlarmResponse copyWith(void Function(AlarmResponse) updates) => super.copyWith((message) => updates(message as AlarmResponse)) as AlarmResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AlarmResponse create() => AlarmResponse._();
  AlarmResponse createEmptyInstance() => create();
  static $pb.PbList<AlarmResponse> createRepeated() => $pb.PbList<AlarmResponse>();
  @$core.pragma('dart2js:noInline')
  static AlarmResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AlarmResponse>(create);
  static AlarmResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $74.DB get db => $_getN(0);
  @$pb.TagNumber(1)
  set db($74.DB v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasDb() => $_has(0);
  @$pb.TagNumber(1)
  void clearDb() => clearField(1);
  @$pb.TagNumber(1)
  $74.DB ensureDb() => $_ensure(0);

  @$pb.TagNumber(2)
  $8.AlarmRow get alarm => $_getN(1);
  @$pb.TagNumber(2)
  set alarm($8.AlarmRow v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAlarm() => $_has(1);
  @$pb.TagNumber(2)
  void clearAlarm() => clearField(2);
  @$pb.TagNumber(2)
  $8.AlarmRow ensureAlarm() => $_ensure(1);

  @$pb.TagNumber(11)
  $fixnum.Int64 get startedAt => $_getI64(2);
  @$pb.TagNumber(11)
  set startedAt($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(11)
  $core.bool hasStartedAt() => $_has(2);
  @$pb.TagNumber(11)
  void clearStartedAt() => clearField(11);

  @$pb.TagNumber(12)
  $fixnum.Int64 get endedAt => $_getI64(3);
  @$pb.TagNumber(12)
  set endedAt($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(12)
  $core.bool hasEndedAt() => $_has(3);
  @$pb.TagNumber(12)
  void clearEndedAt() => clearField(12);

  @$pb.TagNumber(13)
  $fixnum.Int64 get robotId => $_getI64(4);
  @$pb.TagNumber(13)
  set robotId($fixnum.Int64 v) { $_setInt64(4, v); }
  @$pb.TagNumber(13)
  $core.bool hasRobotId() => $_has(4);
  @$pb.TagNumber(13)
  void clearRobotId() => clearField(13);
}

