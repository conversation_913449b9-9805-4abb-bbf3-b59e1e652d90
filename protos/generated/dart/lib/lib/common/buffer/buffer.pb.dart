///
//  Generated code. Do not modify.
//  source: lib/common/buffer/buffer.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'inline/inline.pb.dart' as $71;
import 'shm/shm.pb.dart' as $72;

enum BufferProto_Type {
  inline, 
  memfile, 
  shmem, 
  notSet
}

class BufferProto extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, BufferProto_Type> _BufferProto_TypeByTag = {
    1 : BufferProto_Type.inline,
    2 : BufferProto_Type.memfile,
    3 : BufferProto_Type.shmem,
    0 : BufferProto_Type.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BufferProto', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'lib.common.buffer'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3])
    ..aOM<$71.InlineBufferProto>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'inline', subBuilder: $71.InlineBufferProto.create)
    ..aOM<$72.MemFileProto>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'memfile', subBuilder: $72.MemFileProto.create)
    ..aOM<$72.ShmemBufferProto>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'shmem', subBuilder: $72.ShmemBufferProto.create)
    ..hasRequiredFields = false
  ;

  BufferProto._() : super();
  factory BufferProto({
    $71.InlineBufferProto? inline,
    $72.MemFileProto? memfile,
    $72.ShmemBufferProto? shmem,
  }) {
    final _result = create();
    if (inline != null) {
      _result.inline = inline;
    }
    if (memfile != null) {
      _result.memfile = memfile;
    }
    if (shmem != null) {
      _result.shmem = shmem;
    }
    return _result;
  }
  factory BufferProto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BufferProto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BufferProto clone() => BufferProto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BufferProto copyWith(void Function(BufferProto) updates) => super.copyWith((message) => updates(message as BufferProto)) as BufferProto; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BufferProto create() => BufferProto._();
  BufferProto createEmptyInstance() => create();
  static $pb.PbList<BufferProto> createRepeated() => $pb.PbList<BufferProto>();
  @$core.pragma('dart2js:noInline')
  static BufferProto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BufferProto>(create);
  static BufferProto? _defaultInstance;

  BufferProto_Type whichType() => _BufferProto_TypeByTag[$_whichOneof(0)]!;
  void clearType() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $71.InlineBufferProto get inline => $_getN(0);
  @$pb.TagNumber(1)
  set inline($71.InlineBufferProto v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasInline() => $_has(0);
  @$pb.TagNumber(1)
  void clearInline() => clearField(1);
  @$pb.TagNumber(1)
  $71.InlineBufferProto ensureInline() => $_ensure(0);

  @$pb.TagNumber(2)
  $72.MemFileProto get memfile => $_getN(1);
  @$pb.TagNumber(2)
  set memfile($72.MemFileProto v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasMemfile() => $_has(1);
  @$pb.TagNumber(2)
  void clearMemfile() => clearField(2);
  @$pb.TagNumber(2)
  $72.MemFileProto ensureMemfile() => $_ensure(1);

  @$pb.TagNumber(3)
  $72.ShmemBufferProto get shmem => $_getN(2);
  @$pb.TagNumber(3)
  set shmem($72.ShmemBufferProto v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasShmem() => $_has(2);
  @$pb.TagNumber(3)
  void clearShmem() => clearField(3);
  @$pb.TagNumber(3)
  $72.ShmemBufferProto ensureShmem() => $_ensure(2);
}

