///
//  Generated code. Do not modify.
//  source: robot_syncer/profile_sync.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:async' as $async;

import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'profile_sync.pb.dart' as $51;
import '../util/util.pb.dart' as $1;
export 'profile_sync.pb.dart';

class RoSyProfileSyncServiceClient extends $grpc.Client {
  static final _$getProfileSyncData = $grpc.ClientMethod<
          $51.GetProfileSyncDataRequest, $51.GetProfileSyncDataResponse>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData',
      ($51.GetProfileSyncDataRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) =>
          $51.GetProfileSyncDataResponse.fromBuffer(value));
  static final _$uploadProfile = $grpc.ClientMethod<$51.UploadProfileRequest,
          $1.Empty>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile',
      ($51.UploadProfileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$getProfile =
      $grpc.ClientMethod<$51.GetProfileRequest, $51.GetProfileResponse>(
          '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile',
          ($51.GetProfileRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) =>
              $51.GetProfileResponse.fromBuffer(value));
  static final _$deleteProfile = $grpc.ClientMethod<$51.DeleteProfileRequest,
          $1.Empty>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile',
      ($51.DeleteProfileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));
  static final _$purgeProfile = $grpc.ClientMethod<$51.PurgeProfileRequest,
          $1.Empty>(
      '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile',
      ($51.PurgeProfileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.Empty.fromBuffer(value));

  RoSyProfileSyncServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$51.GetProfileSyncDataResponse> getProfileSyncData(
      $51.GetProfileSyncDataRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfileSyncData, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> uploadProfile($51.UploadProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadProfile, request, options: options);
  }

  $grpc.ResponseFuture<$51.GetProfileResponse> getProfile(
      $51.GetProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> deleteProfile($51.DeleteProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteProfile, request, options: options);
  }

  $grpc.ResponseFuture<$1.Empty> purgeProfile($51.PurgeProfileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$purgeProfile, request, options: options);
  }
}

abstract class RoSyProfileSyncServiceBase extends $grpc.Service {
  $core.String get $name =>
      'carbon.robot_syncer.profile_sync.RoSyProfileSyncService';

  RoSyProfileSyncServiceBase() {
    $addMethod($grpc.ServiceMethod<$51.GetProfileSyncDataRequest,
            $51.GetProfileSyncDataResponse>(
        'GetProfileSyncData',
        getProfileSyncData_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $51.GetProfileSyncDataRequest.fromBuffer(value),
        ($51.GetProfileSyncDataResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$51.UploadProfileRequest, $1.Empty>(
        'UploadProfile',
        uploadProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $51.UploadProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod(
        $grpc.ServiceMethod<$51.GetProfileRequest, $51.GetProfileResponse>(
            'GetProfile',
            getProfile_Pre,
            false,
            false,
            ($core.List<$core.int> value) =>
                $51.GetProfileRequest.fromBuffer(value),
            ($51.GetProfileResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$51.DeleteProfileRequest, $1.Empty>(
        'DeleteProfile',
        deleteProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $51.DeleteProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$51.PurgeProfileRequest, $1.Empty>(
        'PurgeProfile',
        purgeProfile_Pre,
        false,
        false,
        ($core.List<$core.int> value) =>
            $51.PurgeProfileRequest.fromBuffer(value),
        ($1.Empty value) => value.writeToBuffer()));
  }

  $async.Future<$51.GetProfileSyncDataResponse> getProfileSyncData_Pre(
      $grpc.ServiceCall call,
      $async.Future<$51.GetProfileSyncDataRequest> request) async {
    return getProfileSyncData(call, await request);
  }

  $async.Future<$1.Empty> uploadProfile_Pre($grpc.ServiceCall call,
      $async.Future<$51.UploadProfileRequest> request) async {
    return uploadProfile(call, await request);
  }

  $async.Future<$51.GetProfileResponse> getProfile_Pre($grpc.ServiceCall call,
      $async.Future<$51.GetProfileRequest> request) async {
    return getProfile(call, await request);
  }

  $async.Future<$1.Empty> deleteProfile_Pre($grpc.ServiceCall call,
      $async.Future<$51.DeleteProfileRequest> request) async {
    return deleteProfile(call, await request);
  }

  $async.Future<$1.Empty> purgeProfile_Pre($grpc.ServiceCall call,
      $async.Future<$51.PurgeProfileRequest> request) async {
    return purgeProfile(call, await request);
  }

  $async.Future<$51.GetProfileSyncDataResponse> getProfileSyncData(
      $grpc.ServiceCall call, $51.GetProfileSyncDataRequest request);
  $async.Future<$1.Empty> uploadProfile(
      $grpc.ServiceCall call, $51.UploadProfileRequest request);
  $async.Future<$51.GetProfileResponse> getProfile(
      $grpc.ServiceCall call, $51.GetProfileRequest request);
  $async.Future<$1.Empty> deleteProfile(
      $grpc.ServiceCall call, $51.DeleteProfileRequest request);
  $async.Future<$1.Empty> purgeProfile(
      $grpc.ServiceCall call, $51.PurgeProfileRequest request);
}
