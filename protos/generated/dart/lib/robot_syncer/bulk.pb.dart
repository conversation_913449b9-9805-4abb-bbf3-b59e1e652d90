///
//  Generated code. Do not modify.
//  source: robot_syncer/bulk.proto
//
// @dart = 2.12
// ignore_for_file: annotate_overrides,camel_case_types,constant_identifier_names,directives_ordering,library_prefixes,non_constant_identifier_names,prefer_final_fields,return_of_invalid_type,unnecessary_const,unnecessary_import,unnecessary_this,unused_import,unused_shown_name

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import '../google/protobuf/timestamp.pb.dart' as $70;
import '../config/api/config_service.pb.dart' as $2;

import 'bulk.pbenum.dart';

export 'bulk.pbenum.dart';

class BulkEditRequest extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BulkEditRequest', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'userId')
    ..aOM<$70.Timestamp>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'timestamp', subBuilder: $70.Timestamp.create)
    ..pPS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serials')
    ..pc<Operation>(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'operations', $pb.PbFieldType.PM, subBuilder: Operation.create)
    ..hasRequiredFields = false
  ;

  BulkEditRequest._() : super();
  factory BulkEditRequest({
    $core.String? userId,
    $70.Timestamp? timestamp,
    $core.Iterable<$core.String>? serials,
    $core.Iterable<Operation>? operations,
  }) {
    final _result = create();
    if (userId != null) {
      _result.userId = userId;
    }
    if (timestamp != null) {
      _result.timestamp = timestamp;
    }
    if (serials != null) {
      _result.serials.addAll(serials);
    }
    if (operations != null) {
      _result.operations.addAll(operations);
    }
    return _result;
  }
  factory BulkEditRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BulkEditRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BulkEditRequest clone() => BulkEditRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BulkEditRequest copyWith(void Function(BulkEditRequest) updates) => super.copyWith((message) => updates(message as BulkEditRequest)) as BulkEditRequest; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BulkEditRequest create() => BulkEditRequest._();
  BulkEditRequest createEmptyInstance() => create();
  static $pb.PbList<BulkEditRequest> createRepeated() => $pb.PbList<BulkEditRequest>();
  @$core.pragma('dart2js:noInline')
  static BulkEditRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BulkEditRequest>(create);
  static BulkEditRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get userId => $_getSZ(0);
  @$pb.TagNumber(1)
  set userId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasUserId() => $_has(0);
  @$pb.TagNumber(1)
  void clearUserId() => clearField(1);

  @$pb.TagNumber(2)
  $70.Timestamp get timestamp => $_getN(1);
  @$pb.TagNumber(2)
  set timestamp($70.Timestamp v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => clearField(2);
  @$pb.TagNumber(2)
  $70.Timestamp ensureTimestamp() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.List<$core.String> get serials => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<Operation> get operations => $_getList(3);
}

class Operation extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Operation', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOM<KeySpec>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'keySpec', subBuilder: KeySpec.create)
    ..aOM<Action>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'action', subBuilder: Action.create)
    ..hasRequiredFields = false
  ;

  Operation._() : super();
  factory Operation({
    KeySpec? keySpec,
    Action? action,
  }) {
    final _result = create();
    if (keySpec != null) {
      _result.keySpec = keySpec;
    }
    if (action != null) {
      _result.action = action;
    }
    return _result;
  }
  factory Operation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Operation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Operation clone() => Operation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Operation copyWith(void Function(Operation) updates) => super.copyWith((message) => updates(message as Operation)) as Operation; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Operation create() => Operation._();
  Operation createEmptyInstance() => create();
  static $pb.PbList<Operation> createRepeated() => $pb.PbList<Operation>();
  @$core.pragma('dart2js:noInline')
  static Operation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Operation>(create);
  static Operation? _defaultInstance;

  @$pb.TagNumber(1)
  KeySpec get keySpec => $_getN(0);
  @$pb.TagNumber(1)
  set keySpec(KeySpec v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasKeySpec() => $_has(0);
  @$pb.TagNumber(1)
  void clearKeySpec() => clearField(1);
  @$pb.TagNumber(1)
  KeySpec ensureKeySpec() => $_ensure(0);

  @$pb.TagNumber(2)
  Action get action => $_getN(1);
  @$pb.TagNumber(2)
  set action(Action v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAction() => $_has(1);
  @$pb.TagNumber(2)
  void clearAction() => clearField(2);
  @$pb.TagNumber(2)
  Action ensureAction() => $_ensure(1);
}

enum Action_Action {
  set, 
  add, 
  remove, 
  notSet
}

class Action extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, Action_Action> _Action_ActionByTag = {
    1 : Action_Action.set,
    2 : Action_Action.add,
    3 : Action_Action.remove,
    0 : Action_Action.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Action', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..oo(0, [1, 2, 3])
    ..aOM<SetAction>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'set', subBuilder: SetAction.create)
    ..aOM<AddAction>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'add', subBuilder: AddAction.create)
    ..aOM<RemoveAction>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'remove', subBuilder: RemoveAction.create)
    ..hasRequiredFields = false
  ;

  Action._() : super();
  factory Action({
    SetAction? set,
    AddAction? add,
    RemoveAction? remove,
  }) {
    final _result = create();
    if (set != null) {
      _result.set = set;
    }
    if (add != null) {
      _result.add = add;
    }
    if (remove != null) {
      _result.remove = remove;
    }
    return _result;
  }
  factory Action.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Action.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Action clone() => Action()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Action copyWith(void Function(Action) updates) => super.copyWith((message) => updates(message as Action)) as Action; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Action create() => Action._();
  Action createEmptyInstance() => create();
  static $pb.PbList<Action> createRepeated() => $pb.PbList<Action>();
  @$core.pragma('dart2js:noInline')
  static Action getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Action>(create);
  static Action? _defaultInstance;

  Action_Action whichAction() => _Action_ActionByTag[$_whichOneof(0)]!;
  void clearAction() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SetAction get set => $_getN(0);
  @$pb.TagNumber(1)
  set set(SetAction v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSet() => $_has(0);
  @$pb.TagNumber(1)
  void clearSet() => clearField(1);
  @$pb.TagNumber(1)
  SetAction ensureSet() => $_ensure(0);

  @$pb.TagNumber(2)
  AddAction get add => $_getN(1);
  @$pb.TagNumber(2)
  set add(AddAction v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAdd() => $_has(1);
  @$pb.TagNumber(2)
  void clearAdd() => clearField(2);
  @$pb.TagNumber(2)
  AddAction ensureAdd() => $_ensure(1);

  @$pb.TagNumber(3)
  RemoveAction get remove => $_getN(2);
  @$pb.TagNumber(3)
  set remove(RemoveAction v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasRemove() => $_has(2);
  @$pb.TagNumber(3)
  void clearRemove() => clearField(3);
  @$pb.TagNumber(3)
  RemoveAction ensureRemove() => $_ensure(2);
}

class SetAction extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'SetAction', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOM<$2.ConfigValue>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'value', subBuilder: $2.ConfigValue.create)
    ..hasRequiredFields = false
  ;

  SetAction._() : super();
  factory SetAction({
    $2.ConfigValue? value,
  }) {
    final _result = create();
    if (value != null) {
      _result.value = value;
    }
    return _result;
  }
  factory SetAction.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SetAction.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SetAction clone() => SetAction()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SetAction copyWith(void Function(SetAction) updates) => super.copyWith((message) => updates(message as SetAction)) as SetAction; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static SetAction create() => SetAction._();
  SetAction createEmptyInstance() => create();
  static $pb.PbList<SetAction> createRepeated() => $pb.PbList<SetAction>();
  @$core.pragma('dart2js:noInline')
  static SetAction getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SetAction>(create);
  static SetAction? _defaultInstance;

  @$pb.TagNumber(1)
  $2.ConfigValue get value => $_getN(0);
  @$pb.TagNumber(1)
  set value($2.ConfigValue v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasValue() => $_has(0);
  @$pb.TagNumber(1)
  void clearValue() => clearField(1);
  @$pb.TagNumber(1)
  $2.ConfigValue ensureValue() => $_ensure(0);
}

class AddAction extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'AddAction', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  AddAction._() : super();
  factory AddAction({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory AddAction.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory AddAction.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  AddAction clone() => AddAction()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  AddAction copyWith(void Function(AddAction) updates) => super.copyWith((message) => updates(message as AddAction)) as AddAction; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static AddAction create() => AddAction._();
  AddAction createEmptyInstance() => create();
  static $pb.PbList<AddAction> createRepeated() => $pb.PbList<AddAction>();
  @$core.pragma('dart2js:noInline')
  static AddAction getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<AddAction>(create);
  static AddAction? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class RemoveAction extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RemoveAction', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'name')
    ..hasRequiredFields = false
  ;

  RemoveAction._() : super();
  factory RemoveAction({
    $core.String? name,
  }) {
    final _result = create();
    if (name != null) {
      _result.name = name;
    }
    return _result;
  }
  factory RemoveAction.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RemoveAction.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RemoveAction clone() => RemoveAction()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RemoveAction copyWith(void Function(RemoveAction) updates) => super.copyWith((message) => updates(message as RemoveAction)) as RemoveAction; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RemoveAction create() => RemoveAction._();
  RemoveAction createEmptyInstance() => create();
  static $pb.PbList<RemoveAction> createRepeated() => $pb.PbList<RemoveAction>();
  @$core.pragma('dart2js:noInline')
  static RemoveAction getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RemoveAction>(create);
  static RemoveAction? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => clearField(1);
}

class KeySpec extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'KeySpec', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..pc<KeyComponent>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'components', $pb.PbFieldType.PM, subBuilder: KeyComponent.create)
    ..hasRequiredFields = false
  ;

  KeySpec._() : super();
  factory KeySpec({
    $core.Iterable<KeyComponent>? components,
  }) {
    final _result = create();
    if (components != null) {
      _result.components.addAll(components);
    }
    return _result;
  }
  factory KeySpec.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory KeySpec.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  KeySpec clone() => KeySpec()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  KeySpec copyWith(void Function(KeySpec) updates) => super.copyWith((message) => updates(message as KeySpec)) as KeySpec; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static KeySpec create() => KeySpec._();
  KeySpec createEmptyInstance() => create();
  static $pb.PbList<KeySpec> createRepeated() => $pb.PbList<KeySpec>();
  @$core.pragma('dart2js:noInline')
  static KeySpec getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<KeySpec>(create);
  static KeySpec? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<KeyComponent> get components => $_getList(0);
}

class KeyComponent extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'KeyComponent', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..pc<ComponentBranch>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'branches', $pb.PbFieldType.PM, subBuilder: ComponentBranch.create)
    ..hasRequiredFields = false
  ;

  KeyComponent._() : super();
  factory KeyComponent({
    $core.Iterable<ComponentBranch>? branches,
  }) {
    final _result = create();
    if (branches != null) {
      _result.branches.addAll(branches);
    }
    return _result;
  }
  factory KeyComponent.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory KeyComponent.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  KeyComponent clone() => KeyComponent()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  KeyComponent copyWith(void Function(KeyComponent) updates) => super.copyWith((message) => updates(message as KeyComponent)) as KeyComponent; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static KeyComponent create() => KeyComponent._();
  KeyComponent createEmptyInstance() => create();
  static $pb.PbList<KeyComponent> createRepeated() => $pb.PbList<KeyComponent>();
  @$core.pragma('dart2js:noInline')
  static KeyComponent getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<KeyComponent>(create);
  static KeyComponent? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<ComponentBranch> get branches => $_getList(0);
}

enum ComponentBranch_Branch {
  literal, 
  wildcard, 
  notSet
}

class ComponentBranch extends $pb.GeneratedMessage {
  static const $core.Map<$core.int, ComponentBranch_Branch> _ComponentBranch_BranchByTag = {
    1 : ComponentBranch_Branch.literal,
    2 : ComponentBranch_Branch.wildcard,
    0 : ComponentBranch_Branch.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'ComponentBranch', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'literal')
    ..aOM<Pattern>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'wildcard', subBuilder: Pattern.create)
    ..hasRequiredFields = false
  ;

  ComponentBranch._() : super();
  factory ComponentBranch({
    $core.String? literal,
    Pattern? wildcard,
  }) {
    final _result = create();
    if (literal != null) {
      _result.literal = literal;
    }
    if (wildcard != null) {
      _result.wildcard = wildcard;
    }
    return _result;
  }
  factory ComponentBranch.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ComponentBranch.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ComponentBranch clone() => ComponentBranch()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ComponentBranch copyWith(void Function(ComponentBranch) updates) => super.copyWith((message) => updates(message as ComponentBranch)) as ComponentBranch; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static ComponentBranch create() => ComponentBranch._();
  ComponentBranch createEmptyInstance() => create();
  static $pb.PbList<ComponentBranch> createRepeated() => $pb.PbList<ComponentBranch>();
  @$core.pragma('dart2js:noInline')
  static ComponentBranch getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ComponentBranch>(create);
  static ComponentBranch? _defaultInstance;

  ComponentBranch_Branch whichBranch() => _ComponentBranch_BranchByTag[$_whichOneof(0)]!;
  void clearBranch() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get literal => $_getSZ(0);
  @$pb.TagNumber(1)
  set literal($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasLiteral() => $_has(0);
  @$pb.TagNumber(1)
  void clearLiteral() => clearField(1);

  @$pb.TagNumber(2)
  Pattern get wildcard => $_getN(1);
  @$pb.TagNumber(2)
  set wildcard(Pattern v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasWildcard() => $_has(1);
  @$pb.TagNumber(2)
  void clearWildcard() => clearField(2);
  @$pb.TagNumber(2)
  Pattern ensureWildcard() => $_ensure(1);
}

class Pattern extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'Pattern', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'prefix')
    ..aOS(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'suffix')
    ..hasRequiredFields = false
  ;

  Pattern._() : super();
  factory Pattern({
    $core.String? prefix,
    $core.String? suffix,
  }) {
    final _result = create();
    if (prefix != null) {
      _result.prefix = prefix;
    }
    if (suffix != null) {
      _result.suffix = suffix;
    }
    return _result;
  }
  factory Pattern.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Pattern.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Pattern clone() => Pattern()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Pattern copyWith(void Function(Pattern) updates) => super.copyWith((message) => updates(message as Pattern)) as Pattern; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static Pattern create() => Pattern._();
  Pattern createEmptyInstance() => create();
  static $pb.PbList<Pattern> createRepeated() => $pb.PbList<Pattern>();
  @$core.pragma('dart2js:noInline')
  static Pattern getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Pattern>(create);
  static Pattern? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get prefix => $_getSZ(0);
  @$pb.TagNumber(1)
  set prefix($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasPrefix() => $_has(0);
  @$pb.TagNumber(1)
  void clearPrefix() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get suffix => $_getSZ(1);
  @$pb.TagNumber(2)
  set suffix($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSuffix() => $_has(1);
  @$pb.TagNumber(2)
  void clearSuffix() => clearField(2);
}

class BulkEditResponse extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'BulkEditResponse', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..pc<RobotEditRecord>(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'robots', $pb.PbFieldType.PM, subBuilder: RobotEditRecord.create)
    ..hasRequiredFields = false
  ;

  BulkEditResponse._() : super();
  factory BulkEditResponse({
    $core.Iterable<RobotEditRecord>? robots,
  }) {
    final _result = create();
    if (robots != null) {
      _result.robots.addAll(robots);
    }
    return _result;
  }
  factory BulkEditResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory BulkEditResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  BulkEditResponse clone() => BulkEditResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  BulkEditResponse copyWith(void Function(BulkEditResponse) updates) => super.copyWith((message) => updates(message as BulkEditResponse)) as BulkEditResponse; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static BulkEditResponse create() => BulkEditResponse._();
  BulkEditResponse createEmptyInstance() => create();
  static $pb.PbList<BulkEditResponse> createRepeated() => $pb.PbList<BulkEditResponse>();
  @$core.pragma('dart2js:noInline')
  static BulkEditResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<BulkEditResponse>(create);
  static BulkEditResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<RobotEditRecord> get robots => $_getList(0);
}

class RobotEditRecord extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'RobotEditRecord', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'serial')
    ..pc<EditRecord>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'records', $pb.PbFieldType.PM, subBuilder: EditRecord.create)
    ..aOS(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'message')
    ..hasRequiredFields = false
  ;

  RobotEditRecord._() : super();
  factory RobotEditRecord({
    $core.String? serial,
    $core.Iterable<EditRecord>? records,
    $core.String? message,
  }) {
    final _result = create();
    if (serial != null) {
      _result.serial = serial;
    }
    if (records != null) {
      _result.records.addAll(records);
    }
    if (message != null) {
      _result.message = message;
    }
    return _result;
  }
  factory RobotEditRecord.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RobotEditRecord.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  RobotEditRecord clone() => RobotEditRecord()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  RobotEditRecord copyWith(void Function(RobotEditRecord) updates) => super.copyWith((message) => updates(message as RobotEditRecord)) as RobotEditRecord; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static RobotEditRecord create() => RobotEditRecord._();
  RobotEditRecord createEmptyInstance() => create();
  static $pb.PbList<RobotEditRecord> createRepeated() => $pb.PbList<RobotEditRecord>();
  @$core.pragma('dart2js:noInline')
  static RobotEditRecord getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RobotEditRecord>(create);
  static RobotEditRecord? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get serial => $_getSZ(0);
  @$pb.TagNumber(1)
  set serial($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSerial() => $_has(0);
  @$pb.TagNumber(1)
  void clearSerial() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<EditRecord> get records => $_getList(1);

  @$pb.TagNumber(3)
  $core.String get message => $_getSZ(2);
  @$pb.TagNumber(3)
  set message($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMessage() => $_has(2);
  @$pb.TagNumber(3)
  void clearMessage() => clearField(3);
}

class EditRecord extends $pb.GeneratedMessage {
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'EditRecord', package: const $pb.PackageName(const $core.bool.fromEnvironment('protobuf.omit_message_names') ? '' : 'carbon.robot_syncer.bulk'), createEmptyInstance: create)
    ..aOS(1, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'key')
    ..aOM<Action>(2, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'action', subBuilder: Action.create)
    ..e<Outcome>(3, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'outcome', $pb.PbFieldType.OE, defaultOrMaker: Outcome.OUTCOME_UNSPECIFIED, valueOf: Outcome.valueOf, enumValues: Outcome.values)
    ..aOS(4, const $core.bool.fromEnvironment('protobuf.omit_field_names') ? '' : 'message')
    ..hasRequiredFields = false
  ;

  EditRecord._() : super();
  factory EditRecord({
    $core.String? key,
    Action? action,
    Outcome? outcome,
    $core.String? message,
  }) {
    final _result = create();
    if (key != null) {
      _result.key = key;
    }
    if (action != null) {
      _result.action = action;
    }
    if (outcome != null) {
      _result.outcome = outcome;
    }
    if (message != null) {
      _result.message = message;
    }
    return _result;
  }
  factory EditRecord.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory EditRecord.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  EditRecord clone() => EditRecord()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  EditRecord copyWith(void Function(EditRecord) updates) => super.copyWith((message) => updates(message as EditRecord)) as EditRecord; // ignore: deprecated_member_use
  $pb.BuilderInfo get info_ => _i;
  @$core.pragma('dart2js:noInline')
  static EditRecord create() => EditRecord._();
  EditRecord createEmptyInstance() => create();
  static $pb.PbList<EditRecord> createRepeated() => $pb.PbList<EditRecord>();
  @$core.pragma('dart2js:noInline')
  static EditRecord getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<EditRecord>(create);
  static EditRecord? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get key => $_getSZ(0);
  @$pb.TagNumber(1)
  set key($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasKey() => $_has(0);
  @$pb.TagNumber(1)
  void clearKey() => clearField(1);

  @$pb.TagNumber(2)
  Action get action => $_getN(1);
  @$pb.TagNumber(2)
  set action(Action v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasAction() => $_has(1);
  @$pb.TagNumber(2)
  void clearAction() => clearField(2);
  @$pb.TagNumber(2)
  Action ensureAction() => $_ensure(1);

  @$pb.TagNumber(3)
  Outcome get outcome => $_getN(2);
  @$pb.TagNumber(3)
  set outcome(Outcome v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasOutcome() => $_has(2);
  @$pb.TagNumber(3)
  void clearOutcome() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get message => $_getSZ(3);
  @$pb.TagNumber(4)
  set message($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasMessage() => $_has(3);
  @$pb.TagNumber(4)
  void clearMessage() => clearField(4);
}

