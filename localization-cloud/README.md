# CarbonCloud Localization

Repository of localized strings for the [cloud](https://github.com/carbonrobotics/cloud) repo

## Dependencies

`make` for running scripts

[Docker](https://docs.docker.com/get-docker/) to run scripts

[direnv](https://direnv.net/) or your preferred way of managing .envrc files

```bash
brew install direnv
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
```

## Setup

Clone repo

```bash
git clone https://github.com/carbonrobotics/localization-cloud
```

Configure environment variables (recommended)

```bash
cp .env.sample .env
```

Edit `.env` and insert your [Phrase Access token](https://app.phrase.com/settings/oauth_access_tokens)

Install local yarn dependencies

```bash
yarn
```

Bind mount localization into consumers (recommended)

```bash
rm -rf /path/to/cloud/localization-cloud
sudo echo "/path/to/localization-cloud /path/to/cloud/localization-cloud none  bind  0 0" >> /etc/fstab
sudo mount -a
```

Build the utility container

```bash
make build
```

## Translating

### Adding New Translation Strings

1. Add your new translation keys, values, and `#description`s to `translations/en-US.json`.
1. Run `yarn lint:fix` to generate stubs for the other translation files
1. Phrase will get the key changes automatically when changes are committed to the `master` branch. However, it doesn't automatically register descriptions.
1. After changes are merged to `master` run `make push` to sync descriptions

### Requesting Translations

1. When changes are made, push them to Phrase with `make push`. This safely pushes up changes including things that the Phrase GitHub integration does not do by default like descriptions for i18next files
1. To actually get them translated, [login to phrase](https://app.phrase.com)
1. Then click on the "Cloud" Project -> Orders
1. Click "Order translations", give it a name, Select all languages, Select the "Agriculture" Category in the side Options pane, hit "Calculate Price", then proceed to make the order
1. Click through to TextMaster (credentials in Bitwarden), and click "projects in creation" to find the newly requested "projects", one per language
1. For each project, you need to click "Continue" and then "Order" and then "Yes I'm really sure". If you don't, it will be stuck in limbo and not actually translated.
1. Once the home page says "N projects in progress, 0 projects in creation" (where N is the number of non-English languages), you're good

### Syncing Completed Translations

When translations are completed by Phrase, you can run `make pull` to pull the changes into this repo where they can be used by our apps. (Feel free to commit with a message like "Pulling from Phrase")
