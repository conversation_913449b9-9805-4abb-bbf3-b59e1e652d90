{"components": {"AlarmTable": {"export": "{{robots}} cronologia allarmi {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Le metriche spaziali sono offerte gratuitamente durante la fase di valutazione, tuttavia potrebbero essere soggette a modifiche, rimozione o la necessità di un upgrade del piano in qualsiasi momento. I dati devono essere verificati in modo indipendente.", "description#description": "", "title": "Metriche spaziali beta", "title#description": ""}, "tooltip": "Questa funzione è in fase di valutazione e può essere modificata o rimossa in qualsiasi momento.", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Impossibile caricare la chat: {{message}}", "failed#description": ""}, "machineTranslated": "Tradotto automaticamente", "machineTranslated#description": "", "machineTranslatedFrom": "<PERSON><PERSON><PERSON>amente dal {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Questo messaggio è stato eliminato.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} verrà eliminato definitivamente.", "description#description": "", "descriptionActive": "{{subject}} è attivo e pertanto non può essere eliminato.", "descriptionActive#description": ""}, "title": "Sei sicuro?", "title#description": ""}, "CopyToClipboardButton": {"click": "Fai clic per copiare", "click#description": "", "copied": "Copiato!", "copied#description": ""}, "CropEditor": {"failed": "Impossibile caricare l'editor delle colture", "failed#description": "", "viewIn": "Visualizza in Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Can<PERSON><PERSON>", "clear#description": "", "endDate": "Data di fine", "endDate#description": "", "error": "Errore selezione intervallo date", "error#description": "", "invalid": "Non valido", "invalid#description": "", "last7days": "Ultimi 7 giorni", "last7days#description": "", "lastMonth": "<PERSON><PERSON><PERSON> mese", "lastMonth#description": "", "lastWeek": "La settimana scorsa", "lastWeek#description": "", "minusDays": "{{days}} giorni fa", "minusDays#description": "", "plusDays": "tra {{days}} gior<PERSON>", "plusDays#description": "", "startDate": "Data di inizio", "startDate#description": "", "thisMonth": "<PERSON>o mese", "thisMonth#description": "", "thisWeek": "<PERSON><PERSON> setti<PERSON>", "thisWeek#description": "", "today": "<PERSON><PERSON><PERSON>", "today#description": "", "tomorrow": "<PERSON><PERSON>", "tomorrow#description": "", "yesterday": "<PERSON><PERSON>", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "Siamo spiacenti, si è verificato un errore inatteso", "error#description": "", "queryLimitReached": "Essendo stati restituiti troppi dati, verrà effettuato il rendering di un sola porzione del set di dati. Contatta l'assistenza per ricevere aiuto", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Cosa è successo?", "comment#description": "", "feedback": "<PERSON><PERSON><PERSON>", "feedback#description": "", "submit": "Invia e carica nuovamente", "submit#description": ""}, "GdprConsent": {"description": "Controlla e accetta per continuare", "description#description": "", "statement": "Accetto i <0><PERSON><PERSON><PERSON> d'uso</0> e l'<1>Informativa sulla Privacy</1>.", "statement#description": "", "title": "Termini d'uso e Informativa sulla Privacy", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Specificare il Cliente", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Attiva questo menu", "help#description": "", "title": "Scorciatoie da tastiera", "title#description": ""}, "LaserTable": {"export": "{{robots}} laser {{date}}", "export#description": "", "installedOnly": "Solo installati", "installedOnly#description": "", "warnings": {"duplicate": "Questo robot ha più laser registrati nel/i seguente/i alloggiamenti: {{slots}}", "duplicate#description": "", "emptySlot": "Questo robot non ha laser registrati nel/i seguente/i alloggiamenti: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Nuovo codice", "new#description": ""}, "Loading": {"failed": "Siamo spiacenti, non è stato possibile caricare il Carbon Ops Center.", "failed#description": "", "placeholder": "Caricamento in corso...", "placeholder#description": ""}, "ModelName": {"warning": "Attenzione: modello poco affidabile", "warning#description": ""}, "PendingActivationOverlay": {"description": "Stiamo attivando il tuo account. Riceverai un'email quando l'operazione sarà stata completata!", "description#description": "", "errors": {"carbon": {"description": "È stata rilevata un'email Carbon, ma la verifica non è avvenuta a causa dell'accesso eseguito con nome utente/password. Effettua il logout e utilizza l'opzione \"Accedi con Google\" per usufruire dell'attivazione automatica.", "description#description": "", "title": "Account Carbon non verificato", "title#description": ""}}, "hi": "Salve {{name}}!", "hi#description": "", "logOut": "Hai effettuato l'accesso con l'account sbagliato? <0>Effettua il log out</0>.", "logOut#description": "", "title": "In attesa di attivazione", "title#description": ""}, "ResponsiveSubnav": {"more": "Altro", "more#description": ""}, "RobotImplementationSelector": {"status": "Stato di implementazione", "status#description": "", "title": "Modifica lo stato di implementazione", "title#description": "", "warning": "Modificare lo stato di implementazione può attivare flussi di lavoro automatizzati che incidono sull'esperienza del cliente. NON FARLO SE NON SEI SICURO!", "warning#description": ""}, "ShowLabelsButton": {"text": "<PERSON><PERSON><PERSON><PERSON>", "text#description": "", "tooltip": "Mostra diciture", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Mostra i metadati", "tooltip#description": ""}, "almanac": {"crops": {"new": "Aggiungi una nuova coltura", "new#description": "", "none": "Nessuna categoria di colture", "none#description": "", "sync#description": ""}, "cropsSynced": "<PERSON><PERSON> le colture", "cropsSynced#description": "", "delete": {"description": "Questa operazione non può essere annullata", "description#description": ""}, "discard": {"description": "Vuoi scartare le modifiche apportate a {{title}}?", "description#description": "", "title": "Vuoi scartare le modifiche?", "title#description": ""}, "fineTuneDescription": "Il valore predefinito è 5, è possibile diminuire o aumentare il tempo di attivazione del laser di circa il 20% per incremento.", "fineTuneDescription#description": "", "fineTuneTitle": "Moltiplicatore di regolazione fine", "fineTuneTitle#description": "", "formulas": {"all": "<PERSON><PERSON> le <PERSON>i", "all#description": "", "copyFormula": "Copia formula", "copyFormula#description": "", "copySize": "Copia dimensioni", "copySize#description": "", "exponent": {"description": "Il raggio, espresso in mm, viene elevato a questo esponente.", "description#description": "", "label": "Esponente (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Un numero da 1 a 10, il valore predefinito è 5, può diminuire o aumentare il tempo di attivazione del laser di circa il 20% per incremento. Questo è il numero utilizzato in modalità base", "description#description": "", "label": "Indice Fine Tune (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Moltiplicatore generale per l'incremento/decremento dell'Indice di Fine Tune.", "description#description": "", "label": "Valore del Moltiplicatore Fine Tune (FM)", "label#description": ""}, "laserTime": "Tempo di emissione laser", "laserTime#description": "", "maxTime": {"description": "Limite massimo del tempo di emissione in ms", "description#description": "", "label": "Tempo massimo", "label#description": ""}, "multiplier": {"description": "Simoltiplica per il raggio in mm.", "description#description": "", "label": "Moltiplicatore (A)", "label#description": ""}, "offset": {"description": "Millisecondi da aggiungere indipendentemente dal raggio", "description#description": "", "label": "Offset (b)", "label#description": ""}, "pasteFormula": "Incolla formula", "pasteFormula#description": "", "pasteSize": "<PERSON>oll<PERSON>i", "pasteSize#description": "", "sync": "Sincronizza tutte le dimensioni", "sync#description": "", "thresholds": "Soglia dimensioni", "thresholds#description": "", "title": "Formule", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Passa alla Modalità Avanzata", "switchModeAdvanced#description": "", "switchModeBasic": "Passa alla Modalità Base", "switchModeBasic#description": "", "warnings": {"admin": "Le modifiche apportate a questo almanacco saranno sincronizzate con tutte le unità produttive, sia attuali che future.", "admin#description": "", "carbon": "Questo è un almanacco fornito da Carbon Solo l'Indice Fine Tune può essere modificato", "carbon#description": "", "production": "Questo almanacco è in esecuzione su un robot. La modifica avrà effetto immediato sul campo.", "production#description": ""}, "weeds": {"new": "Aggiungi nuova erba infestante", "new#description": "", "none": "Nessuna categoria di erbe infestanti", "none#description": "", "sync#description": ""}, "weedsSynced": "<PERSON>tte le erbe infestanti", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} salvato. Attiva tramite Operator App Quick Tune.", "savedLong#description": "", "testResults": "Anteprima dei risultati", "testResults#description": ""}, "filters": {"capturedAt": "Data di acquisizione", "capturedAt#description": "", "diameter": "Diametro", "diameter#description": "", "filters#description": "", "unappliedFilters": "", "unappliedFilters#description": ""}, "images": {"allImages": "<PERSON><PERSON> le immagini", "allImages#description": "", "categorized": "Suddivise per categoria", "categorized#description": "", "scrollToTop": "Torna all'inizio", "scrollToTop#description": "", "sortBy": {"latest": "<PERSON><PERSON> recente", "latest#description": ""}, "sortedBy": "Ordinato per: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Errore nell'acquisizione dello stato", "error#description": "", "ready": "I risultati sono pronti", "ready#description": "", "session#description": "", "session_many": "", "session_one": "Sessione", "session_other": "Sessioni", "showResults": "Mostra risultati", "showResults#description": "", "status": "Sono stati elaborati {{processed}} risultati su un totale di {{total}}.", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Modificando questo profilo di categoria, la sincronizzazione avverrà per tutte le unità di produzione, sia attuali che future.", "admin#description": "", "adminMeta": "Tutti i profili degli amministratori saranno disponibili per tutti i clienti. Non creare confusione!", "adminMeta#description": "", "production": "Questo profilo di categoria è attualmente in esecuzione su un robot. L'eventuale modifica avrà effetto immediato sul campo.", "production#description": "", "protected": "Questo è un profilo fornito da Carbon. Non è possibile modificare nulla.", "protected#description": "", "unsavedChanges": "Modifiche non salvate. Premi Salva per apportare le modifiche.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_many": "", "changedKey_one": "Chiave modificata", "changedKey_other": "Chiavi modificate", "newKey": "Nuovo nome {{key}}", "newKey#description": "", "stringReqs": "<PERSON><PERSON><PERSON> contenere solo a-z, 0-9, ., -, e _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Questa chiave è stata aggiunta a quella predefinita.", "description#description": ""}, "keyMissing": {"description": "Valore/i predefinito/i mancante/i: {{keys}}", "description#description": ""}, "valueChanged": {"description": "Questo valore è stato modificato rispetto al suo valore predefinito ({{default}}).", "description#description": "", "title": "Configurazione modificata", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Impossibile caricare l'editor clienti", "load#description": ""}}, "CustomerSelector": {"empty": "Non assegnato", "empty#description": "", "title": "Modifica cliente", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "Azionare il laser vs. Ignorare", "description#description": "", "label": "Aziona il laser", "label#description": ""}, "copy": "Copia configurazioni", "copy#description": "", "ignorable": {"description": "Aziona il laser solo se il tempo lo permette, non considerato nel calcolo della velocità consigliata.", "description#description": "", "label": "Ignorabile", "label#description": ""}, "paste": "Incolla le configurazioni", "paste#description": ""}, "warnings": {"production": "Questo discriminatore sta venendo utilizzato da un robot. Le modifiche avranno effetto immediato sul campo.", "production#description": ""}}, "drawer": {"customerMode": "Modalità cliente", "customerMode#description": "", "error": "Impossibile caricare la navigazione", "error#description": ""}, "filters": {"NumericalRange": {"max": "<PERSON><PERSON> ({{units}})", "max#description": "", "min": "Minimo ({{units}})", "min#description": ""}, "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": ""}, "header": {"failed": "Impossibile caricare l'intestazione", "failed#description": "", "mascot": "Pollo mascotte di Carbon Robotics", "mascot#description": "", "search": {"failed": "Impossibile caricare la ricerca", "failed#description": "", "focus": "Focus sul campo di ricerca", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Dimensioni", "label#description": "", "larger": "<PERSON><PERSON><PERSON><PERSON>", "larger#description": "", "smaller": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smaller#description": ""}}, "map": {"bounds": {"reset": "Reimposta la vista", "reset#description": ""}, "errors": {"empty": "<PERSON><PERSON><PERSON> dato sulla posizione disponibile", "empty#description": "", "failed": "Impossibile caricare la mappa", "failed#description": ""}, "filters": {"customer_office": "Ufficio del cliente", "customer_office#description": "", "hq": "Sede Carbon", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Casella postale", "po_box#description": "", "shop": "Officina", "shop#description": "", "storage": "<PERSON><PERSON><PERSON><PERSON>", "storage#description": "", "support_base": "Centro di assistenza", "support_base#description": ""}, "fullscreen": "<PERSON><PERSON> schermo", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Errore livello legenda", "legend#description": "", "notThinning": "NIENTE DIRADAMENTO", "notThinning#description": "", "notWeeding": "NIENTE DISERBO", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "ERRORE MAPPA DI CALORE", "unknown#description": ""}, "fields": {"block": "Blocco: {{block}}", "block#description": "", "location": "Posizione: {{latitude}}, {{longitude}}", "location#description": "", "size": "Dimensioni: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "<PERSON><PERSON>", "name#description": "", "rangeType#description": "", "relative": "Usa l'intervallo relativo", "relative#description": "", "relativeRange#description": ""}, "map": "Mappa", "map#description": "", "measure": {"name": "<PERSON><PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "Da quale categoria vuoi copiare?", "copyFromWhich#description": "", "splitCrops": "Colture separate", "splitCrops#description": "", "splitWeeds": "Erbe infestanti separate", "splitWeeds#description": "", "syncCrops": "Sincronizza tutte le colture", "syncCrops#description": "", "syncWeeds": "Sincronizza tutte le erbe infestanti", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Soglia di affidabilità della previsione per l'esecuzione di un rilevamento in banding dinamico.", "description#description": "", "label": "Soglia banding", "label#description": ""}, "minDoo": {"description": "Minimum Detection Over Opportunity", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Soglia di affidabilità per il rilevamento nel diradamento", "description#description": "", "label": "Soglia diradamento", "label#description": ""}, "weed": {"description": "Soglia di affidabilità della previsione per l'esecuzione di un rilevamento nella protezione inversa delle colture.", "description#description": "", "label": "Soglia protezione inversa delle colture", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Soglia di affidabilità della previsione per l'esecuzione di un rilevamento nella protezione delle colture", "description#description": "", "label": "Soglia protezione delle colture", "label#description": ""}, "weed": {"description": "Soglia di affidabilità della previsione per considerare una pianta infestante.", "description#description": "", "label": "Soglia erba infestante", "label#description": ""}}}, "errors": {"sync": "Le impostazioni di questo modello non sono ancora state sincronizzate con il LaserWeeder. Attendi la sincronizzazione per visualizzare e aggiornare le impostazioni.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Dimensioni separate", "splitSizesLong#description": "", "splitSizesShort": "Separate", "splitSizesShort#description": "", "syncSizesLong": "Sincronizza le dimensioni", "syncSizesLong#description": "", "syncSizesShort": "Sincronizza", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Attenzione:{{stopEmphasis}} Queste impostazioni includono modifiche non salvate che non si riflettono sul robot.", "exportingUnsavedChanges#description": "", "production": "<PERSON>o modello è in esecuzione su un robot. Le modifiche avranno effetto immediato sul campo.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Allarmi s<PERSON>", "unknown#description": ""}, "almanac": {"unknown": "Almanac<PERSON>", "unknown#description": "", "withName": "Almanacco: {{name}}", "withName#description": ""}, "autofixing": "Errore di riparazione automatica", "autofixing#description": "", "banding": {"disabled": "Banding disattivato", "disabled#description": "", "enabled": "Banding attivato", "enabled#description": "", "none": "Niente banding", "none#description": "", "static": "(STATICO)", "static#description": "", "withName": "Banding: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Impossibile caricare lo stato della registrazione", "failed#description": "", "never": "Mai registrato", "never#description": "", "withTime": "Registrato alle {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} colture abilitate ({{pinned}} fissate)", "summary#description": ""}, "delivery": "Consegna", "delivery#description": "", "disconnected": "Disconnesso", "disconnected#description": "", "discriminator": {"unknown": "Discriminatore sconosciuto", "unknown#description": "", "withName": "Discriminatore: {{name}}", "withName#description": ""}, "failed": "Impossibile caricare lo stato del robot", "failed#description": "", "failedShort": "Non riuscito", "failedShort#description": "", "implementation": "Implementazione", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "In magazzino", "inventory#description": "", "job": {"none": "<PERSON><PERSON><PERSON> lavoro", "none#description": "", "withName": "Lavoro: {{name}}", "withName#description": ""}, "lasers": "Laser online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Intera vita utile:", "lifetime#description": "", "lifted": "Standby (sollevato)", "lifted#description": "", "loading": "Caricamento", "loading#description": "", "location": {"known": "Posizione: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Posizione sconosciuta", "unknown#description": ""}, "manufacturing": "In produzione", "manufacturing#description": "", "model": {"withName": "Modello: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Caricamento del modello", "modelLoading#description": "", "notArmed": "Non attivato", "notArmed#description": "", "off_season": "Fuori stagione", "off_season#description": "", "offline": "Offline per {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P sconosciuto", "unknown#description": ""}, "poweringDown": "In fase di spegnimento", "poweringDown#description": "", "poweringUp": "In fase di attivazione", "poweringUp#description": "", "pre_manufacturing": "In Pre-Produzione", "pre_manufacturing#description": "", "stale": "Obsoleto", "stale#description": "", "staleDescription": "Ultimo valore noto. Il robot è offline.", "staleDescription#description": "", "standby": "Standby", "standby#description": "", "thinning": {"disabled": "Diradamento disattivato", "disabled#description": "", "enabled": "Diradamento attivato", "enabled#description": "", "none": "<PERSON><PERSON><PERSON> dirada<PERSON>", "none#description": "", "withName": "Diradamento: {{name}}", "withName#description": ""}, "today": {"none": "Oggi niente diserbo", "none#description": ""}, "unknown": "Stato <PERSON>", "unknown#description": "", "updating": "Installazione dell'aggiornamento in corso", "updating#description": "", "version": {"values": {"unknown": "<PERSON>e sconosciuta", "unknown#description": "", "updateDownloading": "(download della versione {{version}} in corso)", "updateDownloading#description": "", "updateReady": "(versione {{version}} pronta)", "updateReady#description": ""}}, "weeding": "Diserbo {{crop}}", "weeding#description": "", "weedingDisabled": "Diserbo disattivato", "weedingDisabled#description": "", "weedingThinning": "Diserbo e diradamento di {{crop}}", "weedingThinning#description": "", "winterized": "Riposto per l'inverno", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Esiste gi<PERSON>", "exists#description": "", "unknownClass": "Classe robot sconosciuta", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Non creare una nuova configurazione", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "<PERSON><PERSON> {{class}}", "templateForClass#description": "", "templateGeneric": "Modello Robot", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Devi procedere solo se esiste già una configurazione per {{serial}} o se hai intenzione di crearla manualmente.", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Impostazioni avanzate del tachimetro", "advancedFormulaTitle#description": "", "formulaTitle": "Formula", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "La velocità consigliata verrà automaticamente ridotta del valore indicato. Ad esempio, un input del 5% ridurrà la velocità suggerita di 1 mph a 0,95 mph.", "description#description": "", "label": "Correzione della velocità", "label#description": ""}, "decreaseSmoothing": {"description": "Imposta la velocità della decelerazione Più alto è il valore, più è probabile che il tachimetro presenti delle oscillazioni.", "description#description": "", "label": "Gradualizzazione della decelerazione", "label#description": ""}, "increaseSmoothing": {"description": "Imposta la velocità dell'accelerazione Più alto è il valore, più è probabile che il tachimetro presenti delle oscillazioni.", "description#description": "", "label": "Gradualizzazione dell'accelerazione", "label#description": ""}, "maxVelMph": {"description": "Inserisci la velocità massima assoluta che sei disposto a raggiungere. La velocità consigliata non sarà superiore a questo valore", "description#description": "", "label": "Velocità massima", "label#description": ""}, "minVelMph": {"description": "Inserisci la velocità minima assoluta che sei disposto a raggiungere. La velocità consigliata non sarà inferiore a questo valore", "description#description": "", "label": "Velocità minima", "label#description": ""}, "primaryKillRate": {"description": "Questo valore rappresenta la percentuale di erbe infestanti che si desidera eliminare.", "description#description": "", "label": "Tasso Ideale di Eliminazione", "label#description": ""}, "primaryRange": {"description": "Aumenta questo valore se vuoi raggiungere il tuo tasso di eradicazione ideale indipendentemente dall'impatto sulla velocità.", "description#description": "", "label": "Buffer verde", "label#description": ""}, "rows": {"allRows": "<PERSON><PERSON> le file", "allRows#description": "", "row1": "Fila 1", "row1#description": "", "row2": "Fila 2", "row2#description": "", "row3": "Fila 3", "row3#description": ""}, "secondaryKillRate": {"description": "Questo valore rappresenta la percentuale minima accettabile di erbe infestanti eliminate.", "description#description": "", "label": "Tasso Minimo di Eliminazione", "label#description": ""}, "secondaryRange": {"description": "Aumenta questo valore se vuoi aggiungere un po' di margine prima di ricevere una notifica di bassa velocità.", "description#description": "", "label": "<PERSON><PERSON><PERSON> g<PERSON>", "label#description": ""}, "sync": "Sincronizza tutte le file", "sync#description": "", "warnings": {"admin": "La modifica apportata a questo stimatore di velocità sarà sincronizzata con tutte le unità di produzione, sia attuali che future.", "admin#description": "", "production": "Questo stimatore di velocità è in funzione su un robot. La modifica avrà effetto immediato sul campo.", "production#description": "", "protected": "Questo è un profilo fornito da Carbonio. Non è possibile modificare nulla.", "protected#description": "", "unsavedChanges": "Modifiche non salvate. Premi Salva per apportare le modifiche.", "unsavedChanges#description": ""}}, "slider": {"gradual": "Graduale", "gradual#description": "", "immediate": "Immediato", "immediate#description": ""}, "visualization": {"targetSpeed": "Velocità obiettivo", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_many": "", "alarm_one": "allarme", "alarm_other": "allarmi", "fields": {"code": "Codice", "code#description": "", "description": "Descrizione", "description#description": "", "duration": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"ongoing": "IN CORSO", "ongoing#description": ""}}, "identifier": "Identificativo", "identifier#description": "", "impact": {"name": "Impatto", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Iniziato", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_many": "", "almanac_one": "almanacco", "almanac_other": "almanacchi", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_many": "", "assignment_one": "assegnazione", "assignment_other": "assegnazioni", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Istruzioni", "instructions#description": ""}, "intervention#description": "", "intervention_many": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"laserWeed": "Diserbo laser", "laserWeed#description": "", "unrecognized": "tipo sconosciuto ({{value}})", "unrecognized#description": ""}, "job_many": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Fila diserbo laser", "laserWeedRow#description": ""}, "objective_many": "", "objective_one": "obiettivo", "objective_other": "obiettivi", "states": {"acknowledged": "accettato", "acknowledged#description": "", "cancelled": "annullato", "cancelled#description": "", "completed": "completato", "completed#description": "", "failed": "non riuscito", "failed#description": "", "inProgress": "in corso", "inProgress#description": "", "new": "nuovo", "new#description": "", "paused": "in pausa", "paused#description": "", "pending": "in attesa", "pending#description": "", "ready": "pronto", "ready#description": "", "unrecognized": "stato sconosciuto ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Attività #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_many": "", "task_one": "attività", "task_other": "attività"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_many": "", "categoryCollectionProfile_one": "profilo della categoria di piante", "categoryCollectionProfile_other": "profili delle categorie di piante", "fields": {"categories": {"disregard": "Ignora", "disregard#description": "", "name": "Categorie", "name#description": "", "requiredBaseCategories": "Devi avere esattamente queste categorie: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Aggiornato", "updatedAt#description": ""}, "metadata": {"capturedAt": "<PERSON><PERSON>quis<PERSON>", "capturedAt#description": "", "categoryId": "ID categoria", "categoryId#description": "", "imageId": "ID immagine", "imageId#description": "", "pointId": "ID punto", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "radius": "raggio", "radius#description": "", "updatedAt": "Rice<PERSON><PERSON>", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_many": "", "config_one": "configurazione", "config_other": "configurazioni", "key#description": "", "key_many": "", "key_one": "chiave", "key_other": "chiavi", "template#description": "", "template_many": "", "template_one": "modello di configurazione", "template_other": "modelli di configurazione", "value#description": "", "value_many": "", "value_one": "valore", "value_other": "valori"}, "crops": {"categories": {"unknown": "<PERSON><PERSON> sconosciuta", "unknown#description": ""}, "crop#description": "", "crop_many": "", "crop_one": "<PERSON><PERSON>", "crop_other": "colture", "fields": {"confidence": {"fields": {"regionalImages": "Immagini regionali:", "regionalImages#description": "", "totalImages": "Immagini totali", "totalImages#description": ""}, "name": "Confidenza", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Archiviato", "archived#description": "", "unknown": "Confidenza sconosciuta", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Note", "notes#description": "", "pinned": "Fissato", "pinned#description": "", "recommended": "Consigliato", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_many": "", "customer_one": "cliente", "customer_other": "clienti", "fields": {"emails": {"errors": {"formatting": "Un'email per riga", "formatting#description": ""}, "name": "Email", "name#description": ""}, "featureFlags": {"almanac": {"description": "Abilita le schede Almanacco e Discriminatore per i robot (solo se il robot supporta queste funzioni)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Attiva la scheda Profilo della categoria di piante per i robot", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "I \"feature flag\" attivano funzionalità in versione beta per tutti gli utenti presso un cliente.", "description#description": "", "explore": {"description": "Abilita la modalità di esplorazione di mappe e grafici nei rapporti", "description#description": "", "name": "Modalit di esplorazione", "name#description": ""}, "jobs": {"description": "Attiva lavori (solo se supportati dal robot)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Mostra un nuovo design visivo per le metriche del robot", "description#description": "", "name": "Nuvo design metriche", "name#description": ""}, "name": "Feature Flags", "name#description": "", "off": "DISATTIVATO", "off#description": "", "on": "ATTIVATO", "on#description": "", "reports": {"description": "Abilita la scheda Rapporti e le funzioni che contiene", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Mostra i dati spaziali, comprese le mappe di calore e i grafici.", "description#description": "", "name": "<PERSON><PERSON>", "name#description": ""}, "summary": "{{enabled}}/{{total}} Feature Flags attivate", "summary#description": "", "unvalidatedMetrics": {"description": "Visualizza le metriche beta in attesa di validazione sul campo all'interno delle metriche certificate.", "description#description": "", "name": "Metriche beta", "name#description": ""}, "velocityEstimator": {"description": "Abilita la visualizzazione e la modifica dei profili dello stimatore di velocità obiettivo (il robot deve supportarlo)", "description#description": "", "name": "Stimatore della Velocità Obiettivo", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Genera il", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Se questa opzione è abilitata, i rapporti saranno generati settimanalmente con le seguenti impostazioni per tutti i robot attivi.", "description#description": "", "name": "Rapporti settimanali", "name#description": ""}, "weeklyReportHour": "Genera alle", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Periodo di riferimento", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Genera in", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_many": "", "discriminator_one": "discriminatore", "discriminator_other": "discriminatori", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_many": "", "farm_one": "podere", "farm_other": "poderi", "point#description": "", "point_many": "", "point_one": "punto", "point_other": "punti", "zone#description": "", "zone_many": "", "zone_one": "zona", "zone_other": "zone"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_many": "", "fieldDefinition_one": "definizione del campo", "fieldDefinition_other": "definizioni del campo", "fields": {"boundary": "Tracciato perimetrale del campo", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Direzione di semina", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_many": "", "global_one": "valore globale", "global_other": "valori globali", "values": {"plantProfileModelId": {"description": "Modello base usato da tutti i profili cliente e amministratore per '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) ID Modello", "label#description": ""}}}, "images": {"fields": {"camera": "Telecamera", "camera#description": "", "capturedAt": "Data/Ora", "capturedAt#description": "", "geoJson": "Posizione", "geoJson#description": "", "url": "<PERSON>i immagine", "url#description": ""}, "image#description": "", "image_many": "", "image_one": "immagine", "image_other": "immagini"}, "jobs": {"job#description": "", "job_many": "", "job_one": "lavoro", "job_other": "lavori"}, "lasers": {"fields": {"cameraId": "ID telecamera", "cameraId#description": "", "error": {"values": {"false": "Nominale", "false#description": ""}}, "installedAt": "Installato", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Ser<PERSON><PERSON> s<PERSON>", "unknown#description": ""}}, "lifetimeSec": "Tempo di funzionamento", "lifetimeSec#description": "", "powerLevel": "Livello di potenza", "powerLevel#description": "", "removedAt": "<PERSON><PERSON><PERSON>", "removedAt#description": "", "rowNumber": "<PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "Conteggio accensioni", "totalFireCount#description": "", "totalFireTimeMs": "Tempo di accensione", "totalFireTimeMs#description": "", "warranty": {"name": "Garanzia", "name#description": "", "values": {"expired": "Scaduta", "expired#description": "", "hours": "Ore: {{installed}}/{{total}} ({{percent}} restanti)", "hours#description": "", "hoursUnknown": "Ore: scon<PERSON><PERSON><PERSON>", "hoursUnknown#description": "", "months": "Mesi: {{installed}}/{{total}} ({{percent}} restanti)", "months#description": "", "monthsUnknown": "Mesi: s<PERSON><PERSON><PERSON><PERSON>", "monthsUnknown#description": "", "unknown": "Garanzia non nota", "unknown#description": ""}}}, "laser#description": "", "laser_many": "", "laser_one": "laser", "laser_other": "laser"}, "models": {"model#description": "", "model_many": "", "model_one": "modello", "model_other": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "none#description": "", "p2p#description": "", "p2p_many": "", "p2p_one": "Modello P2P", "p2p_other": "Modelli P2P", "unknown": "<PERSON><PERSON>", "unknown#description": ""}, "reportInstances": {"fields": {"authorId": "<PERSON><PERSON><PERSON> <PERSON>", "authorId#description": "", "createdAt": "Pubblicato", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_many": "", "run_one": "creazione di report", "run_other": "creazioni di report"}, "reports": {"fields": {"authorId": "Proprietario", "authorId#description": "", "automateWeekly": {"name": "Automatizzato", "name#description": "", "values": {"weekly": "<PERSON><PERSON><PERSON><PERSON>", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_many": "", "report_one": "report", "report_other": "report"}, "robots": {"classes": {"buds#description": "", "buds_many": "", "buds_one": "<PERSON>", "buds_other": "<PERSON>", "moduleValidationStations#description": "", "moduleValidationStations_many": "", "moduleValidationStations_one": "Stazione di convalida moduli", "moduleValidationStations_other": "Stazioni di convalida moduli", "reapersCarbon#description": "", "reapersCarbon_many": "", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reaper", "reapersCustomer_many": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_many": "", "rtcs_one": "<PERSON><PERSON><PERSON>", "rtcs_other": "<PERSON><PERSON><PERSON><PERSON>", "simulators#description": "", "simulators_many": "", "simulators_one": "Simulatore", "simulators_other": "<PERSON><PERSON><PERSON><PERSON>", "slayersCarbon#description": "", "slayersCarbon_many": "", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayer", "slayersCustomer#description": "", "slayersCustomer_many": "", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "<PERSON><PERSON><PERSON><PERSON>", "unknown": "Classe sconosciuta", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Laser offline", "lasersOffline#description": "", "lifetimeArea": "Area Operativa Totale", "lifetimeArea#description": "", "lifetimeTime": "Tempo Totale di Funzionamento", "lifetimeTime#description": "", "localTime": "Orario locale", "localTime#description": "", "reportedAt": "Ultimo aggiornamento", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Versione software", "softwareVersion#description": "", "supportSlack": "Canale Slack Support", "supportSlack#description": "", "targetVersion": "Versione Obiettivo", "targetVersion#description": ""}, "robot#description": "", "robot_many": "", "robot_one": "robot", "robot_other": "robot", "unknown": "<PERSON> sconosciuto", "unknown#description": ""}, "users": {"activated": "<PERSON><PERSON><PERSON><PERSON>", "activated#description": "", "fields": {"email": "Email", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Attivazione", "name#description": "", "values": {"false": "IN SOSPESO", "false#description": ""}}}, "operator#description": "", "operator_many": "", "operator_one": "operatore", "operator_other": "operatori", "role#description": "", "role_many": "", "role_one": "<PERSON><PERSON><PERSON>", "role_other": "<PERSON><PERSON><PERSON>", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Tecnico)", "carbon_tech#description": "", "farm_manager": "<PERSON><PERSON><PERSON> agricolo", "farm_manager#description": "", "operator_advanced": "Operatore (Avanzato)", "operator_advanced#description": "", "operator_basic": "Operatore", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "<PERSON><PERSON><PERSON> s<PERSON>", "unknown_role#description": ""}, "staff": "Personale", "staff#description": "", "user#description": "", "user_many": "", "user_one": "utente", "user_other": "utenti"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_many": "", "velocityEstimator_one": "stimatore di velocità", "velocityEstimator_other": "stimatori di velocità"}, "weeds": {"categories": {"blossom": "Fioritura", "blossom#description": "", "broadleaf": "Latifoglia", "broadleaf#description": "", "fruit": "<PERSON><PERSON><PERSON>", "fruit#description": "", "grass": "Erba", "grass#description": "", "offshoot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offshoot#description": "", "preblossom": "Pre-fioritura", "preblossom#description": "", "purslane": "<PERSON><PERSON><PERSON><PERSON>", "purslane#description": "", "runner": "Stolone", "runner#description": "", "unknown": "Erba infestante sconosciuta", "unknown#description": ""}, "weed#description": "", "weed_many": "", "weed_one": "erba infestante", "weed_other": "erbe infestanti"}}, "utils": {"actions": {"add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add#description": "", "addLong": "Aggiungi {{subject}}", "addLong#description": "", "apply": "Applica", "apply#description": "", "applyLong": "Applica {{subject}}", "applyLong#description": "", "backLong": "torna a {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "<PERSON><PERSON><PERSON> {{subject}}", "cancelLong#description": "", "clear": "Azzera", "clear#description": "", "confirm": "Conferma", "confirm#description": "", "continue": "Continua", "continue#description": "", "copy": "Copia", "copy#description": "", "copyLong": "Copia {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} creato", "createdLong#description": "", "delete": "Elimina", "delete#description": "", "deleteLong": "Elimina {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} eliminato", "deletedLong#description": "", "disableLong": "Disattiva {{subject}}", "disableLong#description": "", "discard": "Scarta", "discard#description": "", "edit": "Modifica", "edit#description": "", "editLong": "Modifica {{subject}}", "editLong#description": "", "enableLong": "Attiva {{subject}}", "enableLong#description": "", "exit": "<PERSON><PERSON><PERSON>", "exit#description": "", "exitLong": "<PERSON><PERSON>ci da {{subject}}", "exitLong#description": "", "goToLong": "Vai a {{subject}}", "goToLong#description": "", "invite": "Invi<PERSON>", "invite#description": "", "inviteLong": "Invita {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} invitato", "invitedLong#description": "", "leaveUnchanged": "<PERSON><PERSON> invariato", "leaveUnchanged#description": "", "new": "Nuovo", "new#description": "", "newLong": "Nuovo {{subject}}", "newLong#description": "", "next": "Successivo", "next#description": "", "pause": "Pausa", "pause#description": "", "play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "play#description": "", "previous": "Precedente", "previous#description": "", "ranLong": "{{subject}} eseguito", "ranLong#description": "", "reload": "Ricarica", "reload#description": "", "resetLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "resetLong#description": "", "retry": "<PERSON><PERSON><PERSON><PERSON>", "retry#description": "", "run": "<PERSON><PERSON><PERSON><PERSON>", "run#description": "", "runLong": "Esegui {{subject}}", "runLong#description": "", "save": "<PERSON><PERSON>", "save#description": "", "saveLong": "<PERSON><PERSON> {{subject}}", "saveLong#description": "", "saved": "Sal<PERSON><PERSON>", "saved#description": "", "savedLong": "{{subject}} salvato", "savedLong#description": "", "search": "Cerca", "search#description": "", "searchLong": "Cerca {{subject}}", "searchLong#description": "", "selectAll": "Se<PERSON><PERSON>na tutti", "selectAll#description": "", "selectLong": "Seleziona {{subject}}", "selectLong#description": "", "selectNone": "Seleziona nessuno", "selectNone#description": "", "send": "Invia", "send#description": "", "showLong": "Mostra {{subject}}", "showLong#description": "", "submit": "Invia", "submit#description": "", "toggle": "Attiva/Disattiva", "toggle#description": "", "toggleLong": "Attiva/Disattiva {{subject}}", "toggleLong#description": "", "update": "Aggiorna", "update#description": "", "updated": "Aggiornato", "updated#description": "", "updatedLong": "{{subject}} aggiornato", "updatedLong#description": "", "uploaded": "Caricato", "uploaded#description": "", "viewLong": "Visualizza {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Attivo", "active#description": "", "critical": "<PERSON><PERSON><PERSON>", "critical#description": "", "default": "Standard", "default#description": "", "degraded": "Deteriorato", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "Disattivat<PERSON>", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "<PERSON><PERSON><PERSON><PERSON>", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "Errore", "error#description": "", "estopOff": "In funzione", "estopOff#description": "", "estopOn": "In arresto di emergenza", "estopOn#description": "", "fast": "Veloce", "fast#description": "", "few": "Pochi", "few#description": "", "good": "<PERSON><PERSON><PERSON>", "good#description": "", "hidden": "Nascosto", "hidden#description": "", "high": "Elevato", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inattivo", "inactive#description": "", "interlockSafe": "<PERSON><PERSON>", "interlockSafe#description": "", "interlockUnsafe": "Sparo non consentito", "interlockUnsafe#description": "", "large": "Grande", "large#description": "", "laserKeyOff": "Bloccato", "laserKeyOff#description": "", "laserKeyOn": "Inserito", "laserKeyOn#description": "", "liftedOff": "<PERSON><PERSON><PERSON>", "liftedOff#description": "", "liftedOn": "<PERSON>za<PERSON>", "liftedOn#description": "", "loading": "Caricamento in corso", "loading#description": "", "low": "<PERSON><PERSON>", "low#description": "", "majority": "Maggioranza", "majority#description": "", "medium": "Medio", "medium#description": "", "minority": "Minoranza", "minority#description": "", "name": "Nome", "name#description": "", "no": "No", "no#description": "", "none": "<PERSON><PERSON><PERSON>", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "poor": "Scarso", "poor#description": "", "progress": "Avanzamento", "progress#description": "", "serial": "Numero seriale", "serial#description": "", "slow": "<PERSON><PERSON>", "slow#description": "", "small": "<PERSON><PERSON><PERSON>", "small#description": "", "sparse": "<PERSON><PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_many": "", "type_one": "Tipo", "type_other": "T<PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "waterProtectNormal": "Umidità normale", "waterProtectNormal#description": "", "waterProtectTriggered": "Ac<PERSON> rilevata", "waterProtectTriggered#description": "", "yes": "Sì", "yes#description": ""}, "form": {"booleanType": "<PERSON>e essere un booleano", "booleanType#description": "", "copyConfigFrom": "Copia configurazione da...", "copyConfigFrom#description": "", "integerType": "Deve essere un numero intero", "integerType#description": "", "maxLessThanMin": "Il massimo deve essere maggiore del minimo", "maxLessThanMin#description": "", "maxSize": "Non può superare {{limit}} caratteri", "maxSize#description": "", "minGreaterThanMax": "Il minimo deve essere inferiore al massimo", "minGreaterThanMax#description": "", "moveDown": "<PERSON><PERSON><PERSON> in basso", "moveDown#description": "", "moveUp": "<PERSON><PERSON><PERSON> in alto", "moveUp#description": "", "noOptions": "<PERSON><PERSON>un <PERSON>", "noOptions#description": "", "numberType": "Deve essere un numero", "numberType#description": "", "optional": "(facoltativo)", "optional#description": "", "required": "Obbligatorio", "required#description": "", "stringType": "Deve essere una stringa", "stringType#description": ""}, "lists": {"+3": "{{b}} e {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} e {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Carica di più", "loadMore#description": "", "noMoreResults": "<PERSON><PERSON><PERSON> al<PERSON> ris<PERSON>ato", "noMoreResults#description": "", "noResults": "<PERSON><PERSON><PERSON> r<PERSON>", "noResults#description": ""}, "metrics": {"aggregates": {"max": "Max", "max#description": "", "min": "Min", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Raggio medio colture", "avgCropSizeMm#description": "", "avgSpeedMph": "Velocità di marcia media", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Tempo medio di colpo", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Tempo medio di colpo (senza target)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Raggio medio erbe infestanti", "avgWeedSizeMm#description": "", "bandingConfigName": "Config. distribuzione in bande", "bandingConfigName#description": "", "bandingEnabled": "Distribuzione in bande", "bandingEnabled#description": "", "bandingPercentage": "Percentuale distribuita in bande", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Velocità di copertura media", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Densità della coltura", "cropDensitySqFt#description": "", "distanceWeededMeters": "Distanza diserbo", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Colture conservate", "keptCrops#description": "", "killedWeeds": "Erbe infestanti eliminate", "killedWeeds#description": "", "missedCrops": "Colture mancate", "missedCrops#description": "", "missedWeeds": "Erbe infestanti mancate", "missedWeeds#description": "", "notThinning": "Colture senza diradamento", "notThinning#description": "", "notWeeding": "Erbe infestanti senza diserbo", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Efficacia dell'operatore", "operatorEffectiveness#description": "", "overallEfficiency": "Prestazione complessiva", "overallEfficiency#description": "", "skippedCrops": "Colture ignorate", "skippedCrops#description": "", "skippedWeeds": "Erbe infestanti ignorate", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Tempo di Diserbo Obiettivo", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Colture diradate", "thinnedCrops#description": "", "thinningEfficiency": "Prestazione di diradamento", "thinningEfficiency#description": "", "timeEfficiency": "Efficienza operativa", "timeEfficiency#description": "", "totalCrops": "<PERSON><PERSON> trovate (totale)", "totalCrops#description": "", "totalWeeds": "Erbe infestanti trovate", "totalWeeds#description": "", "totalWeedsInBand": "Erbe infestanti trovate (nella banda)", "totalWeedsInBand#description": "", "uptimeSeconds": "Tempo di operatività", "uptimeSeconds#description": "", "validCrops": "<PERSON><PERSON> trovate", "validCrops#description": "", "weedDensitySqFt": "Densità delle erbe infestanti", "weedDensitySqFt#description": "", "weedingEfficiency": "Prestazione di diserbo", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Tempo di diserbo", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Tipo di erba infestante: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Tipo di erba infestante: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Tipo di erba infestante: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Tipo di erba infestante: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Questo viene calcolato prima del diradamento, se il diradamento era abilitato", "avgCropSizeMm#description": "", "bandingConfigName": "Il tuo profilo di banding selezionato più di recente", "bandingConfigName#description": "", "crop": "L'ultima coltura che hai selezionato", "crop#description": "", "cropDensitySqFt": "Questo viene calcolato prima del diradamento, se il diradamento era abilitato", "cropDensitySqFt#description": "", "keptCrops": "La quantità stimata di colture conservate dopo il diradamento", "keptCrops#description": "", "killedWeeds": "LaserWeeder ha identificato l'oggetto come un'erbaccia e l'ha colpita", "killedWeeds#description": "", "missedCrops": "La coltura era stata contrassegnata per il diradamento, ma l'operazione non è stata eseguita. Le ragioni più comuni sono: velocità troppo alta, fuori portata o errore del sistema.", "missedCrops#description": "", "missedWeeds": "L'erba infestante è stata riconosciuta ma non colpita. Le ragioni più comuni sono: velocità troppo alta, fuori portata o errore del sistema.", "missedWeeds#description": "", "operatorEffectiveness": "Indica quanto la velocità reale di spostamento si è avvicinata alla velocità obiettivo suggerita dal Velocity Estimator", "operatorEffectiveness#description": "", "overallEfficiency": "(Efficacia del diserbo + Efficacia del diradamento) / 2, se si diserba e si dirada contemporaneamente", "overallEfficiency#description": "", "skippedCrops": "La coltura è stata saltata di proposito durante il diradamento. Le ragioni più comuni sono: disattivato in Quick Tune, fuori banda o vicino al tubo per l'irrigazione a goccia.", "skippedCrops#description": "", "skippedWeeds": "L'erba infestante è stata volutamente ignorata. Le ragioni più comuni sono: disattivato in Quick Tune o fuori banda.", "skippedWeeds#description": "", "thinningEfficiency": "(Colture Diradate + <PERSON><PERSON> Mantenute) / Colture Stimate Trovate × 100%", "thinningEfficiency#description": "", "timeEfficiency": "(Tempo di Lavoro Attivo / Tempo di Accensione) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "La quantità totale di tempo in cui LaserWeeder è stato acceso. Incluso il tempo trascorso in modalità standby e/o sollevato.", "uptimeSeconds#description": "", "weedDensitySqFt": "Stima erbe infestanti trovate (totale) / Copertura", "weedDensitySqFt#description": "", "weedingEfficiency": "(Erbe infestanti eliminate / Erbe infestanti rilevate nella banda) × 100%", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Il tempo in cui il LaserWeeder ha lavorato attivamente per diserbare o diradare", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Efficienza in termini di velocità", "operatorEffectiveness#description": "", "timeEfficiency": "<PERSON><PERSON><PERSON><PERSON>", "timeEfficiency#description": "", "totalWeeds": "Erbe infestanti trovate (totale)", "totalWeedsInBand": "Erbe infestanti trovate (nella banda)", "totalWeedsInBand#description": "", "uptimeSeconds": "Tempo di accensione", "uptimeSeconds#description": "", "validCrops": "Numero stimato di colture individuate", "validCrops#description": "", "weedingUptimeSeconds": "Tempo di funzionamento effettivo", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "<PERSON><PERSON><PERSON>", "coverage#description": "", "field": "Campo", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Prestazione", "performance#description": "", "speed": "Velocità", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "<PERSON><PERSON><PERSON><PERSON>", "usage#description": ""}, "metric#description": "", "metric_many": "", "metric_one": "metrica", "metric_other": "metriche", "spatial": {"heatmapWarning": "per ~{{area}} 'blocco'", "heatmapWarning#description": "", "metrics": {"altitude": "Altit<PERSON><PERSON>", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "E-Stop", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Interblocco", "interlock#description": "", "keptCropDensity": "Densità coltura mantenuta", "keptCropDensity#description": "", "laserKey": "Chiave laser", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Rapporto di velocità target", "speed#description": "", "speedTargetMinimum": "Velocità target media (minima)", "speedTargetMinimum#description": "", "speedTargetRow1": "Velocità target media (Fila 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Velocità target media (Fila 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Velocità target media (Fila 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Velocità target media", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Diradamento", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "<PERSON>a", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Protezione acqua", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Diserbo", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Selezionata/e", "selected#description": "", "showAll": "<PERSON>ra tutti {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_many": "", "WLong_one": "watt", "WLong_other": "watt", "ac": "ac", "ac#description": "", "ac/h": "ac/h", "ac/h#description": "", "acLong#description": "", "acLong_many": "", "acLong_one": "acro", "acLong_other": "acri", "acres#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "d": "g", "d#description": "", "dLong#description": "", "dLong_many": "", "dLong_one": "<PERSON>ior<PERSON>", "dLong_other": "<PERSON>ior<PERSON>", "day#description": "", "days#description": "", "ft": "ft", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_many": "", "ftLong_one": "piede", "ftLong_other": "piedi", "h": "h", "h#description": "", "hLong#description": "", "hLong_many": "", "hLong_one": "ora", "hLong_other": "ore", "ha": "ha", "ha#description": "", "ha/h": "ha/h", "ha/h#description": "", "haLong#description": "", "haLong_many": "", "haLong_one": "ettaro", "haLong_other": "ettari", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_many": "", "mLong_one": "metro", "mLong_other": "metri", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_many": "", "minLong_one": "minuto", "minLong_other": "minuti", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "m.", "month#description": "", "monthLong#description": "", "monthLong_many": "", "monthLong_one": "mese", "monthLong_other": "mesi", "mph": "mph", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_many": "", "sLong_one": "secondo", "sLong_other": "secondi", "seconds#description": "", "watts#description": "", "week": "s.", "week#description": "", "weekLong#description": "", "weekLong_many": "", "weekLong_one": "<PERSON><PERSON><PERSON>", "weekLong_other": "<PERSON><PERSON><PERSON><PERSON>", "yd#description": "", "year": "a.", "year#description": "", "yearLong#description": "", "yearLong_many": "", "yearLong_one": "anno", "yearLong_other": "anni"}}, "views": {"admin": {"alarms": {"allowWarning": "L'aggiunta di codici alla allowlist consentirà agli allarmi di effettuare il ping dei canali Slack di supporto.", "allowWarning#description": "", "blockWarning": "L'aggiunta di codici alla blocklist impedirà agli allarmi di effettuare il ping dei canali Slack di supporto.", "blockWarning#description": "", "lists": "<PERSON><PERSON><PERSON>", "lists#description": "", "title": "Elenco Globale Allarmi Consentiti", "title#description": "", "titleAllow": "Elenco Allarmi Consentiti", "titleAllow#description": "", "titleBlock": "Elenco Allarmi Bloccati", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Imposta", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> su <PERSON>, <tt>{row1,row2,row3}</tt> su <PERSON>", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_many": "", "operation_one": "operazione", "operation_other": "operazioni", "operationsCount": "Operazioni ({{count}})", "operationsCount#description": "", "operationsHint": "Per aggiungere un'operazione, seleziona un nodo nello schema di configurazione.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_many": "", "encounteredErrors_one": "è stato rilevato {{count}} errore", "encounteredErrors_other": "sono stati rilevati {{count}} errori", "noChanges": "nessuna modifica", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_many": "", "updatedKeys_one": "{{count}} chiave a<PERSON>", "updatedKeys_other": "{{count}} chiavi a<PERSON>"}, "outcomes": {"failure": "Non riuscite", "failure#description": "", "partial": "Successo par<PERSON>", "partial#description": "", "success": "Successo", "success#description": ""}, "title": "Configurazioni di massa", "title#description": ""}, "clearCaches": {"action": "Aggiorna la cache", "action#description": "", "description": "Problemi? Prova prima ad aggiornare la cache di Robot Syncer.", "description#description": ""}, "warnings": {"global": "La modifica apportata a questa configurazione avrà effetto sulle impostazioni predefinite e sulle raccomandazioni per tutti gli elementi {{class}} attuali e futuri.", "global#description": "", "notSimon": "Non sei Simon o <PERSON>, quindi probabilmente non dovresti modificare questo... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Le seguenti modifiche non sono ancora state sincronizzate con {{serial}}:", "description#description": "", "title": "Chiavi non sincronizzate", "title#description": ""}}}, "portal": {"clearCaches": {"action": "Cancella cache", "action#description": "", "description": "Cancella le cache interne di Ops Center. Quest'azione **rallenterà** alcune query nel breve termine, ma **potrebbe** risolvere i problemi con i dati bloccati e non aggiornati.", "description#description": "", "details": "Premi qui se hai modificato manualmente i permessi di un utente in Auth0 (non tramite Ops Center), o hai apportato modifiche a un'integrazione di terze parti come Stream o Slack che non si vedono.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Le opzioni di questa pagina influiscono sul funzionamento di Ops Center in produzione.", "global#description": "", "notPortalAdmin": "Non sei Ansel o <PERSON>, quindi probabilmente non dovresti modificare questo... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Il canale Slack Support deve iniziare con “#”: ad esempio, “#support-001-carbon”.", "supportSlackLeadingHash#description": ""}}, "title": "Amministrazione", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Nascondi la cronologia Pivot", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Assegna ai trattori", "orchestrateView#description": "", "showPivotHistory": "Mostra la cronologia Pivot", "showPivotHistory#description": ""}, "fetchFailed": "Impossibile caricare i dati della posizione", "fetchFailed#description": "", "goLive": "aggiornamento live", "goLive#description": "", "hideRows": "Nascondi file", "hideRows#description": "", "jobDetails": {"assignmentsFailed": "Impossibile recuperare l'assegnazione, riprovare?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Il lavoro non potrà più essere assegnato ai trattori e dovrà essere creato di nuovo.", "description#description": ""}, "customer": {"unknown": "<PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Cliente: {{name}}", "withName#description": ""}, "farm": {"unknown": "Azienda agricola sconosciuta", "unknown#description": "", "withName": "Azienda agricola: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON> s<PERSON>", "unknown#description": "", "withName": "Campo: {{name}}", "withName#description": ""}, "jobFinished": "Lavoro terminato alle {{time}}", "jobFinished#description": "", "jobStarted": "<PERSON><PERSON>o iniziato alle {{time}}", "jobStarted#description": "", "openInFarmView": "Apri nella vista azienda agricola", "openInFarmView#description": "", "state": "Stato: {{state}}", "state#description": "", "type": "Tipo di lavoro: {{type}}", "type#description": ""}, "lastPolled": "Ultima rilevazione", "lastPolled#description": "", "live": "Live", "live#description": "", "objectiveFromOtherJob": "Obiettivo da un altro lavoro", "objectiveFromOtherJob#description": "", "rowWidthUnits": "<PERSON>rg<PERSON>zza fila {{units}}.", "rowWidthUnits#description": "", "selection": {"farms": "Aziende agricole", "farms#description": "", "tractors": "<PERSON><PERSON><PERSON><PERSON>", "tractors#description": ""}, "showRows": "Mostra file", "showRows#description": "", "stalePivots": "Le informazioni pivot potrebbero essere obsolete", "stalePivots#description": "", "suggestedAssignments": "Assegnazioni consigliate", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"hideThesePoints": "Nascondi questi punti", "hideThesePoints#description": "", "onlyShowSelected": "Mostra solo i selezionati", "onlyShowSelected#description": "", "showAllPoints": "Mostra tutti i punti", "showAllPoints#description": "", "showThesePoints": "Mostra questi punti", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Confine", "boundary#description": "", "center": "Centro", "center#description": "", "centerPivot": "Pivot centrale", "centerPivot#description": "", "endpointId": "Id endpoint", "endpointId#description": "", "holes": "<PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Direzione di semina", "plantingHeading#description": "", "point": "Punt<PERSON>", "point#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON><PERSON>", "width#description": ""}, "farm": "Azienda agricola", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "N<PERSON>un fix GPS", "none#description": "", "rtkFixed": "RTK Fisso", "rtkFixed#description": "", "rtkFloat": "RTK Fluttuante", "rtkFloat#description": "", "unknown": "Tipo di fix sconos<PERSON>uto", "unknown#description": ""}, "selectionPanel": {"allPoints": "<PERSON><PERSON> i punti", "allPoints#description": "", "boundary": "Confine", "boundary#description": "", "center": "Centro", "center#description": "", "centerPivot": "Pivot centrale", "centerPivot#description": "", "endpointId": "Id endpoint", "endpointId#description": "", "holes": "<PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Direzione di semina", "plantingHeading#description": "", "point": "Punt<PERSON>", "point#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "width": "<PERSON><PERSON><PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "Punto senza nome <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "Confini dell'azienda agricola", "farmBoundary#description": "", "field": "Campo", "field#description": "", "headland": "Testata", "headland#description": "", "obstacle": "Ostacolo", "obstacle#description": "", "privateRoad": "Strada privata", "privateRoad#description": "", "unknown": "Tipo di zona sconosciuto", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "Disegna", "draw#description": ""}, "errors": {"exactlyTwoPoints": "La linea deve includere solo due punti", "exactlyTwoPoints#description": "", "wrongFieldType": "Il campo \"{{field}}\" dovrebbe essere {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "La forma geometrica dovrebbe essere del tipo {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON dovrebbe essere un oggetto", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Nessun robot online", "empty#description": ""}, "title": "Controllo Missione", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Visualizza la cronologia delle modifiche", "open#description": "", "title": "Cronologia delle modifiche", "title#description": ""}, "errors": {"failed": "Impossibile caricare l'albero di configurazione", "failed#description": ""}, "onlyChanged": "Mostra solo le modifiche apportate", "onlyChanged#description": ""}, "errors": {"empty": "Nessun robot assegnato", "empty#description": ""}, "hardware": {"errors": {"old": "Il robot non sta comunicando i seriali del computer (probabilmente perché è troppo vecchio).", "old#description": ""}, "fields": {"hostname": "Nome dell'host", "hostname#description": ""}, "installedVersion": "Versione installata:", "installedVersion#description": "", "ready": {"name": "Stato di aggiornamento del software", "name#description": "", "values": {"false": "Download in corso...", "false#description": "", "installed": "Installato", "installed#description": "", "true": "Pronto!", "true#description": ""}}, "tabs": {"computers": "Computer", "computers#description": "", "versions": "Versioni", "versions#description": ""}, "targetVersion": "Versione obiettivo:", "targetVersion#description": "", "title": "Hardware", "title#description": "", "updateHistory": "Cronologia degli aggiornamenti della versione <0>presto disponibile™️</0>", "updateHistory#description": ""}, "history": {"borders": "<PERSON><PERSON>", "borders#description": "", "errors": {"invalidDate": "Seleziona un intervallo di date valido", "invalidDate#description": "", "noJobs": "<PERSON>essun lavoro segnalato nella fascia selezionata", "noJobs#description": "", "noMetrics": "<PERSON><PERSON><PERSON> valore misurato", "noMetrics#description": ""}, "moreMetrics": "<PERSON><PERSON><PERSON>", "moreMetrics#description": "", "navTitle": "Cronologia", "navTitle#description": "", "placeholder": "Seleziona un lavoro o una data per visualizzare i dati corrispondenti", "placeholder#description": "", "points": "<PERSON><PERSON><PERSON>", "points#description": "", "warnings": {"beta": {"description": "Le metriche in attesa di convalida sono indicate in blu", "description#description": ""}, "ongoing": "Le metriche relative a questa data non sono ancora definitive", "ongoing#description": ""}}, "status": "Stato", "status#description": "", "summary": {"banding": {"definition": "Definizione", "definition#description": "", "dynamic": "Dinamico", "dynamic#description": "", "dynamicDisabled": "(Banding dinamico disabilitato nella configurazione)", "dynamicDisabled#description": "", "rows": "File", "rows#description": "", "static": "Statico", "static#description": "", "type": "Tipo", "type#description": "", "unknown": "Banding sconosciuto", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Versione", "version#description": ""}, "config": {"changes#description": "", "changes_many": "", "changes_one": "{{count}} modifica dell'almanacco", "changes_other": "{{count}} modifiche dell'almanacco", "cpt": "Soglia di Rilevamento della Coltura", "cpt#description": "", "default": "(PREDEFINITO: {{value}})", "default#description": "", "wpt": "Soglia di Rilevamento delle Erbe Infestanti", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON> sinistra", "backLeft#description": "", "backRight": "<PERSON><PERSON><PERSON>", "backRight#description": "", "frontLeft": "Anteriore sinistra", "frontLeft#description": "", "frontRight": "Anteriore destra", "frontRight#description": "", "title": "Encoder delle ruote", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Impossibile caricare il resoconto del robot", "failed#description": "", "lasers": {"disabled#description": "", "disabled_many": "", "disabled_one": "{{count}} laser disattivato", "disabled_other": "{{count}} laser disattivati", "row": "Fila {{row}}", "row#description": ""}, "machineHealth": "Condizioni della macchina", "machineHealth#description": "", "navTitle": "Riepilogo", "navTitle#description": "", "safetyRadius": {"driptape": "<PERSON><PERSON>", "driptape#description": "", "title": "Raggio di sicurezza", "title#description": ""}, "sections": {"management": "Gestione", "management#description": "", "software": "Software", "software#description": ""}, "supportLinks": {"chipChart": "Grafico \"Chip\"", "chipChart#description": "", "datasetVisualization": "Rappresentazione grafica dei dati", "datasetVisualization#description": "", "title": "<PERSON> assist<PERSON>za", "title#description": ""}}, "support": {"carbon": "Assistenza Carbon", "carbon#description": "", "chatMode": {"legacy": "Chat vecchia versione", "legacy#description": "", "new": "Nuova chat", "new#description": ""}, "errors": {"failed": "Impossibile caricare il messaggio", "failed#description": "", "old": {"description": "{{serial}} sta utilizzando la versione {{version}} del software. Per poter utilizzare la chat di assistenza deve essere la {{target}}", "description#description": "", "title": "Versione del robot insufficiente", "title#description": ""}}, "localTime": "Ora locale: {{time}}", "localTime#description": "", "navTitle": "Assistenza", "navTitle#description": "", "toCarbon": "Messaggio a $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Messaggio a $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} è offline. L'operatore non riceverà il messaggio finché il robot non si connetterà.", "description#description": "", "title": "Robot offline", "title#description": ""}}}, "toggleable": {"internal": "Interno", "internal#description": ""}, "uploads": {"errors": {"empty": "<PERSON><PERSON> da caricare", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "Nome del filtro", "name#description": "", "otherRobots": "<PERSON><PERSON> robot ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Robot fissati", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "Schede", "cards#description": "", "table": "<PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_many": "", "fleetView_one": "filtro", "fleetView_other": "filtri", "tableOnly": "Alcune colonne sono disponibili solo nella vista tabella", "tableOnly#description": ""}}, "knowledge": {"title": "Base di conoscenza", "title#description": ""}, "metrics": {"jobStatus": {"closed": "<PERSON><PERSON><PERSON>", "closed#description": "", "description": "Stato del lavoro", "description#description": "", "open": "Aperto", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Metriche stimate del campo", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Il nostro modello utilizza dati sperimentali sulle colture che potrebbero contenere imprecisioni. Miglioriamo costantemente la sua affidabilità.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Prestazioni e metriche della macchina", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Trascina qui i file da USB (o da qualsiasi altra posizione)", "drop#description": "", "file#description": "", "file_many": "", "file_one": "file", "file_other": "file", "ingestDescription": "I dipendenti Carbon devono usare il servizio Ingest", "ingestDescription#description": "", "ingestLink": "Carica su Ingest", "ingestLink#description": "", "select": "Seleziona i file", "select#description": "", "title": "Carica", "title#description": "", "upload": "Carica su Carbon", "upload#description": "", "uploading": "Caricamento di {{subject}} in corso...", "uploading#description": ""}, "reports": {"explore": {"graph": "Grafico", "graph#description": "", "groupBy": "Raggruppa per", "groupBy#description": "", "title": "Esplora", "title#description": ""}, "scheduled": {"authorCarbonBot": "Bot Carbon", "authorCarbonBot#description": "", "authorUnknown": "<PERSON><PERSON>", "authorUnknown#description": "", "automation": {"customerReports": "Report clienti", "customerReports#description": "", "errorTitle": "Report automatico non valido", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Nessun cliente selezionato", "none#description": ""}}, "reportDay": {"errors": {"none": "<PERSON><PERSON><PERSON> giorno <PERSON>", "none#description": ""}, "name": "Gior<PERSON>", "name#description": ""}, "reportEmails": {"errors": {"none": "Nessuna email assegnata", "none#description": ""}, "name": "Email clienti", "name#description": ""}, "reportHour": {"errors": {"none": "Nessuna ora selezionata", "none#description": ""}, "name": "Ora del report", "name#description": ""}, "reportLookback": {"errors": {"none": "<PERSON><PERSON><PERSON> periodo di lookback definito", "none#description": ""}, "name": "Periodo di lookback del report", "name#description": ""}, "reportTimezone": {"errors": {"none": "Nessun fuso orario selezionato", "none#description": ""}, "name": "Fuso orario del report", "name#description": ""}, "warningDescription": "Viene eseguito ogni {{day}} alle {{hour}} {{timezone}} con {{lookback}} giorni di lookback per tutti i robot {{customer}} attivi.", "warningDescription#description": "", "warningTitle": "Questo è un report automatico!", "warningTitle#description": ""}, "byline": "Da {{author}}", "byline#description": "", "editor": {"columnsHidden": "Colonne nascoste", "columnsHidden#description": "", "columnsVisible": "Colonne visibili", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_many": "", "duplicateNames_one": "Attenzione: c'è un altro rapporto con questo nome", "duplicateNames_other": "Attenzione: ci sono altri {{count}} rapporti con questo nome", "fields": {"automateWeekly": "Automatizza settimanalmente", "automateWeekly#description": "", "name": "Nome del report", "name#description": "", "showAverages": "Mostra medie", "showAverages#description": "", "showTotals": "Mostra totali", "showTotals#description": ""}}, "errors": {"noReport": "Il report non esiste o non puoi accedervi", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} sarà eliminato definitivamente.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "Non sei autorizzato a eliminare {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "L'email non sarà reinviata", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Il report sarà inviato a queste email", "publishEmailsHelperNew#description": ""}, "runAgain": "Esegui di nuovo", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Seleziona una o più colonne", "noColumns#description": "", "noEndDate": "Seleziona una data di fine", "noEndDate#description": "", "noRobots": "Seleziona uno o più robot", "noRobots#description": "", "noStartDate": "Seleziona una data di inizio", "noStartDate#description": ""}, "fields": {"average": "Media", "average#description": "", "averageShort": "MED", "averageShort#description": "", "date": "Data", "date#description": "", "group": "Seriale/Data", "group#description": "", "groupJob": "Seriale/Lavoro", "groupJob#description": "", "mixed": "filtri", "mixed#description": "", "total": "Totale", "total#description": "", "totalShort": "SOM", "totalShort#description": ""}, "unknownReport": "Report sconosciuto", "unknownReport#description": ""}, "title": "Programmato", "title#description": "", "toLine": "per {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "<PERSON><PERSON> le <PERSON>", "all#description": "", "select": "Seleziona metrica", "select#description": ""}, "robotsLabel": {"all": "Tutti i robot", "all#description": "", "none": "<PERSON><PERSON><PERSON> robot", "none#description": "", "select": "Seleziona i robot", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> tramite <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "nome utente e password", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "Provider <PERSON>", "unknown#description": ""}, "cards": {"account": "Account", "account#description": "", "advanced": "Avanzate", "advanced#description": "", "localization": "Localizzazione", "localization#description": ""}, "delete": {"deleteAccount": "Elimina account", "deleteAccount#description": "", "dialog": {"description": "ATTENZIONE: questa azione non può essere annullata. <PERSON>tti i dati and<PERSON>no persi.", "description#description": ""}}, "fields": {"experimental": "Attiva le funzioni sperimentali", "experimental#description": "", "language": "<PERSON><PERSON>", "language#description": "", "measurement": {"name": "Unità di misura", "name#description": "", "values": {"imperial": "Imperiale (in, mph, acri, Fahrenheit)", "imperial#description": "", "metric": "Metrico (mm, km/h, ettari, Celsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "<PERSON>nnetti<PERSON>", "logOut#description": "", "title": "Impostazioni", "title#description": "", "version": "Versione Carbon Ops Center {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "L'utente non esiste, oppure non sei autorizzato a visualizzarlo.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Gestisci l'utente in Auth0", "manage#description": "", "title": "Amministrazione", "title#description": ""}, "permissions": {"title": "Ruoli e Autorizzazioni", "title#description": ""}, "profile": {"title": "<PERSON>ilo", "title#description": ""}}, "toggleable": {"contractors": "Appaltatore", "contractors#description": ""}}}}