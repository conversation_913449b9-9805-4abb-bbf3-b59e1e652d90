{"components": {"AlarmTable": {"export": "{{robots}} Alarm History {{date}}", "export#description": "The file name of the exported CSV file that contains information about different alarms/alerts that have occurred on a given robot"}, "BetaFlag": {"spatial": {"description": "Spatial Metrics are available for free during evaluation but may be subject to change, removal, or plan upgrade requirement at any time. Data should be independantly verified.", "description#description": "Spatial Data is another term for a heatmap. A heatmap is a representation of data in the form of a map or diagram in which data values are represented as colors. \n\nThis message is shown to users because the Spatial Data feature still needs to be tested, which is why it is in Beta.\n\nBeta is a term used in software development, which describes when the software is feature-complete but likely to contain several known or unknown bugs.", "title": "Spatial Metrics Beta", "title#description": "Spatial Metrics as in Geospatial Data. Beta being the reliability state of the feature (alpha vs beta)"}, "tooltip": "This feature is in evaluation and may be subject to change or removal at any time", "tooltip#description": "A disclaimer that tells the user that a given piece of functionality in the app is experimental"}, "Chat": {"errors": {"failed": "<PERSON><PERSON> failed to load: {{message}}", "failed#description": "Error message when the chat system completely fails to load"}, "machineTranslated": "Machine-translated", "machineTranslated#description": "A notice shown on a message that has been automatically translated by a machine from a different language. We show this in case the translation is incorrect or confusing, so that users know that there may be translation issues in play.", "machineTranslatedFrom": "Machine-translated from {{language}}", "machineTranslatedFrom#description": "A notice shown on a message that has been automatically translated by a machine from a different language. For example, if a user speaks English but receives a message in German, we might automatically try to translate it and annotate it as, \"Machine-translated from German\". We show this in case the translation is incorrect or confusing, so that users know that there may be translation issues in play.", "messageDeleted": "This message was deleted.", "messageDeleted#description": "Text shown in place of message content if that message was deleted by the sender"}, "ConfirmationDialog": {"delete": {"description": "{{subject}} will be permanently deleted.", "description#description": "A generic warning that something will create a irreversible destructive action", "descriptionActive": "{{subject}} is active so it cannot be deleted.", "descriptionActive#description": "a message that something in question is being used and therefore deleting it would be a problem to our system"}, "title": "Are you sure?", "title#description": "A question of whether the user is doing something intentionally? It is usually paired with a \"yes\" or \"no\" type of answer"}, "CopyToClipboardButton": {"click": "Click to copy", "click#description": "A tooltip description on a copy icon that helps the user understand that if they click it, the value will be copied to their computers's clipboard", "copied": "Copied!", "copied#description": "Refering to the command which allows the user to create an identical copy of a file or a set of files"}, "CropEditor": {"failed": "Failed to load crop editor", "failed#description": "An alert that shows up if the UI fails to load the code to show the crop editor. The crop editor here is a component that allows a user to see their agricultural crop and make changes to it", "viewIn": "View in Veselka", "viewIn#description": "A link to view a list of models in another internal service called 'Veselka'"}, "DateRangePicker": {"clear": "Clear", "clear#description": "button used to reset the date range selection", "endDate": "End Date", "endDate#description": "In a date range selector, the \"end date\" is the top end of the date range.", "error": "Date range picker error", "error#description": "A generic message shown if there is an issue with a date input that a user selects to select a date range for something", "invalid": "Invalid", "invalid#description": "Not an acceptable input for a date range", "last7days": "Last 7 Days", "last7days#description": "a date range picker options to filter by the last 7 days (which is distinctly different than the last week)", "lastMonth": "Last Month", "lastMonth#description": "not this month, but last month", "lastWeek": "Last Week", "lastWeek#description": "not this week, but last week.", "minusDays": "{{days}} days ago", "minusDays#description": "how many days ago to set a date range picker to filter on", "plusDays": "in {{days}} days", "plusDays#description": "how many days in the future to set a date range picker to filter on", "startDate": "Start Date", "startDate#description": "The beginning date in a date range input", "thisMonth": "This Month", "thisMonth#description": "not this month but last month", "thisWeek": "This Week", "thisWeek#description": "The current week relative to today", "today": "Today", "today#description": "The current day it is", "tomorrow": "Tomorrow", "tomorrow#description": "a day from now.", "yesterday": "Yesterday", "yesterday#description": "a day before today"}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "A little indicator that a feature is in \"beta\" mode. As in - it is ready to be seen and used but should not be expected to be fully dependable yet. This comes from greek \"alpha\" and \"beta\" which are used in software development to indicate how ready a product is. \"alpha\" being not trustworthy. \"beta\" being more trustworthy but still not quite ready.", "dev": "DEV", "dev#description": "This is an internal flag that won't be seen by end users -- only our software developers. the \"DEV\" stands for \"developer\" as in software developer"}, "ErrorBoundary": {"error": "Sorry, unexpected error", "error#description": "A general catch-all for errors that may appear in the app.", "queryLimitReached": "Rendering partial dataset because too much data was returned. Contact support for assistance", "queryLimitReached#description": "A message that shows up if the user has requested too much data and the system can't handle it. It's a way to prevent the system from crashing"}, "FeedbackDialog": {"comment": "What happened?", "comment#description": "A prompt to the user to tell us what kind of issues they have seen in the app.", "feedback": "<PERSON><PERSON><PERSON>", "feedback#description": "A title for a dialog that is presented to the user if there is a problem with the app. We are requesting information about what they saw go wrong so we can fix it", "submit": "Submit and Reload", "submit#description": "A button that is pressed to submit manually written feedback and trigger a reload of the webpage"}, "GdprConsent": {"description": "Please review and agree to continue", "description#description": "Asking the user to look at our legalese and confirm that they are good with it. If they agree, then they can continue onto getting access to the app.", "statement": "I agree to the <0>Terms of Use</0> and <1>Privacy Policy</1>", "statement#description": "A statement by the user signifying that they will comply with our requirements to use the app. The <0> and </0> and the <1> and </1> should be copied over into the translations as is, wrapping the translated versions of what is inside of those tags", "title": "Terms of Use and Privacy Policy", "title#description": "Legal description of how we expect our users to use the app and what rights they have to privacy and their data"}, "InviteUser": {"errors": {"customerRequired": "Customer Required", "customerRequired#description": "A message that shows up if a user tries to invite another user to our system but doesn't specify which customer they are associated with"}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "a date range", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": "simply a date range. The dash is to signify the range of time where it started vs where it ended"}, "KeyboardShortcutsDialog": {"help": "Toggle this menu", "help#description": "Instructions for how to use keyboard shortcuts to show and hide a window that lists all of the keyboard shortcuts available. Hitting that keyboard shortcut \"toggles\" (shows/hides) that dialog.", "title": "Keyboard Shortcuts", "title#description": "a key or combination of keys providing quick access to a particular function within a computer program"}, "LaserTable": {"export": "{{robots}} Lasers {{date}}", "export#description": "The name of the CSV file that is exported on this page that shows laser information for a given robot.", "installedOnly": "Installed Only", "installedOnly#description": "A table filter that limits results to only lasers that have been installed on the robot", "warnings": {"duplicate": "This robot has multiple lasers registered in the following slot(s): {{slots}}", "duplicate#description": "A warning that a robot has more than one laser in a given slot or slots. A slot is a place where a laser can be installed on a robot", "emptySlot": "This robot has no laser registered in the following slot(s): {{slots}}", "emptySlot#description": "A warning that a robot has no laser in a given slot or slots. A slot is a place where a laser can be installed on a robot"}}, "ListManager": {"new": "New Code", "new#description": "placeholder text for an input that allows a user to manage alarms. The \"Code\" in this context is a number that is associated to an alarm."}, "Loading": {"failed": "Sorry, Carbon Ops Center failed to load.", "failed#description": "A generic message that shows up when a thing on our webpage fails to load. ", "placeholder": "Loading...", "placeholder#description": "A status to indicate that the data we need hasn't been received from our backend system yet"}, "ModelName": {"warning": "Warning: Low reliability model", "warning#description": "A warning that an AI model that is used to detect weeds or crops on the robot is not very good and should not be expected to perform well"}, "PendingActivationOverlay": {"description": "We're activating your account. You'll get an email when it's complete!", "description#description": "A message shown to the user to let them know that they're request to get access to our system is acknowledged and in process.", "errors": {"carbon": {"description": "Carbon email detected but not verified due to username/password login. Log out and use the \"Sign in with Google\" option to be activated automatically.", "description#description": "A message that shows up if a user has tried to create an account with our system using a username / password as opposed to logging in through Google. ", "title": "Unverified Carbon Account", "title#description": "A message that is shown to a user when their login credentials have not been approved by a carbon employee yet."}}, "hi": "Hi {{name}}!", "hi#description": "Greetings!", "logOut": "Logged in with the wrong account? <0>Log out</0>.", "logOut#description": "A prompt that tries to determine whether a user should be logged in under a different set of credentials. Don't translate or change the <0> or </0> but please translate the words inside of these tags. ", "title": "Awaiting activation", "title#description": "A message that shows if the user's account is not activated yet. This means they can't see things yet and someone needs to verify that they are an official user."}, "ResponsiveSubnav": {"more": "More", "more#description": "A navigation dropdown that is shown when there isn't enough room to display all of the navigational options. \"More\" as in -> More links to display"}, "RobotImplementationSelector": {"status": "Implementation Status", "status#description": "A category applied to the robot depending on it's state. Such as being made, being shipped, stored for the winter etc.", "title": "Change Implementation Status", "title#description": "The title for a dialog prompting a user to change the status of a robot. Statuses can be things like \"winterized\" or \"off-season\" and can indicate where a robot is or if it's being used.", "warning": "Changing implementation status may trigger automated workflows affecting customer experience. DON'T DO IT IF YOU'RE NOT SURE!", "warning#description": "A warning to our internal Carbon employee users that modifying the robot's implementation status has consequences."}, "ShowLabelsButton": {"text": "Labels", "text#description": "Toggle option that shows an overlay on a picture of a plant depicting where the center of the plant is and what it's radius is (a plant label)", "tooltip": "Show Labels", "tooltip#description": "Tooltip for toggle option that shows an overlay on a picture of a plant depicting where the center of the plant is and what it's radius is (a plant label)"}, "ShowMetadataButton": {"tooltip": "Show Metadata", "tooltip#description": "Toggle option that will show more information about an entity. This would typically be used for debugging by admins."}, "almanac": {"crops": {"new": "Add New Crop", "new#description": "A button that allows the user to add a agricultural crop to this configuration page", "none": "No Crop Categories", "none#description": "A message that shows up when there are no crop options to select from", "sync#description": "An option that allows a user to copy configurations that were set to apply to one crop to apply to all crops"}, "cropsSynced": "All Crops", "cropsSynced#description": "A title that indicates that you are viewing configurations for all crops as opposed to configurations for a specific crop. This happens when the crops are \"synced\".", "delete": {"description": "This cannot be undone", "description#description": "A warning that an action is not reversible"}, "discard": {"description": "Discard changes to {{title}}?", "description#description": "Revert any updates that the user did to an almanac configuration?", "title": "Discard Changes?", "title#description": "Asking the user if they are OK with their updates being ignored and no save/changes occurring"}, "fineTuneDescription": "Default is 5, can decrease or increase the laser shooting time by ~20% per increment", "fineTuneDescription#description": "A description about what a \"FIne Tune Multiplier\" does. Each increment (integers like 5,6,7) can effect how long the laser is shot at a plant ", "fineTuneTitle": "Fine Tune Multiplier", "fineTuneTitle#description": "A number that is used to help configure how long the laser is shot. \"Fine Tune\" referencing tuning your configurations. ", "formulas": {"all": "All Sizes", "all#description": "A configuration category to configure all sizes of plants such as small medium and large", "copyFormula": "Copy Formula", "copyFormula#description": "Refering to the command which allows the user to create an identical copy of a file or a set of files. In this example, the user will copy a mathematical formula", "copySize": "Copy Sizes", "copySize#description": "Duplicate the size thresholds that are set that relate to how big a plant is", "exponent": {"description": "Raises the radius in mm to this exponent", "description#description": "A description of what changing the variable in the formula will do. \"exponent\" in this scenario refers to mathematical exponents", "label": "Exponent (e)", "label#description": "A mathematical exponent such as 2^2=4. 'e' here is the name of the variable"}, "fineTuneMultiplier": {"description": "Number from 1-10, Default is 5, can decrease or increase the laser shooting time by ~20% per increment. This is the number used in basic mode", "description#description": "This is the description of the fine tune multiplier on the almanac. it is used to adjust the laser shoot time and is based off a mathematical equation", "label": "Fine Tune Index (FI)", "label#description": "Fine Tune Index, abbreviated by \"FI\", is a mathematical multiplier, on a scale from 1 to 10, used to adjust how long a laser shoots a weed"}, "fineTuneMultiplierVal": {"description": "Overall multiplier for the increment / decrement of the Fine Tune Index", "description#description": "Description for a modifier value in a formula configuration", "label": "Fine Tune Muliplier Value (FM)", "label#description": "Label for a modifier value in a formula configuration"}, "laserTime": "Laser Time", "laserTime#description": "How long the laser takes to shoot something", "maxTime": {"description": "Cap to the amount of time to shoot in ms", "description#description": "Sets the maximum amount of time a laser can be shooting a plant. ", "label": "Max Time", "label#description": "The maximum amount of time in milleseconds that a laser can shoot a plant"}, "multiplier": {"description": "Multiplies against the radius in mm", "description#description": "a mathematical multiplication factor that is multiplied by the radius of a plant to determine how long a laser shoots", "label": "Multiplier (A)", "label#description": "A constant in a formula that multiplies the radius and helps determine how long it takes to shoot a plant. \"Multiplier\" as in mathematical multiplication. \"A\" is the associated formula variable"}, "offset": {"description": "Amount of milliseconds to add regardless of the radius", "description#description": "A fixed number of milleseconds to add to the total time a laser is shooting the plant as dictated by the mathematical formula. ", "label": "Offset (b)", "label#description": "The number of milleseconds to add to laser shoot time as defined in the mathematical formula. Offset here is a mathematical offset (think similar to y=mx+b where b is the offset for the line formula). And 'b' is purely the name of the variable in the formula"}, "pasteFormula": "Paste Formula", "pasteFormula#description": "After a user copies a formula, they can then paste, or make words that you have removed or copied appear in a new place on a computer screen. ", "pasteSize": "Paste Sizes", "pasteSize#description": "Apply copied sizes to the current configurations.", "sync": "Sync All Sizes", "sync#description": "Button to sync all the user inputted values so that they don't have to reenter the same value", "thresholds": "<PERSON><PERSON>", "thresholds#description": "Plant size limits that help demarcate settings. ", "title": "Formulas", "title#description": "Formulas as in mathematical formulas"}, "protected#description": "A warning that editing abilities are limited for this almanac (which is a term used in attempt to be analogous to the farming concept of an almanac). Fine Tune Index in this context is a number that is used to effect how long the laser shoots. Try to be consistent in translating \"Fine Tune Index\" with other usages amongst this app. ", "switchModeAdvanced": "Switch to Advanced Mode", "switchModeAdvanced#description": "Change the view from the simplified view to a \"advanced\" view that requires more work and insight to use", "switchModeBasic": "Switch to Basic Mode", "switchModeBasic#description": "Change the display mode to go back to the simplified view where the inputs are easier to configure. ", "warnings": {"admin": "Modifying this almanac will sync to all current and future production units.", "admin#description": "A warning that making the change to the \"almanac\" configuration will effect all robots.", "carbon": "This is a Carbon-provided almanac. Only the Fine Tune Index can be modified.", "carbon#description": "This is the warning a user will see if they adjust the Carbon default almanac. \n\nAlmanac is a term used in attempt to be analogous to the farming concept of an almanac). Fine Tune Index in this context is a number that is used to effect how long the laser shoots. Try to be consistent in translating \"Fine Tune Index\" with other usages amongst this app", "production": "This almanac is actively running on a robot. Editing it will take effect in the field immediately.", "production#description": "A warning that the configuration for \"almanac\" that the user is editing is currently being used by a robot that is running."}, "weeds": {"new": "Add New Weed", "new#description": "A button to add a new weed (as in a plant you don't want to grow somewhere) to a configuration that helps determine how to thin the plants in your field.", "none": "No Weed Categories", "none#description": "A message that shows up if there are no weed types to be able to configure", "sync#description": "A button that lets a user copy the configurations they have set for a specific weed type and apply it to all other weeds types"}, "weedsSynced": "All Weeds", "weedsSynced#description": "A title that indicates that you are viewing configurations for all weeds as opposed to configurations for a specific weed. This happens when the weeds are \"synced\"."}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} saved. Activate through Operator App Quick Tune", "savedLong#description": "Message confirming that {{subject}} has been saved and how to activate it", "testResults": "Preview Results", "testResults#description": "A button that triggers a model to produce plant classification results based on their selections so they can evaluate performance"}, "filters": {"capturedAt": "Captured Date", "capturedAt#description": "A filter that allows a user to get results by when the image was taken in the field", "diameter": "Diameter", "diameter#description": "A filter that allows a user to get results by the diameter of the plant", "filters#description": "Options to narrow down the results", "unappliedFilters": "There are unapplied filter changes", "unappliedFilters#description": "Message shown to the user when they have changed filters but have not hit apply"}, "images": {"allImages": "All images", "allImages#description": "A tab that shows all available images of plants that can be categorized", "categorized": "Categorized", "categorized#description": "A tab that shows the images that the user has categorized by category", "scrollToTop": "Back to Top", "scrollToTop#description": "Clicking this automatically scrolls the user back to the top of all of the categorization image options", "sortBy": {"latest": "Latest", "latest#description": "newest to oldest"}, "sortedBy": "Sorted By: {{sortBy}}", "sortedBy#description": "Indicator that the image results are sorted by a specific factor"}, "session": {"error": "Error fetching status", "error#description": "Message shown when fetching the session status fails", "ready": "Results are ready", "ready#description": "Message that lets the users know that the results are ready to be looked at", "session#description": "A open request for the system to use the profile to generate example classification results. When it is complete, the user can see pictures that are labeled with plant classifications.", "session_one": "Session", "session_other": "Sessions", "showResults": "Show results", "showResults#description": "Toggle that displays the results of the classification session. It will show the plants with their generated plant classifications", "status": "{{processed}} / {{total}}", "status#description": "message to tell the user how many pictures have been classified so far. They won't be able to see results until all are done", "statusLong": "Processed {{processed}} / {{total}} images", "statusLong#description": "message to tell the user how many pictures have been classified so far"}, "session#description": "A open request for the system to use the profile to generate example classification results. When it is complete, the user can see pictures that are labeled with plant classifications.", "warnings": {"admin": "Modifying this plant profile will sync to all current and future production units.", "admin#description": "This is a warning that is shown to users if they edit the category profile feature. A category profile is a way for a user to select images of plants to inform the model what groups of images it should recognize.", "adminMeta": "All admin profiles will be available to all customers. Don't create clutter!", "adminMeta#description": "A warning shown to our admin users to caution them against creating cruft profiles.", "production": "This plant profile is actively running on a robot. The operator will be notified of updates and can choose to use the latest changes.", "production#description": "A warning message that lets the user know that the robot in the field is using it and that it won't immediately effect the robot without a user choosing to use the latest updates.", "protected": "This is a carbon provided profile. Nothing can be modified.", "protected#description": "An informational snippet telling the user that this specific configuration set can't be changed because it is set by our company", "unsavedChanges": "Unsaved Changes. Press save to apply changes.", "unsavedChanges#description": "A warning that the user has made changes to configurations and those changes will not be applied until the user decides to save them"}}, "config": {"changedKey#description": "A button that helps the user only see which values in a list of configurations have been modified by a person and aren't just set to the default value. The \"key\" here is an identifier in a key-value pair.", "changedKey_one": "Changed Key", "changedKey_other": "Changed Keys", "newKey": "new {{key}} name", "newKey#description": "Label for a field that allows you to name a new item in a list. e.g \"new robot name\", \"new crop name\"", "stringReqs": "Can only contain a-z, 0-9, ., -, and _", "stringReqs#description": "Instructions for what valid values are for our configs which are variable key-value pairs. It only allows letters in the latin alphabet and digits 0-9 as well as the special underscore character", "warnings": {"keyExtra": {"description": "This key has been added on top of the default.", "description#description": "A label describing that a key has been added list a list of keys that is not in the default list given by the company"}, "keyMissing": {"description": "Missing default(s): {{keys}}", "description#description": "a warning that certain configuration keys are missing default values"}, "valueChanged": {"description": "This value has been changed from it's default ({{default}})", "description#description": "Information that the value of a configuration is different from what our system has determined is default", "title": "Configuration Changed", "title#description": "A notice that a set of configurations that the user can change has updated"}}}, "customers": {"CustomerEditor": {"errors": {"load": "Failed to load customer editor", "load#description": "A generic message that is shown if the UI component that lets people edit our Customer info has failed to load"}}, "CustomerSelector": {"empty": "Not Assigned", "empty#description": "There is a not a customer selected to own the robot", "title": "Change Customer", "title#description": "Update which of our customers own a given robot"}}, "discriminator": {"configs": {"avoid": {"description": "Shoot vs ignore", "description#description": "whether to shoot a plant with a laser or just to ignore that plant category", "label": "Shoot", "label#description": "Whether to shoot a plant or not with a laser"}, "copy": "Copy Configs", "copy#description": "Copy the configuration values and settings. \"Configs\" is a shortening for configurations", "ignorable": {"description": "Shoot only if time allows, not considered in velocity recommendation", "description#description": "a description on a setting that allows a plant of a certain type to only be shot with a laser when there is time to, but not to prioritize it", "label": "Ignorable", "label#description": "A configuration flag that indicates that it would be ok to ignore or skip shooting the plant in this category if shooting it would slow us down too much"}, "paste": "Paste Configs", "paste#description": "Paste configuration values that have previously been \"copied\". This takes the copied values and overwrites existing values."}, "warnings": {"production": "This discriminator is actively running on a robot. Editing it will take effect in the field immediately.", "production#description": "A warning that a configuration profile that is being edited is already being used by a running robot."}}, "drawer": {"customerMode": "Customer Mode", "customerMode#description": "a toggle that Carbon employees can see that lets them turn off their special carbon views and only see what the customer will see.", "error": "Failed to load navigation", "error#description": "An error message that appears when the side nav digital navigational element in the web page fails to show up."}, "filters": {"NumericalRange": {"max": "Max ({{units}})", "max#description": "The maximum value in the numerical range filter", "min": "Min ({{units}})", "min#description": "The minimum value in the numerical range filter"}, "filters": "Filters", "filters#description": "Options to narrow down the results", "greaterOrEqualTo": ">= {{value}}", "greaterOrEqualTo#description": "Indicates that the filtered values will be greater than or equal to the selected value", "lessOrEqualTo": "<= {{value}}", "lessOrEqualTo#description": "Indicates that the filtered values will be less than or equal to the selected value", "range": "{{min}} - {{max}}", "range#description": "Indicates that the filtered values will be within the range of the selected values"}, "header": {"failed": "Failed to load header", "failed#description": "An error message that appears when the top nav digital navigational element in the web page fails to show up.", "mascot": "Carbon Robotics chicken mascot", "mascot#description": "An alt tag on an image that describes what the image is. The image is of a chicken (like the bird) who is our company's mascot. ", "search": {"failed": "Failed to load search", "failed#description": "The UI failed to load the visual component for the global search bar", "focus": "Focus Search", "focus#description": "An option that, when activated, focuses the users' mouse onto a search input box."}}, "images": {"ImageSizeSlider": {"label": "Size", "label#description": "provides context to the increase/decrease buttons which result in the image sizes changing", "larger": "larger", "larger#description": "A tooltip on a button that you click to make images on a screen be larger in size", "smaller": "smaller", "smaller#description": "A tooltip on a button that you click to make images on a screen be smaller in size"}}, "map": {"bounds": {"reset": "Reset View", "reset#description": "A button on a map that allows a user to return to the default zoom level that the map has which shows all of the visible robots"}, "errors": {"empty": "No location data reported", "empty#description": "An error message that shows on a map that tells the user that there is no custom data to show on the map -- thereby making it empty", "failed": "Failed to load map", "failed#description": "The generic error message that is displayed if there are any technical issues with loading the map component."}, "filters": {"customer_office": "Customer Office", "customer_office#description": "A locational category of where our customer has their buildings they work out of", "hq": "Carbon HQ", "hq#description": "Carbon is the name of our company and HQ is the shortening of \"Headquarters\" -- as in where our company is based out of.", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "A button on a map that exposes filters to determine which geospatial data is displayed on the map", "po_box": "PO Box", "po_box#description": "A location where we have a post office box (for mail)", "shop": "Shop", "shop#description": "Similar to a \"workshop\", this indicates a place where our robots go to get repaired and worked on", "storage": "Storage", "storage#description": "A location where we store supplies and equipment and parts for the robots etc.", "support_base": "Support Base", "support_base#description": "A location where our support team (our team that physically helps our users with our robots) is \"based\" out of."}, "fullscreen": "Fullscreen", "fullscreen#description": "A button that expands the map to take up the full size of the user's window.", "heatmaps": {"absoluteRange#description": "A scale for a geospatial metric. For an absolute range, the scale is determined by fixed, absolute values.", "customRange#description": "A scale for a geospatial metric. For a custom range, the user may select the parameters of the scale.", "editor": {}, "errors": {"invalidNumbers#description": "An error shown when the user has entered values for a custom range, but did not enter a valid number. The specific invalid inputs will appear in {{failures}}.", "legend": "Layer legend error", "legend#description": "A generic error message presented to a user if the UI component for the geospatial data layer color legend fails to load", "notThinning": "NOT THINNING", "notThinning#description": "A notice that the robot was not thinning plants and therefore we don't have geospatial metrics to show related to thinning. This is stylized in all caps.", "notWeeding": "NOT WEEDING", "notWeeding#description": "A notice that the robot was not weeding and therefore we don't have geospatial metrics to show related to weeding. This is stylized in all caps.", "outOfOrder#description": "An error shown when the user has entered values for a custom range, but the numbers were not entered in ascending order. The specific invalid inputs will appear in {{failures}}.", "unknown": "HEATMAP ERROR", "unknown#description": "This is the error that pops up when the user tries to load spatial data, also known as a heatmap, and it fails to load. A heatmap is a representation of data in the form of a map or diagram in which data values are represented as colors.\n"}, "fields": {"block": "Block: {{block}}", "block#description": "An internal way to see our custom \"block ids\" which help us debug locational info", "location": "Location: {{latitude}}, {{longitude}}", "location#description": "This refers to the GPS (global positioning system) coordinates of where a particular metric or measurement was taken ", "size": "Size: {{width}} × {{length}} ({{area}})", "size#description": "Gives an overview of the physical size of a field. It gives both the width and length and calculates the area to display to the user."}, "name": "Layers", "name#description": "A title that references different display layers on a map -- as in different categories of geospatial visualizations to show", "rangeType#description": "Prompts the user to select the color scale for a geospatial metric.", "relative": "Use Relative Range", "relative#description": "Prompts the user to use a dynamic scale to determine the color for a geospatial metric. The range of colors is relative to the data instead of the default range.", "relativeRange#description": "A scale for a geospatial metric. For a relative range, the scale is dynamic and depends on the data values."}, "map": "Map", "map#description": "A button to switch to a view that shows information on geospatial map similar to google maps", "measure": {"name": "Measure", "name#description": "A button that provides the ability to click on the map and measure distances between two geospatial points. It also allows a user to measure areas by clicking 3+ points."}}, "modelinator": {"categories": {"copyFromWhich": "Copy from which category?", "copyFromWhich#description": "This message appears when the user has pressed a button to copy settings from one category to all other categories, but hasn't selected a source category yet.", "splitCrops": "Split Crops", "splitCrops#description": "An option that allows a user to use different settings for each crop", "splitWeeds": "Split Weeds", "splitWeeds#description": "An option that allows a user to use different settings for each type of weed", "syncCrops": "Sync All Crops", "syncCrops#description": "An option that allows a user to copy configurations that were set to apply to one crop to apply to all crops", "syncWeeds": "Sync All Weeds", "syncWeeds#description": "A button that lets a user copy the configurations they have set for a specific weed type and apply it to all other weeds types"}, "configs": {"bandingThreshold": {"description": "Prediction confidence threshold to use a detection in dynamic banding", "description#description": "The concept of a band is to define an linear area that we can act upon. For example, imagine a tractor driving over a field, and say we set a band of 12 inches centered around the center of the tractor (Length wise the band is infinite as the tractor drives forward). We would only act on objects detected within those 12 inches., , The term banding is used to define the functionality of using bands, aka banding., , A threshold is related to the confidence of the AI. Every prediction of the AI comes with a percentage confidence. The threshold is related to the minimum level of confidence that a prediction needs to have in this case., , In this case, this is the minimum level of confidence that a prediction needs in order to be used by the banding algorithms. And this phrase is the description of that.", "label": "Banding Threshold", "label#description": "The concept of a band is to define an linear area that we can act upon. For example, imagine a tractor driving over a field, and say we set a band of 12 inches centered around the center of the tractor (Length wise the band is infinite as the tractor drives forward). We would only act on objects detected within those 12 inches., , The term banding is used to define the functionality of using bands, aka banding., , A threshold is related to the confidence of the AI. Every prediction of the AI comes with a percentage confidence. The threshold is related to the minimum level of confidence that a prediction needs to have in this case., , In this case, this is the minimum level of confidence that a prediction needs in order to be used by the banding algorithm"}, "minDoo": {"description": "Minimum detection over opportunity", "description#description": "This is the full name for MinDoo. It is part of our computer algorithm determining if a plant predicted as a weed or crop should be trusted ", "label": "<PERSON>", "label#description": "Shortening of \"Minimum detection over opportunity\""}, "thinningThreshold": {"crop": {"description": "Prediction confidence threshold to use a detection in thinning", "description#description": "The model predicts whether a plant is a weed or not and determines how confident it is in this guess. This configuration setting determines at which threshold of confidence a model will need to have to decide that something is a plant that should be thinned.", "label": "Thinning Threshold", "label#description": "An input for a level of confidence the model needs to be at when guessing whether something is a crop in order to determine if we should kill that crop as an act of thinning it."}, "weed": {"description": "Prediction confidence threshold to use a detection for reverse crop protection", "description#description": "Reverse Crop(agriculture) protection is an algorithm, a AI model prediction need to meet a minimum level of confidence (threshold) to be used in that algorithm", "label": "Reverse Crop Protect Threshold", "label#description": "Crop(agriculture) protection algorithm, but in reverse. Threshold is the minimum confidence that the ai model needs to have in one of its prediction for it to be used in the algorithm"}}, "weedingThreshold": {"crop": {"description": "Prediction confidence threshold to use a detection for crop protection", "description#description": "Description, Prediction is meant in the AI model making a prediction sense. Each prediction is made with a level of confidence, and the threshold is the minimum level of confidence to accept a prediction. A detections is a synonym for prediction. Crop is meant in the agricultural sense., , Crop protection means avoiding shooting near crops to not accidently shoot them with the laser, hence protecting the crop", "label": "Crop Protect Threshold", "label#description": "Crop(agriculture) Protection is a feature to protect crops by preventing weeds too close to a crop from being shot., , The threshold is the minimum level of a confidence that our AI model needs to have when predicting a crop for it to be used for that feature."}, "weed": {"description": "Prediction confidence threshold to consider a weed", "description#description": "The model predicts whether a plant is a weed or not and determines how confident it is in this guess. This configuration setting determines at which threshold of confidence a model will need to have to decide that something is a weed", "label": "Weeding Threshold", "label#description": "A setting that someone can set to determine whether something is a weed or not in relation to how confident a model has guessed it is."}}}, "errors": {"sync": "The settings for this model have not synced from the LaserWeeder yet. Please wait for sync to view and update settings.", "sync#description": "An error telling the user that the robot hasn't reported its model settings to our system yet. It is asking the user to wait until that happens to view and update those settings."}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "Used to show a represent both a kind of plant (the {{category}}) and a size (the {{size}}). For example, \"Grass: Small\" or \"Broccoli: All Sizes\". Please leave the {{category}} and {{size}} placeholders unchanged; do not translate them.", "splitSizesLong": "Split Sizes", "splitSizesLong#description": "A button that a user can press to allow using different settings for each category in a list, e.g., to treat small crops differently from large crops.", "splitSizesShort": "Split", "splitSizesShort#description": "A button that a user can press to allow using different settings for each category in a list, e.g., to treat small crops differently from large crops. This is short for \"Split Sizes\" but is abbreviated to take up less space.", "syncSizesLong": "Sync Sizes", "syncSizesLong#description": "Button to sync all the user inputted values so that they don't have to reenter the same value", "syncSizesShort": "Sync", "syncSizesShort#description": "Button to sync all the user inputted values so that they don't have to reenter the same value. This is short for \"Sync Sizes\" but is abbreviated to take up less space."}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Warning:{{stopEmphasis}} These settings include unsaved changes that are not reflected on the robot.", "exportingUnsavedChanges#description": "A notice that appears when the user exports the configuration for a model after making changes to that configuration but before saving them. The user needs to know that the exported changes represent their edited state, not the live state.", "production": "This model is actively running on a robot. Editing it will take effect in the field immediately.", "production#description": "A notice that the selected model and model configurations are being actively used on a running robot."}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Unknown Alarms", "unknown#description": "An alarm triggered by the robot that we don't recognize"}, "almanac": {"unknown": "Unknown Almanac", "unknown#description": "An almanac for which we don't have a good display name or id. Therefore it's \"unknown\"", "withName": "Almanac: {{name}}", "withName#description": "a status for the name of the almanac that's currently active"}, "autofixing": "Autofixing Error", "autofixing#description": "A loading message that shows when the robot is attempting to self repair a problem\n", "banding": {"disabled": "Banding Disabled", "disabled#description": "A little status indicator telling the user that banding is not active for the robot", "enabled": "Banding Enabled", "enabled#description": "A status telling the user that agricultural banding is turned on for weeding for the robot", "none": "No Banding", "none#description": "\"banding\" is not enabled which is where we try to only weed in a \"band\" or strip of land where the plants are generally planted", "static": "(STATIC)", "static#description": "So if \"banding\" is when we weed within a strip of where plants are planted as opposed to the entire area the robot passes over, there are different ways of determining that strip size. One is \"Static\" where you simply define a fixed number of how wide that strip is. Another is \"Dynamic\" where we try to smart infer the size of the band ourselves.", "withName": "Banding: {{name}}", "withName#description": "A little status telling you what banding profile is enabled"}, "checkedIn": {"failed": "Failed to load check-in status", "failed#description": "A generic error message shown when we can't load the UI component that shows the user when the last time we heard from the robot was", "never": "Never checked in", "never#description": "A message shown when the robot has never checked in with our system yet", "withTime": "Checked in {{time}}", "withTime#description": "When we last got a message from the robot"}, "crop": {"summary": "{{enabled}}/{{total}} Crops Enabled ({{pinned}} pinned)", "summary#description": "A status that indicates how many available options for crops are enabled for a robot."}, "delivery": "Delivery", "delivery#description": "A category for robot that indicates that the robot is currently being delivered to the customer", "disconnected": "Disconnected", "disconnected#description": "The robot is not able to be reached and is disconnected from our system", "discriminator": {"unknown": "Unknown Discriminator", "unknown#description": "A configuration profile that we don't have a name for but exists", "withName": "Discriminator: {{name}}", "withName#description": "\"Discriminator\" is a custom term that describes a bunch of custom configurations. It helps determine the differences between things. The Discriminator feature allows our software to customize which types and sizes of weeds to shoot and what size of plants to consider for thinning. This isn't a real English word but is a phrase derived from \"discriminate\" as in \"discriminating between types of plants\""}, "failed": "Failed to load robot status", "failed#description": "A generic error message shown to the user if the UI component that shows a robot's status fails to load", "failedShort": "Failed", "failedShort#description": "A generic message shown if a UI component fails to load.", "implementation": "Implementation", "implementation#description": "The robot", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventory", "inventory#description": "A category for robot that indicates that the robot hasn't been sold yet but is finished and ready to be sold and delivered", "job": {"none": "No Job", "none#description": "There is a not an active job set on the robot.", "withName": "Job: {{name}}", "withName#description": "A status blurb saying which job is currently active"}, "lasers": "Lasers Online: {{online}}/{{total}}", "lasers#description": "For a given robot, how many of their physical lasers are operating and functional", "lifetime": "Lifetime:", "lifetime#description": "Title for metrics that are available over the course of the robot's entire existence as opposed to confined within a given time range or filter set.", "lifted": "Standby (Lifted)", "lifted#description": "This refers to the state of the hardware. Our robot can be lifted by the tractor it is connected to. If it is in the air, it has the standby (lifted) status and is not actively weeding", "loading": "Loading", "loading#description": "Whether or not the UI component is in a loading state", "location": {"known": "Location: <0>{{latitude}}, {{longitude}}</0>", "known#description": "The GPS location where the robot is. Don't translate <0></0> just leave those as is", "unknown": "Location Unknown", "unknown#description": "The GPS location of the robot is not known"}, "manufacturing": "Manufacturing", "manufacturing#description": "A robot classification that indicates that the robot is being built ", "model": {"withName": "Model: <0>{{name}}</0>", "withName#description": "The name of the AI model. Don't translate the <0> </0>. Leave those as is"}, "modelLoading": "Model Loading", "modelLoading#description": "The AI model is downloading to the robot", "notArmed": "Not Armed", "notArmed#description": "A status description of the state of a robot saying that the lasers have not been armed (or \"activated\")", "off_season": "Off-Season", "off_season#description": "A robot category that indicates that a robot is not being used because it is not needed due to the agriculture season", "offline": "Offline for {{duration}}", "offline#description": "How long the robot has not been connected to our systems. \"Offline\" as in -> not reachable.", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "P2P is the name of an AI model that helps track plants so we can target them. Please leave the <0> </0> tags as is and don't translate them directly.", "unknown": "P2P Unknown", "unknown#description": "We don't have a way of knowing what P2P AI model is running on the robot right now or one doesn't exist."}, "poweringDown": "Powering Down", "poweringDown#description": "The robot is in the process of shutting down", "poweringUp": "Powering Up", "poweringUp#description": "The robot is turning on", "pre_manufacturing": "Pre-Manufacturing", "pre_manufacturing#description": "A category for the robot where it hasn't even started to be built yet", "stale": "Stale", "stale#description": "The information we have received from the robot is \"stale\". As in- we haven't gotten new information for so long that the information we have and are displaying is too old to consider.", "staleDescription": "Last known value. Robot is offline.", "staleDescription#description": "The robot hasn't connected to our system in a while. This snippet lets the user know we are merely showing what the robot last reported despite the fact that we acknowledge that the robot is not currently connected.", "standby": "Standby", "standby#description": "This is the status of the robot when it is not actively shooting lasers at any weed or crop ", "thinning": {"disabled": "Thinning Disabled", "disabled#description": "Plant thinning is not currently active for the robot", "enabled": "Thinning Enabled", "enabled#description": "Plant thinning is happening on the robot", "none": "No Thinning", "none#description": "Thinning is not currently happening on this robot", "withName": "Thinning: {{name}}", "withName#description": "The name of the thinning profile"}, "today": {"none": "No weeding today", "none#description": "The robot has not weeded today. This is shown in a way to indicate that we don't have metrics for how much it's weeded when it hasn't weeded at all."}, "unknown": "Unknown Status", "unknown#description": "The status of the robot is not identified ", "updating": "Update Installing", "updating#description": "The robot's new software is currently installing", "version": {"values": {"unknown": "Unknown Version", "unknown#description": "The software version is being reported but we aren't able to match it up with a known version.", "updateDownloading": "({{version}} downloading)", "updateDownloading#description": "a message telling a user that a new version of robot software is downloading on the robot", "updateReady": "({{version}} ready)", "updateReady#description": "A message that the user sees when a new software version is ready to be installed but hasn't been installed yet"}}, "weeding": "Weeding {{crop}}", "weeding#description": "This tells the user what agricultural crop is actively being weeded by the robot", "weedingDisabled": "Weeding Disabled", "weedingDisabled#description": "Weeding is not currently happening on the robot", "weedingThinning": "Weeding and Thinning {{crop}}", "weedingThinning#description": "The robot is currently both weeding and thinning a certain crop. ", "winterized": "Winterized", "winterized#description": "A status option for the robot itself. Winterized is when the robot is put away for the season due to the growing season being over"}, "dialogs": {"new": {"errors": {"exists": "Already exists", "exists#description": "Our robot serial names must be unique, so if someone tries to make a new robot with a serial that is already taken, this message tells them that the serial is already being used", "unknownClass": "Unknown robot class", "unknownClass#description": "Error indicating that we can't tell what \"class\" (type) of robot the user wants to create. Some valid classes include slayers, reaper, tractors, and simulators."}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "Copy the values of one configuration with the intent on applying it to another configuration section", "ignoreConfig": "Don't create new config", "ignoreConfig#description": "When creating a new robot, don't automatically create a new robot config associated with it"}, "template#description": "\"Slayer\" is the internal name of our implement. This is only shown to Carbon employees. You can probably keep is as \"Slayer\". The template is a preconfigured option set for setting up a brand new robot", "templateForClass": "{{class}} Template", "templateForClass#description": "A configuration template for a specific kind (\"class\") of robot. This is a set of default values for lots of different settings on the robot. The class might be something like \"<PERSON>\" (a codename) or \"T<PERSON><PERSON>\".", "templateGeneric": "Robot Template", "templateGeneric#description": "A configuration template for a robot. This is a set of default values for lots of different settings on the robot.", "warnings": {"ignoreConfig": "You should only proceed if a config for {{serial}} already exists or if you plan to create it manually", "ignoreConfig#description": "a warning for a person creating a new robot where the robot doesn't have a config that will be automatically created."}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Advanced Speedometer Settings", "advancedFormulaTitle#description": "A heading for a section that exposes configs that are harder to use (thus advanced)", "formulaTitle": "Formula", "formulaTitle#description": "As in a mathematical formula like y=mx+b"}, "cruiseOffsetPercent": {"description": "Automatically decrease the suggested speed by your entered value. For example, an input of 5% will reduce the suggested speed of 1 mph to 0.95 mph", "description#description": "a description to a velocity offset input that helps a user configure what speed the system tells them to drive.", "label": "Velocity Offset", "label#description": "An input value to help a user configure how fast the system should tell them to drive. This is a constant."}, "decreaseSmoothing": {"description": "Customize the rate at which velocity decreases. The higher the value, the more likely the speedometer will fluctuate", "description#description": "a description to an input that lets a user figure out how snappy they want speed to decrease", "label": "Deceleration Smoothing", "label#description": "The rate at which the recommended speed of the robot will decrease. (whether the speed recommendation is jumpy or more gradual)"}, "increaseSmoothing": {"description": "Customize the rate at which velocity increases. The higher the value, the more likely the speedometer will fluctuate", "description#description": "a description to an input that lets a user figure out how snappy they want speed to increase", "label": "Acceleration Smoothing", "label#description": "This is how abrupt or gentle the user wishes the tractor to speed up"}, "maxVelMph": {"description": "Input the absolute highest speed you are willing to travel. Speed recommendations will not be above this value", "description#description": "This is the description for Maximum Speed. The user can input a value in miles per hour or kilometers per hour that they do not wish to exceed", "label": "Maximum Speed", "label#description": "The highest speed a robot will travel"}, "minVelMph": {"description": "Input the absolute lowest speed you are willing to travel. Speed recommendations will not be below this value", "description#description": "This is the description for Minimum Speed. The user can input a value in miles per hour or kilometers per hour that they do not wish to go below", "label": "Minimum Speed", "label#description": "The absolute slowest speed a user wants the tractor to travel at"}, "primaryKillRate": {"description": "This value is your desired percentage of weeds killed", "description#description": "a description for an input that helps a user set what percentages of weeds killed is acceptable to them. This helps the system define a recommendation for how fast to drive the machine at in order to achieve these goals", "label": "Ideal Kill Rate", "label#description": "Our robot uses lasers to shoot weeds in a farm. This percentage is the preferred percent of weeds that should be shot. For example, 100%"}, "primaryRange": {"description": "Increase this value if you want to hit your ideal kill rate regardless of speed impact", "description#description": "a description to an input that lets the system know how important it is to the user that they kill the percentage of weeds they want to kill. This will effect how fast the robot recommends you drive at", "label": "<PERSON> Buffer", "label#description": "A title that is pointing to the colored visualization where the green area is the 'green buffer'"}, "rows": {"allRows": "All Rows", "allRows#description": "an option to apply configuration settings to all rows that exist on a robot. This essentially means that the entire robot (usually there are 3 separate pieces) will act the same", "row1": "Row 1", "row1#description": "We have separate sections of the robot that act independently from each other to shoot weeds. This is Row 1 of 3 of those sections", "row2": "Row 2", "row2#description": "We have separate sections of the robot that act independently from each other to shoot weeds. This is Row 2 of 3 of those sections.", "row3": "Row 3", "row3#description": "We have separate sections of the robot that act independently from each other to shoot weeds. This is Row 3 of 3 of those sections."}, "secondaryKillRate": {"description": "This value is the lowest acceptable percentage of weeds killed", "description#description": "a description on an input that helps a user define what percentage of weeds killed is acceptable", "label": "Minimum Kill Rate", "label#description": "Our robot uses lasers to shoot weeds in a farm. This percentage is the absolute lowest percent of weeds that should be shot. For example, the minimum kill rate could be 95%"}, "secondaryRange": {"description": "Increase this value if you want to add leeway before receiving a low speed notification", "description#description": "a description on an input that helps determine the buffer between a target speed and the min and max speeds that should be recommended to a robot to drive at", "label": "Yellow Buffer", "label#description": "An input that references the visual that shows that the yellow area is a buffer around max and min speeds"}, "sync": "Sync All Rows", "sync#description": "Label for a checkbox that sets all rows to use the same configuration instead of configuring them independantly", "warnings": {"admin": "Modifying this velocity estimator will sync to all current and future production units.", "admin#description": "This is a warning that is shown to users if they edit the velocity estimator feature. Velocity estimator is a way to finetune the recommended tractor speed based on a user's preferences", "production": "This velocity estimator is actively running on a robot. Editing it will take effect in the field immediately.", "production#description": "A warning message that lets the user know that editing this configuration will effect the robot that is currently usin git in the field.", "protected": "This is a carbon provided profile. Nothing can be modified.", "protected#description": "An informational snippet telling the user that this specific configuration set can't be changed because it is set by our company", "unsavedChanges": "Unsaved Changes. Press save to apply changes.", "unsavedChanges#description": "A warning that the user has made changes to configurations and those changes will not be applied until the user decides to save them"}}, "slider": {"gradual": "Gradual", "gradual#description": "This refers to how fast the user wishes to speed up or slow down. Gradual means the users wants to do so at a slower pace.\n", "immediate": "Immediate", "immediate#description": "This refers to how abruptly or instantly the user wishes to see their recommended speed"}, "visualization": {"targetSpeed": "Target Speed", "targetSpeed#description": "An indicator in the green section of the visualization that this is the speed at which the system would recommend the robot be driven at"}}}, "models": {"alarms": {"alarm#description": "Events that are alarming", "alarm_one": "alarm", "alarm_other": "alarms", "fields": {"code": "Code", "code#description": "Each type of alarm is given a code to uniquely identify it", "description": "Description", "description#description": "table header for details about an alarm", "duration": {"name": "Duration", "name#description": "Table header for how long an alarm has been active", "values": {"ongoing": "ONGOING", "ongoing#description": "Whether the alarm is currently ongoing, (aka currently active)"}}, "identifier": "Identifier", "identifier#description": "Unique identifier for an alarm", "impact": {"name": "Impact", "name#description": "Impact level of an alarm, what impact does this alarm being active have on the product", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "Level of impact, where there is no impact", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Level", "name#description": "Table header for the severity level of an alarm\n", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Started", "started#description": "Table header for when an alarm started"}}, "almanacs": {"almanac#description": "Refers to a feature allowing the customization of how the laser will shoot different weeds based on their size and category.", "almanac_one": "almanac", "almanac_other": "almanacs", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "An assignment is a record that maps an obective to a tractor. A tractor may be assigned to one or more objectives, but an objective may only be assigned to one tractor.", "assignment_one": "assignment", "assignment_other": "assignments", "autotractor": "AutoTractor", "autotractor#description": "AutoTractor is the name of a product for a tractor (a vehicle on a farm) that can drive autonomously or by remote control.", "fields": {"instructions": "Instructions", "instructions#description": "The instructions for a task describe what needs to be done for that task."}, "intervention#description": "An intervention is an event where a human driver steps in to drive a tractor remotely.", "intervention_one": "Intervention", "intervention_other": "Interventions", "job#description": "A job is a high-level operation that a customer wants to complete. A job is made up of smaller tasks.", "jobTypes": {"laserWeed": "<PERSON><PERSON> Weed", "laserWeed#description": "An autotractor job where the tractors laser weed a zone.", "unrecognized": "unknown type ({{value}})", "unrecognized#description": "A string which indicates that the job is not of a type recognized by the software. {{value}} is the raw numeric value of the job type, to assist operators and Carbon in debugging the issue."}, "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "Manually Assisted", "manuallyAssisted#description": "Designator for a task which has been completed by a human rather than by an autotractor.", "objective#description": "An objective is the largest chunk of work that a single robot can do. Most likely this will correspond to 'laser weeding a single row'.", "objectiveTypes": {"laserWeedRow": "Laser Weed Row", "laserWeedRow#description": "An objective involving an autotractor using a laser weeder over an entire crop row."}, "objective_one": "objective", "objective_other": "objectives", "states": {"acknowledged": "acknowledged", "acknowledged#description": "", "cancelled": "cancelled", "cancelled#description": "A state a job can be in. A job is 'cancelled' when the operator has decided to permanently stop work on it before all of the work could be done.", "completed": "completed", "completed#description": "A state a job can be in. A job is 'completed' when all of the work has been successfully done on it.", "failed": "failed", "failed#description": "A state a job can be in. A job is 'failed' when a robot failed to complete some of the work on it due to a technical issue and no further progress can be made.", "inProgress": "in progress", "inProgress#description": "A state a job can be in. A job is 'in progress' when it can be assigned to robots and some but not all of the work has been done on it.", "new": "new", "new#description": "", "paused": "paused", "paused#description": "A state a job can be in. A job is 'paused' when the operator has decided to temporarily prevent work on it from being assigned to robots.", "pending": "pending", "pending#description": "A state a job can be in. A job is 'pending' when it has been created but it cannot yet be assigned to robots.", "ready": "ready", "ready#description": "A state a job can be in. A job is 'ready' when it can be assigned to robots but no work has yet been done on it.", "unrecognized": "unknown state ({{value}})", "unrecognized#description": "A string which indicates that the job is not in a state understood by the software. {{value}} is the raw numeric value of the state, to assist operators and Carbon in debugging the issue."}, "task#description": "A task is a specific thing that needs to be done. For example, a task might be \"drive to that supply depot\" or \"weed this field\".", "taskN": "Task #{{index}}", "taskN#description": "Describes the position of a task in a sequence. For example, the first task should say \"Task #1\".", "taskTypes": {"followPath": "Follow Path", "followPath#description": "Task where the tractor follows a specified path.", "goToAndFace": "Go to", "goToAndFace#description": "Task where a tractor goes somewhere and faces some way.", "goToReversiblePath": "Go to path", "goToReversiblePath#description": "Task where a tractor goes to either end of a specified path.", "laserWeed": "<PERSON><PERSON> Weed", "laserWeed#description": "Task where a tractor laser weeds some section of a field.", "manual": "Manual", "manual#description": "Task that will be done manually, without the robot operating autonomously.", "sequence": "Sequence", "sequence#description": "Task which represents a sequence of other tasks.", "stopAutonomy": "Stop Autonomy", "stopAutonomy#description": "Task where a tractor stops operating autonomously.", "tractorState": "Tractor State", "tractorState#description": "Task where a tractor changes its state.", "unknown": "Unknown", "unknown#description": "A task of unknown type."}, "task_one": "task", "task_other": "tasks"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "A category profile is a way for a user to select images of plants to inform the model what groups of images it should recognize.", "categoryCollectionProfile_one": "plant profile", "categoryCollectionProfile_other": "plant profiles", "fields": {"categories": {"disregard": "Disregard", "disregard#description": "A category of plant which the model should simply 'ignore' and not shoot but also not count as a crop", "name": "Categories", "name#description": "The names of different groups that images of plants can be classified as.", "requiredBaseCategories": "You must have these exact categories: ", "requiredBaseCategories#description": "Error message that appears if the user does not have exactly the required category names defined"}, "categories#description": "The names of different groups that images of plants can be classified as.", "name": "$t(utils.descriptors.name)", "updatedAt": "Updated", "updatedAt#description": "Table header for the last time the profile was updated"}, "metadata": {"capturedAt": "Captured", "capturedAt#description": "When the image was originally captured on the robot", "categoryId": "Category ID", "categoryId#description": "The global unique identifier for the associated category id that the image has been classified as", "imageId": "Image ID", "imageId#description": "The global unique identifier for a given image", "pointId": "Point ID", "pointId#description": "The global unique identifier for a given label point which describes a single plant on the image", "ppcm": "ppcm", "ppcm#description": "Pixels per centimeter - a way to map the pixels of the image to real world measurements (cm)", "radius": "radius", "radius#description": "The radius of the plant in pixels", "updatedAt": "Received", "updatedAt#description": "When the image was received in our system", "x": "x", "x#description": "The 'x' position in an x,y image coordinate system", "y": "y", "y#description": "The 'y' position in an x,y image coordinate system"}}, "configs": {"config#description": "Refers to configuration", "config_one": "config", "config_other": "configs", "key#description": "A key is a unique identifier, in this case a unique identifier for a configuration.", "key_one": "key", "key_other": "keys", "template#description": "A config template is a set of default values for how a certain kind of robot should be configured.", "template_one": "config template", "template_other": "config templates", "value#description": "Value to be given to a particular configuration. Can sometimes be a number, text or a boolean.\n\nAn example of a configuration might be: enable_feature_a: True\n\nWhere \"True\" in this case is the value.", "value_one": "value", "value_other": "values"}, "crops": {"categories": {"unknown": "Unknown <PERSON><PERSON>", "unknown#description": "Unknown agricultural crop"}, "crop#description": "Agricultural Crop", "crop_one": "crop", "crop_other": "crops", "fields": {"confidence": {"fields": {"regionalImages": "Regional Images:", "regionalImages#description": "IMages within a geographic region", "totalImages": "Total Images:", "totalImages#description": "Total count of images for this model"}, "name": "Confidence", "name#description": "Table header for how confident we are that the recommended model (AI model)  for a crop (agricultural crop) is going to perform well.", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Archived", "archived#description": "Allows filtering out crops that have been archived", "unknown": "Unknown Confidence", "unknown#description": "confidence level of a crop and its associated recommended model is unknown"}}, "id": "$t(utils.descriptors.id)", "id#description": "Unique identifier", "notes": "Notes", "notes#description": "Notes provided alongside a particular agricultural crop. Usually just useful information about said crops", "pinned": "Pinned", "pinned#description": "When a model (Ai model) is pinned to a crop (Agricultural crop), it means we will not change the model that is associated with that crop.\n", "recommended": "Recommended", "recommended#description": "Table header for the ai model that is currently recommended for a particular crop"}}, "customers": {"customer#description": "Customer refers to a customer of our company. In particular this is typically the name of a business.", "customer_one": "customer", "customer_other": "customers", "fields": {"emails": {"errors": {"formatting": "One email per line", "formatting#description": "Error message when creating new users"}, "name": "Emails", "name#description": "Email adressses"}, "featureFlags": {"almanac": {"description": "Enables the Almanac and Discriminator tabs for robots (robot must also support almanac and discriminator)", "description#description": "Feature flag description", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Enables the Plant Profile tab for robots", "description#description": "Description of plant profile feature flag", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Feature flags enable beta functionality for all users at a customer", "description#description": "Description for what a feature flag is", "explore": {"description": "Enables map and graph explore mode in reports", "description#description": "Description of explore mode feature flag", "name": "Explore Mode", "name#description": "Name of a feature flag to enable Explore Mode, refers to exploration"}, "jobs": {"description": "Enables Jobs (robot must also support jobs)", "description#description": "Description of a feature flag for the jobs feature", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Shows a new visual design for robot metrics", "description#description": "Description for \"Metrics Redesign\" feature flag", "name": "Metrics Redesign", "name#description": "A project to update the visual design of our metrics (robot statistics)."}, "name": "Feature Flags", "name#description": "Feature flags allow toggling features on a per customer basis", "off": "OFF", "off#description": "Boolean state for feature flag, off aka inactive", "on": "ON", "on#description": "Boolean state for feature flag, on aka active", "reports": {"description": "Enables Reports tab and features within", "description#description": "description of a feature flag", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Show spatial data including heatmaps and graphs", "description#description": "description of geospatial data feature", "name": "Spatial Data", "name#description": "Refers to Geospatial data, aka data overlayed on a map"}, "summary": "{{enabled}}/{{total}} Feature Flags Enabled", "summary#description": "Count of feature flags enabled", "unvalidatedMetrics": {"description": "Show beta metrics pending field validation in certified metrics", "description#description": "feature flag description", "name": "Beta Metrics", "name#description": "Enables metrics that are currently Beta"}, "velocityEstimator": {"description": "Enables viewing and editing target velocity estimator profiles (robot must also support target velocity estimator)", "description#description": "feature flag description", "name": "Target Velocity Estimator", "name#description": "Feature to customize velocity estimation, aka the feature where the robot automatically estimates the speed at which it should operate"}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "Account identifier from salesforce for a customer", "weeklyReportDay": "Run on", "weeklyReportDay#description": "Which day of the week the report should be run on", "weeklyReportEnabled": {"description": "If enabled, reports will run weekly with the following settings for all active robots", "description#description": "description of weekly report runs", "name": "Weekly Reports", "name#description": "Whether weekly reports are enabled for a particular customer"}, "weeklyReportHour": "Run at", "weeklyReportHour#description": "Time of the day at which a report should be run", "weeklyReportLookbackDays": "Lookback", "weeklyReportLookbackDays#description": "How far back in days does the reporting feature look", "weeklyReportTimezone": "Run in", "weeklyReportTimezone#description": "Time zone to run the report in"}}, "discriminators": {"discriminator#description": "Feature allowing discrimination between types of weeds, e.g. target grasses but not broadleaves", "discriminator_one": "discriminator", "discriminator_other": "discriminators", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "A description of a farm where crops are planted, along with location information for various points of interest, like fields, roads, and obstacles.", "farm_one": "farm", "farm_other": "farms", "point#description": "A point of interest on a farm, such as the center of a field, or a waypoint along a road.", "point_one": "point", "point_other": "points", "zone#description": "A region of interest on a farm, such as a field, a road, or an obstacle.", "zone_one": "zone", "zone_other": "zones"}, "fieldDefinitions": {"fieldDefinition#description": "A description of a farm field: where it's located, what shape it is, the heading along which crops are planted, etc.", "fieldDefinition_one": "field definition", "fieldDefinition_other": "field definitions", "fields": {"boundary": "Field boundary", "boundary#description": "The shape of the perimeter of a farm field, often a rectangle or a circle", "name": "$t(utils.descriptors.name)", "plantingHeading": "Planting heading", "plantingHeading#description": "The compass direction in which rows of crops are planted. \"Heading\" means direction."}}, "globals": {"global#description": "A Global refers to a system-wide configuration value. For example a Base Model ID used for all Plant Profile test predictions.", "global_one": "global value", "global_other": "global values", "values": {"plantProfileModelId": {"description": "Base model used by all customer and admin profiles for '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "Text that tells the user what the model id will do", "label": "$t(components.categoryCollectionProfile.actions.testResults) Model ID", "label#description": "The model ID that will be used globally as the base model for testing the results of a plant category profile"}}}, "images": {"fields": {"camera": "Camera", "camera#description": "Camera that takes images", "capturedAt": "Date / Time", "capturedAt#description": "Table header for the date at which an image was captured", "geoJson": "Location", "geoJson#description": "Table header for a geographic location", "url": "Open Image", "url#description": "Open an image, refer to an image taken by a camera"}, "image#description": "Images taken by a camera", "image_one": "image", "image_other": "images"}, "jobs": {"job#description": "A job is a task that has been done. For example weeding Field A would be considered a job.", "job_one": "job", "job_other": "jobs"}, "lasers": {"fields": {"cameraId": "Camera ID", "cameraId#description": "Unique identifier of a camera", "error": {"values": {"false": "Nominal", "false#description": "Refer to a normal state, aka no errors"}}, "installedAt": "Installed", "installedAt#description": "Table header for the date at which a laser was installed", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Unknown Serial", "unknown#description": "Serial number of a laser is unknown"}}, "lifetimeSec": "On Time", "lifetimeSec#description": "Table header for the amount of time a laser has been running for", "powerLevel": "Power Level", "powerLevel#description": "table header for the Power (in watts) of a laser", "removedAt": "Removed", "removedAt#description": "Table header for the date at which a laser was removed from a robot", "rowNumber": "Row", "rowNumber#description": "Table header for the row that particular laser is mounted on. A row is a section on a laser weeder robots, each section is mounted over an agricultural row.", "totalFireCount": "Fire Count", "totalFireCount#description": "Amount of times a laser was fired", "totalFireTimeMs": "Fire Time", "totalFireTimeMs#description": "table header for the amount of time the laser was actively firing for", "warranty": {"name": "Warranty", "name#description": "table header for whether or not a laser is still covered under warranty", "values": {"expired": "Expired", "expired#description": "Warranty is expired", "hours": "Hours: {{installed}}/{{total}} ({{percent}} remaining)", "hours#description": "Number of hours remaining on the warranty", "hoursUnknown": "Hours: Unknown", "hoursUnknown#description": "Number of hours is unknown", "months": "Months: {{installed}}/{{total}} ({{percent}} remaining)", "months#description": "Warranty time in month remaining", "monthsUnknown": "Months: Unknown", "monthsUnknown#description": "number of months is unknown", "unknown": "Unknown <PERSON><PERSON>y", "unknown#description": "Warranty is unknown"}}}, "laser#description": "Light emitting device, laser", "laser_one": "laser", "laser_other": "lasers"}, "models": {"model#description": "Model refers to an AI deeplearning model", "model_one": "model", "model_other": "models", "none": "No model", "none#description": "Model refers to an AI deeplearning model\n\nIn this case there is \"No Model\"", "p2p#description": "P2P is the name of an AI model that helps track plants so we can target them.", "p2p_one": "P2P Model", "p2p_other": "P2P Models", "unknown": "Unknown Model", "unknown#description": "Model refers to an AI deeplearning model"}, "reportInstances": {"fields": {"authorId": "Run by", "authorId#description": "Who ran the report, aka who created the report", "createdAt": "Published", "createdAt#description": "Date/time the report was created/published at", "name": "$t(utils.descriptors.name)"}, "run#description": "Running a report is the action of creating the report.", "run_one": "report run", "run_other": "report runs"}, "reports": {"fields": {"authorId": "Owner", "authorId#description": "Table header for the user that created the report", "automateWeekly": {"name": "Automated", "name#description": "Toggle to automate a particular report for a particular customer", "values": {"weekly": "Weekly", "weekly#description": "Every week"}}, "name": "$t(utils.descriptors.name)"}, "report#description": "Refers to the results of a reporting feature.\n\nCreating a report creates a document in table form with numbers about the activites of the selected robots, aka a report.", "report_one": "report", "report_other": "reports"}, "robots": {"classes": {"buds#description": "\"<PERSON>\" is the name of our first generation of robot, likely doesn't need to be translated unless it doesn't make sense in your language", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "Name of a type of robot that is purely used in manufacturing to validate an individual module's readiness to be sent to the field", "moduleValidationStations_one": "Module Validation Station", "moduleValidationStations_other": "Module Validation Stations", "reapersCarbon#description": "Name of a generation of robot, likely doesn;t need to be translated", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reapers", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "Name of a type of robot for a tractor on a farm, controlling the tractor itself rather than a separate piece of equipment.", "rtcs_one": "Tractor", "rtcs_other": "Tractors", "simulators#description": "Type of robot, in this case simulated robots (aka fake virtual robots)", "simulators_one": "Simulator", "simulators_other": "Simulators", "slayersCarbon#description": "Name of a generation of robot, likely doesn't need to be translated", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "Name of a generation of robot, this is a branded term, don't translated unless it absolutely necessary", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "Unknown Class", "unknown#description": "Unknown classification of robot"}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "Table header for whether or not a robot is currently thinning", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "Table header for whether or not a robot is currently weeding", "lasersOffline": "Lasers Offline", "lasersOffline#description": "Table header for how many lasers are offline on the robot", "lifetimeArea": "Lifetime Area", "lifetimeArea#description": "Table header for the total area a robot has operated", "lifetimeTime": "Lifetime Time", "lifetimeTime#description": "Table header for the total amount of time a robot has been running for", "localTime": "Local Time", "localTime#description": "Table header for the local time at the robot's location", "reportedAt": "Last Updated", "reportedAt#description": "Table header for the last time a robot was updated", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Software Version", "softwareVersion#description": "Table header for the software version of a robot", "supportSlack": "Support Slack channel", "supportSlack#description": "\"Slack\" is the name of a messaging application. Each robot has a \"channel\" in Slack, where messages about the robot are sent. This field indicates the name of that channel, which might be something like \"#support-001\".", "targetVersion": "Target Version", "targetVersion#description": "Table header for the target software version of a robot"}, "robot#description": "Refers to the product our company makes, which are robots aka automated machines etc.", "robot_one": "robot", "robot_other": "robots", "unknown": "Unknown Robot", "unknown#description": "A given robot is unknown"}, "users": {"activated": "Activated", "activated#description": "Refers to wether or not a user account has been activated", "fields": {"email": "Email", "email#description": "Email address", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Activation", "name#description": "table header for whether or not a user has been activated", "values": {"false": "PENDING", "false#description": "User activation is pending"}}}, "operator#description": "The person who operates a robot", "operator_one": "operator", "operator_other": "operators", "role#description": "Role refers to the type of user account, e.g. Basic Operator, Advanced Operator, Farm Manager, etc.", "role_one": "Role", "role_other": "Roles", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "Name for the default role for internal Carbon Robotics users", "carbon_tech": "Carbon Robotics (Technical)", "carbon_tech#description": "Name for the role for technical Carbon Robotics users", "farm_manager": "Farm Manager", "farm_manager#description": "Name for the role of a farm manager", "operator_advanced": "Operator (Advanced)", "operator_advanced#description": "Name for the role of an operator with advanced privileges", "operator_basic": "Operator", "operator_basic#description": "Name for the role of a default operator", "robot_role": "Robot", "robot_role#description": "Name for the role of a robot", "unknown_role": "Unknown Role", "unknown_role#description": "Name for an unknown role that isn't in the supported list"}, "staff": "Staff", "staff#description": "Staff is meant in the sense of company staff", "user#description": "User, refers to user accounts etc.", "user_one": "user", "user_other": "users"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "Which velocity estimator configuration is active", "velocityEstimator_one": "velocity estimator", "velocityEstimator_other": "velocity estimators"}, "weeds": {"categories": {"blossom": "Blossom", "blossom#description": "Category of weed (unwanted plant in agriculture), see glossary", "broadleaf": "Broadleaf", "broadleaf#description": "Category of weed (unwanted plant in agriculture), see glossary", "fruit": "Fruit", "fruit#description": "Category of weed (unwanted plant in agriculture), see glossary", "grass": "Grass", "grass#description": "Category of weed (unwanted plant in agriculture)", "offshoot": "Offshoot", "offshoot#description": "Category of weed (unwanted plant in agriculture), see glossary", "preblossom": "Pre-blossom", "preblossom#description": "Category of weed (unwanted plant in agriculture), see glossary", "purslane": "<PERSON><PERSON><PERSON>", "purslane#description": "Category of weed (unwanted plant in agriculture), see glossary", "runner": "Runner", "runner#description": "Category of weed (unwanted plant in agriculture), see glossary", "unknown": "Unknown Weed", "unknown#description": "Category of weed (unwanted plant in agriculture), in this case this is the default fallback of \"unknown\""}, "weed#description": "Refers to unwanted plants in agriculture", "weed_one": "weed", "weed_other": "weeds"}}, "utils": {"actions": {"add": "Add", "add#description": "Name of an action that adds something to a list", "addLong": "Add {{subject}}", "addLong#description": "Action button that adds a new instance of {{subject}} to a list", "apply": "Apply", "apply#description": "Name of an action that applies a change", "applyLong": "Apply {{subject}}", "applyLong#description": "Name of an action that applies a change to {{subject}}", "backLong": "back to {{subject}}", "backLong#description": "Action button that navigates the user back to {{subject}}", "cancel": "Cancel", "cancel#description": "Name of an action that does not proceed with an action in-progress", "cancelLong": "Cancel {{subject}}", "cancelLong#description": "Name of an action that stops the {{subject}} from completing", "clear": "Clear", "clear#description": "Name of an action that wipes out state", "confirm": "Confirm", "confirm#description": "Name of an action that affirmatively confirms an action that may have large consequences", "continue": "Continue", "continue#description": "Action button that allows a process to proceed to the next step", "copy": "Copy", "copy#description": "Name of an action that creates a copy of something", "copyLong": "Copy {{subject}}", "copyLong#description": "Name of an action that creates a copy of {{subject}}", "create": "Create", "create#description": "Name of an action that creates a new object", "createdLong": "{{subject}} created", "createdLong#description": "Message confirming that {{subject}} was created successfully", "delete": "Delete", "delete#description": "Name of an action that destroys an instance of an object", "deleteLong": "Delete {{subject}}", "deleteLong#description": "Action button that deleted {{subject}}", "deletedLong": "{{subject}} deleted", "deletedLong#description": "Message confirming that {{subject}} was deleted", "disableLong": "Disable {{subject}}", "disableLong#description": "Action button that disables {{subject}}", "discard": "Discard", "discard#description": "Name of an action that resets an object being edited to it's original state", "edit": "Edit", "edit#description": "Name of an action that opens an object into a state where it can be edited", "editLong": "Edit {{subject}}", "editLong#description": "Name of an action that opens {{subject}} into a state where it can be edited", "enableLong": "Enable {{subject}}", "enableLong#description": "Action button that turns on {{subject}}", "exit": "Exit", "exit#description": "Name of an action that exits a certain state", "exitLong": "Exit {{subject}}", "exitLong#description": "Action button that exits the view for {{subject}}", "goToLong": "Go to {{subject}}", "goToLong#description": "Action button that navigates the user to {{subject}}", "invite": "Invite", "invite#description": "Name of an action that gives someone access to an object", "inviteLong": "Invite {{subject}}", "inviteLong#description": "Action button that invites {{subject}} to join", "invitedLong": "{{subject}} invited", "invitedLong#description": "Message confirming that {{subject}} has been invited", "leaveUnchanged": "Leave unchanged", "leaveUnchanged#description": "Action button that allows the user to choose not to continue to (destructively) cancel an operation", "new": "New", "new#description": "Name of an action that creates a new instance of something", "newLong": "New {{subject}}", "newLong#description": "Action button that creates a new instances of {{subject}}", "next": "Next", "next#description": "Name of an action that moves to the following instance of something", "pause": "Pause", "pause#description": "Name of an action that stops an ongoing slideshow", "play": "Play", "play#description": "Name of an action that starts an ongoing slideshow", "previous": "Previous", "previous#description": "Action button that moves to the previous step in a process", "ranLong": "{{subject}} ran", "ranLong#description": "Message confirming that {{subject}} has run", "reload": "Reload", "reload#description": "Name of an action that restarts the current view or process", "resetLong": "Reset {{subject}}", "resetLong#description": "Action button that resets {{subject}} to its default state", "retry": "Retry", "retry#description": "Name of an action that retries something", "run": "Run", "run#description": "Name of an action that triggers a process to begin", "runLong": "Run {{subject}}", "runLong#description": "Action button that runs {{subject}}", "save": "Save", "save#description": "Name of an action that persists the current state of an object", "saveLong": "Save {{subject}}", "saveLong#description": "Action button that saves {{subject}}", "saved": "Saved", "saved#description": "Message indicating that an object was saved", "savedLong": "{{subject}} saved", "savedLong#description": "Message confirming that {{subject}} has been saved", "search": "Search", "search#description": "Name of an action that finds matching objects in a list of options", "searchLong": "Search {{subject}}", "searchLong#description": "Action button or search box placeholder for a list of {{subject}}", "selectAll": "Select All", "selectAll#description": "Action button that selects all items in a list", "selectLong": "Select {{subject}}", "selectLong#description": "Action button that selects {{subject}} for further modification", "selectNone": "Select None", "selectNone#description": "Action button that deselects all items in a list; i.e., makes it so that no item is selected", "send": "Send", "send#description": "Name of an action that sends a message", "showLong": "Show {{subject}}", "showLong#description": "Action button that shows hidden item(s) {{subject}}", "submit": "Submit", "submit#description": "Name of an action that submits a set of instructions", "toggle": "Toggle", "toggle#description": "Name of an action that turns something off if it's on or on if it's off", "toggleLong": "Toggle {{subject}}", "toggleLong#description": "Name of an action that turns {{subject}} off if it's on or on if it's off", "update": "Update", "update#description": "Name of an action that saves the changes to an existing object", "updated": "Updated", "updated#description": "Message confirming that something has been updated successfully", "updatedLong": "Updated {{subject}}", "updatedLong#description": "Message confirming that {{subject}} has been updated successfully", "uploaded": "Uploaded", "uploaded#description": "Message confirming that something has been uploaded successfully", "viewLong": "View {{subject}}", "viewLong#description": "An action button that allows the user to view some {{subject}} in more detail"}, "descriptors": {"active": "Active", "active#description": "The state of something being activated", "critical": "Critical", "critical#description": "Qualitative description of the severity of something being high", "default": "<PERSON><PERSON><PERSON>", "default#description": "The initial value or something given by carbon before any modifications", "degraded": "Degraded", "degraded#description": "State of something performing but at a lower level than normal", "dense": "<PERSON><PERSON>", "dense#description": "Quantitative description of high density", "disabled": "Disabled", "disabled#description": "The state of something being off", "duration": "Duration", "duration#description": "A length of time.", "enabled": "Enabled", "enabled#description": "The state of being turned on", "ended": "Ended", "ended#description": "Label for the end of a date or time range.", "endedAt": "Ended At", "endedAt#description": "Label for the end of a date or time range.", "error": "Error", "error#description": "The state of something being wrong or the reason why something is not working", "estopOff": "Running", "estopOff#description": "Description of the emergency stop when it is NOT pressed", "estopOn": "E-Stopped", "estopOn#description": "Description of the emergency stop when it is pressed", "fast": "Fast", "fast#description": "Quantitative description of something that is moving quickly", "few": "Few", "few#description": "Quantitative description of a small number of things", "good": "Good", "good#description": "Qualitative description of something that is good", "hidden": "Hidden", "hidden#description": "State of not being visible", "high": "High", "high#description": "Quantitative description of a large value", "id": "ID", "id#description": "Unique identifier", "inactive": "Inactive", "inactive#description": "The state of not being active", "interlockSafe": "Shooting Allowed", "interlockSafe#description": "State of the interlock safety mechanism that DOES allow shooting", "interlockUnsafe": "Shooting Prevented", "interlockUnsafe#description": "State of the interlock safety mechanism that does NOT allow shooting", "large": "Large", "large#description": "Quantitative description of an object that is larger than average", "laserKeyOff": "Locked", "laserKeyOff#description": "Describes the state of a physical safety key that must be turned on for the lasers to fire", "laserKeyOn": "Engaged", "laserKeyOn#description": "Describes the state of a physical safety key that must be turned on for the lasers to fire", "liftedOff": "Lowered", "liftedOff#description": "State of the lifted sensor indicating that the machine is lowered", "liftedOn": "Lifted", "liftedOn#description": "State of the lifted sensor indicating that the machine is lifted", "loading": "Loading", "loading#description": "State is being loaded and is not yet available", "low": "Low", "low#description": "Quantitative description of a small value", "majority": "Majority", "majority#description": "Quantitative description of something that makes up more than 50% of a whole", "medium": "Medium", "medium#description": "Quantitative description of an object of average size", "minority": "Minority", "minority#description": "Quantitative description of something that makes up less than 50% of a whole", "name": "Name", "name#description": "The description of the name of a person, object, or machine", "no": "No", "no#description": "A descriptor for a boolean option that is false", "none": "None", "none#description": "The absence of something: e.g., a selection may be \"none\" if nothing is selected, or the impact level of an alarm may be \"none\" if there is no impact", "offline": "Offline", "offline#description": "Describes the state of not being connected", "ok": "OK", "ok#description": "Qualitative descriptor for something that is medium range. Not too good, not to bad", "poor": "Poor", "poor#description": "Qualitative description of something that is not good", "progress": "Progress", "progress#description": "Describes how far along a process is. Often represented as a percentage or a ratio.", "serial": "Serial", "serial#description": "Name of a unique alphanumeric identifier for an object", "slow": "Slow", "slow#description": "Quantitative description of low speed", "small": "Small", "small#description": "Quantitative description of something that is of small size", "sparse": "Sparse", "sparse#description": "Quantitative description of low density", "started": "Started", "started#description": "Label for the start of a date or time range.", "startedAt": "Started At", "startedAt#description": "Label for the start of a date or time range.", "type#description": "A descriptor for what kind of thing something is.", "type_one": "Type", "type_other": "Types", "unknown": "Unknown", "unknown#description": "Placeholder for an a value when it is unknown", "waterProtectNormal": "Normal Moisture", "waterProtectNormal#description": "Description of the state of the water protection safety mechanism when water is NOT detected", "waterProtectTriggered": "Water Detected", "waterProtectTriggered#description": "Description of the state of the water protection safety mechanism when water is detected", "yes": "Yes", "yes#description": "A descriptor for a boolean option that is true"}, "form": {"booleanType": "Must be a boolean", "booleanType#description": "Validation error for a field that must be a boolean", "copyConfigFrom": "Copy config from...", "copyConfigFrom#description": "Copy the values of one configuration with the intent on applying it to another configuration section", "integerType": "Must be an integer", "integerType#description": "Validation error for a field that must be an integer", "maxLessThanMin": "Max must be greater than min", "maxLessThanMin#description": "Validation error for a numerical range input", "maxSize": "Cannot exceed {{limit}} characters", "maxSize#description": "A warning on a form field about the character length of an input", "minGreaterThanMax": "Min must be less than max", "minGreaterThanMax#description": "Validation error for a numerical range input", "moveDown": "Move down", "moveDown#description": "A button on a form that allows moving an item down in a list: e.g., moving a task to be later in a list of tasks", "moveUp": "Move up", "moveUp#description": "A button on a form that allows moving an item up in a list: e.g., moving a task to be earlier in a list of tasks", "noOptions": "No options", "noOptions#description": "Message shown if a drop down list contains no available options", "numberType": "Must be a number", "numberType#description": "Validation error for a field that must be a number", "optional": "(optional)", "optional#description": "Label indicating that a form field does not need to be filled out to proceed", "required": "Required", "required#description": "Label indicated that a form field must be filled out", "stringType": "Must be a string", "stringType#description": "Validation error for a field that must be a string"}, "lists": {"+3": "{{b}}, and {{c}}", "+3#description": "Formatting for the last two items in a list with three or more items: e.g. \"Updated profile(s): {{a}}, {{b}}, *{{c}}, and {{d}}*\"", "1": "{{a}}", "1#description": "Formatting for a list with one item: e.g. \"Updated profile(s): *{{a}}*\"", "2": "{{a}} and {{b}}", "2#description": "Formatting for a list with two items: e.g. \"Updated profile(s): *{{a}} and {{b}}*\"", "3+": "{{a}}, {{b}}", "3+#description": "Formatting for beginning items in a list with three or more items: e.g. \"Updated profile(s): *{{a}}, {{b}}*, {{c}}, and {{d}}\"", "loadMore": "Load more", "loadMore#description": "A request from a user to load additional paginated results", "noMoreResults": "No more results", "noMoreResults#description": "What is shown if a user has seen all available results", "noResults": "No results", "noResults#description": "What is shown when a search produces no data"}, "metrics": {"aggregates": {"max": "Max", "max#description": "Abbreviation for aggregate: \"maximum\"", "min": "Min", "min#description": "Abbreviation for aggregate: \"minimum\""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Average Crop Radius", "avgCropSizeMm#description": "Name of a metric that describes the average radius of crops observed by the machine", "avgSpeedMph": "Average Travel Speed", "avgSpeedMph#description": "Name of a metric that describes the average speed at which the machine traveled", "avgTargetableReqLaserTime": "Average Shoot Time", "avgTargetableReqLaserTime#description": "Name of a metric that describes the average shoot time requested for targets that the machine shot", "avgUntargetableReqLaserTime": "Average Shoot Time (Untargeted)", "avgUntargetableReqLaserTime#description": "Name of a metric that describes the average shoot time requested for targets that the machine ignored", "avgWeedSizeMm": "Average Weed Radius", "avgWeedSizeMm#description": "Name of a metric that describes the average radius of weeds observed by the machine", "bandingConfigName": "Banding Config", "bandingConfigName#description": "Name of a metric describing the name of the banding configuration being used", "bandingEnabled": "Banding", "bandingEnabled#description": "Name of a metric describing whether or not banding is enabled", "bandingPercentage": "Percentage Banded", "bandingPercentage#description": "Name of a metric describing what percentage of the area the robot is supposed to observe", "coverageSpeedAcresHr": "Average Coverage Speed", "coverageSpeedAcresHr#description": "Name of a metric describing the average area over time covered by a machine", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Crop Density", "cropDensitySqFt#description": "Name of a metric describing the density of crops observed by the machine", "distanceWeededMeters": "Weeding Distance", "distanceWeededMeters#description": "Name of a metric describing the distance travelled by a machine", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Crops Kept", "keptCrops#description": "Name of a metric describing the number of crops remaining after the machine thinned", "killedWeeds": "Weeds Killed", "killedWeeds#description": "Name of a metric describing the number of weeds the machine killed", "missedCrops": "Crops Missed", "missedCrops#description": "Name of a metric describing the number of crops the machine tried to shoot but wasn't able to", "missedWeeds": "Weeds Missed", "missedWeeds#description": "Name of a metric describing the number of weeds the machine tried to shoot but wasn't able to", "notThinning": "Not Thinning Crops", "notThinning#description": "Name of a metric describing the number of crops observed by the machine while not thinning", "notWeeding": "Not Weeding Weeds", "notWeeding#description": "Name of a metric describing the number of weeds observed by the machine while not weeding", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Operator Effectiveness", "operatorEffectiveness#description": "Metric that describes the effectiveness of the operator at following the target speed", "overallEfficiency": "Overall Performance", "overallEfficiency#description": "Name of a metric that describes the effectiveness of thinning and weeding on a machine", "skippedCrops": "Crops Ignored", "skippedCrops#description": "Name of a metric describing the number of crops observed but ignored by the machine", "skippedWeeds": "Weeds Ignored", "skippedWeeds#description": "Name of a metric describing the number of weeds observed but ignored by the machine", "targetWeedingTimeSeconds": "Target Weeding Time", "targetWeedingTimeSeconds#description": "Metric that describes how long weeding should have taken if the operator was driving the optimal speed", "thinnedCrops": "Crops Thinned", "thinnedCrops#description": "Name of a metric counting the number of crops thinned by the machine", "thinningEfficiency": "Thinning Performance", "thinningEfficiency#description": "Name of a metric describing how effectively a machine was thinning", "timeEfficiency": "Operational Efficiency", "timeEfficiency#description": "Name of a metric describing what percentage of time that the machine was on was it actually operating", "totalCrops": "Crops Found (Total)", "totalCrops#description": "Name of a metric describing the total count of all crops found by the machine", "totalWeeds": "Weeds Found", "totalWeeds#description": "Name of a metric describing the total count of all weeds found by the machine", "totalWeedsInBand": "Weeds Found (In-band)", "totalWeedsInBand#description": "Name of a metric describing the total number of weeds found within an area configured by the user", "uptimeSeconds": "Uptime", "uptimeSeconds#description": "Name of a metric describing how long the machine was turned on", "validCrops": "Crops Found", "validCrops#description": "Name of a metric describing the total count of all crops found by the machine", "weedDensitySqFt": "Weed Density", "weedDensitySqFt#description": "Name of a metric that describes the density of weeds observed in the field", "weedingEfficiency": "Weeding Performance", "weedingEfficiency#description": "Name of a metric that describes how effectively a machine was weeding", "weedingUptimeSeconds": "Weeding Time", "weedingUptimeSeconds#description": "Name of a metric that describes the amount of time a machine was weeding", "weedsTypeCountBroadleaf": "Weed Type: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "Name of a metric describing the percentage of weeds that were categorized as Broadleaf", "weedsTypeCountGrass": "Weed Type: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "Name of a metric describing the percentage of weeds that were categorized as Grass", "weedsTypeCountOffshoot": "Weed Type: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "Name of a metric describing the percentage of weeds that were categorized as Offshoot", "weedsTypeCountPurslane": "Weed Type: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": "Name of a metric describing the percentage of weeds that were categorized as Purslane"}, "metricsHelp": {"avgCropSizeMm": "This is calculated prior to thinning if thinning was enabled", "avgCropSizeMm#description": "Help text describing how we compute a metric called Average Crop Radius", "bandingConfigName": "Your most recently selected banding profile", "bandingConfigName#description": "Help text describing the meaning of a metric called Banding Config", "crop": "Your most recently selected crop", "crop#description": "Help text explaining how we select which crop (plant) to display", "cropDensitySqFt": "Crops Found / Coverage. This is calculated prior to thinning if thinning was enabled", "cropDensitySqFt#description": "Help text explaining how we calculate a metric called Estimated Crop Density. \"Thinning\" refers to the process of deliberately killing some crops so that other crops can grow bigger. The slash indicates division (crops found divided by coverage), not an alternative.", "keptCrops": "The estimated number of crops kept post-thinning", "keptCrops#description": "Help text explaining how we calculate a metric called Crops Kept. \"Thinning\" refers to the process of deliberately killing some crops so that other crops can grow bigger. These are the crops that we choose not to kill.", "killedWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> identified object as a weed and shot it", "killedWeeds#description": "Help text explaining how we calculate a metric called Weeds Killed", "missedCrops": "<PERSON><PERSON> was marked for thinning but missed. Common reasons include: overspeeding, out of range, or system error.", "missedCrops#description": "Help text explaining how we calculate a metric called Crops Missed.", "missedWeeds": "We<PERSON> was identified but missed. Common reasons include: overspeeding, out of range, or system error.", "missedWeeds#description": "Help text explaining how we calculate a metric called Weeds Missed.", "operatorEffectiveness": "Shows how well actual travel speed matched the target speed recommended by the Velocity Estimator", "operatorEffectiveness#description": "Help text explaining how we calculate a metric called Speed Efficiency", "overallEfficiency": "(Weeding Performance + Thinning Performance) / 2, if you’re both weeding and thinning. The slash indicates division (sum of performance divided by 2), not an alternative.", "overallEfficiency#description": "Help text explaining how we calculate a metric called Overall Performance", "skippedCrops": "<PERSON><PERSON> was intentionally skipped during thinning. Common reasons include: disabled in Quick Tune, out of band, or nearby driptape.", "skippedCrops#description": "Help text explaining how we calculate a metric called Crops Skipped. \"Out of band\" means: we only shoot crops that are within a desired strip of land, called a \"band\".", "skippedWeeds": "<PERSON><PERSON> was intentionally skipped. Common reasons include: disabled in Quick Tune, or out of band.", "skippedWeeds#description": "Help text explaining how we calculate a metric called Weeds Skipped. \"Out of band\" means: we only shoot crops that are within a desired strip of land, called a \"band\".", "thinningEfficiency": "(Crops Thinned + Crops Kept) / Crops Found × 100%", "thinningEfficiency#description": "Help text describing how we compute a metric called Thinning Performance. \"Thinned Crops\" means crops that we decided to kill to help other crops grow bigger. The slash indicates division (crops thinned plus crops kept, all divided by crops found), not an alternative.", "timeEfficiency": "(Active Working Time / Power On Time) × 100%", "timeEfficiency#description": "Help text describing how we compute a metric called Machine Utilization. Power-on time refers to the amount of time that the machine is powered. The slash indicates division (active working time divided by power on time), not an alternative.", "uptimeSeconds": "The full amount of time LaserWeeder was powered on. Includes when it is in standby mode and/or lifted.", "uptimeSeconds#description": "Help text describing how we compute a metric called Power On Time.", "weedDensitySqFt": "Weeds Found (total) / Coverage", "weedDensitySqFt#description": "Help text describing how we compute a metric called Estimated Weed Density. \"Coverage\" refers to an amount of land area covered, like \"5 acres\" or \"2 hectares\". The slash indicates division (total weeds found divided by coverage), not an alternative.", "weedingEfficiency": "(Weeds Killed / Weeds Found In Band) × 100%", "weedingEfficiency#description": "Help text describing how we compute a metric called Weeding Performance. A \"band\" is a strip of land in which we'll shoot weeds. The slash indicates division (weeds killed divided by weeds found in band), not an alternative.", "weedingUptimeSeconds": "The amount of time LaserWeeder was actively weeding or thinning", "weedingUptimeSeconds#description": "Help text describing how we compute a metric called Active Working Time."}, "metricsRenamed": {"bandingConfigName": "Banding Profile", "bandingConfigName#description": "Name of a metric describing the banding configuration being used. A \"band\" is a strip of land in which we'll shoot weeds. A \"profile\" refers to how the bands are configured.", "operatorEffectiveness": "Speed Efficiency", "operatorEffectiveness#description": "Name of a metric describing how well the speed of the tractor has matched the desired speed", "timeEfficiency": "Machine Utilization", "timeEfficiency#description": "Name of a metric describing how much of the time the robot has been doing useful work while it's powered on", "totalWeeds": "Weeds Found (total)", "totalWeedsInBand": "Weeds Found (in-band)", "totalWeedsInBand#description": "Name of a metric describing the total number of weeds found within an area configured by the user", "uptimeSeconds": "Power On Time", "uptimeSeconds#description": "Name of a metric describing the amount of time that the robot has been physically powered, turned on", "validCrops": "Crops Found", "validCrops#description": "Name of a metric describing our best guess for the number of crops (plants) in a farm field", "weedingUptimeSeconds": "Active Working Time", "weedingUptimeSeconds#description": "Name of a metric describing the amount of time that the robot has been doing useful work"}}, "groups": {"coverage": "Coverage", "coverage#description": "Title of a group of metrics describing the area covered by a machine", "field": "Field", "field#description": "Title of a group of metrics describing the composition or configuration of a field", "hardware": "Hardware (Internal)", "hardware#description": "Title of a group of metrics describing the state of a robot's hardware. These are only shown to our company's internal employees, not external customers.", "performance": "Performance", "performance#description": "Title of a group of metrics describing the effectiveness of a machine's operation", "speed": "Speed", "speed#description": "Name of a group of metrics describing how fast a machine traveled", "speedDetails": "Speed Details (Internal)", "speedDetails#description": "Title of a group of metrics describing details about how fast different parts of the robot were traveling. These are only shown to our company's internal employees, not external customers.", "usage": "Usage", "usage#description": "Title of a group of metrics describing how long a machine was used"}, "metric#description": "Noun that described a something that can be measured quantitatively over time", "metric_one": "metric", "metric_other": "metrics", "spatial": {"heatmapWarning": "per ~{{area}} 'block'", "heatmapWarning#description": "Description of the average size of the \"block\" that metrics are recorded on", "metrics": {"altitude": "Altitude", "altitude#description": "Name of a metric that describes the altitude above sea level of the machine", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "E-Stop", "estopped#description": "Name of a metric that describes whether a machine was stopped or not via the emergency stop buttons", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Interlock", "interlock#description": "Metric that describes the state of the interlock (a satefy feature)", "keptCropDensity": "Kept Crop Density", "keptCropDensity#description": "Metric that describes that density of crops remaining after a machine thinned", "laserKey": "Laser Key", "laserKey#description": "Metric that describes the state of the laser safety key", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Target Speed Ratio", "speed#description": "Metric that describes the ratio of the robot's target speed with the tractor's actual speed", "speedTargetMinimum": "Average Target Speed (Minimum)", "speedTargetMinimum#description": "Name of a metric that describes the minimum of the average target speeds of three different rows", "speedTargetRow1": "Average Target Speed (Row 1)", "speedTargetRow1#description": "Name of a metric that describes the average target speed of row one", "speedTargetRow2": "Average Target Speed (Row 2)", "speedTargetRow2#description": "Name of a metric that describes the average target speed of row two", "speedTargetRow3": "Average Target Speed (Row 3)", "speedTargetRow3#description": "Name of a metric that describes the average target speed of row three", "speedTargetSmoothed": "Average Target Speed", "speedTargetSmoothed#description": "Name of a metric that describes the average target speed of the machine", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Thinning", "thinning#description": "Name of a metric that describes whether the machine was thinning or not", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Time", "time#description": "Name of a metric that describes the time at which data was sampled", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "Name of a metric that describes the total number of crops found meeting some validation criteria", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Water Protection", "waterProtect#description": "name of a metric that describes whether the machine's water protection safety was triggered or now", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Weeding", "weeding#description": "Name of a metric that described whether the machine was weeding or not", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Selected", "selected#description": "Name for one or more rows that are staged for changes", "showAll": "Show all {{objects}}", "showAll#description": "This text is shown in a drop-down menu that allows a user to select an attribute to filter by. For example, a user could select a customer to see only entities belonging to that customer, or they could click this item, which might read \"Show all customers\", to see entities from all customers."}, "units": {"%": "%", "%#description": "Abbreviation for measure of percentage", "/ac": "/ac", "/ac#description": "Abbreviation for a meausure of density: per acre", "/ft2": "/ft²", "/ft2#description": "Abbreviation for a meausure of density: per square foot", "/ha": "/ha", "/ha#description": "Abbreviation for a meausure of density: per hectare", "/in2": "/in²", "/in2#description": "Abbreviation for a meausure of density: per square inch", "/km2": "/km²", "/km2#description": "Abbreviation for a meausure of density: per square kilometer", "/m2": "/m²", "/m2#description": "Abbreviation for a meausure of density: per square meter", "/mi2": "/mi²", "/mi2#description": "Abbreviation for a meausure of density: per square mile", "W": "W", "W#description": "Abbreviation for a meausure of electrical power: watts", "WLong#description": "Meausure of electrical power: watts", "WLong_one": "watt", "WLong_other": "watts", "ac": "ac", "ac#description": "Abbreviation for a measure of area: acre", "ac/h": "ac/h", "ac/h#description": "Abbreviation for measure of coverage: acres per hour", "acLong#description": "Units for a measure of area", "acLong_one": "acre", "acLong_other": "acres", "acres#description": "Units for a measure of area", "cm": "cm", "cm#description": "Abbreviation for measure of area: centimeters", "cm2": "cm²", "cm2#description": "Abbreviation for measure of area: square centimeters", "d": "d", "d#description": "Abbreviation for unit for a measure of time", "dLong#description": "Unit for a measure of time", "dLong_one": "day", "dLong_other": "days", "day#description": "Unit for a measure of time", "days#description": "Units for a measure of time", "ft": "ft", "ft#description": "Abbreviation for a measure of length: feet", "ft/s": "ft/s", "ft/s#description": "Abbreviation for a measure of speed: feet per second", "ft2": "ft²", "ft2#description": "Abbreviation for measure of area: square feet", "ftLong#description": "Units for a measure of length: feet", "ftLong_one": "foot", "ftLong_other": "feet", "h": "h", "h#description": "Abbreviation for a measure of time: hour", "hLong#description": "Units for a measure of time: hour", "hLong_one": "hour", "hLong_other": "hours", "ha": "ha", "ha#description": "Abbreviation for a measure of area: hectares", "ha/h": "ha/h", "ha/h#description": "Abbreviation for measure of coverage: hectares per hour", "haLong#description": "Units for a measure of area: hectares", "haLong_one": "hectare", "haLong_other": "hectares", "hectares#description": "Units for a measure of area", "hours#description": "Units for a measure of time", "in": "in", "in#description": "Units for a measure of length", "in2": "in²", "in2#description": "Abbreviation for a meausure of area: square inches", "km": "km", "km#description": "Abbreviation for a meausure of length: kilometers", "km/h": "km/h", "km/h#description": "Abbreviation for a meausure of speed: kilometers per hour", "km2": "km²", "km2#description": "Abbreviation for a meausure of area: square kilometers", "kph#description": "Abbreviation for a meausure of speed: kilometers per hour", "m": "m", "m#description": "Abbreviation for a meausure of length: meters", "m/s": "m/s", "m/s#description": "Abbreviation for a meausure of speed: meters per second", "m2": "m²", "m2#description": "Abbreviation for a meausure of area: square meters", "mLong#description": "Meausure of length: meters", "mLong_one": "meter", "mLong_other": "meters", "mi": "mi", "mi#description": "Abbreviation for a meausure of length: miles", "mi2": "mi²", "mi2#description": "Abbreviation for a meausure of area: square miles", "min": "min", "min#description": "Abbreviation for units for a meausure of time", "minLong#description": "Units for a meausure of time: minute", "minLong_one": "minute", "minLong_other": "minutes", "minutes#description": "Units for a meausure of time", "mm": "mm", "mm#description": "Abbreviation for a meausure of length: millimeters", "month": "mo", "month#description": "Abbreviation for a meausure of time: month", "monthLong#description": "Units for a meausure of time: month", "monthLong_one": "month", "monthLong_other": "months", "mph": "mph", "mph#description": "Abbreviation for a meausure of speed: miles per hour", "ms": "ms", "ms#description": "Abbreviation for a meausure of time: milliseconds", "s": "s", "s#description": "Abbreviation for a meausure of time: seconds", "sLong#description": "Units for a meausure of time: seconds", "sLong_one": "second", "sLong_other": "seconds", "seconds#description": "Units for a meausure of time", "watts#description": "Units for a meausure of electrical power", "week": "w", "week#description": "Abbreviation for a measure of time: week. Note: should not be the same as the abbreviation for \"seconds\"!", "weekLong#description": "Units for a meausure of time: week", "weekLong_one": "week", "weekLong_other": "weeks", "yd#description": "Abbreviation for a meausure of length: yard", "year": "yr", "year#description": "Abbreviation for a meausure of time: year", "yearLong#description": "Units for a meausure of time: year", "yearLong_one": "year", "yearLong_other": "years"}}, "views": {"admin": {"alarms": {"allowWarning": "Adding codes to the allowlist will allow alarms to ping support Slack channels", "allowWarning#description": "When the robot is not functioning as expected, we have alarms that can be triggered. Each alarm has a 4 or 5 digit code associated with it, such as WT002.\n\nThis page allows a user to input specific alarm codes to alert the entire Carbon Robotics Support team on our messaging platform (called Slack). \n\nNote: the example shown in the photo, \"mey\", is a proper noun / last name of an employee and not representative of the usual code that would be added here.", "blockWarning": "Adding codes to the blocklist will prevent alarms to ping support Slack channels", "blockWarning#description": "When the robot is not functioning as expected, we have alarms that can be triggered. Each alarm has a 4 or 5 digit code associated with it, such as WT002.\n\nThis block function allows a user to input specific alarm codes that will NOT alert the entire Carbon Robotics Support team on our messaging platform (called Slack). ", "lists": "Lists", "lists#description": "This list is a list of 4 or 5 digit codes, associated with specific alarms, that will notify the entire Carbon Robotics Support team when active", "title": "Global Alarm Allowlist", "title#description": "When the robot is not functioning as expected, we have alarms that can be triggered. Each alarm has a 4 or 5 digit code associated with it, such as WT002.\n\nThis is the master list of alarms that are permitted to notify the entire Carbon Robotics Support team on our messaging platform (Slack).\n\nNote: the example shown in the photo, \"mey\", is a proper noun / last name of an employee and not representative of the usual code that would be added here.", "titleAllow": "Alarm Allowlist", "titleAllow#description": "When the robot is not functioning as expected, we have alarms that can be triggered. Each alarm has a 4 or 5 digit code associated with it, such as WT002.\n\nThis is the master list of alarms that are permitted to notify the entire Carbon Robotics Support team on our messaging platform (Slack).", "titleBlock": "Alarm Blocklist", "titleBlock#description": "When the robot is not functioning as expected, we have alarms that can be triggered. Each alarm has a 4 or 5 digit code associated with it, such as WT002.\n\nThis is the master list of alarms that are NOT permitted to notify the entire Carbon Robotics Support team on our messaging platform (Slack)."}, "config": {"bulk": {"actions": {"set": "Set", "set#description": "Set (change) a specific option to a specific value."}, "allRows": "<all rows>", "allRows#description": "Our first-generation robot is divided into three \"rows\", each with one computer and ten lasers. This describes the set of all three rows.", "allRowsDescription": "<tt>rows/*</tt> on <PERSON>, <tt>{row1,row2,row3}</tt> on Slayer", "allRowsDescription#description": "This describes what the concept of \"all rows\" means on our robots: for Reapers, it means \"rows/*\", whereas for Slayers it means \"row1, row2, and row3\". \"Slayer\" is the code name for our first-generation robot, and \"Reaper\" is the code name for our second-generation robot. Neither of these should be translated. The strings \"rows/*\", \"row1\", \"row2\", and \"row3\" also should not be translated.", "listItems": "<list items>", "listItems#description": "This refers collectively to the items in a homogeneous list.", "operation#description": "An operation that can be performed", "operation_one": "operation", "operation_other": "operations", "operationsCount": "Operations ({{count}})", "operationsCount#description": "A display of how many operations have been planned. An operation is a description of a change to be performed.", "operationsHint": "Select a node in the config schema to add an operation.", "operationsHint#description": "Instructional hint to a user. The user should navigate the configuration schema to find the node that they're looking for, and will then be able to plan an operation to change that node.", "outcomeDescriptions": {"encounteredErrors#description": "Indicates that the operation ran into some specified number of errors", "encounteredErrors_one": "encountered {{count}} error", "encounteredErrors_other": "encountered {{count}} errors", "noChanges": "no changes", "noChanges#description": "Indicates that there were no successes and no failures; nothing changed", "updatedKeys#description": "Indicates that the given number of configuration keys (settings) were updated", "updatedKeys_one": "updated {{count}} key", "updatedKeys_other": "updated {{count}} keys"}, "outcomes": {"failure": "Failure", "failure#description": "Indicates that all operations failed", "partial": "Partial success", "partial#description": "Indicates that at least one operation failed and at least one operation succeeded, so overall the result was a partial success", "success": "Success", "success#description": "Indicates that all operations succeeded"}, "title": "Bulk Configs", "title#description": "Bulk Configs is an interface that lets an administrator change many configurations at the same time: i.e., make changes in bulk."}, "clearCaches": {"action": "<PERSON><PERSON><PERSON>", "action#description": "This is a button that allows a Carbon Robotics employee to refresh the Robot Syncer cache. \n\nIn software, a cache is a hardware or software component that stores data so that future requests for that data can be served faster. The data stored in a cache that might be causing problems due to old data", "description": "Problems? Try refreshing the Robot Syncer cache first.", "description#description": "This is a note that appears when a Carbon Robotics employee hovers over the \"clear caches\" button. It explains that clearing the cache will slow down some queries in the short term, but it may fix issues with stuck stale data. \n\nIn software, a cache is a hardware or software component that stores data so that future requests for that data can be served faster. The data stored in a cache that might be causing problems due to old data"}, "warnings": {"global": "Modifying this config will affect the defaults and recommendations for all current and future {{class}}", "global#description": "This is an internal warninh for the configurations page. If a Carbon Robotics employee edits the config page then it will affect the configurations of every robot.", "notSimon": "You're not <PERSON> or <PERSON>, so you probably shouldn't be editing this... 👀", "notSimon#description": "This is an internal note on the configurations page. <PERSON> and <PERSON> are proper names. <PERSON> and <PERSON> are the only people in the organization who should be editing the config page.", "unsyncedKeys": {"description": "The following changes have not yet been synced to {{serial}}:", "description#description": "This is a warning to the user that the keys are not being used by the robot. This is a list of keys that are not synced with the robot. This means that these config values on the robot are out of date.", "title": "Unsynced Keys", "title#description": "This is a list of keys that are not synced with the robot. This means that these config values on the robot are out of date. This is a warning to the user that the keys are not being used by the robot."}}}, "portal": {"clearCaches": {"action": "<PERSON> Caches", "action#description": "This is a button that allows a Carbon Robotics employee to clear the internal caches for Ops Center. \n\nIn software, a cache is a hardware or software component that stores data so that future requests for that data can be served faster. The data stored in a cache that might be causing problems due to old data", "description": "Clears the internal caches for Ops Center. This <0>will</0> slow some queries down in the short term but <1>may</1> fix issues with stuck stale data.", "description#description": "This is a note that appears when a Carbon Robotics employee hovers over the \"clear caches\" button. It explains that clearing the cache will slow down some queries in the short term, but it may fix issues with stuck stale data. \n\nIn software, a cache is a hardware or software component that stores data so that future requests for that data can be served faster. The data stored in a cache that might be causing problems due to old data", "details": "Press this if you have manually edited a user's permissions in Auth0 (not through Ops Center), or made changes to a third-party integration like Stream or Slack that aren't being reflected.", "details#description": "This note explains more details about when a particular button should be pressed."}, "title": "Ops Center", "title#description": "Ops Center is the name of the software that Carbon Robotics uses to monitor and control the robots. It is a web-based application that allows Carbon Robotics employees to view the status of the robots, as well as control them remotely.", "warnings": {"global": "Options on this page will affect the live operation of Ops Center in production.", "global#description": "This is an internal warninh for the configurations page.", "notPortalAdmin": "You're not <PERSON><PERSON> or <PERSON>, so you probably shouldn't be editing this... 👀", "notPortalAdmin#description": "This is an internal note on the configurations page. <PERSON><PERSON> and <PERSON> are proper names. <PERSON><PERSON> and <PERSON> are the only people in the organization who should be editing the config page."}}, "robot": {"warnings": {"supportSlackLeadingHash": "Support Slack channel should start with \"#\": e.g., \"#support-001-carbon\"", "supportSlackLeadingHash#description": "A user can set the \"support Slack channel\" for a robot, which should start with the hash/octothorpe character \"#\". This warning appears when the user enters a value that does not start with that character."}}, "title": "Admin", "title#description": "Admin is shorthand for the word Administrator. Under the \"admin\" tab there is access to certain permissions that only Carbon Robotics employees would have. Customers do not have admin privileges."}, "autotractor": {"actions": {"hidePivotHistory": "Hide Pivot History", "hidePivotHistory#description": "<PERSON>des pivot history on the autotractor map.", "markComplete": "Mark Complete", "markComplete#description": "Marks a task complete, regardless of it's progress.", "orchestrateView": "Assign to tractors", "orchestrateView#description": "Navigates to a view where the work associated with an autotractor job can be assigned to tractors.", "showPivotHistory": "Show Pivot History", "showPivotHistory#description": "Shows pivot history on the autotractor map, scoped to the selected date range."}, "fetchFailed": "Failed to load location data", "fetchFailed#description": "Error displayed in snackbar component when the location fetch times out.", "goLive": "live update", "goLive#description": "Aria label for a button which makes the Mission Control map update in real time.", "hideRows": "Hide Rows", "hideRows#description": "Hides the overlay of rows on the map.", "jobDetails": {"assignmentsFailed": "Failed to fetch assignment, retry?", "assignmentsFailed#description": "Warning when the assignments query fails. Appears in a tooltip over a retry button.", "cancelDialog": {"description": "The job will no longer be able to be assigned to tractors and must be recreated.", "description#description": "A warning that cancelled Autotractor jobs can no longer be assigned to Autotractors and a new job must be created instead."}, "customer": {"unknown": "Customer unknown", "unknown#description": "Label indicating that the customer was created on behalf of an unknown customer.", "withName": "Customer: {{name}}", "withName#description": "Label indicating that the Autotractor job was created on behalf of a customer named {{name}}."}, "farm": {"unknown": "Farm unknown", "unknown#description": "Label indicating that the farm on which the Autotractor job is located is unknown.", "withName": "Farm: {{name}}", "withName#description": "Label indicating that the Autotractor job is located on a farm named {{name}}."}, "field": {"unknown": "Field unknown", "unknown#description": "Label indicating that the field on which the Autotractor job is operating is unknown.", "withName": "Field: {{name}}", "withName#description": "Label indicating that the Autotractor job is operating on a field named {{name}}."}, "jobFinished": "Job finished at {{time}}", "jobFinished#description": "Label indicating that the Autotractor job finished at the time {{time}}.", "jobStarted": "Job started at {{time}}", "jobStarted#description": "Label indicating that the Autotractor job started at the time {{time}}.", "openInFarmView": "Open in farm view", "openInFarmView#description": "Tooltip indicating that when the button is clicked, the user's browser will open the associated object in farm view", "state": "State: {{state}}", "state#description": "Label indicating the current state of the Autotractor job (pending, ready, in progress, etc.)", "type": "Job type: {{type}}", "type#description": "Label indicating which type of task this Autotractor job performs (laser weeding, ground prep, etc.)"}, "lastPolled": "Last Polled", "lastPolled#description": "The last time a data point was polled.", "live": "Live", "live#description": "Indicates that the Mission Control map is live-updating with real time location data.", "objectiveFromOtherJob": "Objective from another job", "objectiveFromOtherJob#description": "The specified objective is from a different job.", "rowWidthUnits": "Row width {{units}}", "rowWidthUnits#description": "Input variable for the width of the field's rows.", "selection": {"farms": "Farms", "farms#description": "Label for a collapsible list of farms.", "tractors": "Tractors", "tractors#description": "Label for a collapsible list of tractors."}, "showRows": "Show Rows", "showRows#description": "Show an overlay of rows on the map.", "stalePivots": "Pivot info may be stale", "stalePivots#description": "Indicates the pivot information on screen may be out of date", "suggestedAssignments": "Suggested Assignments", "suggestedAssignments#description": "The recommended job assignments for a given tractor.", "taskCriteria": {"gearStateValid": "Gear", "gearStateValid#description": "Expected tractor gear.", "headingValid": "Heading", "headingValid#description": "Expected heading delta.", "hitchStateValid": "Hitch Position", "hitchStateValid#description": "Expected hitch position.", "posDistValid": "Distance", "posDistValid#description": "Expected distance from the starting point", "posXteValid": "Cross Track", "posXteValid#description": "Expected cross track error."}, "unassignDialog": {"body": "You are attempting unassign a tractor from the objective it is currently working on. Any further progress the tractor makes will not be tracked. Ensure the tractor stops before you continue."}}, "farms": {"actions": {"hideThesePoints": "Hide These Points", "hideThesePoints#description": "Hide all the points in this category", "onlyShowSelected": "Only Show Selected", "onlyShowSelected#description": "Button that hides all points except for the selected one(s).", "showAllPoints": "Show All Points", "showAllPoints#description": "Button that shows all points on the farm map.", "showThesePoints": "Show These Points", "showThesePoints#description": "Show all the points in this category"}, "detailsPanel": {"boundary": "Boundary", "boundary#description": "Label for the collapsable section including zone boundary information.", "center": "Center", "center#description": "Centerpoint of the pivot, associated with a point in space.", "centerPivot": "Center Pivot", "centerPivot#description": "Label for the collapsable section including center pivot info.", "endpointId": "Endpoint Id", "endpointId#description": "Id for the gps device on the end of the center pivot.", "holes": "Holes", "holes#description": "Label for the collapsable section including information on holes in the zone boundary.", "length": "Length", "length#description": "Length of the center pivot arm.", "plantingHeading": "Planting heading", "plantingHeading#description": "The compass direction in which rows of crops are planted. \"Heading\" means direction. This is also represented by an AB line of two points.", "point": "Point", "point#description": "A point of interest on a farm, such as the center of a field, or a waypoint along a road.", "points": "Points", "points#description": "Label for a list of points.", "width": "<PERSON><PERSON><PERSON>", "width#description": "Width of the center pivot arm."}, "farm": "Farm", "farm#description": "The data model we use to represent a farm.", "fixTypes": {"gps": "GPS", "gps#description": "GPS stands for Global Positioning System. This is used to describe points on the Earth that were measured with a GPS device, as opposed to manually entering coordinates or picking a point on a map.", "none": "No fix", "none#description": "This describes a point on the Earth that was selected by hand, either by entering latitude and longitude coordinates, or by identifying a position on a satellite image. \"No fix\" means that the point was not collected with an accurate connection to GPS satellites or similar systems: it is not \"locked on\" or \"fixed\" to those systems.", "rtkFixed": "RTK Fixed", "rtkFixed#description": "RTK (Real-Time Kinematics) is a technical term for a system that improves the location accuracy of GPS data collection. \"RTK Fixed\" is a specific state that RTK can be in, indicating that the GPS receiver has \"locked on\" to, or \"fixed\" to, its exact position and can produce very accurate data.", "rtkFloat": "RTK Floating", "rtkFloat#description": "RTK (Real-Time Kinematics) is a technical term for a system that improves the location accuracy of GPS data collection. \"RTK Floating\" is a specific state that RTK can be in, indicating that the GPS receiver has not yet managed to precisely identify its position; it is still \"floating\" around within some margin of error.", "unknown": "Unknown fix type", "unknown#description": "This describes a point on the Earth that was collected by an unknown mechanism. \"Unknown fix\" means the way that the collector \"locked on\" or \"fixed\" to their location is not known."}, "selectionPanel": {"allPoints": "All Points", "allPoints#description": "All the points in the farm definition.", "boundary": "Boundary", "boundary#description": "Label for the collapsable section including zone boundary information.", "center": "Center", "center#description": "Centerpoint of the pivot, associated with a point in space.", "centerPivot": "Center Pivot", "centerPivot#description": "Label for the collapsable section including center pivot info.", "endpointId": "Endpoint Id", "endpointId#description": "Id for the gps device on the end of the center pivot.", "holes": "Holes", "holes#description": "Label for the collapsable section including information on holes in the zone boundary.", "length": "Length", "length#description": "Length of the center pivot arm.", "plantingHeading": "Planting heading", "plantingHeading#description": "The compass direction in which rows of crops are planted. \"Heading\" means direction. This is also represented by an AB line of two points.", "point": "Point", "point#description": "A point of interest on a farm, such as the center of a field, or a waypoint along a road.", "points": "Points", "points#description": "Label for a list of points.", "width": "<PERSON><PERSON><PERSON>", "width#description": "Width of the center pivot arm."}, "unnamedPoint": "Unnamed point <0>{{pointId}}</0>", "unnamedPoint#description": "In the context of a map of a farm, this describes a point on the Earth that has some meaning but has not been given a name. For instance, this point may represent the center of a field. The \"point ID\" parameter is an internal, technical identifier for the point, like \"QOpggy75FAhS\". It does not have any semantic meaning.", "zoneTypes": {"farmBoundary": "Farm Boundary", "farmBoundary#description": "The boundary of the entire farm.", "field": "Field", "field#description": "A field on a farm where crops are planted.", "headland": "Headland", "headland#description": "An area of land adjacent to a farm field in which crops are planted, but on the outskirts. A headland might have no crops in it and only be used for driving, or it may have crops in it but there may be differences between the headland and the rest of the field.", "obstacle": "Obstacle", "obstacle#description": "An obstacle that must be avoided while driving on a farm. For example, this could be a fence, or a ditch, or a piece of irrigation equipment, or a big rock.", "privateRoad": "Private road", "privateRoad#description": "A road on a farm that is on private property. Farm employees are permitted to drive on the road, but members of the public are not.", "unknown": "Unknown zone type", "unknown#description": "Some geographic region on a farm (a \"zone\") without additional information about what kind of zone it is."}}, "fieldDefinitions": {"controls": {"draw": "Draw", "draw#description": "Label for a button that, when clicked, lets the user draw lines and polygons on a visual map of the world instead of manually entering coordinates"}, "errors": {"exactlyTwoPoints": "Line should have exactly two points", "exactlyTwoPoints#description": "The user has drawn a \"line\", but it contains more than two points. They should draw the line again and include only two points.", "wrongFieldType": "Field \"{{field}}\" should be {{want}}", "wrongFieldType#description": "The user pasted in a computer-friendly value, but one of the fields was of the wrong type.", "wrongGeometryType": "Geometry should be of type {{want}}", "wrongGeometryType#description": "The user has specified the wrong kind of geometric object. For example, maybe we asked them for a polygon but they gave us a line.", "wrongJsonType": "JSON should be an object", "wrongJsonType#description": "The user pasted in a computer-friendly value, but it was the wrong kind of value. We want it to be a kind of value called an \"object\"; maybe they gave us a number."}}, "fleet": {"missionControl": {"errors": {"empty": "No robots online", "empty#description": "This is an error shown to users if they try to use Mission Control view and there are no robots online. Mission Control mode is a way to view all the robots that are active in a timed slideshow format."}, "title": "Mission Control", "title#description": "Mission Control is a special type of view that allows the user to watch through all the robots in the fleet, with a robot changing every 10 seconds. It shows some summary-level information as well as a visual representation"}, "robots": {"config": {"auditLog": {"open": "View change history", "open#description": "This is a button that loads the audit log for a particular configuration key", "title": "Change History", "title#description": "The audit log is a way for the user to view the history of changes made to the configurations."}, "errors": {"failed": "Failed to load config tree", "failed#description": "This is an error that pops up on our configuration page when the config tree fails to load. \nThe configurations page is a way to make the software function specific to the user's liking. \n\nIn software, a \"tree\" is a nonlinear data structure that resembles the shape of a tree and organizes data in a way that is easy to navigate."}, "onlyChanged": "Only Show Changed", "onlyChanged#description": "The configurations page is a way to make the software function specific to the user's liking. If you toggle on the \"only show changed\" button, only configurations that have been adjusted will be shown to the user"}, "errors": {"empty": "No robots assigned", "empty#description": "This is an error the user sees when they try to filter their view of robots, and no robots match the selected criteria"}, "hardware": {"errors": {"old": "Robot not reporting computer serials (probably too old)", "old#description": "This is an error that shows when the user cannot view the serial number associated with a certain computer system on the robot. It is likely because it is out of date"}, "fields": {"hostname": "Hostname", "hostname#description": "A hostname is a unique label assigned to a device connected to a computer network. Under this page, Carbon employees are able to view the unique serial number associated with each computer"}, "installedVersion": "Installed version:", "installedVersion#description": "This is the software version that is currently downloaded and installed on the robot", "ready": {"name": "Software Update Status", "name#description": "This status tells the user if the newest software update is actively downloading or completed", "values": {"false": "Downloading...", "false#description": "When the software version status says \"downloading\" that means its getting ready to update to the newest software and changes. ", "installed": "Installed", "installed#description": "When the software version status says \"installed\" that means the newest software update has been successfully installed onto the robot", "true": "Ready!", "true#description": "\"ready\" means that the newest software update has been downloaded and can be installed onto the robot"}}, "tabs": {"computers": "Computers", "computers#description": "Each robot has multiple computers inside it that are responsible for the functionality of the robot. The computers page is an internal page for Carbon Robotics employees to view the serial number associated with each computer inside the robot. ", "versions": "Versions", "versions#description": "Every time software gets changed or updated, there is a new 'version' associated with these changes. \n\nThis is a page for Carbon Robotics employees to view which version of the software is installed on the robot, as well as its status. \n"}, "targetVersion": "Target version:", "targetVersion#description": "Every time software gets changed or updated, there is a new 'version' associated with these changes. \n\nWhen a new software version is ready to be installed on the robot, a Carbon Robotics employee will set it as the \"target version\". ", "title": "Hardware", "title#description": "Our robot depends on both software and hardware to function. The \"hardware\" view is a way for users to view the hardware status of the robot, such as if the robot was lifted off the ground, stopped, or unable to fire lasers. ", "updateHistory": "Version update history <0>coming soon™️</0>", "updateHistory#description": "Currently, you can see the version of the software that is installed on the robot, but there is no history log of previous versions installed. \n\nThis note explains that in the future, Carbon Robotics employees will be able to view the history log of past software versions"}, "history": {"borders": "Borders", "borders#description": "On the history page of the robot, the user can view all metrics spatially, on a heatmap. These heatmaps are built with tiny little colored squares. If the borders button is toggled on, then the tiny squares will have a white border around each. If the borders button is toggled off, then there will be no white border around each square. ", "errors": {"invalidDate": "Select valid date range", "invalidDate#description": "This is a note that appears to users if they try to pick an invalid date range on the history page of the robot. \n\nFor example, if they do not select an end date then they will receive this error. ", "noJobs": "No jobs reported in selected range", "noJobs#description": "This is an error that shows up to users on the history page of the robot if they filter their metrics by job and there were no jobs active during that specific timeframe. ", "noMetrics": "No metrics reported", "noMetrics#description": "This is a note that appears on the Metrics tab of the History page if the user selects a timeframe in which the robot was not active, and thus did not collect any metrics."}, "moreMetrics": "See more metrics", "moreMetrics#description": "On the History page of each robot, when the user clicks on a \"Job\", or \"Day\" they will see about 3 metrics associated with that job or day. They need to press the \"See more metrics\" button if they want to see the entire list of metrics collected.", "navTitle": "History", "navTitle#description": "The history page is a page where the user can view all metrics associated with the robot. The user can select the date range, as well as if they want to see the metrics represented in number format or visually in a heatmap. ", "placeholder": "Select a job or date to view data", "placeholder#description": "On the history page, there are two main ways for a user to filter the metrics associated with the robot. \n\nThe first way is called \"jobs\" which is a way for farmers to split up work based on field or amount of time by creating different jobs. \n\nThe second way is based on date, in which the metrics shown are solely specific to which day they were collected", "points": "Points", "points#description": "A point is a single historical GPS position of the robot", "warnings": {"beta": {"description": "Metrics pending validation are shown in blue", "description#description": "Each robot collects many different metrics. Metrics that are validated are shown in white-colored text, and unvalidated metrics are shown in blue-colored text."}, "ongoing": "Metrics for this date are not final yet", "ongoing#description": "If a user views the metrics of their robot on the History page and the robot is actively collecting those metrics, they will see a note that the metrics are not completely finished collecting"}}, "status": "Status", "status#description": "This is the current state of the robot. The following statuses can be assigned to each robot: Pre-Manufacturing, Manufacturing. Inventory, Delivery, Implementation, Active, Inactive, Off-Season, Winterized", "summary": {"banding": {"definition": "Definition", "definition#description": "The concept of a band is to define an linear area that we can act upon. For example, imagine a tractor driving over a field, and say we set a band of 12 inches centered around the center of the tractor (Length wise the band is infinite as the tractor drives forward). We would only act on objects detected within those 12 inches. The term banding is used to define the functionality of using bands, aka banding. \n\nEach banding has its own configuration, or specific definition. ", "dynamic": "Dynamic", "dynamic#description": "The concept of a band is to define a linear area that we can act upon. For example, imagine a tractor driving over a field, and say we set a band of 12 inches centered around the center of the tractor (Length wise the band is infinite as the tractor drives forward). We would only act on objects detected within those 12 inches.\n\nThe term banding is used to define the functionality of using bands, aka banding.\n\nDynamic banding means the defined bands will move automatically to match their best possible position based on an algorithm. It is dynamic because it moves and adapts to the situation.", "dynamicDisabled": "(Dynamic banding disabled in config)", "dynamicDisabled#description": "Banding is a way for our software to only focus on a certain part of the field, and reduce the amount of surface area our computer vision has to detect. There are two types of banding - static and dynamic. The meaning of this sentence is that dynamic banding has been turned off in the configuration settings", "rows": "Rows", "rows#description": "Section of our LaserWeeder robot, based on the concept of a farm row", "static": "Static", "static#description": "Banding is a concept where our robot chooses to only look at part of the field, closest to the seedline of each row. There are two types of banding - static and dynamic. Static means the lines of visualization will not change once entered", "type": "Type", "type#description": "Banding is a setting that allows the user to make the robot only weed sections or strips of the field, often along the seedline. We have two types or variations of banding, static and dynamic. ", "unknown": "Unknown Banding", "unknown#description": "Banding is a concept where our robot chooses to only look at part of the field, closest to the seedline of each row. This is an error shown to users when it is unclear if they are banding, or what type of banding they are using", "v1": "v1", "v1#description": "v1 is an abbreviation for \"Version 1\" or the first version, and in this case was the first version of a particular feature/setting called \"banding\"", "v2": "v2", "v2#description": "v2 is an abbreviation for \"Version 2\" or the second version, and in this case was the second version of a particular feature/setting called \"banding\"", "version": "Version", "version#description": "In software, version refers to a collection of changes and updates. This is the version of a particular software feature"}, "config": {"changes#description": "This is the number of times the almanac settings have been adjusted. The almanac is what allows us to determine how long a target should be shot with the laser for", "changes_one": "{{count}} Almanac Change", "changes_other": "{{count}} Almanac Changes", "cpt": "Crop Point Threshold", "cpt#description": "This is a term that refers to the confidence score required by a computer program to determine if a target should be considered a crop. The lower the number, the less confidence is needed to identify something as a crop and vice versa.\n\n", "default": "(DEFAULT: {{value}})", "default#description": "In the configuration page, the place where certain settings and values can be changed to customize the performance of the robot, there are default, or standard values for each setting.  ", "wpt": "Weed Point Threshold", "wpt#description": "This is a term used by a computer program to determine what confidence score is required for a target to be considered a weed. The lower the number, the less confidence is needed to identify something as a weed and vice versa.\n\n"}, "encoders": {"backLeft": "Back Left", "backLeft#description": "This is referring to a particular wheel of the robot. In this particular case , back left refers to the wheel that is furthest from the tractor driver, on the left side ", "backRight": "Back Right", "backRight#description": "A wheel encoder is a piece of equipment that allows us to determine how far the robot has traveled based on the number of turns the encoder makes. Back right refers to the tire on the robot that is furthest away from the tractor driver, on the right side. ", "frontLeft": "Front Left", "frontLeft#description": "A wheel encoder is a piece of equipment that allows us to determine how far the robot has traveled based on the number of turns the encoder makes. Front left refers to the tire on the robot that is closest to the tractor driver, on the left side. ", "frontRight": "Front Right", "frontRight#description": "A wheel encoder is a piece of equipment that allows us to determine how far the robot has traveled based on the number of turns the encoder makes. Front right refers to the tire on the robot that is closest to the tractor driver, on the right side. ", "title": "Wheel Encoders", "title#description": "This is a page on the robot summary page to determine that the wheel encoders are properly aligned. Wheel encoders are a piece of machinery that count the number of times the motor has rotated, which then allows us to calculate the distance the robot has traveled", "unknown": "?", "unknown#description": "This is a question mark because the summary page of a robot is unsure of what the wheel encoder positioning is. A wheel encoder is a piece of equipment that allows us to determine how far the robot has traveled based on the number of turns the encoder makes"}, "failed": "Failed to load robot summary", "failed#description": "When a user clicks on a selected robot, one of the options they can view is a summary page. This is an error for when the summary of the robot does not appear", "lasers": {"disabled#description": "We have 30 lasers on each robot. This metric on the summary page tells us if any of the 30 lasers are disabled, and if so how many", "disabled_one": "{{count}} Disabled <PERSON><PERSON>", "disabled_other": "{{count}} Disabled <PERSON><PERSON>", "row": "Row {{row}}", "row#description": "The robot is divided into 3 \"rows\". You can think of each row as one section. Each row has 10 lasers within it, totaling 30 lasers on each robot."}, "machineHealth": "Machine Health", "machineHealth#description": "This is a status page where users can view the overall status of their robot and its individual components, both hardware and software", "navTitle": "Summary", "navTitle#description": "Summary is a brief synopsis of the recent activity and metrics of the robot  ", "safetyRadius": {"driptape": "<PERSON><PERSON><PERSON><PERSON>", "driptape#description": "This refers to the to a type of infrastructure on a farm that is used to deliver water to the crops. Driptape is a type of irrigation system that is placed on the ground and delivers water directly to the roots of the plants.", "title": "Safety Radius", "title#description": "This is a page on the robot summary page to determine what the current safety radiuses are. A Safety Radius is a setting that allows the user to set a distance around specific types of detected objects. If a weed is detected within this radius, the robot will not fire the laser at it to avoid hitting the detected object."}, "sections": {"management": "Management", "management#description": "On the summary page of a robot, you are able to see how the machine is being utilized on that day. This is under the \"management\" section. It includes statistics such as time on and area weeded", "software": "Software", "software#description": "On the Summary page of each robot, users can view which software version is currently running. When new changes are made to software, they are released as new \"versions\""}, "supportLinks": {"chipChart": "Chip Chart", "chipChart#description": "A \"chip chart\" is a visualization that shows \"chips\", which are small parts of an image. (\"Chip\" as in a chip of stone off a rock formation.) This visualization is intended to help our machine learning engineers understand and improve performance.", "datasetVisualization": "Dataset Visualization", "datasetVisualization#description": "This is a link that allows the user to view the dataset visualization of the current robot parameters. A dataset is a collection of images that is used to train the model. The dataset visualization is a way to view the data in a visual format", "title": "Support Links", "title#description": "This is a page on the robot summary page that allows the user to view all the support links associated with the robot. Support links are links that are used to help troubleshoot issues with the robot"}}, "support": {"carbon": "Carbon Support", "carbon#description": "The Technical Support Team at Carbon Robotics is often referred to as \"Support\" or \"Carbon Support\". It is a team that is responsible for helping our customers and troubleshooting issues when customers reach out to us through the Support chat box.  ", "chatMode": {"legacy": "Legacy Chat", "legacy#description": "An old version of our internal chat messaging system. \"Legacy\" means \"the old version, which still works but usually you should use the new version instead\".", "new": "New Chat", "new#description": "A new version of our internal chat messaging system."}, "errors": {"failed": "Failed to load message", "failed#description": "This is an error that shows to users when a message in the chatbox between the Carbon Support team and the customer does not appear", "old": {"description": "{{serial}} is running software version {{version}}. Must be {{target}} to use support chat.", "description#description": "Every time we make a collection of software changes, we release it as an update and give it a version number. This sentence is saying that the robot is running an outdated software version which must be updated to the new set software, called the target version, in order to chat with our Support team", "title": "Insufficient Robot Version", "title#description": "Each robot needs to be up to date with a particular software version. A software version is a collection of updates or changes. In this case, the robot software version is not accepted which is why it throws an errror"}}, "localTime": "Local Time: {{time}}", "localTime#description": "Local Time refers to the time observed in the specific locality ", "navTitle": "Support", "navTitle#description": "This is a page for our Technical Support team to assist customers and troubleshoot issues through a chat platform. ", "toCarbon": "Message to $t(views.fleet.robots.support.carbon)", "toCarbon#description": "This is the placeholder text in the chat field to message our Carbon Support team if you're logged in as a customer", "toOperator": "Message to $t(models.users.operator_one)", "toOperator#description": "Send a message to a user (over a chat application)", "warnings": {"offline": {"description": "{{serial}} is running offline. Operator will not receive message until robot has connectivity.", "description#description": "This is a message the Carbon Robotics Support Team will receive if they try to send a message to a customer when the robot is turned off. An Operator is the job title of many of our customers. Operators are the people who drive the machine", "title": "Robot Offline", "title#description": "Our robot must be powered on before it can begin weeding. This state is explaining that the robot is turned off or not connected"}}}, "toggleable": {"internal": "Internal", "internal#description": "Label for a checkbox that shows or hides internal robots in a list"}, "uploads": {"errors": {"empty": "No uploads", "empty#description": "This is an error that shows when there are no files or data to upload"}}}, "title": "Fleet", "title#description": "At Carbon Robotics, the word 'fleet' refers to the total number of our robots (referred to as Slayers) that are being used commercially on various farms across the world. \n\nWhen you first log onto this website, Carbon Ops Center, as a Carbon employee you would see an overview of the company-wide fleet. For each customer, they would only see the robots they own in their fleet view. ", "views": {"fields": {"name": "Filter Name", "name#description": "The name of the filter that the user has created", "otherRobots": "Other Robots ({{robotCount}})", "otherRobots#description": "This is a list of robots that the user has not pinned to the top of the fleet view. This is a way for the user to keep track of the robots that are less important to them.", "pinnedRobotIds": "Pinned Robots", "pinnedRobotIds#description": "This is a list of robots that the user has pinned to the top of the fleet view. This is a way for the user to keep track of the robots that are most important to them.", "viewMode": {"values": {"cards": "Cards", "cards#description": "This is a view mode that allows the user to see the robots in a card format. Each card represents a robot and shows a summary of their status", "table": "Table", "table#description": "This is a view mode that allows the user to see the robots in a table format. Each row represents a robot and shows a summary of their status"}}}, "fleetView#description": "The fleet view is a way for the user to filter the robots in the fleet. The user can create multiple filters to view the robots in different ways.", "fleetView_one": "filter", "fleetView_other": "filters", "tableOnly": "Some columns are only available in table view", "tableOnly#description": "This is a note that appears when the user is in the card view and sees that some columns are grayed out because they are only available in the table view"}}, "knowledge": {"title": "Knowledge Base", "title#description": "Knowledge Base contains all of our educational materials for customers. This includes but is not limited to: software release notes, training manuals, tractor manuals, and tutorial videos."}, "metrics": {"jobStatus": {"closed": "Closed", "closed#description": "Describes a job that is closed, completed, done, finished. A job is a unit of work, like \"weed field 42\".", "description": "Job Status", "description#description": "A job is a unit of work, like \"weed field 42\". A \"job status\" describes the current state of the job, and is either open (ongoing) or closed (completed).", "open": "Open", "open#description": "Describes a job that is open, ongoing, active, in progress. A job is a unit of work, like \"weed field 42\"."}, "sections": {"estimatedFieldMetrics": "Estimated Field Metrics", "estimatedFieldMetrics#description": "This heading groups metrics that offer statistical inferences about the field in which the robot has been operating. Metrics in this category include \"crop density\", \"weeds killed\", and \"average weed radius\".", "estimatedFieldMetricsDisclaimer": "Our model uses experimental crop data that may contain inaccuracies. We’re continuously refining its reliability.", "estimatedFieldMetricsDisclaimer#description": "This disclaimer is meant to remind customers that the metrics in this section are only estimates and may fundamentally have less than 100% accuracy. This contrasts with the section for machine statistics, which we expect to be fully correct.", "performanceAndMachineStats": "Performance and Machine Metrics", "performanceAndMachineStats#description": "This heading groups metrics that relate to a robot's objective statistics and how well it's performing. Metrics in this category include \"power-on time\", \"distance traveled\", and \"area covered\"."}}, "offline": {"drop": "Drag files here from USB (or anywhere else)", "drop#description": "This is a page where photos from our robot can be manually uploaded, either by USB (Universal Serial Bus), or another means, if the upload cannot be completed through normal sync ", "file#description": "File refers to a document or image that can be uploaded ", "file_one": "file", "file_other": "files", "ingestDescription": "Carbon Employees should use Ingest service", "ingestDescription#description": "This is the note that appears to internal Carbon employee when they try to use the Upload button. Ingest is the name of an internal tool", "ingestLink": "Upload to Ingest", "ingestLink#description": "This is part of the Upload page. We redirect Carbon Robotics employees to upload any photos that could not be automatically uploaded from robot to a different internal tool for uploading photos called Ingest", "select": "Select Files", "select#description": "This button is part of the Upload page and is the customer facing view. If the customer presses the blue Select Files button, they will be prompted to find the photo(s) in their files that they wish to upload manually", "title": "Upload", "title#description": "The Upload page is a page where photos from our robot can be manually uploaded, usually by USB, if the upload cannot be completed through normal sync ", "upload": "Upload to Carbon", "upload#description": "This is a page where photos from our robot can be manually uploaded, usually by USB, if the upload cannot be completed through normal sync", "uploading": "Uploading {{subject}}...", "uploading#description": "The subject referred to in this is likely a photo or file that is being uploaded to"}, "reports": {"explore": {"graph": "Graph", "graph#description": "Explore mode, is a way for users to view heatmaps on a time-series graph across multiple robots and days at once.", "groupBy": "Group By", "groupBy#description": "\"Group By\" is a way to sort which robots, timeframe, or metrics the user wants to view in Explore Mode", "title": "Explore", "title#description": "The title of this feature is \"Explore\" because it references the functionality of this featue, which allows users to view metrics of multiple robots and days at one time. Thus, they don't have to specify too much what data they want to see and are allowed to discover trends or insight"}, "scheduled": {"authorCarbonBot": "Carbon Bot", "authorCarbonBot#description": "\"Carbon\" refers to our company, Carbon Robotics. \"Bot\" refers to an an autonomous program that can interact with systems or users.\n ", "authorUnknown": "Unknown Author", "authorUnknown#description": "Each report generated has the name of the user who created shown. \"Unknown author\" means that for some reason the identity of the user is unknown or not showing", "automation": {"customerReports": "Customer Reports", "customerReports#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot", "errorTitle": "Invalid Automated Report", "errorTitle#description": "The reports section allows users to build a custom view of their robot metrics. One option is to automate the report, meaning it will automatically deliver to their email on a weekly cadence. If the invalid automated report error is thrown, that means there is an issue with this automation. ", "reportCustomer": {"errors": {"none": "No customer selected", "none#description": "Users can build and view reports summarizing the metrics and activity of their robot. This is an error that will be displayed if no customer is selected because the software will not know which robots to display"}}, "reportDay": {"errors": {"none": "No day selected", "none#description": "Users can build and view reports summarizing the metrics and activity of their robot. This is an error that will be displayed if the user does not select what day of the week the automated report should be emailed"}, "name": "Report Day", "name#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. If automation is turned on this means that it will send to the customer email once a week. Report day means the day of the week that this report will be delivered"}, "reportEmails": {"errors": {"none": "No emails assigned", "none#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. This is an error that will be displayed if no emails are attached to the report because that means the automated report cannot be delivered"}, "name": "Customer Emails", "name#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. Their email address must be linked to the report in order for it to be delivered automatically every week"}, "reportHour": {"errors": {"none": "No hour selected", "none#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. This is an error that will be displayed if no specific time is selected because that means the automated report do not know when to be delivered"}, "name": "Report Hour", "name#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. This is the time of day the customer will receive their report"}, "reportLookback": {"errors": {"none": "No lookback defined", "none#description": "Users can build and view reports summarizing the metrics and activity of their robot. This is an error that will be displayed if the user fails to define a set amount of time the report should show metrics from (for example, up to 3 months ago)"}, "name": "Report Lookback", "name#description": "Users can build and view reports summarizing the metrics and activity of their robot. Report lookback is the set amount of time that metrics will be shown from, for example, if you select 3 months then all metrics from the last 3 months will display"}, "reportTimezone": {"errors": {"none": "No timezone selected", "none#description": "Users can build and view reports summarizing the metrics and activity of their robot. This is an error that occurs if the timezone is not selected or found because the automated report will not know what time to be delivered"}, "name": "Report Timezone", "name#description": "Users can build and view reports summarizing the metrics and activity of their robot. This is the timezone of the end user that the report will be delivered at"}, "warningDescription": "It runs every {{day}} at {{hour}} in {{timezone}} with {{lookback}} days lookback for all active {{customer}} robots.", "warningDescription#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. If they choose to automate this report, then it will run on a certain day of the week, at a certain hour, in a certain timezone, with metrics displaying from a certain amount of past time, for a certain number of robots", "warningTitle": "This is an automated report!", "warningTitle#description": "The reports section allows users to build a custom view of their robot metrics. One option is to automate the report, meaning it will automatically deliver to their email on a weekly cadence. This is a messge that will show to users to make sure automation is what they desire"}, "byline": "By {{author}}", "byline#description": "Users can make customized reports for the desired robots and metrics they wish to view. Once they create a report, they are the author of that report. One can sort the table of reports by author. ", "editor": {"columnsHidden": "Hidden Columns", "columnsHidden#description": "Under the reports section, when users build a report, they can drag and drop which metrics they want to view in the report. Any that are not in the \"visible\" column will remain in the \"hidden column\"", "columnsVisible": "Visible Columns", "columnsVisible#description": "The reports section allows users to build a custom view of their robot metrics. Users can drag and drop metrics from the \"hidden column\" to the \"visible column\" meaning it will show up in the report ", "duplicateNames#description": "When a user is creating a new report, they must name it. While the name need not be unique, we warn them if it isn't using this message in case they didn't know", "duplicateNames_one": "Warning: there is another report with this name", "duplicateNames_other": "Warning: there are {{count}} other reports with this name", "fields": {"automateWeekly": "Automate Weekly", "automateWeekly#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. Reports can be delivered to automatically send to the user on a weekly cadence", "name": "Report Name", "name#description": "Reports are a way for users to view specific metrics for their robot in a given period. Each report must be titled ", "showAverages": "Show Averages", "showAverages#description": "All customers are able to build and view reports summarizing the metrics and activity of their robot. Users can also choose to select the option to display the average value of each metric", "showTotals": "Show Totals", "showTotals#description": "The reports section allows users to build a custom view of their robot metrics. Users can check the \"show totals\" button if they want to see a sum of their metrics in the report"}}, "errors": {"noReport": "Report does not exist or you do not have access", "noReport#description": "This is an error message shown to users if they do not have the correct permissions to view a report or if the report has been deleted"}, "reportList": {"deleteConfirmationDescription": "{{list}} will be permanently deleted.", "deleteConfirmationDescription#description": "This is a confirmation that pops up when deleting a report to ensure the user wants to carry through with this erasure of a report", "errors": {"unauthorized": "You are not authorized to delete {{subject}}.", "unauthorized#description": "This is the error a user will get if they try to delete a report that they do not have the permissions for"}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "Email will not be re-sent", "publishEmailsHelperExisting#description": "Explanatory text next to a field of emails explaining that upon submitting the form that nothing will be sent to those emails even if they have changed", "publishEmailsHelperNew": "Report will be sent to these emails", "publishEmailsHelperNew#description": "Explanatory text next to a field of emails explaining that upon submitting the form that reports will be sent to the list of emails"}, "runAgain": "Run Again", "runAgain#description": "Button that when clicked re-runs a report that had already been run at least once"}, "table": {"errors": {"noColumns": "Select one or more columns", "noColumns#description": "The reports section allows users to build a custom view of their robot metrics. Users can drag and drop metrics from the \"hidden column\" to the \"visible column\" meaning it will show up in the report. If the user does not select any metrics to show up in the visible column, they will get this error message", "noEndDate": "Select end date", "noEndDate#description": "The reports section allows users to build a custom view of their robot metrics. Users must select a start and end date or else they will see this error message", "noRobots": "Select one or more robots", "noRobots#description": "Error text explaining that data cannot be shown without indicating which robot(s) to fetch data for", "noStartDate": "Select start date", "noStartDate#description": "Placeholder text for a page that requires a start date to render any content notifying the user that a start date is required"}, "fields": {"average": "Average", "average#description": "In the reports section, users can select to view an average of their metrics, which in arithmetic is the sum of all values divided by the quantity  ", "averageShort": "AVG", "averageShort#description": "This is an abbreviation for the word \"average\". Users can choose the option to display the average value of each metric in the reports they build", "date": "Date", "date#description": "Reports are a way for users to view specific metrics for their robot in a given period of time. Each report must have a date range and there is also the date of creation for the report", "group": "Serial/Date", "group#description": "This is part of the reports table. Each robot in our fleet is numbered SX, with X denoting a specific serial number. Date is the time period that the metric was collected", "groupJob": "Serial/Job", "groupJob#description": "This is part of the reports table. Each robot in our fleet is numbered SX, with X denoting a specific serial number. Job is the name of a task that was in process when that the metric was collected", "mixed": "(Mixed)", "mixed#description": "This is shown to summarize a data set that has multiple values. When the data set has only one value, we simply show that value; when there are different values, we show \"(Mixed)\" to represent that there is a mixture of values.", "total": "Total", "total#description": "Reports are a way for users to view specific metrics for their robot in a given period. Each report has the option to show a total, or summation, of metric values", "totalShort": "SUM", "totalShort#description": "The reports section allows users to build a custom view of their robot metrics. In addition to individual daily metrics, users can look at a summation of these statistics"}, "unknownReport": "Unknown Report", "unknownReport#description": "Users can build and view reports on robots and their statistics. In this case, the software is unsure of what report is being generated"}, "title": "Scheduled", "title#description": "Users can create reports, with custom metrics for their selected robots and date range. They can also determine if they want these reports to be scheduled and automatically sent to them ", "toLine": "for {{customer}}", "toLine#description": "Users can make customized reports for the desired customer, robot, and metrics they wish to view. One can sort the table of reports by customer."}, "tools": {"metricsLabel": {"all": "All Metrics", "all#description": "As the robot travels through the farm, it collects information about the weeds and crops as well as robot performance statistics. These statistics are displayed as metrics, and this is a way to view every single metric", "select": "Select Metrics", "select#description": "The robot collects many different statistics as it travels through the farm, this is a way for users to only choose to view which stats and metrics they are interested in"}, "robotsLabel": {"all": "All Robots", "all#description": "The main product we sell at Carbon Robotics is a robot. This view is a way to generate a report with metrics for every single robot in commercial use", "none": "No Robots", "none#description": "This means that none of our robots have been selected ", "select": "Select Robots", "select#description": "This is a button that allows the user to pick robots from a dropdown list to generate a report and metrics for"}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> via <1>{{identityProvider}}</1>", "account#description": "This describes a user's account in terms of their email address and their identity provider. For example, if the user has clicked \"Sign in with Google\", this might read, \"<EMAIL> via Google OAuth\". Please leave the <0></0> and <1></1> tags as-is and don't translate them directly.", "apple": "Apple", "apple#description": "Indicates that the user has registered an account by connecting to their Apple account. This refers to Apple Computer, Inc. (the company that makes iPhones and Mac computers), not \"apple\" like the fruit.", "auth0": "username and password", "auth0#description": "Indicates that the user has registered an account by choosing a username (typically an email address) and a password, rather than signing in with their Google account or another identity provider.", "google": "Google OAuth", "google#description": "Indicates that the user has registered an account by connecting to their Google account.", "unknown": "unknown provider", "unknown#description": "Indicates that the user has registered an account by a mechanism that we weren't expecting."}, "cards": {"account": "Account", "account#description": "The title for a group of settings concerning the user's account", "advanced": "Advanced", "advanced#description": "Advanced settings are special permissions only available to certain users ", "localization": "Localization", "localization#description": "Localization is a term for adapting a product's translation to a specific country or region. Under the settings page, users will see the word \"Localization\" and then can select which language they wish to view the Carbon Ops Center website in."}, "delete": {"deleteAccount": "Delete Account", "deleteAccount#description": "This is a button that users can click if they wish to permanently delete their Carbon Ops Center user account", "dialog": {"description": "WARNING: This action cannot be undone. All data will be lost.", "description#description": "This is a warning message that appears to users if they try to delete their account. It is a reminder that this action is permanent and cannot be undone"}}, "fields": {"experimental": "Enable experimental features", "experimental#description": "This is a toggle button only visible to Carbon Robotics employees. It is found under the Settings page. ", "language": "Language", "language#description": "This is on the Settings page. Users can select which language they wish to view Carbon Ops Center in by selecting from a dropdown menu. ", "measurement": {"name": "Units of Measure", "name#description": "Under the settings page, users can select if they wish to view their metrics in the imperial or metric system of measurement", "values": {"imperial": "Imperial (in, mph, acres, fahrenheit)", "imperial#description": "The imperial system of measurement uses measurement such as inches, miles per hour, acres, and farenheit. Users can select if they prefer to use the imperial or. metric system when viewing statistics of their robot", "metric": "Metric (mm, km/h, hectares, celsius)", "metric#description": "This is the metric system of measurement and a custom setting for users. Users can pick if they want to see statistics in the metric or imperial system"}}, "showMascot#description": "This is an internal setting for Carbon Robotics employees. If they turn on this setting, they will see a picture of our rooster mascot on the top right corner of the website."}, "logOut": "Log Out", "logOut#description": "This is the button users click if they no longer wish to be signed into their Carbon Ops Center user account. ", "title": "Settings", "title#description": "The settings page on Carbon Ops Center is where users can customize how they view the website. For example, users can select which language and unit of measurement system they want to view the website in. ", "version": "Carbon Ops Center version {{version}} ({{hash}})", "version#description": "This is the version of the Carbon Ops Center website that the user is currently viewing. 'version' is a user-facing release name like v1.0.104 and 'hash' is a code revision like 72f59fd6"}, "users": {"errors": {"notFound": "User does not exist, or you do not have permission to view them.", "notFound#description": "This is an error thrown when the user account one is searching for either does not exist or one needs different permission settings"}, "manage#description": "This a link for Carbon Employees to navigate to our authentication plaform, which is called Auth0. In Auth0 we are able to manage user permissions and logins", "sections": {"admin": {"manage": "Manage user in Auth0", "manage#description": "This a link for Carbon Employees to navigate to our authentication plaform, which is called Auth0. In Auth0 we are able to manage user permissions and logins", "title": "Admin", "title#description": "This is a section of the user settings page that is only visible to Carbon Robotics employees. It is where we can manage user permissions and logins"}, "permissions": {"title": "Role and Permissions", "title#description": "This is a section of the user settings page where users can view and edit their role and permissions. This includes what they are able to view and do on the Carbon Ops Center website"}, "profile": {"title": "Profile", "title#description": "This is a section of the user settings page where users can view and edit their personal information. This includes their name, email, and password"}}, "toggleable": {"contractors": "Contractors", "contractors#description": "Contractors are people that are paid to do work for a designated amount of time"}}}}