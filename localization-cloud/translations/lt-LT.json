{"components": {"AlarmTable": {"export": "{{robots}} įspėjimų istorija, {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Vertinimo etapu erdviniai rodikliai pasiekiami nemo<PERSON>mai, bet jie bet kada gali būti pakeisti, p<PERSON><PERSON>linti arba planą gali reikėti atnaujinti. Duomenys turėtų būti patikrinti nepriklausomų subjektų.", "description#description": "", "title": "Erdviniai beta versijos rod<PERSON>i", "title#description": ""}, "tooltip": "Ši funkcija šiuo metu vertinama ir gali būti bet kada pakeista arba pa<PERSON>a", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Nepavyko įkelti pokalbio: {{message}}", "failed#description": ""}, "machineTranslated": "<PERSON><PERSON><PERSON><PERSON> vertimas", "machineTranslated#description": "", "machineTranslatedFrom": "<PERSON><PERSON><PERSON><PERSON> vertimas iš {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Ši žinutė buvo ištrinta.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} bus ištrinta visam laikui.", "description#description": "", "descriptionActive": "{{subject}} a<PERSON><PERSON><PERSON><PERSON>, todė<PERSON> negalima iš<PERSON>nti.", "descriptionActive#description": ""}, "title": "Ar esate tikri?", "title#description": ""}, "CopyToClipboardButton": {"click": "Spustelėti ir kopijuoti", "click#description": "", "copied": "Nukopijuota!", "copied#description": ""}, "CropEditor": {"failed": "Nepavyki įkelti kultūrų tvarkytuvo", "failed#description": "", "viewIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> platformoje „Veselka“", "viewIn#description": ""}, "DateRangePicker": {"clear": "Išvalyti", "clear#description": "", "endDate": "Pabaigos data", "endDate#description": "", "error": "Datų intervalo parink<PERSON> k<PERSON>a", "error#description": "", "invalid": "Netinkamas", "invalid#description": "", "last7days": "Paskutinės 7 dienos", "last7days#description": "", "lastMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastMonth#description": "", "lastWeek": "Praėjusi savaitė", "lastWeek#description": "", "minusDays": "<PERSON><PERSON><PERSON> {{days}} d.", "minusDays#description": "", "plusDays": "per {{days}} d.", "plusDays#description": "", "startDate": "Pradžios data", "startDate#description": "", "thisMonth": "<PERSON><PERSON>", "thisMonth#description": "", "thisWeek": "Ši sava<PERSON>", "thisWeek#description": "", "today": "Šiandien", "today#description": "", "tomorrow": "<PERSON><PERSON><PERSON><PERSON>", "tomorrow#description": "", "yesterday": "<PERSON><PERSON><PERSON>", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "KŪRĖJAS", "dev#description": ""}, "ErrorBoundary": {"error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, įvyko ne<PERSON><PERSON><PERSON>a k<PERSON>a", "error#description": "", "queryLimitReached": "Pateikiamas dalinis duomen<PERSON>, nes buvo grąžinta per daug duomenų. Kreipkitės pagalbos į palaikymo tarnybą", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "<PERSON><PERSON> nut<PERSON>?", "comment#description": "", "feedback": "Atsiliepi<PERSON>", "feedback#description": "", "submit": "Pateikti ir įkelti iš naujo", "submit#description": ""}, "GdprConsent": {"description": "Perž<PERSON>ūrėkite ir sutikite, kad <PERSON>te tęsti", "description#description": "", "statement": "Su<PERSON><PERSON> su <0><PERSON><PERSON><PERSON><PERSON></0> ir <1>Privatumo politika</1>", "statement#description": "", "title": "<PERSON><PERSON><PERSON><PERSON> ir privatumo politika", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Reikalingas k<PERSON>", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Perjungt<PERSON> šį meniu", "help#description": "", "title": "Spartieji klaviatū<PERSON>", "title#description": ""}, "LaserTable": {"export": "{{robots}} lazeriai, {{date}}", "export#description": "", "installedOnly": "Tik įdiegti", "installedOnly#description": "", "warnings": {"duplicate": "<PERSON><PERSON> robotas turi keli<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> liz<PERSON>: {{slots}}", "duplicate#description": "", "emptySlot": "<PERSON><PERSON> robotas neturi lazerio, registr<PERSON>to <PERSON><PERSON> liz<PERSON>: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "<PERSON><PERSON><PERSON> kodas", "new#description": ""}, "Loading": {"failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nepavy<PERSON> įkelti \"Carbon Ops Center\".", "failed#description": "", "placeholder": "Įkeliama…", "placeholder#description": ""}, "ModelName": {"warning": "Įspėjimas: ma<PERSON><PERSON> modelis", "warning#description": ""}, "PendingActivationOverlay": {"description": "Aktyvuojame jū<PERSON>ų paskyrą. Kai šis proceas bus  baigtas, gausite el. <PERSON>.", "description#description": "", "errors": {"carbon": {"description": "„Carbon“ el. pa<PERSON><PERSON> a<PERSON>, bet <PERSON><PERSON>t<PERSON><PERSON><PERSON>, kad jį galima naudoti prisijungiant su naudotojo vardu ir slaptaž<PERSON>žiu. Atsijunkite ir naudokite parinktį „Prisijungti naudojant „Google“, kad paskyra būtų aktyvuota automatiškai.", "description#description": "", "title": "Nepatvirtinta „Carbon“ paskyra", "title#description": ""}}, "hi": "<PERSON><PERSON><PERSON>, {{name}}!", "hi#description": "", "logOut": "Prisijungėte nurodydami klaidingą paskyrą? <0>Atsijunkite</0>.", "logOut#description": "", "title": "Laukiama aktyvavimo", "title#description": ""}, "ResponsiveSubnav": {"more": "Daugiau", "more#description": ""}, "RobotImplementationSelector": {"status": "Įgyvendinimo būsena", "status#description": "", "title": "Įgyvendinimo bū<PERSON>os keit<PERSON>s", "title#description": "", "warning": "Pakeitus įgyvendinimo būseną, gali būti aktyvuotą automatizuota darbo eiga, paveiksianti klientų naudojimosi patogumą. JEI NESATE TIKRI, TO NEDARYKITE!", "warning#description": ""}, "ShowLabelsButton": {"text": "Žymekliai", "text#description": "", "tooltip": "<PERSON><PERSON><PERSON> et<PERSON>", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "<PERSON><PERSON><PERSON> metaduomenis", "tooltip#description": ""}, "almanac": {"crops": {"new": "Pridėti naują kultūr<PERSON>", "new#description": "", "none": "Nėra kultūrų kategorijų", "none#description": "", "sync#description": ""}, "cropsSynced": "<PERSON><PERSON><PERSON>", "cropsSynced#description": "", "delete": {"description": "Šio ve<PERSON> negalima at<PERSON>", "description#description": ""}, "discard": {"description": "<PERSON><PERSON><PERSON> „{{title}}“ p<PERSON><PERSON><PERSON>?", "description#description": "", "title": "<PERSON><PERSON><PERSON>?", "title#description": ""}, "fineTuneDescription": "Numatytoji vertė yra 5. <PERSON><PERSON><PERSON> trukm<PERSON> gali būti sumažinta arba padidinta apie 20 % kiekvienu etapu", "fineTuneDescription#description": "", "fineTuneTitle": "<PERSON><PERSON><PERSON><PERSON> derinimo da<PERSON>", "fineTuneTitle#description": "", "formulas": {"all": "Visi dydžiai", "all#description": "", "copyFormula": "Kopijuoti formulę", "copyFormula#description": "", "copySize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copySize#description": "", "exponent": {"description": "Spindulys (mm) pakeliamas š<PERSON>o la<PERSON>u", "description#description": "", "label": "<PERSON><PERSON><PERSON> (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Skaičius nuo 1 iki 10. Numatytoji vertė yra 5. <PERSON><PERSON><PERSON> trukmė gali būti sumažinta arba padidinta apie 20 % kiekvienu etapu. <PERSON>, na<PERSON><PERSON><PERSON> pag<PERSON>", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> derini<PERSON> (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Bend<PERSON> tikslaus derinimo indek<PERSON> didini<PERSON> / ma<PERSON><PERSON><PERSON> daugiklis", "description#description": "", "label": "<PERSON><PERSON>slaus derini<PERSON> (FM)", "label#description": ""}, "laserTime": "<PERSON><PERSON><PERSON>", "laserTime#description": "", "maxTime": {"description": "<PERSON><PERSON><PERSON><PERSON> (ms)", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "multiplier": {"description": "Dauginama i<PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON> mm", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> (A)", "label#description": ""}, "offset": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "description#description": "", "label": "Po<PERSON>link<PERSON>", "label#description": ""}, "pasteFormula": "Įklijuoti formulę", "pasteFormula#description": "", "pasteSize": "Įklijuoti d<PERSON><PERSON><PERSON>", "pasteSize#description": "", "sync": "Sinchronizuoti visus <PERSON>", "sync#description": "", "thresholds": "Dydžių ribos", "thresholds#description": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Perjungti išplėstinį režimą", "switchModeAdvanced#description": "", "switchModeBasic": "Perjungti pagrindinį režimą", "switchModeBasic#description": "", "warnings": {"admin": "Pakeitus šį almanachą, bus sinchronizuoti visi esami ir būsimi gamybos vienetai.", "admin#description": "", "carbon": "<PERSON> yra „Carbon“ teikiamas almanachas. Galima keisti tik tikslaus derinimo indeksą.", "carbon#description": "", "production": "Šį almanachą aktyviai naudoja robotas. Redagavimas bus pritaikytas laukui iš karto.", "production#description": ""}, "weeds": {"new": "Pridėti naują piktžolę", "new#description": "", "none": "Nėra piktžolių kategorijų", "none#description": "", "sync#description": ""}, "weedsSynced": "<PERSON>is<PERSON>", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} išsaugotas. Aktyvinti operatoriaus programėlėje „Quick Tune“.", "savedLong#description": "", "testResults": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "testResults#description": ""}, "filters": {"capturedAt": "Fiksavimo data", "capturedAt#description": "", "diameter": "<PERSON><PERSON><PERSON><PERSON>", "diameter#description": "", "filters#description": "", "unappliedFilters": "", "unappliedFilters#description": ""}, "images": {"allImages": "Visi paveikslėliai", "allImages#description": "", "categorized": "<PERSON>ris<PERSON><PERSON><PERSON>", "categorized#description": "", "scrollToTop": "Grįžti į viršų", "scrollToTop#description": "", "sortBy": {"latest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "latest#description": ""}, "sortedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pagal: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "<PERSON><PERSON><PERSON> gau<PERSON> b<PERSON><PERSON> duomenis", "error#description": "", "ready": "Rezultatai paruošti", "ready#description": "", "session#description": "", "session_few": "<PERSON><PERSON><PERSON>", "session_many": "<PERSON><PERSON><PERSON>", "session_one": "<PERSON><PERSON><PERSON>", "session_other": "<PERSON><PERSON><PERSON>", "showResults": "<PERSON><PERSON><PERSON> rezultatus", "showResults#description": "", "status": "apdorota {{processed}} / {{total}} rezultatų", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Pakeitus šį augalų profilį, jis bus sinchronizuojamas su visais esamais ir būsimais gamybos vienetais.", "admin#description": "", "adminMeta": "Visi administratorių profiliai bus pasiekiami visiems klientams. Nekurkite per daug profilių!", "adminMeta#description": "", "production": "Šis augalo profilis aktyviai veikia robote. Operatoriui bus pranešama apie atnaujinimus ir jis gal<PERSON>s pasirinkti naudoti nau<PERSON>usius pakeitimus.", "production#description": "", "protected": "<PERSON> bend<PERSON>ės pateiktas profilis. <PERSON><PERSON><PERSON> negal<PERSON> b<PERSON><PERSON> k<PERSON>.", "protected#description": "", "unsavedChanges": "Neišsaugoti pakeitimai. Paspauskite i<PERSON>i, kad pritaikyti pakeitimus.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_few": "Pakeistų raktų", "changedKey_many": "Pakeistų raktų", "changedKey_one": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "changedKey_other": "Pakeistų raktų", "newKey": "naujas {{key}} pavadinimas", "newKey#description": "", "stringReqs": "<PERSON><PERSON> būti tik <PERSON>ie simboliai: a–z, 0–9, . ir _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Šis raktas pridėtas papildomai prie numatytųjų raktų.", "description#description": ""}, "keyMissing": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> (-<PERSON><PERSON><PERSON>): {{keys}}", "description#description": ""}, "valueChanged": {"description": "Ši vertė pakeista iš numatytos<PERSON> vertė<PERSON> ({{default}})", "description#description": "", "title": "Konfigūracija p<PERSON>ista", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Nepavyko įkelti kliento tvarkytuvo", "load#description": ""}}, "CustomerSelector": {"empty": "Nepriskirta", "empty#description": "", "title": "<PERSON><PERSON><PERSON>", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "Šaudyti ir ignoruoti", "description#description": "", "label": "Š<PERSON><PERSON><PERSON>", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON><PERSON><PERSON> nustatymus", "copy#description": "", "ignorable": {"description": "<PERSON><PERSON><PERSON><PERSON> tik tada, kai <PERSON><PERSON>, neatsižvelgiama į greičio rekomendacijas", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "paste": "Įklijuoti nustatymus", "paste#description": ""}, "warnings": {"production": "Šis diskriminatorius aktyviai veikia robote. Redagavimas iš karto įsigalios lauke.", "production#description": ""}}, "drawer": {"customerMode": "Kliento režimas", "customerMode#description": "", "error": "Nepavyko įkelti navigacijos", "error#description": ""}, "filters": {"NumericalRange": {"max": "Maks. ({{units}})", "max#description": "", "min": "Min. ({{units}})", "min#description": ""}, "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": ""}, "header": {"failed": "Nepavyko įkelti antraštės", "failed#description": "", "mascot": "\"Carbon Robotics\" viščiukų talismanas", "mascot#description": "", "search": {"failed": "Nepavyko įkelti paieškos", "failed#description": "", "focus": "Fokuso pai<PERSON>š<PERSON>", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "<PERSON><PERSON><PERSON>", "label#description": "", "larger": "<PERSON><PERSON><PERSON>", "larger#description": "", "smaller": "<PERSON><PERSON><PERSON><PERSON>", "smaller#description": ""}}, "map": {"bounds": {"reset": "Atstatyti vaizdą", "reset#description": ""}, "errors": {"empty": "Duomenų apie buvimo vietą nepateikta", "empty#description": "", "failed": "Nepavyko įkelti žemėlapio", "failed#description": ""}, "filters": {"customer_office": "Klientų biuras", "customer_office#description": "", "hq": "\"Carbon HQ\" būst<PERSON><PERSON>", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "<PERSON><PERSON><PERSON>", "po_box#description": "", "shop": "Parduotuvė", "shop#description": "", "storage": "Saug<PERSON><PERSON>", "storage#description": "", "support_base": "Atraminė bazė", "support_base#description": ""}, "fullscreen": "Pilnas ekranas", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Sluoksnio legend<PERSON> k<PERSON>a", "legend#description": "", "notThinning": "NĖRA RETINAMAS", "notThinning#description": "", "notWeeding": "NĖRA PIKŽOLĖ", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "ŠILUMOS ŽEMĖLAPIO KLAIDA", "unknown#description": ""}, "fields": {"block": "Blokas: {{block}}", "block#description": "", "location": "Vieta: {{latitude}}, {{longitude}}", "location#description": "", "size": "Dydis: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Sluoksniai", "name#description": "", "rangeType#description": "", "relative": "Naudoti santykinį diapazoną", "relative#description": "", "relativeRange#description": ""}, "map": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map#description": "", "measure": {"name": "Priemonė", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "<PERSON><PERSON> kokios kategori<PERSON> kopijuoti?", "copyFromWhich#description": "", "splitCrops": "Atskirti kultūras", "splitCrops#description": "", "splitWeeds": "Atskirti piktžoles", "splitWeeds#description": "", "syncCrops": "Sinchronizuoti visas kultūras", "syncCrops#description": "", "syncWeeds": "Sinchronizuoti visas piktžoles", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Prognozavimo patikimumo riba aptikimo naudojimui dinaminė<PERSON> juo<PERSON>", "description#description": "", "label": "Juostų nustatymo riba", "label#description": ""}, "minDoo": {"description": "Mažiausia aptikimo galimybė", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Prognozavi<PERSON> pat<PERSON>, kad būtų galima naudoti aptikimą retinimo metu", "description#description": "", "label": "Retinamo Riba", "label#description": ""}, "weed": {"description": "Prognozavimo patikimumo riba naudojant aptikimą atvirkštinei kultūros a<PERSON>augai", "description#description": "", "label": "Atvirkš<PERSON><PERSON> k<PERSON>ū<PERSON> a<PERSON>a", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Prognozavimo patikimumo riba siekiant naudoti aptikimą kultū<PERSON> a<PERSON>", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>a", "label#description": ""}, "weed": {"description": "Prognozavi<PERSON> pat<PERSON>, kai piktž<PERSON><PERSON> laikoma piktžole", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}}}, "errors": {"sync": "Šio modelio nustatymai dar nesinchronizuoti su \"LaserWeeder\". <PERSON><PERSON><PERSON><PERSON>, kad <PERSON> perž<PERSON>ūrėti ir atnaujinti nustatymus.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Atskirti d<PERSON>žius", "splitSizesLong#description": "", "splitSizesShort": "Atskirti", "splitSizesShort#description": "", "syncSizesLong": "Sinchron<PERSON><PERSON><PERSON>", "syncSizesLong#description": "", "syncSizesShort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Įspėjimas:{{stopEmphasis}} <PERSON><PERSON> nustatymai apima ne<PERSON> pake<PERSON>, kurie neper<PERSON><PERSON>i <PERSON>.", "exportingUnsavedChanges#description": "", "production": "Šis modelis aktyviai veikia robote. Redagavimas iš karto įsigalios lauke.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Nežinomi pav<PERSON>ai", "unknown#description": ""}, "almanac": {"unknown": "<PERSON>ež<PERSON>mas <PERSON>", "unknown#description": "", "withName": "Almanachas: {{name}}", "withName#description": ""}, "autofixing": "Automatinio <PERSON>", "autofixing#description": "", "banding": {"disabled": "Juostų nustatymas išjungtas", "disabled#description": "", "enabled": "Juostų nustatymas Įjungtas", "enabled#description": "", "none": "Nėra juostų nustatymo", "none#description": "", "static": "(STATINIS)", "static#description": "", "withName": "Juostų nustatymas: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Nepavyko įkelti registracijos būsenos", "failed#description": "", "never": "<PERSON><PERSON><PERSON>", "never#description": "", "withTime": "<PERSON>žsir<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Įgalinti pasėliai ({{pinned}} pinned)", "summary#description": ""}, "delivery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delivery#description": "", "disconnected": "Atjungtas", "disconnected#description": "", "discriminator": {"unknown": "Než<PERSON>mas diskriminatorius", "unknown#description": "", "withName": "Diskriminatorius: {{name}}", "withName#description": ""}, "failed": "Nepavyko įkelti roboto būsenos", "failed#description": "", "failedShort": "Nepavyko", "failedShort#description": "", "implementation": "Įgyvendinimas", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventorius", "inventory#description": "", "job": {"none": "<PERSON><PERSON><PERSON> da<PERSON>o", "none#description": "", "withName": "Užduotis: {{name}}", "withName#description": ""}, "lasers": "Veikiantys lazeriai: {{online}}/{{total}}", "lasers#description": "", "lifetime": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>:", "lifetime#description": "", "lifted": "Budė<PERSON><PERSON> (pakeltas)", "lifted#description": "", "loading": "<PERSON><PERSON><PERSON><PERSON>", "loading#description": "", "location": {"known": "Vieta: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Vieta než<PERSON>", "unknown#description": ""}, "manufacturing": "Gamyba", "manufacturing#description": "", "model": {"withName": "Modelis: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "<PERSON><PERSON><PERSON>", "modelLoading#description": "", "notArmed": "<PERSON><PERSON><PERSON>", "notArmed#description": "", "off_season": "<PERSON>e sezono metu", "off_season#description": "", "offline": "Neprisijungus {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P Nežinomas", "unknown#description": ""}, "poweringDown": "<PERSON><PERSON><PERSON>", "poweringDown#description": "", "poweringUp": "<PERSON><PERSON><PERSON> įjungimas", "poweringUp#description": "", "pre_manufacturing": "<PERSON><PERSON><PERSON>", "pre_manufacturing#description": "", "stale": "<PERSON><PERSON><PERSON><PERSON>", "stale#description": "", "staleDescription": "Paskutinė žinoma re<PERSON>š<PERSON>ė. Robotas neprisijungęs.", "staleDescription#description": "", "standby": "Budėji<PERSON>", "standby#description": "", "thinning": {"disabled": "Retinimas išjungtas", "disabled#description": "", "enabled": "Re<PERSON><PERSON> įjungtas", "enabled#description": "", "none": "Nėra retinamo", "none#description": "", "withName": "Retinamas: {{name}}", "withName#description": ""}, "today": {"none": "Šiandien piktžolių ravėti nereikia", "none#description": ""}, "unknown": "Než<PERSON><PERSON>", "unknown#description": "", "updating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updating#description": "", "version": {"values": {"unknown": "Nežinoma versija", "unknown#description": "", "updateDownloading": "({{version}} atsiųsti)", "updateDownloading#description": "", "updateReady": "({{version}} paruo<PERSON><PERSON>)", "updateReady#description": ""}}, "weeding": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{crop}}", "weeding#description": "", "weedingDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weedingDisabled#description": "", "weedingThinning": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir retinimas {{crop}}", "weedingThinning#description": "", "winterized": "Žiemoti", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "<PERSON>au yra", "exists#description": "", "unknownClass": "Nežinoma roboto k<PERSON>", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Nekurkite naujos konfigūracijos", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "{{class}} š<PERSON><PERSON><PERSON>", "templateForClass#description": "", "templateGeneric": "<PERSON><PERSON>", "templateGeneric#description": "", "warnings": {"ignoreConfig": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tęsti tik tuo atveju, jei konfigū<PERSON> {{serial}} jau yra arba jei planuojate jį sukurti rankiniu būdu.", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Išplėstiniai spidometro nustatymai", "advancedFormulaTitle#description": "", "formulaTitle": "Formulė", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Automatiškai sumažinkite siūlomą greitį įvesta verte. Pavyzdžiui, įvedus 5 %, siūlomas 1 mylios per valandą greitis sumažės iki 0,95 mylios per valandą.", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "decreaseSmoothing": {"description": "Pritaikykite greičio ma<PERSON> greitį. <PERSON><PERSON> did<PERSON> vert<PERSON>, tuo dides<PERSON><PERSON> t<PERSON>, kad spid<PERSON><PERSON> s<PERSON>", "description#description": "", "label": "Lėtėjimo <PERSON>", "label#description": ""}, "increaseSmoothing": {"description": "Pritaikykite grei<PERSON><PERSON> did<PERSON>mo greitį. <PERSON><PERSON> did<PERSON> vert<PERSON>, tuo dides<PERSON><PERSON> t<PERSON>, kad spid<PERSON><PERSON> s<PERSON>", "description#description": "", "label": "Pagreič<PERSON>", "label#description": ""}, "maxVelMph": {"description": "Įveskite absoliučiai didžiausią greitį, kuriuo norite važiuoti. Greičio rekomendacijos nebus didesnės už šią vertę", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> gre<PERSON>", "label#description": ""}, "minVelMph": {"description": "Įveskite absoliučiai mažiausią greitį, kuriuo norite važiuoti. Greičio rekomendacijos nebus mažesnės už šią vertę", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> gre<PERSON>", "label#description": ""}, "primaryKillRate": {"description": "Ši vertė yra jūsų pageidaujamas <PERSON>kintų piktžolių procentas.", "description#description": "", "label": "Ideali naikinimo norma", "label#description": ""}, "primaryRange": {"description": "Padidinkite ši<PERSON> vert<PERSON>, jei norite pasiekti idealų naikinimo lygį, nepriklausomai nuo greičio poveikio.", "description#description": "", "label": "Žalioji buferinė zona", "label#description": ""}, "rows": {"allRows": "Visos vagos", "allRows#description": "", "row1": "Vaga 1", "row1#description": "", "row2": "Vaga 2", "row2#description": "", "row3": "Vaga 3", "row3#description": ""}, "secondaryKillRate": {"description": "Ši vertė yra mažiausias leistinas sun<PERSON>kintų piktžolių procentas.", "description#description": "", "label": "<PERSON><PERSON><PERSON> na<PERSON>", "label#description": ""}, "secondaryRange": {"description": "Padidinkite ši<PERSON>, jei norite, kad prieš gaunant praneš<PERSON> apie mažą greitį liktų daugiau laiko.", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> buferis", "label#description": ""}, "sync": "Sinchronizuoti visas vagas", "sync#description": "", "warnings": {"admin": "Pakeitus šį greičio įvertį, jis bus sinchronizuojamas su visais esamais ir būsimais gamybos vienetais.", "admin#description": "", "production": "Šis greičio įvertinimo įrenginys aktyviai veikia robote. Redagavimas iš karto įsigalios lauke.", "production#description": "", "protected": "Tai anglies dioksido profilis. Nieko negalima keisti.", "protected#description": "", "unsavedChanges": "Neišsaugoti pakeitimai. Paspauskite iš<PERSON>oti, kad pritaikytumėte pakeitimus.", "unsavedChanges#description": ""}}, "slider": {"gradual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gradual#description": "", "immediate": "<PERSON><PERSON><PERSON>", "immediate#description": ""}, "visualization": {"targetSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> greitis", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_few": "Įspėjimai", "alarm_many": "Įspėjimai", "alarm_one": "Įspėjimas", "alarm_other": "Įspėjimai", "fields": {"code": "<PERSON><PERSON>", "code#description": "", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description#description": "", "duration": {"name": "Trukmė", "name#description": "", "values": {"ongoing": "TĘSTINAS", "ongoing#description": ""}}, "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identifier#description": "", "impact": {"name": "Poveikis", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Lygis", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Pradžia", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_few": "almanachai", "almanac_many": "almanachai", "almanac_one": "almanachas", "almanac_other": "almanachai", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_few": "<PERSON>ž<PERSON><PERSON><PERSON>", "assignment_many": "<PERSON>ž<PERSON><PERSON><PERSON>", "assignment_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignment_other": "<PERSON>ž<PERSON><PERSON><PERSON>", "autotractor": "„AutoTractor“", "autotractor#description": "", "fields": {"instructions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instructions#description": ""}, "intervention#description": "", "intervention_few": "", "intervention_many": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"laserWeed": "Rav<PERSON><PERSON><PERSON> lazeriu", "laserWeed#description": "", "unrecognized": "<PERSON><PERSON><PERSON><PERSON> tipas ({{value}})", "unrecognized#description": ""}, "job_few": "", "job_many": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Lazeriu ravima vaga", "laserWeedRow#description": ""}, "objective_few": "uždaviniai", "objective_many": "uždaviniai", "objective_one": "uždavinys", "objective_other": "uždaviniai", "states": {"acknowledged": "<PERSON><PERSON><PERSON><PERSON>", "acknowledged#description": "", "cancelled": "atšaukta", "cancelled#description": "", "completed": "baigta", "completed#description": "", "failed": "<PERSON><PERSON><PERSON><PERSON>", "failed#description": "", "inProgress": "vykdoma", "inProgress#description": "", "new": "naujas", "new#description": "", "paused": "pristab<PERSON><PERSON>", "paused#description": "", "pending": "lauki<PERSON>", "pending#description": "", "ready": "<PERSON><PERSON><PERSON><PERSON>", "ready#description": "", "unrecognized": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Užduotis #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_few": "<PERSON>ž<PERSON><PERSON><PERSON>", "task_many": "<PERSON>ž<PERSON><PERSON><PERSON>", "task_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "task_other": "<PERSON>ž<PERSON><PERSON><PERSON>"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_few": "augalų profiliai", "categoryCollectionProfile_many": "augalų profiliai", "categoryCollectionProfile_one": "augalų profilis", "categoryCollectionProfile_other": "augalų profiliai", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disregard#description": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "requiredBaseCategories": "Privalote apibrėžti šias konkrečias kategorijas: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt#description": ""}, "metadata": {"capturedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capturedAt#description": "", "categoryId": "Kategorijos ID", "categoryId#description": "", "imageId": "Nuotraukos ID", "imageId#description": "", "pointId": "Taško ID", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "radius": "spindulys", "radius#description": "", "updatedAt": "<PERSON><PERSON><PERSON>", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_few": "Nustatymai", "config_many": "Nustatymai", "config_one": "Nustatymai", "config_other": "Nustatymai", "key#description": "", "key_few": "raktai", "key_many": "raktai", "key_one": "raktas", "key_other": "raktai", "template#description": "", "template_few": "konfigū<PERSON><PERSON><PERSON>", "template_many": "konfigū<PERSON><PERSON><PERSON>", "template_one": "konfigū<PERSON><PERSON><PERSON>", "template_other": "konfigū<PERSON><PERSON><PERSON>", "value#description": "", "value_few": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value_many": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value_other": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "crops": {"categories": {"unknown": "Nežinoma žemės ū<PERSON>", "unknown#description": ""}, "crop#description": "", "crop_few": "<PERSON><PERSON><PERSON><PERSON>", "crop_many": "<PERSON><PERSON><PERSON><PERSON>", "crop_one": "<PERSON><PERSON><PERSON><PERSON>", "crop_other": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"confidence": {"fields": {"regionalImages": "Regioniniai vaizdai:", "regionalImages#description": "", "totalImages": "<PERSON><PERSON> viso vaizdų:", "totalImages#description": ""}, "name": "Pasitikė<PERSON>s", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Archyvuota", "archived#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Pastabos", "notes#description": "", "pinned": "Prisegtas", "pinned#description": "", "recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_few": "klientai", "customer_many": "klientai", "customer_one": "k<PERSON>as", "customer_other": "klientai", "fields": {"emails": {"errors": {"formatting": "Po vieną el. laišką vienoje eilutėje", "formatting#description": ""}, "name": "Elektroniniai laiškai", "name#description": ""}, "featureFlags": {"almanac": {"description": "Įjungiami robotų skirtukai \"<PERSON>nac<PERSON>\" ir \"Diskriminatorius\" (robotas taip pat turi palaikyti almanachą ir diskriminatorių).", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Įjungiamas robotų augalo profilio <PERSON>", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Funkcijų žymos leidžia visiems kliento naudotojams naudoti beta versijos funkcijas", "description#description": "", "explore": {"description": "Ataskaitose įjungtas žemėlapio ir grafiko tyrinėji<PERSON> re<PERSON>s", "description#description": "", "name": "Tyrinė<PERSON><PERSON>", "name#description": ""}, "jobs": {"description": "Įjungiamos darbo vietos (robotas taip pat turi palaikyti darbo vietas)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "<PERSON><PERSON> na<PERSON>j<PERSON> robotų rodiklių vizualinį dizainą", "description#description": "", "name": "Rodiklių dizaino atnaujinimas", "name#description": ""}, "name": "Funkcijų vėliavėlės", "name#description": "", "off": "IŠJUNGTA", "off#description": "", "on": "ĮJUNGTA", "on#description": "", "reports": {"description": "Įjungiamas <PERSON><PERSON><PERSON> ir funk<PERSON>", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "<PERSON><PERSON><PERSON> er<PERSON> duo<PERSON>, įskaitant <PERSON><PERSON><PERSON><PERSON> ir diagramas.", "description#description": "", "name": "Erdviniai duomenys", "name#description": ""}, "summary": "{{enabled}}/{{total}} Įjungtos funkcijos v<PERSON>liavėlės", "summary#description": "", "unvalidatedMetrics": {"description": "Sertifikuotose metrikose rodyti beta metrikas, la<PERSON><PERSON><PERSON><PERSON> lauko <PERSON>", "description#description": "", "name": "Beta rodikliai", "name#description": ""}, "velocityEstimator": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ti ir redaguoti taikinio greičio įvertinimo profilius (robotas taip pat turi palaikyti taikinio greičio įvertinimą).", "description#description": "", "name": "Greičio įvertinimo įrenginys", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "<PERSON><PERSON><PERSON><PERSON>", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Jei ši funkcija įjungta, ataskaitos bus paleidžiamos kas savaitę su šiais visų aktyvių robotų nustatymais", "description#description": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>askait<PERSON>", "name#description": ""}, "weeklyReportHour": "Vykdykite", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Grįžtamoji informacija", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Paleiskite", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_few": "diskriminatoriai", "discriminator_many": "diskriminatoriai", "discriminator_one": "diskriminatorius", "discriminator_other": "diskriminatoriai", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_few": "ū<PERSON><PERSON>", "farm_many": "ū<PERSON><PERSON>", "farm_one": "<PERSON><PERSON>", "farm_other": "ū<PERSON><PERSON>", "point#description": "", "point_few": "taškai", "point_many": "taškai", "point_one": "<PERSON><PERSON><PERSON>", "point_other": "taškai", "zone#description": "", "zone_few": "zonos", "zone_many": "zonos", "zone_one": "zona", "zone_other": "zonos"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_few": "<PERSON><PERSON><PERSON>", "fieldDefinition_many": "<PERSON><PERSON><PERSON>", "fieldDefinition_one": "<PERSON><PERSON><PERSON>", "fieldDefinition_other": "<PERSON><PERSON><PERSON>", "fields": {"boundary": "<PERSON><PERSON> forma", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "<PERSON><PERSON><PERSON> kryptis", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_few": "visuotin<PERSON><PERSON> ve<PERSON>", "global_many": "visuotin<PERSON><PERSON> ve<PERSON>", "global_one": "visuotin<PERSON> vertė", "global_other": "visuotin<PERSON><PERSON> ve<PERSON>", "values": {"plantProfileModelId": {"description": "<PERSON><PERSON><PERSON>, naudojamas visų klientų ir administratorių profiliuose, skirtas '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) modelio ID", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "Data / laikas", "capturedAt#description": "", "geoJson": "Vieta", "geoJson#description": "", "url": "Atidaryti vaizdą", "url#description": ""}, "image#description": "", "image_few": "vai<PERSON>i", "image_many": "vai<PERSON>i", "image_one": "vaizdas", "image_other": "vai<PERSON>i"}, "jobs": {"job#description": "", "job_few": "užduočių", "job_many": "užduočių", "job_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "job_other": "užduočių"}, "lasers": {"fields": {"cameraId": "Kameros ID", "cameraId#description": "", "error": {"values": {"false": "Nominalus", "false#description": ""}}, "installedAt": "Įdiegta", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> serijinis numeris", "unknown#description": ""}}, "lifetimeSec": "<PERSON><PERSON>", "lifetimeSec#description": "", "powerLevel": "Galios lygis", "powerLevel#description": "", "removedAt": "<PERSON><PERSON><PERSON><PERSON>", "removedAt#description": "", "rowNumber": "Vaga", "rowNumber#description": "", "totalFireCount": "<PERSON><PERSON><PERSON><PERSON>", "totalFireCount#description": "", "totalFireTimeMs": "<PERSON><PERSON><PERSON><PERSON>", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "Pasibaigęs", "expired#description": "", "hours": "Valandos: {{installed}}/{{total}} ({{percent}} likęs)", "hours#description": "", "hoursUnknown": "Valandos: <PERSON><PERSON><PERSON><PERSON><PERSON>", "hoursUnknown#description": "", "months": "Mėnesiai: {{installed}}/{{total}} ({{percent}} likęs)", "months#description": "", "monthsUnknown": "Mėnesiai: <PERSON><PERSON><PERSON><PERSON><PERSON>", "monthsUnknown#description": "", "unknown": "Nežinoma garantija", "unknown#description": ""}}}, "laser#description": "", "laser_few": "lazeriai", "laser_many": "lazeriai", "laser_one": "lazeris", "laser_other": "lazeriai"}, "models": {"model#description": "", "model_few": "model<PERSON>i", "model_many": "model<PERSON>i", "model_one": "modelis", "model_other": "model<PERSON>i", "none": "Nėra modelio", "none#description": "", "p2p#description": "", "p2p_few": "P2P modeliai", "p2p_many": "P2P modeliai", "p2p_one": "P2P modelis", "p2p_other": "P2P modeliai", "unknown": "Než<PERSON><PERSON> modelis", "unknown#description": ""}, "reportInstances": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON>", "authorId#description": "", "createdAt": "Paskelbta", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> atask<PERSON>", "run_many": "<PERSON><PERSON><PERSON><PERSON><PERSON> atask<PERSON>", "run_one": "paleisti ataskaitą", "run_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> atask<PERSON>"}, "reports": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Automatizuotas", "name#description": "", "values": {"weekly": "<PERSON><PERSON><PERSON><PERSON>", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_few": "ataskaitos", "report_many": "ataskaitos", "report_one": "ataskaita", "report_other": "ataskaitos"}, "robots": {"classes": {"buds#description": "", "buds_few": "Buds", "buds_many": "Buds", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "", "moduleValidationStations_few": "Modulių tikrinimo stotelės", "moduleValidationStations_many": "Modulių tikrinimo stotelės", "moduleValidationStations_one": "Modulių tikrinimo stotelė", "moduleValidationStations_other": "Modulių tikrinimo stotelės", "reapersCarbon#description": "", "reapersCarbon_few": "Reapers", "reapersCarbon_many": "Reapers", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reapers", "reapersCustomer_few": "", "reapersCustomer_many": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_few": "traktoriai", "rtcs_many": "traktoriai", "rtcs_one": "trak<PERSON><PERSON>", "rtcs_other": "traktoriai", "simulators#description": "", "simulators_few": "Simulators", "simulators_many": "Simulators", "simulators_one": "Simulator", "simulators_other": "Simulators", "slayersCarbon#description": "", "slayersCarbon_few": "Slayers", "slayersCarbon_many": "Slayers", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "", "slayersCustomer_few": "Laserweeders", "slayersCustomer_many": "Laserweeders", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "Nežinoma klasė", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Neveikiantys <PERSON>i", "lasersOffline#description": "", "lifetimeArea": "Visas apdorotas plotas", "lifetimeArea#description": "", "lifetimeTime": "Visas veikimo laikas", "lifetimeTime#description": "", "localTime": "<PERSON><PERSON><PERSON> laikas", "localTime#description": "", "reportedAt": "Paskutinį kartą atnaujinta", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Programinės įrangos versija", "softwareVersion#description": "", "supportSlack": "„Slack“ pagalbos kanalas", "supportSlack#description": "", "targetVersion": "Tikslinė versija", "targetVersion#description": ""}, "robot#description": "", "robot_few": "robotai", "robot_many": "robotai", "robot_one": "robotas", "robot_other": "robotai", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown#description": ""}, "users": {"activated": "Aktyvuota", "activated#description": "", "fields": {"email": "El. <PERSON>", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Aktyva<PERSON>mas", "name#description": "", "values": {"false": "LAUKIAMA", "false#description": ""}}}, "operator#description": "", "operator_few": "operatoriai", "operator_many": "operatoriai", "operator_one": "operatorius", "operator_other": "operatoriai", "role#description": "", "role_few": "Funkcijos", "role_many": "Funkcijos", "role_one": "Funkcija", "role_other": "Funkcijos", "roles": {"carbon_basic": "„Carbon Robotics“", "carbon_basic#description": "", "carbon_tech": "„Carbon Robotics“ (techn.)", "carbon_tech#description": "", "farm_manager": "„Farm Manager“ (<PERSON><PERSON><PERSON>ady<PERSON>)", "farm_manager#description": "", "operator_advanced": "Operatorius (vyresn.)", "operator_advanced#description": "", "operator_basic": "Operatorius", "operator_basic#description": "", "robot_role": "Robotas", "robot_role#description": "", "unknown_role": "Nežinoma funkcija", "unknown_role#description": ""}, "staff": "Darbuotojai", "staff#description": "", "user#description": "", "user_few": "naudo<PERSON>ja<PERSON>", "user_many": "naudo<PERSON>ja<PERSON>", "user_one": "naudotojas", "user_other": "naudo<PERSON>ja<PERSON>"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_few": "Greičio įvertinimo įrenginiai", "velocityEstimator_many": "Greičio įvertinimo įrenginiai", "velocityEstimator_one": "Greičio įvertinimo įrenginys", "velocityEstimator_other": "Greičio įvertinimo įrenginiai"}, "weeds": {"categories": {"blossom": "<PERSON><PERSON><PERSON>", "blossom#description": "", "broadleaf": "Plačialapė piktžolė", "broadleaf#description": "", "fruit": "<PERSON><PERSON><PERSON>", "fruit#description": "", "grass": "<PERSON><PERSON><PERSON><PERSON>", "grass#description": "", "offshoot": "Siaurialapės p<PERSON>ž<PERSON>ės", "offshoot#description": "", "preblossom": "<PERSON><PERSON><PERSON><PERSON>", "preblossom#description": "", "purslane": "Portulaka", "purslane#description": "", "runner": "Ūsas", "runner#description": "", "unknown": "Nežinomos piktžolės", "unknown#description": ""}, "weed#description": "", "weed_few": "Piktžolės", "weed_many": "Piktžolės", "weed_one": "Piktžolė", "weed_other": "Piktžolės"}}, "utils": {"actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "add#description": "", "addLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "addLong#description": "", "apply": "<PERSON><PERSON><PERSON>", "apply#description": "", "applyLong": "<PERSON><PERSON><PERSON> {{subject}}", "applyLong#description": "", "backLong": "atgal į {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "cancelLong#description": "", "clear": "Išvalyti", "clear#description": "", "confirm": "Patvirtinkite", "confirm#description": "", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continue#description": "", "copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} sukurti", "createdLong#description": "", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete#description": "", "deleteLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} i<PERSON><PERSON><PERSON>", "deletedLong#description": "", "disableLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "disableLong#description": "", "discard": "<PERSON><PERSON><PERSON><PERSON>", "discard#description": "", "edit": "Red<PERSON><PERSON><PERSON>", "edit#description": "", "editLong": "Redaguoti {{subject}}", "editLong#description": "", "enableLong": "Įjungti {{subject}}", "enableLong#description": "", "exit": "<PERSON><PERSON><PERSON><PERSON>", "exit#description": "", "exitLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "exitLong#description": "", "goToLong": "Eikite į {{subject}}", "goToLong#description": "", "invite": "<PERSON><PERSON><PERSON><PERSON>", "invite#description": "", "inviteLong": "Kvie<PERSON><PERSON> {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} k<PERSON>timas", "invitedLong#description": "", "leaveUnchanged": "<PERSON><PERSON><PERSON>", "leaveUnchanged#description": "", "new": "<PERSON><PERSON><PERSON>", "new#description": "", "newLong": "Naujas {{subject}}", "newLong#description": "", "next": "Kitas", "next#description": "", "pause": "Pauzė", "pause#description": "", "play": "<PERSON><PERSON><PERSON><PERSON>", "play#description": "", "previous": "<PERSON><PERSON><PERSON><PERSON>", "previous#description": "", "ranLong": "{{subject}} paleistas", "ranLong#description": "", "reload": "Įkelti pakartotinai", "reload#description": "", "resetLong": "Atstatyti {{subject}}", "resetLong#description": "", "retry": "Bandyti dar kartą", "retry#description": "", "run": "<PERSON><PERSON><PERSON><PERSON>", "run#description": "", "runLong": "Pale<PERSON>i {{subject}}", "runLong#description": "", "save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "save#description": "", "saveLong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "saveLong#description": "", "saved": "Išsaugota", "saved#description": "", "savedLong": "{{subject}} i<PERSON><PERSON><PERSON><PERSON>", "savedLong#description": "", "search": "Pa<PERSON>š<PERSON>", "search#description": "", "searchLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "searchLong#description": "", "selectAll": "Pasirinkti viską", "selectAll#description": "", "selectLong": "Pasirinkite {{subject}}", "selectLong#description": "", "selectNone": "Nepasirinkti nieko", "selectNone#description": "", "send": "Si<PERSON>sti", "send#description": "", "showLong": "<PERSON><PERSON><PERSON> {{subject}}", "showLong#description": "", "submit": "Pat<PERSON><PERSON><PERSON>", "submit#description": "", "toggle": "<PERSON><PERSON><PERSON><PERSON>", "toggle#description": "", "toggleLong": "Perjungti {{subject}}", "toggleLong#description": "", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update#description": "", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updated#description": "", "updatedLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "updatedLong#description": "", "uploaded": "Įkelti", "uploaded#description": "", "viewLong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Aktyvus", "active#description": "", "critical": "Kritiškai svarbus", "critical#description": "", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default#description": "", "degraded": "Su<PERSON><PERSON><PERSON><PERSON>", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Įjungta", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "<PERSON><PERSON><PERSON>", "error#description": "", "estopOff": "<PERSON><PERSON><PERSON><PERSON>", "estopOff#description": "", "estopOn": "Paspaustas avarinio sustabdymo my<PERSON>s", "estopOn#description": "", "fast": "G<PERSON><PERSON>", "fast#description": "", "few": "Keletas", "few#description": "", "good": "<PERSON><PERSON><PERSON>", "good#description": "", "hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hidden#description": "", "high": "Aukštas", "high#description": "", "id": "ID", "id#description": "", "inactive": "Neaktyvus", "inactive#description": "", "interlockSafe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interlockSafe#description": "", "interlockUnsafe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interlockUnsafe#description": "", "large": "<PERSON><PERSON><PERSON>", "large#description": "", "laserKeyOff": "Užrakinta", "laserKeyOff#description": "", "laserKeyOn": "Įsitraukęs", "laserKeyOn#description": "", "liftedOff": "Su<PERSON><PERSON><PERSON><PERSON>", "liftedOff#description": "", "liftedOn": "<PERSON><PERSON><PERSON>", "liftedOn#description": "", "loading": "Įkeliama", "loading#description": "", "low": "<PERSON><PERSON><PERSON>", "low#description": "", "majority": "Dauguma", "majority#description": "", "medium": "<PERSON><PERSON><PERSON><PERSON>", "medium#description": "", "minority": "<PERSON><PERSON><PERSON><PERSON>", "minority#description": "", "name": "Pavadinimas", "name#description": "", "no": "Ne", "no#description": "", "none": "Nėra", "none#description": "", "offline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offline#description": "", "ok": "G<PERSON><PERSON>", "ok#description": "", "poor": "<PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Pažanga", "progress#description": "", "serial": "<PERSON><PERSON><PERSON>", "serial#description": "", "slow": "Lėtas", "slow#description": "", "small": "<PERSON><PERSON><PERSON>", "small#description": "", "sparse": "<PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_few": "Tipai", "type_many": "Tipai", "type_one": "Tipas", "type_other": "Tipai", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "waterProtectNormal": "<PERSON><PERSON> d<PERSON>", "waterProtectNormal#description": "", "waterProtectTriggered": "<PERSON><PERSON><PERSON><PERSON> van<PERSON>", "waterProtectTriggered#description": "", "yes": "<PERSON><PERSON>", "yes#description": ""}, "form": {"booleanType": "<PERSON><PERSON> b<PERSON><PERSON> log<PERSON>s", "booleanType#description": "", "copyConfigFrom": "Kopijuoti konfigūravimo duomenis iš...", "copyConfigFrom#description": "", "integerType": "<PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON>", "integerType#description": "", "maxLessThanMin": "Maks. vertė turi būti did<PERSON>n<PERSON> nei min. vertė", "maxLessThanMin#description": "", "maxSize": "Negali v<PERSON> {{limit}} ženklai", "maxSize#description": "", "minGreaterThanMax": "Min. vertė turi būti ma<PERSON> nei maks. vertė", "minGreaterThanMax#description": "", "moveDown": "<PERSON><PERSON><PERSON>", "moveDown#description": "", "moveUp": "<PERSON><PERSON><PERSON>", "moveUp#description": "", "noOptions": "Nėra <PERSON>ų", "noOptions#description": "", "numberType": "<PERSON><PERSON><PERSON><PERSON>", "numberType#description": "", "optional": "(neprivaloma)", "optional#description": "", "required": "<PERSON><PERSON><PERSON><PERSON>", "required#description": "", "stringType": "<PERSON><PERSON><PERSON><PERSON>", "stringType#description": ""}, "lists": {"+3": "{{b}}, ir {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} ir {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Įkelti daugiau", "loadMore#description": "", "noMoreResults": "Nėra daugiau rezultatų", "noMoreResults#description": "", "noResults": "Rezultatų nėra", "noResults#description": ""}, "metrics": {"aggregates": {"max": "<PERSON><PERSON><PERSON><PERSON>", "max#description": "", "min": "Minimal<PERSON>", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON>", "avgCropSizeMm#description": "", "avgSpeedMph": "Vid<PERSON>nis važ<PERSON><PERSON><PERSON> greitis", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "<PERSON><PERSON><PERSON><PERSON>", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "<PERSON><PERSON><PERSON><PERSON> (netikslinis)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Vidutinis piktžolių spindulys", "avgWeedSizeMm#description": "", "bandingConfigName": "Juostų konfigūracija", "bandingConfigName#description": "", "bandingEnabled": "Juostų nustatymas", "bandingEnabled#description": "", "bandingPercentage": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> nustatyt<PERSON> juostos", "bandingPercentage#description": "", "coverageSpeedAcresHr": "<PERSON><PERSON><PERSON><PERSON> apr<PERSON><PERSON><PERSON> greitis", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Pasėli<PERSON> tankumas", "cropDensitySqFt#description": "", "distanceWeededMeters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keptCrops#description": "", "killedWeeds": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "killedWeeds#description": "", "missedCrops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "missedCrops#description": "", "missedWeeds": "Praleistos p<PERSON>", "missedWeeds#description": "", "notThinning": "<PERSON>ere<PERSON><PERSON>", "notThinning#description": "", "notWeeding": "Neravimos piktžolės", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Operatoriaus efektyvumas", "operatorEffectiveness#description": "", "overallEfficiency": "Bendras efe<PERSON>yvumas", "overallEfficiency#description": "", "skippedCrops": "<PERSON><PERSON><PERSON><PERSON>, kurių nepaisyta", "skippedCrops#description": "", "skippedWeeds": "Piktž<PERSON>ės, kurių nepaisyta", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Tikslinė ravėjimo trukmė", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thinnedCrops#description": "", "thinningEfficiency": "<PERSON><PERSON><PERSON>", "thinningEfficiency#description": "", "timeEfficiency": "Eksploatavi<PERSON> e<PERSON>", "timeEfficiency#description": "", "totalCrops": "<PERSON><PERSON><PERSON>", "totalCrops#description": "", "totalWeeds": "<PERSON><PERSON><PERSON>", "totalWeeds#description": "", "totalWeedsInBand": "<PERSON><PERSON><PERSON> (juostoje)", "totalWeedsInBand#description": "", "uptimeSeconds": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> t<PERSON>", "uptimeSeconds#description": "", "validCrops": "<PERSON><PERSON><PERSON>", "validCrops#description": "", "weedDensitySqFt": "Piktžolių tankis", "weedDensitySqFt#description": "", "weedingEfficiency": "<PERSON>v<PERSON><PERSON><PERSON>", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Piktžolių tipas: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Piktžolių tipas: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Piktžolių tipas: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Piktžolių tipas: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> p<PERSON> retin<PERSON>, jei retinimas buvo įjungtas.", "avgCropSizeMm#description": "", "bandingConfigName": "Jūsų paskutinis pasirinktas juostų profilis", "bandingConfigName#description": "", "crop": "Jūsų paskutinė pasirinkta kultūra", "crop#description": "", "cropDensitySqFt": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> p<PERSON> retin<PERSON>, jei retinimas buvo įjungtas.", "cropDensitySqFt#description": "", "keptCrops": "Numatomas kultūr<PERSON> skaič<PERSON> po retinimo", "keptCrops#description": "", "killedWeeds": "„LaserWeeder“ atpažino objektą kaip piktžolę ir jį nušovė", "killedWeeds#description": "", "missedCrops": "Kult<PERSON>ra buvo pa<PERSON><PERSON><PERSON> retin<PERSON>, bet praleista. Įprastos priežastys: per <PERSON><PERSON><PERSON>, nepasiekiamas arba siste<PERSON> k<PERSON>.", "missedCrops#description": "", "missedWeeds": "Piktž<PERSON><PERSON> buvo at<PERSON>, bet praleista. Įprastos priežastys: per <PERSON><PERSON><PERSON>, nepasiekiamas arba siste<PERSON> k<PERSON>.", "missedWeeds#description": "", "operatorEffectiveness": "<PERSON><PERSON>, ka<PERSON> gerai važiavimo greitis atitiko tikslinį greitį, kurį rekomendavo greičio įvertinimo įrenginys", "operatorEffectiveness#description": "", "overallEfficiency": "(<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON> + <PERSON><PERSON><PERSON> ve<PERSON>) / 2, jei kartu ir ravite, ir retinate", "overallEfficiency#description": "", "skippedCrops": "Kultūra buvo tikslingai praleista retinant. Įprastos priežastys: išjungta greitojo nustatymo men<PERSON>, yra ne juostoje arba netoli lašinės juosto<PERSON>.", "skippedCrops#description": "", "skippedWeeds": "Piktžolė buvo tikslingai praleista. Įprastos priežastys: išjungta greitojo nustatymo meniu arba yra ne juostoje.", "skippedWeeds#description": "", "thinningEfficiency": "(<PERSON><PERSON><PERSON><PERSON><PERSON> kult<PERSON> + <PERSON><PERSON><PERSON> kult<PERSON>) / Numatomos rasti kultū<PERSON> × 100 %", "thinningEfficiency#description": "", "timeEfficiency": "(Aktyvaus darbo laikas / Įjungimo laikas) × 100 %", "timeEfficiency#description": "", "uptimeSeconds": "<PERSON><PERSON> la<PERSON>, kai „<PERSON>“ buvo įjungtas. Įskaitant budė<PERSON><PERSON> laiką ir (arba) pakėlimą.", "uptimeSeconds#description": "", "weedDensitySqFt": "Numatomos rasti k<PERSON> (iš viso) / apr<PERSON><PERSON>is", "weedDensitySqFt#description": "", "weedingEfficiency": "(Sunai<PERSON><PERSON> p<PERSON> / Juostoje rastos piktžol<PERSON>) × 100 %", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON>, kurį „LaserWeeder“ aktyviai ravėjo ar retino", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Greičio efektyvumas", "operatorEffectiveness#description": "", "timeEfficiency": "<PERSON><PERSON><PERSON><PERSON>", "timeEfficiency#description": "", "totalWeeds": "<PERSON><PERSON><PERSON> (iš viso)", "totalWeedsInBand": "<PERSON><PERSON><PERSON> (juostoje)", "totalWeedsInBand#description": "", "uptimeSeconds": "Įjungimo laikas", "uptimeSeconds#description": "", "validCrops": "Numatomos rasti k<PERSON>", "validCrops#description": "", "weedingUptimeSeconds": "Aktyvaus darbo laikas", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "<PERSON><PERSON><PERSON><PERSON>", "coverage#description": "", "field": "<PERSON><PERSON>", "field#description": "", "hardware": "", "hardware#description": "", "performance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "performance#description": "", "speed": "<PERSON><PERSON><PERSON>", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "<PERSON><PERSON><PERSON><PERSON>", "usage#description": ""}, "metric#description": "", "metric_few": "<PERSON><PERSON><PERSON><PERSON>", "metric_many": "<PERSON><PERSON><PERSON><PERSON>", "metric_one": "<PERSON><PERSON><PERSON>", "metric_other": "<PERSON><PERSON><PERSON><PERSON>", "spatial": {"heatmapWarning": "~{{area}} bloke", "heatmapWarning#description": "", "metrics": {"altitude": "<PERSON><PERSON><PERSON><PERSON>", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "Avarinio sustab<PERSON> my<PERSON>", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "B<PERSON>ka<PERSON><PERSON>", "interlock#description": "", "keptCropDensity": "Paliktų kultūrų tankis", "keptCropDensity#description": "", "laserKey": "Lazerio raktas", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Greičio efektyvumas", "speed#description": "", "speedTargetMinimum": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> gre<PERSON> (mažiausias)", "speedTargetMinimum#description": "", "speedTargetRow1": "Vid<PERSON>nis tik<PERSON>lini<PERSON> greitis (1 vaga)", "speedTargetRow1#description": "", "speedTargetRow2": "<PERSON><PERSON><PERSON><PERSON> tik<PERSON> greitis (2 vaga)", "speedTargetRow2#description": "", "speedTargetRow3": "<PERSON><PERSON><PERSON><PERSON> tik<PERSON> greitis (3 vaga)", "speedTargetRow3#description": "", "speedTargetSmoothed": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> greitis", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Retinimas", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "<PERSON><PERSON>", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Apsauga nuo vandens", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Pasirinkta", "selected#description": "", "showAll": "Rodyti visus {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/pėd.²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/col.²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/myl²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_few": "vatai", "WLong_many": "vatai", "WLong_one": "vatas", "WLong_other": "vatai", "ac": "ac", "ac#description": "", "ac/h": "ac/val.", "ac/h#description": "", "acLong#description": "", "acLong_few": "akrų", "acLong_many": "akrų", "acLong_one": "akras", "acLong_other": "akrų", "acres#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "d": "d.", "d#description": "", "dLong#description": "", "dLong_few": "dienų", "dLong_many": "dienų", "dLong_one": "diena", "dLong_other": "dienų", "day#description": "", "days#description": "", "ft": "ft", "ft#description": "", "ft/s": "pėd./sek.", "ft/s#description": "", "ft2": "pėd.²", "ft2#description": "", "ftLong#description": "", "ftLong_few": "pėdų", "ftLong_many": "pėdų", "ftLong_one": "pėda", "ftLong_other": "pėdų", "h": "val.", "h#description": "", "hLong#description": "", "hLong_few": "valandų", "hLong_many": "valandų", "hLong_one": "valanda", "hLong_other": "valandų", "ha": "ha", "ha#description": "", "ha/h": "ha/val.", "ha/h#description": "", "haLong#description": "", "haLong_few": "hektarų", "haLong_many": "hektarų", "haLong_one": "<PERSON><PERSON><PERSON><PERSON>", "haLong_other": "hektarų", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "col.²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/val.", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/sek.", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_few": "metrų", "mLong_many": "metrų", "mLong_one": "metras", "mLong_other": "metrų", "mi": "mi", "mi#description": "", "mi2": "myl.²", "mi2#description": "", "min": "min.", "min#description": "", "minLong#description": "", "minLong_few": "minučių", "minLong_many": "minučių", "minLong_one": "minutė", "minLong_other": "minučių", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "mėn.", "month#description": "", "monthLong#description": "", "monthLong_few": "mėnesių", "monthLong_many": "mėnesių", "monthLong_one": "<PERSON><PERSON><PERSON><PERSON>", "monthLong_other": "mėnesių", "mph": "myl./val.", "mph#description": "", "ms": "ms.", "ms#description": "", "s": "sek.", "s#description": "", "sLong#description": "", "sLong_few": "sekundžių", "sLong_many": "sekundžių", "sLong_one": "<PERSON>kund<PERSON>", "sLong_other": "sekundžių", "seconds#description": "", "watts#description": "", "week": "sav.", "week#description": "", "weekLong#description": "", "weekLong_few": "savaičių", "weekLong_many": "savaičių", "weekLong_one": "savaitė", "weekLong_other": "savaičių", "yd#description": "", "year": "m.", "year#description": "", "yearLong#description": "", "yearLong_few": "metai", "yearLong_many": "metai", "yearLong_one": "metai", "yearLong_other": "metai"}}, "views": {"admin": {"alarms": {"allowWarning": "Įtraukus kodų į leidžiamųjų sąrašą, bus leidžiama naudojant įspėjimus susisiekti su pagalbos kanalais platformoje „Slack“", "allowWarning#description": "", "blockWarning": "Įtraukus kodų į blokavimo s<PERSON>, nebus leidžiama naudojant įspėjimus susisiekti su pagalbos kanalais platformoje „Slack“", "blockWarning#description": "", "lists": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lists#description": "", "title": "Visuotinis įspėjimų leidžiamų<PERSON> s<PERSON>šas", "title#description": "", "titleAllow": "Įspėjimų le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "titleAllow#description": "", "titleBlock": "Įspėjimų bloka<PERSON><PERSON> s<PERSON>", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "<PERSON>ustat<PERSON><PERSON>", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> naudojant „<PERSON>“, <tt>{row1,row2,row3}</tt> naudojant „<PERSON>“", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_few": "operacijos", "operation_many": "operacijos", "operation_one": "operacija", "operation_other": "operacijos", "operationsCount": "Operacijos ({{count}})", "operationsCount#description": "", "operationsHint": "Pasirinkite mazgą konfigūrac<PERSON>jos schemoje, kad pridėtumėte operaciją.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_few": "aptikta {{count}} k<PERSON><PERSON>", "encounteredErrors_many": "aptikta {{count}} k<PERSON><PERSON>", "encounteredErrors_one": "aptik<PERSON> {{count}} k<PERSON>a", "encounteredErrors_other": "aptikta {{count}} k<PERSON><PERSON>", "noChanges": "be pakeitimų", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_few": "atnaujinta {{count}} klavišų", "updatedKeys_many": "atnaujinta {{count}} klavišų", "updatedKeys_one": "at<PERSON><PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedKeys_other": "atnaujinta {{count}} klavišų"}, "outcomes": {"failure": "Nepavyko", "failure#description": "", "partial": "<PERSON><PERSON> pav<PERSON>o", "partial#description": "", "success": "<PERSON><PERSON><PERSON>", "success#description": ""}, "title": "Grup<PERSON>s k<PERSON>", "title#description": ""}, "clearCaches": {"action": "At<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "action#description": "", "description": "Problema? Pirmiausia pabandykite atnaujinti „Robot Syncer“ talpyklą.", "description#description": ""}, "warnings": {"global": "Pakeitus š<PERSON> nustatymus, bus paveiktos visų esamų ir būsimų {{class}} numatytosios vertės ir rekomendacijos", "global#description": "", "notSimon": "<PERSON><PERSON><PERSON>, to<PERSON><PERSON><PERSON> tik<PERSON>i neturėtum<PERSON>te to redaguoti… 👀", "notSimon#description": "", "unsyncedKeys": {"description": "", "description#description": "", "title": "Nesinchronizuoti raktai", "title#description": ""}}}, "portal": {"clearCaches": {"action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action#description": "", "description": "Išvalomi vidiniai Operacijų centro podėliai. <PERSON>, trumpuo<PERSON> la<PERSON> **sulėtės** kai kurių užklausų teikimas, bet **gali** bū<PERSON> i<PERSON><PERSON>rę<PERSON> su užstrigusiais pasenusiais duomenimis susijusios problemos", "description#description": "", "details": "Paspauskite šį mygtuką, jei rankiniu būdu redagavote naudotojo leidimus puslapyje „Auth0“ (ne per operacijų centrą) arba atlikote pakeitimus trečiosios šalies integracijoje, pvz., „Stream“ arba „Slack“, kurie nerodomi.", "details#description": ""}, "title": "Operacijų centras", "title#description": "", "warnings": {"global": "Šiame puslapyje pateikiamos parinktys paveiks tiesioginį Operacijų centro veikimą gamybos etapu.", "global#description": "", "notPortalAdmin": "<PERSON><PERSON><PERSON> arb<PERSON>, tad greičiausiai neturėtum<PERSON>te to redaguoti... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "„Slack“ pagalbos kanalas turėtų prasidėti „#“: pvz., „#support-001-carbon“.", "supportSlackLeadingHash#description": ""}}, "title": "<PERSON><PERSON>", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Slėpti ašies istoriją", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Priskirti traktoriams", "orchestrateView#description": "", "showPivotHistory": "Rodyti ašies istoriją", "showPivotHistory#description": ""}, "fetchFailed": "Nepavyko įkelti vietovės duomenų", "fetchFailed#description": "", "goLive": "atnaujinimas realiuoju laiku", "goLive#description": "", "hideRows": "Slėpti vagas", "hideRows#description": "", "jobDetails": {"assignmentsFailed": "Nepavyko rasti užduoties, bandyti dar kartą?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Darbo nebegalima priskirti traktoriams ir jį reikia sukurti iš naujo.", "description#description": ""}, "customer": {"unknown": "<PERSON><PERSON>ž<PERSON><PERSON>", "unknown#description": "", "withName": "Klientas: {{name}}", "withName#description": ""}, "farm": {"unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Ūkis: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Laukas: {{name}}", "withName#description": ""}, "jobFinished": "<PERSON><PERSON> baigtas {{time}}", "jobFinished#description": "", "jobStarted": "<PERSON><PERSON> pradė<PERSON> {{time}}", "jobStarted#description": "", "openInFarmView": "Atverti ūkio vaizdą", "openInFarmView#description": "", "state": "Būsena: {{state}}", "state#description": "", "type": "Darbo tipas: {{type}}", "type#description": ""}, "lastPolled": "Paskutinė apklausa", "lastPolled#description": "", "live": "Realiuoju laiku", "live#description": "", "objectiveFromOtherJob": "<PERSON><PERSON> da<PERSON>", "objectiveFromOtherJob#description": "", "rowWidthUnits": "<PERSON>agos plotis {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Ūkiai", "farms#description": "", "tractors": "Traktoriai", "tractors#description": ""}, "showRows": "<PERSON><PERSON><PERSON> vagas", "showRows#description": "", "stalePivots": "Ašies informacija gali būti pase<PERSON>i", "stalePivots#description": "", "suggestedAssignments": "<PERSON><PERSON><PERSON><PERSON>", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"hideThesePoints": "Slėpti šiuos taškus", "hideThesePoints#description": "", "onlyShowSelected": "Rod<PERSON><PERSON> tik pasirinktus", "onlyShowSelected#description": "", "showAllPoints": "<PERSON><PERSON><PERSON> visus taškus", "showAllPoints#description": "", "showThesePoints": "Rodyti šiuos taš<PERSON>", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Riba", "boundary#description": "", "center": "Centras", "center#description": "", "centerPivot": "<PERSON><PERSON><PERSON><PERSON>", "centerPivot#description": "", "endpointId": "Galutinio taško ID", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON>", "length#description": "", "plantingHeading": "<PERSON><PERSON><PERSON> kryptis", "plantingHeading#description": "", "point": "<PERSON><PERSON><PERSON>", "point#description": "", "points": "Taškai", "points#description": "", "width": "<PERSON><PERSON><PERSON>", "width#description": ""}, "farm": "Ūkis", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Nefik<PERSON><PERSON><PERSON>", "none#description": "", "rtkFixed": "RTK fiksuotas", "rtkFixed#description": "", "rtkFloat": "„RTK plaukiojantis“", "rtkFloat#description": "", "unknown": "Nežinomas fi<PERSON> tip<PERSON>", "unknown#description": ""}, "selectionPanel": {"allPoints": "Visi taškai", "allPoints#description": "", "boundary": "Riba", "boundary#description": "", "center": "Centras", "center#description": "", "centerPivot": "<PERSON><PERSON><PERSON><PERSON>", "centerPivot#description": "", "endpointId": "<PERSON><PERSON><PERSON>", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON>", "length#description": "", "plantingHeading": "<PERSON><PERSON><PERSON> kryptis", "plantingHeading#description": "", "point": "<PERSON><PERSON><PERSON>", "point#description": "", "points": "Taškai", "points#description": "", "width": "<PERSON><PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "<PERSON><PERSON><PERSON> be pavadinimo <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "Ūkio ribos", "farmBoundary#description": "", "field": "<PERSON><PERSON>", "field#description": "", "headland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headland#description": "", "obstacle": "<PERSON><PERSON><PERSON><PERSON>", "obstacle#description": "", "privateRoad": "<PERSON><PERSON><PERSON><PERSON> keli<PERSON>", "privateRoad#description": "", "unknown": "Nežinoma zona", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "Linijoje turi būti tik<PERSON>liai du taškai", "exactlyTwoPoints#description": "", "wrongFieldType": "<PERSON><PERSON> „{{field}}“ t<PERSON><PERSON><PERSON><PERSON> būti {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "Geometrinė forma turėtų būti {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON turėtų būti objektas", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Nėra įjungtų robotų", "empty#description": ""}, "title": "<PERSON><PERSON><PERSON>", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Žiūrėti pakeitimų istoriją", "open#description": "", "title": "Pakeitimų istorija", "title#description": ""}, "errors": {"failed": "Nepavyko įkelti nustatymų medžio", "failed#description": ""}, "onlyChanged": "<PERSON><PERSON><PERSON> tik pakeistus", "onlyChanged#description": ""}, "errors": {"empty": "Nepriskirta jokių robotų", "empty#description": ""}, "hardware": {"errors": {"old": "Robotas nenurodo kompiuterio serijos numerių (tikriausia per senas)", "old#description": ""}, "fields": {"hostname": "Pagrindinio kompiuterio vardas", "hostname#description": ""}, "installedVersion": "Įdiegta versija:", "installedVersion#description": "", "ready": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "name#description": "", "values": {"false": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "false#description": "", "installed": "Įdiegta", "installed#description": "", "true": "Paruošta!", "true#description": ""}}, "tabs": {"computers": "Kompiuteriai", "computers#description": "", "versions": "<PERSON><PERSON><PERSON><PERSON>", "versions#description": ""}, "targetVersion": "Tikslinė versija:", "targetVersion#description": "", "title": "Techninė įranga", "title#description": "", "updateHistory": "Versijų naujinimo istorija <0>netrukus bus pasiekiama™️</0>", "updateHistory#description": ""}, "history": {"borders": "Rėmeliai", "borders#description": "", "errors": {"invalidDate": "Pasirinkite tinkamą datų intervalą", "invalidDate#description": "", "noJobs": "Pasirinktu intervalu nėra pateiktų užduočių", "noJobs#description": "", "noMetrics": "Nepateikiami jokie <PERSON>", "noMetrics#description": ""}, "moreMetrics": "<PERSON>r. <PERSON><PERSON><PERSON><PERSON> rod<PERSON>", "moreMetrics#description": "", "navTitle": "Istorija", "navTitle#description": "", "placeholder": "<PERSON>i norite per<PERSON><PERSON><PERSON><PERSON><PERSON> duomen<PERSON>, pasirinkite užduotį arba datą", "placeholder#description": "", "points": "Taškai", "points#description": "", "warnings": {"beta": {"description": "<PERSON> rod<PERSON>i rodomi m<PERSON>lyn<PERSON> spalvos", "description#description": ""}, "ongoing": "<PERSON><PERSON> da<PERSON> rod<PERSON>i dar ne galutiniai", "ongoing#description": ""}}, "status": "<PERSON><PERSON><PERSON><PERSON>", "status#description": "", "summary": {"banding": {"definition": "Apibrėžimas", "definition#description": "", "dynamic": "<PERSON><PERSON><PERSON>", "dynamic#description": "", "dynamicDisabled": "(Nustatymų skiltyje dinaminis juostų nustatymas išjungtas)", "dynamicDisabled#description": "", "rows": "Vagos", "rows#description": "", "static": "<PERSON><PERSON><PERSON>", "static#description": "", "type": "Tipas", "type#description": "", "unknown": "Nežinomas juostų nustatymas", "unknown#description": "", "v1": "1 vers.", "v1#description": "", "v2": "2 vers.", "v2#description": "", "version": "<PERSON><PERSON><PERSON>", "version#description": ""}, "config": {"changes#description": "", "changes_few": "Almanacho pakeitimų: {{count}}", "changes_many": "Almanacho pakeitimų: {{count}}", "changes_one": "{{count}} almanac<PERSON> pakeitimas", "changes_other": "Almanacho pakeitimų: {{count}}", "cpt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cpt#description": "", "default": "(NUMATYTOJI VERTĖ: {{value}})", "default#description": "", "wpt": "Piktžolių atpažinimo riba", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON> galinis", "backLeft#description": "", "backRight": "<PERSON><PERSON><PERSON><PERSON>", "backRight#description": "", "frontLeft": "P<PERSON><PERSON><PERSON> kairioji", "frontLeft#description": "", "frontRight": "<PERSON><PERSON><PERSON><PERSON>", "frontRight#description": "", "title": "Ratų koduotuvai", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Nepavyko įkelti roboto suves<PERSON>s", "failed#description": "", "lasers": {"disabled#description": "", "disabled_few": "{{count}} išjungtų lazerių", "disabled_many": "{{count}} išjungtų lazerių", "disabled_one": "{{count}} i<PERSON><PERSON><PERSON><PERSON> lazeris", "disabled_other": "{{count}} išjungtų lazerių", "row": "Vaga {{row}}", "row#description": ""}, "machineHealth": "<PERSON><PERSON><PERSON><PERSON>", "machineHealth#description": "", "navTitle": "Su<PERSON><PERSON><PERSON>", "navTitle#description": "", "safetyRadius": {"driptape": "<PERSON><PERSON><PERSON><PERSON> juosta", "driptape#description": "", "title": "Apsaugin<PERSON> spindulys", "title#description": ""}, "sections": {"management": "<PERSON><PERSON><PERSON>", "management#description": "", "software": "Programinė įranga", "software#description": ""}, "supportLinks": {"chipChart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chipChart#description": "", "datasetVisualization": "Duomenų rinkinio vizualizacija", "datasetVisualization#description": "", "title": "Pagalbos nuorodos", "title#description": ""}}, "support": {"carbon": "„Carbon“ pagalbos komanda", "carbon#description": "", "chatMode": {"legacy": "<PERSON><PERSON><PERSON><PERSON> pokal<PERSON>", "legacy#description": "", "new": "<PERSON><PERSON><PERSON> pokal<PERSON>", "new#description": ""}, "errors": {"failed": "Nepavyko įkelti žinutės", "failed#description": "", "old": {"description": "{{serial}} veikia programinės įrangos {{version}} versija. <PERSON><PERSON> pasi<PERSON> paga<PERSON> poka<PERSON>, turi bū<PERSON> {{target}}.", "description#description": "", "title": "Netinkama roboto versija", "title#description": ""}}, "localTime": "Viet<PERSON> laikas: {{time}}", "localTime#description": "", "navTitle": "Pagalba", "navTitle#description": "", "toCarbon": "Žinutė $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Žinutė $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} i<PERSON><PERSON><PERSON><PERSON>. Operatorius ne<PERSON>, kol robote neveiks r<PERSON>.", "description#description": "", "title": "Robotas išjungtas", "title#description": ""}}}, "toggleable": {"internal": "<PERSON><PERSON><PERSON><PERSON>", "internal#description": ""}, "uploads": {"errors": {"empty": "Įkėlimų nėra", "empty#description": ""}}}, "title": "Robotų grupė", "title#description": "", "views": {"fields": {"name": "Filtro pavadinimas", "name#description": "", "otherRobots": "<PERSON><PERSON> robotai ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "<PERSON><PERSON><PERSON><PERSON> robotai", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards#description": "", "table": "Lentelė", "table#description": ""}}}, "fleetView#description": "", "fleetView_few": "filtrai", "fleetView_many": "filtrai", "fleetView_one": "filtras", "fleetView_other": "filtrai", "tableOnly": "<PERSON> kurie stulpeliai galimi tik lent<PERSON> rod<PERSON>", "tableOnly#description": ""}}, "knowledge": {"title": "Žinių bazė", "title#description": ""}, "metrics": {"jobStatus": {"closed": "<PERSON>ždarytas", "closed#description": "", "description": "<PERSON><PERSON> b<PERSON>", "description#description": "", "open": "Atidarytas", "open#description": ""}, "sections": {"estimatedFieldMetrics": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Mūsų modelis naudoja eksperimentinius kultūrų duomenis, kuriuose gali pasitaikyti netikslumų. Nuolat tobuliname jo patikimumą.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "<PERSON><PERSON><PERSON><PERSON> ir įrenginio rod<PERSON>i", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Nuvilkite failus čia iš USB (arba bet kurios kitos vietos)", "drop#description": "", "file#description": "", "file_few": "failų", "file_many": "failų", "file_one": "failas", "file_other": "failų", "ingestDescription": "„Carbon“ darbuotojai turėtų naudotis paslauga „Ingest“", "ingestDescription#description": "", "ingestLink": "Įkelti į „Ingest“", "ingestLink#description": "", "select": "<PERSON><PERSON><PERSON><PERSON> failus", "select#description": "", "title": "Įkelti", "title#description": "", "upload": "Įkelti į „Carbon“", "upload#description": "", "uploading": "Įkeliama {{subject}}…", "uploading#description": ""}, "reports": {"explore": {"graph": "<PERSON><PERSON><PERSON>", "graph#description": "", "groupBy": "Grupė pagal", "groupBy#description": "", "title": "Naršykite", "title#description": ""}, "scheduled": {"authorCarbonBot": "Carbon Bot", "authorCarbonBot#description": "", "authorUnknown": "Nežinomas autorius", "authorUnknown#description": "", "automation": {"customerReports": "Klientų ataskaitos", "customerReports#description": "", "errorTitle": "<PERSON>eg<PERSON><PERSON><PERSON> automatin<PERSON> ataskaita", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Nėra pasirinkto kliento", "none#description": ""}}, "reportDay": {"errors": {"none": "Nėra pasirinktos dienos", "none#description": ""}, "name": "Ataskaitos diena", "name#description": ""}, "reportEmails": {"errors": {"none": "Nėra priskirtų el. laiškų", "none#description": ""}, "name": "Klientų el. laiškai", "name#description": ""}, "reportHour": {"errors": {"none": "Nepasirinkta valanda", "none#description": ""}, "name": "Ataskaitos valanda", "name#description": ""}, "reportLookback": {"errors": {"none": "Nenustatytas grįžtamasis ryšys", "none#description": ""}, "name": "Atgalinė ataskaita", "name#description": ""}, "reportTimezone": {"errors": {"none": "<PERSON><PERSON> juo<PERSON>", "none#description": ""}, "name": "Pranešti apie laiko juostą", "name#description": ""}, "warningDescription": "Ji veikia kas  {{day}} per {{hour}} į {{timezone}} su {{lookback}} visų aktyviųjų dienų atgalinė peržiūra {{customer}} robotai.", "warningDescription#description": "", "warningTitle": "Tai automatinė ataskaita!", "warningTitle#description": ""}, "byline": "By {{author}}", "byline#description": "", "editor": {"columnsHidden": "Paslėptieji stulpeliai", "columnsHidden#description": "", "columnsVisible": "<PERSON><PERSON>", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_few": "Įspėjimas: jau yra {{count}} kit<PERSON> ataskaitų tokiu pavadinimu", "duplicateNames_many": "Įspėjimas: jau yra {{count}} kit<PERSON> ataskaitų tokiu pavadinimu", "duplicateNames_one": "Įspėjimas: jau yra dar viena ataskaita tokiu pavadinimu", "duplicateNames_other": "Įspėjimas: jau yra {{count}} kit<PERSON> ataskaitų tokiu pavadinimu", "fields": {"automateWeekly": "Automatizuoti kas savaitę", "automateWeekly#description": "", "name": "Ataskaitos pavadinimas", "name#description": "", "showAverages": "<PERSON><PERSON><PERSON> vid<PERSON>", "showAverages#description": "", "showTotals": "<PERSON><PERSON><PERSON>as", "showTotals#description": ""}}, "errors": {"noReport": "Ataskaita neegzistuoja arba neturite prieigos", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} bus visam la<PERSON><PERSON>.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "<PERSON><PERSON><PERSON> net<PERSON>te te<PERSON> {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "El. laiš<PERSON> nebus išsiųstas pakartotinai", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Ataskaita bus išsiųsta šiais el. pašto adresais", "publishEmailsHelperNew#description": ""}, "runAgain": "<PERSON><PERSON><PERSON> i<PERSON> naujo", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Pasirinkite vieną ar daugiau stulpelių", "noColumns#description": "", "noEndDate": "Pasirinkite pabaigos datą", "noEndDate#description": "", "noRobots": "Pasirinkite vieną ar daugiau robotų", "noRobots#description": "", "noStartDate": "Pasirinkite pradžios datą", "noStartDate#description": ""}, "fields": {"average": "Vidutiniškai", "average#description": "", "averageShort": "AVG", "averageShort#description": "", "date": "Data", "date#description": "", "group": "Serija/Data", "group#description": "", "groupJob": "<PERSON><PERSON><PERSON>./darbas", "groupJob#description": "", "mixed": "(<PERSON><PERSON><PERSON>)", "mixed#description": "", "total": "<PERSON><PERSON> viso", "total#description": "", "totalShort": "SUMA", "totalShort#description": ""}, "unknownReport": "Nežinoma ataskaita", "unknownReport#description": ""}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": "", "toLine": "skirta {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "V<PERSON>", "all#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": ""}, "robotsLabel": {"all": "Visi robotai", "all#description": "", "none": "Nėra robotų", "none#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> per <1>{{identityProvider}}</1>", "account#description": "", "apple": "„Apple“", "apple#description": "", "auth0": "naudo<PERSON><PERSON> var<PERSON> ir <PERSON>", "auth0#description": "", "google": "„Google OAuth“", "google#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": ""}, "cards": {"account": "Paskyra", "account#description": "", "advanced": "Išplėstiniai", "advanced#description": "", "localization": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localization#description": ""}, "delete": {"deleteAccount": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "deleteAccount#description": "", "dialog": {"description": "ĮSPĖJIMAS: <PERSON>io ve<PERSON> at<PERSON>uk<PERSON> negal<PERSON>. Visi duomenys bus prarasti.", "description#description": ""}}, "fields": {"experimental": "Įjungti eksperimentines funkcijas", "experimental#description": "", "language": "Kalba", "language#description": "", "measurement": {"name": "<PERSON><PERSON><PERSON> v<PERSON>", "name#description": "", "values": {"imperial": "Imperiniai (col., myl./val., a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "imperial#description": "", "metric": "Metriniai (mm, km/val., <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "<PERSON>si<PERSON><PERSON><PERSON>", "logOut#description": "", "title": "Nustatymai", "title#description": "", "version": "„Carbon Ops Center“ versija {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "Toks naudotojas neegzistuoja arba neturite leidimo <PERSON>.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "<PERSON><PERSON><PERSON><PERSON> vald<PERSON> „Auth0“", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Fukcija ir leidimai", "title#description": ""}, "profile": {"title": "Profilis", "title#description": ""}}, "toggleable": {"contractors": "Rangovai", "contractors#description": ""}}}}