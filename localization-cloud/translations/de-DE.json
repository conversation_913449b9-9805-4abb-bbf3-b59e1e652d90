{"components": {"AlarmTable": {"export": "{{robots}} Alarmhistorie {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Räumliche Kennzahlen stehen während der Evaluierung kostenlos zur Verfügung, können aber jederzeit geä<PERSON>, entfernt oder aktualisiert werden. Die Daten sollten von unabhängiger Stelle überprüft werden.", "description#description": "", "title": "Räumliche Kennzahlen Beta", "title#description": ""}, "tooltip": "Diese Funktion befindet sich in der Testphase und kann jederzeit geändert oder entfernt werden", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Chat konnte nicht geladen werden: {{message}}", "failed#description": ""}, "machineTranslated": "Maschinell übersetzt", "machineTranslated#description": "", "machineTranslatedFrom": "<PERSON><PERSON><PERSON><PERSON> übersetzt von {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "<PERSON>se Nachricht wurde gelöscht.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} wird endgültig gelöscht.", "description#description": "", "descriptionActive": "{{subject}} ist aktiv und kann daher nicht gelöscht werden.", "descriptionActive#description": ""}, "title": "Sind Sie sicher?", "title#description": ""}, "CopyToClipboardButton": {"click": "Zum Kopieren an<PERSON>en", "click#description": "", "copied": "Kopiert!", "copied#description": ""}, "CropEditor": {"failed": "Der Nutzpflanzen-Editor kon<PERSON> nicht geladen werden", "failed#description": "", "viewIn": "Ansicht in Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Löschen", "clear#description": "", "endDate": "Enddatum", "endDate#description": "", "error": "Fehler bei der Auswahl des Datumsbereichs", "error#description": "", "invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalid#description": "", "last7days": "Die letzten 7 Tage", "last7days#description": "", "lastMonth": "<PERSON>m let<PERSON><PERSON>", "lastMonth#description": "", "lastWeek": "Letzte Woche", "lastWeek#description": "", "minusDays": "Vor {{days}} Tagen", "minusDays#description": "", "plusDays": "in {{days}} <PERSON><PERSON>", "plusDays#description": "", "startDate": "Anfangsdatum", "startDate#description": "", "thisMonth": "In diesem <PERSON>", "thisMonth#description": "", "thisWeek": "In dieser <PERSON><PERSON>e", "thisWeek#description": "", "today": "<PERSON><PERSON>", "today#description": "", "tomorrow": "<PERSON><PERSON>", "tomorrow#description": "", "yesterday": "Gestern", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "ENTW", "dev#description": ""}, "ErrorBoundary": {"error": "Sorry, uner<PERSON><PERSON>", "error#description": "", "queryLimitReached": "Rendering eines Teildatensatzes, da zu viele Daten zurückgegeben wurden. Wenden Si<PERSON> sich für weitere Unterstützung an den Support", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Was ist passiert?", "comment#description": "", "feedback": "Rückmeldung", "feedback#description": "", "submit": "A<PERSON><PERSON>en und erneut laden", "submit#description": ""}, "GdprConsent": {"description": "Bitte überprüfen und stimmen Sie dem Fortfahren zu", "description#description": "", "statement": "Ich stimme den <0>Nutzungsbedingungen</0> und <1>Datenschutzbestimmungen</1> zu", "statement#description": "", "title": "Nutzungsbedingungen und Datenschutzbestimmungen", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "<PERSON><PERSON>", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "<PERSON><PERSON>", "help#description": "", "title": "Tastaturkürzel", "title#description": ""}, "LaserTable": {"export": "{{robots}} Laser {{date}}", "export#description": "", "installedOnly": "<PERSON><PERSON>", "installedOnly#description": "", "warnings": {"duplicate": "Dieser Roboter hat mehrere Laser in den folgenden Slots registriert: {{slots}}", "duplicate#description": "", "emptySlot": "Dieser Roboter hat keinen Laser in den folgenden Slots registriert: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Neuer Code", "new#description": ""}, "Loading": {"failed": "Carbon Ops Center konnte leider nicht geladen werden.", "failed#description": "", "placeholder": "Wird geladen ...", "placeholder#description": ""}, "ModelName": {"warning": "Warnung: <PERSON><PERSON> mit geringer Zuverlässigkeit", "warning#description": ""}, "PendingActivationOverlay": {"description": "Wir aktivieren Ihr Konto. Sie erhalten eine E-Mail, wenn der Vorgang abgeschlossen ist!", "description#description": "", "errors": {"carbon": {"description": "Die Carbon-E-Mail wurde erkannt, aber aufgrund der Anmeldung mit Benutzername/Passwort nicht verifiziert. Melden Sie sich ab und nutzen Sie die Option „Mit Google anmelden“, um diese automatisch zu aktivieren.", "description#description": "", "title": "Nicht verifiziertes Carbon-Konto", "title#description": ""}}, "hi": "Hallo {{name}}!", "hi#description": "", "logOut": "Mit dem falschen Konto angemeldet? <0>Abmelden</0>.", "logOut#description": "", "title": "Warten auf Aktivierung", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON><PERSON>", "more#description": ""}, "RobotImplementationSelector": {"status": "Implementierungsstatus", "status#description": "", "title": "Implementierungsstatus ändern", "title#description": "", "warning": "Eine Änderung des Implementierungsstatus kann automatisierte Arbeitsabläufe auslösen, die das Kundenerlebnis beeinträchtigen. TUN SIE ES NICHT, WENN SIE SICH NICHT SICHER SIND!", "warning#description": ""}, "ShowLabelsButton": {"text": "Etikett", "text#description": "", "tooltip": "Etiketten anzeigen", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "<PERSON><PERSON>ten zeigen", "tooltip#description": ""}, "almanac": {"crops": {"new": "Neue Nutzpflanze hinzufügen", "new#description": "", "none": "<PERSON><PERSON>katego<PERSON>", "none#description": "", "sync#description": ""}, "cropsSynced": "Alle Nutzpflanzen", "cropsSynced#description": "", "delete": {"description": "Dies kann nicht rückgängig gemacht werden", "description#description": ""}, "discard": {"description": "Änderungen an {{title}} verwerfen?", "description#description": "", "title": "Änderungen verwerfen?", "title#description": ""}, "fineTuneDescription": "Der Standardwert ist 5, die Laser Schussdauer kann um etwa 20 % pro Schritt verkürzt oder verlängert werden", "fineTuneDescription#description": "", "fineTuneTitle": "Multiplikator fein abstimmen", "fineTuneTitle#description": "", "formulas": {"all": "Alle Größen", "all#description": "", "copyFormula": "Formel kopieren", "copyFormula#description": "", "copySize": "Größen kopieren", "copySize#description": "", "exponent": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> den Radius in mm um diesen Exponenten", "description#description": "", "label": "Exponent (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "<PERSON><PERSON> Zahl von 1 bis 10, der Standardwert ist 5, kann die Laser-Schussdauer um ca. 20 % pro Schritt verkürzen oder verlängern. Dies ist die Zahl, die im Basismodus verwendet wird", "description#description": "", "label": "Feinabstimmungsindex (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Gesamtmultiplikator für die Erhöhung/Verringerung des Feinabstimmungsindex", "description#description": "", "label": "Feinabstimmung des Multiplikatorwerts (FM)", "label#description": ""}, "laserTime": "<PERSON><PERSON><PERSON>", "laserTime#description": "", "maxTime": {"description": "Begrenzen Sie die Schusszeit in ms", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "multiplier": {"description": "Wird mit dem Radius in mm multipliziert", "description#description": "", "label": "Multiplikator (A)", "label#description": ""}, "offset": {"description": "<PERSON><PERSON><PERSON> der Millisekunden, die unabhängig vom Radius hinzugefügt werden sollen", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> (b)", "label#description": ""}, "pasteFormula": "Formel einfügen", "pasteFormula#description": "", "pasteSize": "Größen einfügen", "pasteSize#description": "", "sync": "Alle Größen synchronisieren", "sync#description": "", "thresholds": "Größenschwellenwerte", "thresholds#description": "", "title": "Formeln", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "In den fortgeschrittenen Modus wechseln", "switchModeAdvanced#description": "", "switchModeBasic": "In den Basismodus wechseln", "switchModeBasic#description": "", "warnings": {"admin": "<PERSON>n Si<PERSON> diesen Almanach ändern, wird er mit allen aktuellen und zukünftigen Produktionseinheiten synchronisiert.", "admin#description": "", "carbon": "Dies ist ein von Carbon bereitgestellter Almanach. Nur der Feinabstimmungsindex kann geändert werden.", "carbon#description": "", "production": "Dieser Almanach läuft aktiv auf einem Roboter. Eine Bearbeitung wird im Feld sofort wirksam.", "production#description": ""}, "weeds": {"new": "Neues Unkraut hinzufügen", "new#description": "", "none": "<PERSON><PERSON>", "none#description": "", "sync#description": ""}, "weedsSynced": "Alle Unkräuter", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} gespeichert. Operator App Quick Tune aktivieren", "savedLong#description": "", "testResults": "Vorschau-Ergebnisse", "testResults#description": ""}, "filters": {"capturedAt": "Aufgenommen am", "capturedAt#description": "", "diameter": "Durchmesser", "diameter#description": "", "filters#description": "", "unappliedFilters": "", "unappliedFilters#description": ""}, "images": {"allImages": "Alle Bilder", "allImages#description": "", "categorized": "<PERSON><PERSON><PERSON><PERSON>", "categorized#description": "", "scrollToTop": "Zurück nach oben", "scrollToTop#description": "", "sortBy": {"latest": "Neueste", "latest#description": ""}, "sortedBy": "Sortiert nach: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Fehler beim Abrufen des Status", "error#description": "", "ready": "Die Ergebnisse sind fertig", "ready#description": "", "session#description": "", "session_one": "Sit<PERSON>ng", "session_other": "Sitzungen", "showResults": "Ergebnisse zeigen", "showResults#description": "", "status": "verarbeitet: {{processed}} / {{total}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Änderungen an diesem Pflanzenprofil werden mit allen aktuellen und künftigen Produktionseinheiten synchronisiert.", "admin#description": "", "adminMeta": "Alle Admin-Profile werden für alle Kunden sichtbar sein. Schaffen Sie keine Unordnung!", "adminMeta#description": "", "production": "Dieses Pflanzenprofil läuft aktiv auf einem Roboter. Der Bediener wird über Updates informiert und kann die neuen Änderungen übernehmen.", "production#description": "", "protected": "Dies ist ein von Carbon erstelltes Profil. Es kann nichts geändert werden.", "protected#description": "", "unsavedChanges": "Änderungen nicht gespeichert. Drücken Sie „Speichern”, um die Änderungen zu übernehmen.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_one": "Geänderter Schlüssel", "changedKey_other": "Geänderte Schlüssel", "newKey": "Neuer {{key}}-Name", "newKey#description": "", "stringReqs": "<PERSON>f nur a-z, 0-9, . und _ enthalten", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "<PERSON>ser Schlüssel wurde zusätzlich zum Standardschlüssel hinzugefügt.", "description#description": ""}, "keyMissing": {"description": "Fehlende Standardwerte: {{keys}}", "description#description": ""}, "valueChanged": {"description": "Dieser Wert wurde gegenüber dem Standardwert ({{default}}) geändert.", "description#description": "", "title": "Konfiguration geändert", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Der Kundeneditor konnte nicht geladen werden", "load#description": ""}}, "CustomerSelector": {"empty": "<PERSON>cht zu<PERSON>", "empty#description": "", "title": "Kunde ändern", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON>", "description#description": "", "label": "Schießen", "label#description": ""}, "copy": "Konfig. kopieren", "copy#description": "", "ignorable": {"description": "<PERSON><PERSON> s<PERSON>, wenn es die Zeit erlaubt, nicht in der Geschwindigkeitsempfehlung berücksichtigt", "description#description": "", "label": "Ignorierbar", "label#description": ""}, "paste": "Konfig. einfügen", "paste#description": ""}, "warnings": {"production": "Dieser Diskriminator ist bereits auf einem Roboter aktiv. Wenn es bearbeitet wird, wirkt sich dies sofort auf das Feld aus.", "production#description": ""}}, "drawer": {"customerMode": "Kundenspezifischer Modus", "customerMode#description": "", "error": "Navigation konnte nicht geladen werden", "error#description": ""}, "filters": {"NumericalRange": {"max": "Max. ({{units}})", "max#description": "", "min": "Mind. ({{units}})", "min#description": ""}, "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": ""}, "header": {"failed": "Header kon<PERSON> nicht geladen werden", "failed#description": "", "mascot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Carbon Robotics", "mascot#description": "", "search": {"failed": "<PERSON>e konnte nicht geladen werden", "failed#description": "", "focus": "<PERSON><PERSON> foku<PERSON>", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Größe", "label#description": "", "larger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "larger#description": "", "smaller": "kleiner", "smaller#description": ""}}, "map": {"bounds": {"reset": "<PERSON><PERSON><PERSON>", "reset#description": ""}, "errors": {"empty": "<PERSON><PERSON> vorhanden", "empty#description": "", "failed": "Karte konnte nicht geladen werden", "failed#description": ""}, "filters": {"customer_office": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customer_office#description": "", "hq": "Zentrale Carbon", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "Postfach", "po_box#description": "", "shop": "Werkstatt", "shop#description": "", "storage": "Lager", "storage#description": "", "support_base": "Support-Standort", "support_base#description": ""}, "fullscreen": "Vollbild", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "<PERSON><PERSON> Layer-Legende", "legend#description": "", "notThinning": "KEINE AUSDÜNNUNG", "notThinning#description": "", "notWeeding": "KEIN JÄTEN", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "HEATMAP-FEHLER", "unknown#description": ""}, "fields": {"block": "Standort: {{block}}", "block#description": "", "location": "Standort: {{latitude}}, {{longitude}}", "location#description": "", "size": "Größe: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Layer", "name#description": "", "rangeType#description": "", "relative": "Entsprechenden Farbbereich verwenden", "relative#description": "", "relativeRange#description": ""}, "map": "<PERSON><PERSON>", "map#description": "", "measure": {"name": "Messen", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "Aus welcher Kategorie kopieren?", "copyFromWhich#description": "", "splitCrops": "Nutzpflanzen aufteilen", "splitCrops#description": "", "splitWeeds": "Unkraut aufteilen", "splitWeeds#description": "", "syncCrops": "Alle Nutzpflanzen synchronisieren", "syncCrops#description": "", "syncWeeds": "Alle Unkräuter synchronisieren", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Schwellenwert für die Zuverlässigkeit der Prognose zur Verwendung einer Erkennung in der dynamischen Bänderung", "description#description": "", "label": "Bänderungs-Schwellenwert", "label#description": ""}, "minDoo": {"description": "Minimale Erkennung über Opportunität (DOO)", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Schwellenwert für die Zuverlässigkeit der Prognose zur Verwendung einer Erkennung in der Ausdünnung", "description#description": "", "label": "Ausdünnungs-Schwellenwert", "label#description": ""}, "weed": {"description": "Schwellenwert für die Zuverlässigkeit der Prognose zur Verwendung einer Erkennung für den umgekehrten Schutz von Nutzpflanzen", "description#description": "", "label": "Schwellenwert für den umgekehrten Schutz von Nutzpf<PERSON>zen", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Schwellenwert für die Zuverlässigkeit der Prognose zur Verwendung einer Erkennung für den Schutz von Nutzpflanzen", "description#description": "", "label": "Schwellenwert für den Schutz von Nutzpflanzen", "label#description": ""}, "weed": {"description": "Schwellenwert für die Zuverlässigkeit, um ein Unkraut zu berücksichtigen", "description#description": "", "label": "Jät-Schwellenwert", "label#description": ""}}}, "errors": {"sync": "Die Einstellungen für dieses Modell wurden noch nicht mit dem LaserWeeder synchronisiert. Bitte warten Sie auf die Synchronisierung, um die Einstellungen anzuzeigen und zu aktualisieren.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "Größen aufteilen", "splitSizesLong#description": "", "splitSizesShort": "Aufteilen", "splitSizesShort#description": "", "syncSizesLong": "Größen synchronisieren", "syncSizesLong#description": "", "syncSizesShort": "Synchroni<PERSON><PERSON>", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Warnung:{{stopEmphasis}} Diese Einstellungen enthalten nicht gespeicherte Änderungen, die nicht auf den Roboter übertragen werden.", "exportingUnsavedChanges#description": "", "production": "Dieses Modell läuft aktiv auf einem Roboter. Wenn es bearbeitet wird, wirkt sich dies sofort auf das Feld aus.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Unbekannte Alarme", "unknown#description": ""}, "almanac": {"unknown": "Unbekannter Almanach", "unknown#description": "", "withName": "Almanach: {{name}}", "withName#description": ""}, "autofixing": "Selbstreparatur-Fehler", "autofixing#description": "", "banding": {"disabled": "Bänderung Deaktiviert", "disabled#description": "", "enabled": "Bänderung Aktiviert", "enabled#description": "", "none": "<PERSON><PERSON>", "none#description": "", "static": "(STATISCH)", "static#description": "", "withName": "Bänderung: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Check-in-Status konnte nicht geladen werden", "failed#description": "", "never": "<PERSON><PERSON> an<PERSON>", "never#description": "", "withTime": "Angemeldet {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Nutzpflanzen Aktiviert ({{pinned}} angeheftet)", "summary#description": ""}, "delivery": "Lieferung", "delivery#description": "", "disconnected": "Abgeschaltet", "disconnected#description": "", "discriminator": {"unknown": "Unbekannter Diskriminator", "unknown#description": "", "withName": "Diskriminator: {{name}}", "withName#description": ""}, "failed": "Roboter-Status konnte nicht geladen werden", "failed#description": "", "failedShort": "Fehlgeschlagen", "failedShort#description": "", "implementation": "Implementierung", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "<PERSON><PERSON>", "inventory#description": "", "job": {"none": "<PERSON><PERSON>", "none#description": "", "withName": "Aufgabe: {{name}}", "withName#description": ""}, "lasers": "Laser Online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Lebensdauer:", "lifetime#description": "", "lifted": "Bereit (Angehoben)", "lifted#description": "", "loading": "Wird geladen", "loading#description": "", "location": {"known": "Standort: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Standort Unbekannt", "unknown#description": ""}, "manufacturing": "Fertigung", "manufacturing#description": "", "model": {"withName": "Modell: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "<PERSON>l wird geladen", "modelLoading#description": "", "notArmed": "Nicht aktiviert", "notArmed#description": "", "off_season": "Außerhalb der Saison", "off_season#description": "", "offline": "Offline während {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P Unbekannt", "unknown#description": ""}, "poweringDown": "Schaltet Aus", "poweringDown#description": "", "poweringUp": "Schaltet Ein", "poweringUp#description": "", "pre_manufacturing": "Vorfertigung", "pre_manufacturing#description": "", "stale": "<PERSON><PERSON><PERSON>", "stale#description": "", "staleDescription": "Letzter bekannter Wert. Roboter ist offline.", "staleDescription#description": "", "standby": "Bereit", "standby#description": "", "thinning": {"disabled": "Ausdünnung Deaktiviert", "disabled#description": "", "enabled": "Ausdünnung Aktiviert", "enabled#description": "", "none": "<PERSON><PERSON>", "none#description": "", "withName": "Ausdünnung: {{name}}", "withName#description": ""}, "today": {"none": "<PERSON><PERSON> kein Jäten", "none#description": ""}, "unknown": "Status Unbekannt", "unknown#description": "", "updating": "Update wird installiert", "updating#description": "", "version": {"values": {"unknown": "Unbekannte Version", "unknown#description": "", "updateDownloading": "({{version}} wird her<PERSON><PERSON><PERSON><PERSON>n", "updateDownloading#description": "", "updateReady": "({{version}} bereit)", "updateReady#description": ""}}, "weeding": "Unkrautbeseitigung  {{crop}}", "weeding#description": "", "weedingDisabled": "Unkrautbeseitigung deaktiviert", "weedingDisabled#description": "", "weedingThinning": "Unkrautbeseitigung und Ausdünnung {{crop}}", "weedingThinning#description": "", "winterized": "Überwinterung", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Ist bereits vorhanden", "exists#description": "", "unknownClass": "Unbekannte Roboterklasse", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "<PERSON><PERSON> neue Konfig erstellen", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "{{class}} Vorlage", "templateForClass#description": "", "templateGeneric": "Roboter-Vor<PERSON>", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Sie sollten nur fortfahren, wenn bereits eine Konfig für {{serial}} vorhanden ist oder wenn Sie planen, diese manuell zu erstellen", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Erweiterte Tachometereinstellungen", "advancedFormulaTitle#description": "", "formulaTitle": "Formel", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Verringern Sie die vorgeschlagene Geschwindigkeit automatisch um den von Ihnen eingegebenen Wert. Beispielsweise verringert eine Eingabe von 5 % die empfohlene Geschwindigkeit von 1 km/h auf 0,95 km/h", "description#description": "", "label": "Geschwindigkeitseinstellung", "label#description": ""}, "decreaseSmoothing": {"description": "Passen Sie die Geschwindigkeit an, mit der die Geschwindigkeit abnimmt. <PERSON>öher der Wert, desto wahrscheinlicher ist es, dass der Tachometer schwankt", "description#description": "", "label": "Glättung der Verlangsamung", "label#description": ""}, "increaseSmoothing": {"description": "Passen Sie die Geschwindigkeit an, mit der die Geschwindigkeit zunimmt. <PERSON> höher der Wert, desto wahrscheinlicher ist es, dass der Tachometer schwankt", "description#description": "", "label": "Glättung der Beschleunigung", "label#description": ""}, "maxVelMph": {"description": "Geben Sie die absolut höchste Geschwindigkeit ein, die Si<PERSON> fahren möchten. Geschwindigkeitsempfehlungen werden diesen Wert nicht überschreiten", "description#description": "", "label": "Maximale Geschwindigkeit", "label#description": ""}, "minVelMph": {"description": "Geben Sie die absolut niedrigste Geschwindigkeit ein, die Si<PERSON> fahren möchten. Geschwindigkeitsempfehlungen werden diesen Wert nicht unterschreiten", "description#description": "", "label": "Mindestgeschwindigkeit", "label#description": ""}, "primaryKillRate": {"description": "Dieser Wert ist Ihr gewünschter Prozentsatz an abgetötetem Unkraut", "description#description": "", "label": "Ideale Abtötungsrate", "label#description": ""}, "primaryRange": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> diesen Wert, wenn Sie unabhängig von der Auswirkung auf die Geschwindigkeit Ihre ideale Tötungsrate erreichen möchten", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "label#description": ""}, "rows": {"allRows": "<PERSON><PERSON> Reihen", "allRows#description": "", "row1": "Reihe 1", "row1#description": "", "row2": "Reihe 2", "row2#description": "", "row3": "Reihe 3", "row3#description": ""}, "secondaryKillRate": {"description": "Dieser Wert ist der niedrigste akzeptable Prozentsatz an abgetötetem Unkraut", "description#description": "", "label": "Minimale Abtötungsrate", "label#description": ""}, "secondaryRange": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> diesen <PERSON>rt, wenn Sie mehr Spielraum haben möchten, bevor Sie eine Benachrichtigung über eine niedrige Geschwindigkeit erhalten", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "sync": "Alle Reihen synchronisieren", "sync#description": "", "warnings": {"admin": "<PERSON>n Si<PERSON> diese Geschwindigkeitsschätzung ändern, wird er mit allen aktuellen und zukünftigen Produktionseinheiten synchronisiert.", "admin#description": "", "production": "Diese Geschwindigkeitsschätzung läuft aktiv auf einem Roboter. Eine Bearbeitung wird im Feld sofort wirksam.", "production#description": "", "protected": "Hierbei handelt es sich um ein von Carbon gemachtes Profil. Nichts kann verändert werden.", "protected#description": "", "unsavedChanges": "Nicht gespeicherte Änderungen. Drücken Sie Speichern, um die Änderungen zu übernehmen", "unsavedChanges#description": ""}}, "slider": {"gradual": "<PERSON><PERSON><PERSON>", "gradual#description": "", "immediate": "Sofort", "immediate#description": ""}, "visualization": {"targetSpeed": "Sollgeschwindigkeit", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_one": "Alarm", "alarm_other": "Alarme", "fields": {"code": "Code", "code#description": "", "description": "Beschreibung", "description#description": "", "duration": {"name": "<PERSON><PERSON>", "name#description": "", "values": {"ongoing": "AKTIV", "ongoing#description": ""}}, "identifier": "<PERSON><PERSON><PERSON>", "identifier#description": "", "impact": {"name": "Auswirkung", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Stuf<PERSON>", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "<PERSON> angefangen", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_one": "Almanach", "almanac_other": "Almanache", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_one": "Auftrag", "assignment_other": "Aufträge", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Anweisungen", "instructions#description": ""}, "intervention#description": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"laserWeed": "Läser-Jäten", "laserWeed#description": "", "unrecognized": "unbekannter Typ ({{value}})", "unrecognized#description": ""}, "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Läser-Jäten-Reihe", "laserWeedRow#description": ""}, "objective_one": "<PERSON><PERSON>", "objective_other": "<PERSON><PERSON>", "states": {"acknowledged": "<PERSON><PERSON><PERSON><PERSON>", "acknowledged#description": "", "cancelled": "abgebrochen", "cancelled#description": "", "completed": "abgeschlossen", "completed#description": "", "failed": "fehlgeschlagen", "failed#description": "", "inProgress": "läuft", "inProgress#description": "", "new": "neu", "new#description": "", "paused": "p<PERSON><PERSON><PERSON>", "paused#description": "", "pending": "offen", "pending#description": "", "ready": "bereit", "ready#description": "", "unrecognized": "unbekannter Status ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Aufgabe #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_one": "Aufgabe", "task_other": "Aufgaben"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_one": "Pflanzenprofil", "categoryCollectionProfile_other": "Pflanzenprofile", "fields": {"categories": {"disregard": "Ignorieren", "disregard#description": "", "name": "<PERSON><PERSON><PERSON>", "name#description": "", "requiredBaseCategories": "<PERSON>e müssen genau diese Kategorien haben: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "<PERSON>ktual<PERSON><PERSON>", "updatedAt#description": ""}, "metadata": {"capturedAt": "Aufgenommen", "capturedAt#description": "", "categoryId": "Kategorien-ID", "categoryId#description": "", "imageId": "Bild-ID", "imageId#description": "", "pointId": "Punkt-<PERSON>", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "radius": "<PERSON><PERSON>", "radius#description": "", "updatedAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_one": "Konfig", "config_other": "Konfigs", "key#description": "", "key_one": "Schlüssel (1)", "key_other": "<PERSON><PERSON><PERSON><PERSON> (mehrere)", "template#description": "", "template_one": "Konfig.-Vorlage", "template_other": "Konfig.-Vorlagen", "value#description": "", "value_one": "Wert", "value_other": "<PERSON><PERSON>"}, "crops": {"categories": {"unknown": "Unbekannte Nutzpflanze", "unknown#description": ""}, "crop#description": "", "crop_one": "Nutzpflanze", "crop_other": "Nutzpflanzen", "fields": {"confidence": {"fields": {"regionalImages": "Regionale Bilder:", "regionalImages#description": "", "totalImages": "Bilder insgesamt:", "totalImages#description": ""}, "name": "Sicherheit", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "<PERSON><PERSON><PERSON><PERSON>", "archived#description": "", "unknown": "Sicherheit unbekannt", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "<PERSON><PERSON><PERSON><PERSON>", "notes#description": "", "pinned": "Angeheftet", "pinned#description": "", "recommended": "Empfehlenswert", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_one": "Kunde", "customer_other": "<PERSON><PERSON>", "fields": {"emails": {"errors": {"formatting": "Pro Zeile eine E-Mail-Adresse", "formatting#description": ""}, "name": "E-Mail-Adressen", "name#description": ""}, "featureFlags": {"almanac": {"description": "Aktiviert die Tabs „Almanach“ und „Diskriminator“ für Roboter (Roboter muss auch Almanach und Diskriminator unterstützen)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Aktiviert den Pflanzenprofil-Tab für Roboter", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Funktionsflaggen ermöglichen die Beta-Funktionalität für alle Benutzer eines Kunden", "description#description": "", "explore": {"description": "Erkundungsmodus für Karten und Grafiken in Berichten aktiviert", "description#description": "", "name": "Erkundungsmodus", "name#description": ""}, "jobs": {"description": "Aktiv<PERSON><PERSON> Aufgaben (Roboter muss auch Aufgaben unterstützen)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "<PERSON><PERSON>gt ein neues visuelles Design für Robotermetriken an", "description#description": "", "name": "Neues Metrik-Design", "name#description": ""}, "name": "Funktionsflaggen", "name#description": "", "off": "AUS", "off#description": "", "on": "EIN", "on#description": "", "reports": {"description": "Aktiviert den Tab „Berichte“ und die darin enthaltenen Funktionen", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Geodaten anzeigen, einschließlich räumlicher Kennzahlen und Diagrammen", "description#description": "", "name": "Geodaten", "name#description": ""}, "summary": "{{enabled}}/{{total}} Funktionsflaggen aktiviert", "summary#description": "", "unvalidatedMetrics": {"description": "Beta-Kennzahlen bis zur Feldvalidierung in zertifizierten Metriken anzeigen", "description#description": "", "name": "Beta-<PERSON><PERSON><PERSON><PERSON>", "name#description": ""}, "velocityEstimator": {"description": "Ermöglicht das Anzeigen und Bearbeiten von Zielgeschwindigkeitsschätzungsprofilen (Roboter muss auch Zielgeschwindigkeitsschätzung unterstützen)", "description#description": "", "name": "Zielgeschwindigkeitsschätzung", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Läuft am", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "<PERSON>n diese Option aktiviert ist, werden Berichte wöchentlich mit den folgenden Einstellungen für alle aktiven Roboter ausgeführt", "description#description": "", "name": "Wöchentliche Berichte", "name#description": ""}, "weeklyReportHour": "Läuft um", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Zurückschauen", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Läuft in (Zeitzone)", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_one": "Diskriminator", "discriminator_other": "Diskriminatoren", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_one": "<PERSON><PERSON>", "farm_other": "<PERSON><PERSON><PERSON>", "point#description": "", "point_one": "<PERSON><PERSON>", "point_other": "Punkte", "zone#description": "", "zone_one": "<PERSON><PERSON><PERSON>", "zone_other": "Bereiche"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_one": "Feld-Definition", "fieldDefinition_other": "Feld-Definitionen", "fields": {"boundary": "Feldbegrenzung", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Pflanzrichtung", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_one": "Gesamtwert", "global_other": "Gesamtwerte", "values": {"plantProfileModelId": {"description": "Basismodell, das alle Kunden- und Admin-Profile für '$t(components.categoryCollectionProfile.actions.testResults)' verwenden", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) Modell-ID", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "Datum / Uhrzeit", "capturedAt#description": "", "geoJson": "<PERSON><PERSON>", "geoJson#description": "", "url": "Foto öffnen", "url#description": ""}, "image#description": "", "image_one": "Foto", "image_other": "Fotos"}, "jobs": {"job#description": "", "job_one": "Aufgabe", "job_other": "Aufgaben"}, "lasers": {"fields": {"cameraId": "Kamera ID", "cameraId#description": "", "error": {"values": {"false": "Nominal", "false#description": ""}}, "installedAt": "Installiert", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "<PERSON>nn<PERSON>mer unbekannt", "unknown#description": ""}}, "lifetimeSec": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifetimeSec#description": "", "powerLevel": "Leistungsstufe", "powerLevel#description": "", "removedAt": "Entfernt", "removedAt#description": "", "rowNumber": "<PERSON><PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "Anzahl der Schüsse", "totalFireCount#description": "", "totalFireTimeMs": "Dauer der Schüsse", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "Abgelaufen", "expired#description": "", "hours": "Stunden: {{installed}}/{{total}} ({{percent}} remaining)", "hours#description": "", "hoursUnknown": "Stunden: Unbekannt", "hoursUnknown#description": "", "months": "Monate: {{installed}}/{{total}} ({{percent}} remaining)", "months#description": "", "monthsUnknown": "Monate: <PERSON><PERSON><PERSON><PERSON>", "monthsUnknown#description": "", "unknown": "<PERSON><PERSON><PERSON> unbekannt", "unknown#description": ""}}}, "laser#description": "", "laser_one": "Laser", "laser_other": "Laser"}, "models": {"model#description": "", "model_one": "<PERSON><PERSON>", "model_other": "<PERSON><PERSON>", "none": "<PERSON><PERSON>", "none#description": "", "p2p#description": "", "p2p_one": "P2P-Modell", "p2p_other": "P2P-Modelle", "unknown": "<PERSON><PERSON> unbekannt", "unknown#description": ""}, "reportInstances": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON> von", "authorId#description": "", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_one": "Berichtserstellung", "run_other": "Berichtserstellungen"}, "reports": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Automatisch", "name#description": "", "values": {"weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_one": "Bericht", "report_other": "Berichte"}, "robots": {"classes": {"buds#description": "", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "", "moduleValidationStations_one": "Modulbestätigungsstation", "moduleValidationStations_other": "Modulbestätigungsstationen", "reapersCarbon#description": "", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reapers", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_one": "Traktor", "rtcs_other": "Traktoren", "simulators#description": "", "simulators_one": "Simulator", "simulators_other": "Simulatoren", "slayersCarbon#description": "", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "<PERSON><PERSON><PERSON> unbekannt", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Laser Offline", "lasersOffline#description": "", "lifetimeArea": "Lebensdauer Bereich", "lifetimeArea#description": "", "lifetimeTime": "Lebensdauer Zeit", "lifetimeTime#description": "", "localTime": "Ortszeit", "localTime#description": "", "reportedAt": "Letzte Aktualisierung", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Software-Version", "softwareVersion#description": "", "supportSlack": "Support Slack-Kanal", "supportSlack#description": "", "targetVersion": "Ziel-Version", "targetVersion#description": ""}, "robot#description": "", "robot_one": "<PERSON><PERSON>", "robot_other": "<PERSON><PERSON>", "unknown": "<PERSON><PERSON> unbekannt", "unknown#description": ""}, "users": {"activated": "Aktiviert", "activated#description": "", "fields": {"email": "E-Mail-Adresse", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Aktivierung", "name#description": "", "values": {"false": "ANSTEHEND", "false#description": ""}}}, "operator#description": "", "operator_one": "<PERSON><PERSON><PERSON>", "operator_other": "<PERSON><PERSON><PERSON>", "role#description": "", "role_one": "Funktion", "role_other": "Funktionen", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Technisch)", "carbon_tech#description": "", "farm_manager": "Hofmanager", "farm_manager#description": "", "operator_advanced": "Bediener (erweitert)", "operator_advanced#description": "", "operator_basic": "<PERSON><PERSON><PERSON>", "operator_basic#description": "", "robot_role": "<PERSON><PERSON>", "robot_role#description": "", "unknown_role": "Unbekannte Funktion", "unknown_role#description": ""}, "staff": "<PERSON><PERSON><PERSON><PERSON>", "staff#description": "", "user#description": "", "user_one": "<PERSON><PERSON><PERSON>", "user_other": "<PERSON><PERSON><PERSON>"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_one": "Geschwindigkeitsschätzer", "velocityEstimator_other": "Geschwindigkeitsschätzer"}, "weeds": {"categories": {"blossom": "Blüte", "blossom#description": "", "broadleaf": "Breitwegerich", "broadleaf#description": "", "fruit": "Obst", "fruit#description": "", "grass": "Gras", "grass#description": "", "offshoot": "<PERSON><PERSON><PERSON>", "offshoot#description": "", "preblossom": "Vorblüte", "preblossom#description": "", "purslane": "Portulak", "purslane#description": "", "runner": "Ausläufer", "runner#description": "", "unknown": "<PERSON>kra<PERSON> unbekannt", "unknown#description": ""}, "weed#description": "", "weed_one": "Unkra<PERSON>", "weed_other": "Unkr<PERSON><PERSON>"}}, "utils": {"actions": {"add": "Hinzufügen", "add#description": "", "addLong": "{{subject}} hi<PERSON><PERSON><PERSON>gen", "addLong#description": "", "apply": "Übernehmen", "apply#description": "", "applyLong": "{{subject}} ü<PERSON><PERSON><PERSON>en", "applyLong#description": "", "backLong": "<PERSON><PERSON> zu {{subject}}", "backLong#description": "", "cancel": "Stornieren", "cancel#description": "", "cancelLong": "{{subject}} abbrechen", "cancelLong#description": "", "clear": "Löschen", "clear#description": "", "confirm": "Bestätigen", "confirm#description": "", "continue": "Fortfahren", "continue#description": "", "copy": "<PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "{{subject}} kopieren", "copyLong#description": "", "create": "<PERSON><PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} erstellt", "createdLong#description": "", "delete": "Löschen", "delete#description": "", "deleteLong": "{{subject}} löschen", "deleteLong#description": "", "deletedLong": "{{subject}} <PERSON><PERSON><PERSON><PERSON>", "deletedLong#description": "", "disableLong": "{{subject}} deaktivieren", "disableLong#description": "", "discard": "Verwerfen", "discard#description": "", "edit": "<PERSON><PERSON><PERSON>", "edit#description": "", "editLong": "{{subject}} bearbeiten", "editLong#description": "", "enableLong": "{{subject}} aktivieren", "enableLong#description": "", "exit": "Schließen", "exit#description": "", "exitLong": "{{subject}} sch<PERSON>ßen", "exitLong#description": "", "goToLong": "Weiter nach {{subject}}", "goToLong#description": "", "invite": "Einladen", "invite#description": "", "inviteLong": "{{subject}} ein<PERSON>n", "inviteLong#description": "", "invitedLong": "{{subject}} eingeladen", "invitedLong#description": "", "leaveUnchanged": "<PERSON><PERSON><PERSON><PERSON><PERSON> lassen", "leaveUnchanged#description": "", "new": "<PERSON>eu", "new#description": "", "newLong": "{{subject}} neu", "newLong#description": "", "next": "<PERSON><PERSON>", "next#description": "", "pause": "Pause", "pause#description": "", "play": "Abspielen", "play#description": "", "previous": "Zurück", "previous#description": "", "ranLong": "{{subject}} ausgeführt", "ranLong#description": "", "reload": "<PERSON><PERSON><PERSON> laden", "reload#description": "", "resetLong": "{{subject}} <PERSON><PERSON><PERSON><PERSON>", "resetLong#description": "", "retry": "<PERSON><PERSON><PERSON> versuchen", "retry#description": "", "run": "Ausführen", "run#description": "", "runLong": "{{subject}} ausführen", "runLong#description": "", "save": "Speichern", "save#description": "", "saveLong": "{{subject}} spe<PERSON>rn", "saveLong#description": "", "saved": "Gespe<PERSON>rt", "saved#description": "", "savedLong": "{{subject}} g<PERSON><PERSON><PERSON><PERSON>", "savedLong#description": "", "search": "<PERSON><PERSON>", "search#description": "", "searchLong": "{{subject}} suchen", "searchLong#description": "", "selectAll": "Alle auswählen", "selectAll#description": "", "selectLong": "{{subject}} ausw<PERSON><PERSON>en", "selectLong#description": "", "selectNone": "<PERSON><PERSON> au<PERSON>", "selectNone#description": "", "send": "Senden", "send#description": "", "showLong": "{{subject}} anzeigen", "showLong#description": "", "submit": "Unterbreiten", "submit#description": "", "toggle": "Umschalten", "toggle#description": "", "toggleLong": "{{subject}} umschalten", "toggleLong#description": "", "update": "Aktualisieren", "update#description": "", "updated": "<PERSON>ktual<PERSON><PERSON>", "updated#description": "", "updatedLong": "{{subject}} aktualisiert", "updatedLong#description": "", "uploaded": "Hochgeladen", "uploaded#description": "", "viewLong": "{{subject}} an<PERSON><PERSON>", "viewLong#description": ""}, "descriptors": {"active": "Aktiv", "active#description": "", "critical": "<PERSON><PERSON><PERSON>", "critical#description": "", "default": "Standard", "default#description": "", "degraded": "<PERSON><PERSON><PERSON><PERSON>", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "Deaktiviert", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Aktiviert", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "<PERSON><PERSON>", "error#description": "", "estopOff": "In Betrieb", "estopOff#description": "", "estopOn": "Notausgeschaltet", "estopOn#description": "", "fast": "<PERSON><PERSON><PERSON>", "fast#description": "", "few": "Wenige", "few#description": "", "good": "Gut", "good#description": "", "hidden": "Ausgeblendet", "hidden#description": "", "high": "Hoch", "high#description": "", "id": "ID", "id#description": "", "inactive": "Nicht aktiv", "inactive#description": "", "interlockSafe": "Schießen Erlaubt", "interlockSafe#description": "", "interlockUnsafe": "Schießen verhindert", "interlockUnsafe#description": "", "large": "Hoch", "large#description": "", "laserKeyOff": "<PERSON><PERSON><PERSON><PERSON>", "laserKeyOff#description": "", "laserKeyOn": "Eingeschaltet", "laserKeyOn#description": "", "liftedOff": "Abgesenkt", "liftedOff#description": "", "liftedOn": "Angehoben", "liftedOn#description": "", "loading": "Wird geladen", "loading#description": "", "low": "<PERSON><PERSON><PERSON>", "low#description": "", "majority": "Mehrhe<PERSON>", "majority#description": "", "medium": "<PERSON><PERSON>lwer<PERSON>", "medium#description": "", "minority": "Minderheit", "minority#description": "", "name": "Name", "name#description": "", "no": "<PERSON><PERSON>", "no#description": "", "none": "<PERSON><PERSON>", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "poor": "Mangelhaft", "poor#description": "", "progress": "Fort<PERSON><PERSON>t", "progress#description": "", "serial": "Seriennummer", "serial#description": "", "slow": "Langsam", "slow#description": "", "small": "<PERSON>", "small#description": "", "sparse": "<PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_one": "<PERSON><PERSON>", "type_other": "Typen", "unknown": "Unbekannt", "unknown#description": "", "waterProtectNormal": "Normale Feuchtigkeit", "waterProtectNormal#description": "", "waterProtectTriggered": "Wasser festgestellt", "waterProtectTriggered#description": "", "yes": "<PERSON>a", "yes#description": ""}, "form": {"booleanType": "Muss ein boolescher Wert sein", "booleanType#description": "", "copyConfigFrom": "Konfig. kopieren von ...", "copyConfigFrom#description": "", "integerType": "Muss eine ganze <PERSON> sein", "integerType#description": "", "maxLessThanMin": "<PERSON><PERSON> muss größer als Mind. sein", "maxLessThanMin#description": "", "maxSize": "Darf {{limit}} Zeichen nicht überschreiten", "maxSize#description": "", "minGreaterThanMax": "Mind. muss größer als max. sein", "minGreaterThanMax#description": "", "moveDown": "Nach unten verschieben", "moveDown#description": "", "moveUp": "Nach oben verschieben", "moveUp#description": "", "noOptions": "<PERSON><PERSON>en", "noOptions#description": "", "numberType": "Muss eine <PERSON>ahl sein", "numberType#description": "", "optional": "(optional)", "optional#description": "", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required#description": "", "stringType": "Muss eine Folge sein", "stringType#description": ""}, "lists": {"+3": "{{b}}, und {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} und {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "<PERSON><PERSON><PERSON> laden", "loadMore#description": "", "noMoreResults": "<PERSON><PERSON> weiteren Ergebnisse", "noMoreResults#description": "", "noResults": "<PERSON><PERSON>", "noResults#description": ""}, "metrics": {"aggregates": {"max": "<PERSON>.", "max#description": "", "min": "<PERSON>.", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Durchschnittlicher Nutzpflanzen-Radius", "avgCropSizeMm#description": "", "avgSpeedMph": "Durchschnittliche Fahrgeschwindigkeit", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Durchschnittliche Schussdauer", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Durchschnittliche Schussdauer (ungezielt)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Durchschnittlicher Unkraut-Radius", "avgWeedSizeMm#description": "", "bandingConfigName": "Konfig. Bänderung", "bandingConfigName#description": "", "bandingEnabled": "Bänderung", "bandingEnabled#description": "", "bandingPercentage": "Prozent Bänderung", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Durchschnittliche Abdeckungsgeschwindigkeit", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Nutzpflanzen-Dichte", "cropDensitySqFt#description": "", "distanceWeededMeters": "Entfernung Unkrautvernichtung", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Erhaltene Nutzpflanzen", "keptCrops#description": "", "killedWeeds": "Vernichtete Unkräuter", "killedWeeds#description": "", "missedCrops": "Verfehlte Nutzpflanzen", "missedCrops#description": "", "missedWeeds": "Verfehlte Unkräuter", "missedWeeds#description": "", "notThinning": "Nicht ausgedünnte Nutzpflanzen", "notThinning#description": "", "notWeeding": "Nicht gejätete Unkräuter", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Effektivität des Betreibers", "operatorEffectiveness#description": "", "overallEfficiency": "Gesamtleistung", "overallEfficiency#description": "", "skippedCrops": "Ignorierte Nutzpflanzen", "skippedCrops#description": "", "skippedWeeds": "Ignorierte Unkräuter", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Zieldauer Unkrautbeseitigung", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Ausgedünnte Nutzpflanzen", "thinnedCrops#description": "", "thinningEfficiency": "Ausdünnungsleistung", "thinningEfficiency#description": "", "timeEfficiency": "Effektive Betriebsleistung", "timeEfficiency#description": "", "totalCrops": "Gefundene Nutzpflanzen", "totalCrops#description": "", "totalWeeds": "Gefundene Unkräuter", "totalWeeds#description": "", "totalWeedsInBand": "Gefundene Unkräuter (in Bänderung)", "totalWeedsInBand#description": "", "uptimeSeconds": "Maschinenlaufzeit", "uptimeSeconds#description": "", "validCrops": "Gefundene Nutzpflanzen", "validCrops#description": "", "weedDensitySqFt": "Unkraut-Dichte", "weedDensitySqFt#description": "", "weedingEfficiency": "Jätleistung", "weedingEfficiency#description": "", "weedingUptimeSeconds": "<PERSON><PERSON><PERSON><PERSON>", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Unkraut-Art: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Unkraut-Art: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Unkraut-Art: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Unkraut-Art: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Dies wird vor der Durchforstung berechnet, wenn Durchforstung aktiviert wurde", "avgCropSizeMm#description": "", "bandingConfigName": "Ihr zuletzt ausgewähltes Banding-Profil", "bandingConfigName#description": "", "crop": "Ihre zuletzt ausgewählte Pflanze", "crop#description": "", "cropDensitySqFt": "Dies wird vor der Durchforstung berechnet, wenn Durchforstung aktiviert wurde", "cropDensitySqFt#description": "", "keptCrops": "Die geschätzte Zahl der Pflanzen, die nach der Durchforstung erhalten bleiben", "keptCrops#description": "", "killedWeeds": "LaserWeeder hat ein Objekt als Unkraut identifiziert und beseitigt", "killedWeeds#description": "", "missedCrops": "Die Pflanze war zum <PERSON>rch<PERSON><PERSON> mark<PERSON>, wurde aber verfehlt. Häufige Gründe: Geschwindigkeitsüberschreitung, außer Reichweite oder Systemfehler.", "missedCrops#description": "", "missedWeeds": "Unkraut wurde identifiziert, aber verfehlt. Häufige Gründe: Geschwindigkeitsüberschreitung, außer Reichweite oder Systemfehler.", "missedWeeds#description": "", "operatorEffectiveness": "<PERSON><PERSON><PERSON>, wie gut das tatsächliche Fahrtempo mit dem von Velocity Estimator empfohlenen Zieltempo übereinstimmt", "operatorEffectiveness#description": "", "overallEfficiency": "(Jätleistung + Durchforstungsleistung) / 2, wenn <PERSON><PERSON> sowohl jäten als auch durchforsten", "overallEfficiency#description": "", "skippedCrops": "Die Pflanze wurde beim Durchforsten absichtlich ausgelassen. Häufige Gründe: in Quick Tune deaktiviert, außerhalb des Streifens oder Tropfband in der Nähe.", "skippedCrops#description": "", "skippedWeeds": "Das Unkraut wurde absichtlich ausgelassen. Häufige Gründe: in Quick Tune deaktiviert oder außerhalb der Reihe.", "skippedWeeds#description": "", "thinningEfficiency": "(durchforstete Pflanzen + erhaltene Pflanzen) / Geschätzte gefundene Pflanzen × 100 %", "thinningEfficiency#description": "", "timeEfficiency": "(aktive Arbeitszeit / Einschaltdauer) × 100 %", "timeEfficiency#description": "", "uptimeSeconds": "Die gesamte Zeit, während welcher der LaserWeeder eingeschaltet war. Inklusive der Zeit im Standby-Modus und/oder der Zeit, während der er gehoben wird.", "uptimeSeconds#description": "", "weedDensitySqFt": "Geschätztes gefundenes Unkraut (ingesamt) / Abdeckung", "weedDensitySqFt#description": "", "weedingEfficiency": "(vernichtetes Unkraut / im Streifen gefundenes Unkraut) × 100 %", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Die gesamte Zeit, während welcher der LaserWeeder aktiv Unkraut vernichtet oder durchforstet hat", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Geschwindigkeitseffizienz", "operatorEffectiveness#description": "", "timeEfficiency": "Maschinennutzung", "timeEfficiency#description": "", "totalWeeds": "Gefundenes Unkraut (insgesamt)", "totalWeedsInBand": "Gefundenes Unkraut (im Streifen)", "totalWeedsInBand#description": "", "uptimeSeconds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uptimeSeconds#description": "", "validCrops": "Geschätzte gefundene Pflanzen", "validCrops#description": "", "weedingUptimeSeconds": "Aktive Arbeitszeit", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coverage#description": "", "field": "<PERSON><PERSON>", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Le<PERSON><PERSON>", "performance#description": "", "speed": "Geschwindigkeit", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "<PERSON><PERSON>ung", "usage#description": ""}, "metric#description": "", "metric_one": "<PERSON><PERSON><PERSON>", "metric_other": "<PERSON><PERSON><PERSON><PERSON>", "spatial": {"heatmapWarning": "pro ~20×20ft 'Block'", "heatmapWarning#description": "", "metrics": {"altitude": "Höhenlage", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "Not-Aus", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Verriegelung", "interlock#description": "", "keptCropDensity": "Erhaltene Nutzpflanzen-Dichte", "keptCropDensity#description": "", "laserKey": "Laser-Sc<PERSON><PERSON><PERSON>", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Geschwindigkeits-Effizienz", "speed#description": "", "speedTargetMinimum": "Durchschnittliche Sollgeschwindigkeit (Minimum)", "speedTargetMinimum#description": "", "speedTargetRow1": "Durchschnittliche Sollgeschwindigkeit (Reihe 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Durchschnittliche Sollgeschwindigkeit (Reihe 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Durchschnittliche Sollgeschwindigkeit (Reihe 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Durchschnittliche Sollgeschwindigkeit", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Ausdünnung", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Zeit", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Wasserschutz", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "<PERSON><PERSON><PERSON>", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Ausgewählt", "selected#description": "", "showAll": "Alle {{objects}} zeigen", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "pro ha", "/ac#description": "", "/ft2": "pro ft²", "/ft2#description": "", "/ha": "pro ha", "/ha#description": "", "/in2": "pro cm²", "/in2#description": "", "/km2": "pro km²", "/km2#description": "", "/m2": "pro m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_one": "<PERSON>", "WLong_other": "<PERSON>", "ac": "ac", "ac#description": "", "ac/h": "ac/h", "ac/h#description": "", "acLong#description": "", "acLong_one": "<PERSON><PERSON><PERSON>", "acLong_other": "<PERSON><PERSON><PERSON>", "acres#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "d": "T", "d#description": "", "dLong#description": "", "dLong_one": "Tag", "dLong_other": "Tage", "day#description": "", "days#description": "", "ft": "ft", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_one": "<PERSON><PERSON>", "ftLong_other": "<PERSON><PERSON>", "h": "Std.", "h#description": "", "hLong#description": "", "hLong_one": "Stunde", "hLong_other": "Stunden", "ha": "ha", "ha#description": "", "ha/h": "ha/h", "ha/h#description": "", "haLong#description": "", "haLong_one": "<PERSON><PERSON><PERSON>", "haLong_other": "<PERSON><PERSON><PERSON>", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_one": "<PERSON>er", "mLong_other": "<PERSON>er", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "<PERSON>.", "min#description": "", "minLong#description": "", "minLong_one": "Minute", "minLong_other": "Minuten", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "Mo", "month#description": "", "monthLong#description": "", "monthLong_one": "<PERSON><PERSON>", "monthLong_other": "Monate", "mph": "mph", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_one": "Sekunden", "sLong_other": "Sekunde", "seconds#description": "", "watts#description": "", "week": "Wo", "week#description": "", "weekLong#description": "", "weekLong_one": "<PERSON><PERSON><PERSON>", "weekLong_other": "<PERSON><PERSON><PERSON>", "yd#description": "", "year": "J", "year#description": "", "yearLong#description": "", "yearLong_one": "<PERSON><PERSON><PERSON>", "yearLong_other": "Jahre"}}, "views": {"admin": {"alarms": {"allowWarning": "Durch das Hinzufügen von Codes zur Positivliste können Alarme an die unterstützenden Slack-Kanäle per Ping gesendet werden.", "allowWarning#description": "", "blockWarning": "Das Hinzufügen von Codes zur Blockliste verhindert, dass Alarme Slack-Kanäle anpingen.", "blockWarning#description": "", "lists": "Listen", "lists#description": "", "title": "Universelle Alarm-Positivliste", "title#description": "", "titleAllow": "Alarm-Positivliste", "titleAllow#description": "", "titleBlock": "Alarm-Blockliste", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Einstellen", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> auf <PERSON>, <tt>{row1,row2,row3}</tt> auf Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_one": "Vorgang", "operation_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "operationsCount": "Vorgänge ({{count}})", "operationsCount#description": "", "operationsHint": "Wählen Sie einen Knotenpunkt im Konfig.-<PERSON><PERSON><PERSON> aus, um einen Vorgang hinzuzufügen.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_one": "{{count}} <PERSON><PERSON> aufgetreten", "encounteredErrors_other": "{{count}} <PERSON><PERSON> aufgetreten", "noChanges": "keine Änderungen", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_one": "{{count}} Schlüssel aktualisiert", "updatedKeys_other": "{{count}} Schlüssel aktualisiert"}, "outcomes": {"failure": "Fehlschlag", "failure#description": "", "partial": "Teilerfolg", "partial#description": "", "success": "Erfolg", "success#description": ""}, "title": "Massenkonfigurationen", "title#description": ""}, "clearCaches": {"action": "<PERSON><PERSON> aktualisieren", "action#description": "", "description": "Probleme? Aktualisieren Sie zunächst den Robot-Syncer-Cache.", "description#description": ""}, "warnings": {"global": "Eine Änderung dieser Konfiguration wirkt sich auf die Standardeinstellungen und Empfehlungen für alle aktuellen und zukünftigen {{class}} aus.", "global#description": "", "notSimon": "Sie sind nicht <PERSON> – deshalb sollten Sie das hier wahrscheinlich nicht bearbeiten… 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Die folgenden Änderungen wurden nicht mit {{serial}} synchronisiert:", "description#description": "", "title": "Nicht synchronisierte Schlüssel", "title#description": ""}}}, "portal": {"clearCaches": {"action": "<PERSON><PERSON><PERSON>", "action#description": "", "description": "Löscht die internen Caches für das Ops Center. Dies **verlangsamt** kurzfristig einige Abfragen, **kann** aber Probleme mit veralteten Daten beheben", "description#description": "", "details": "<PERSON><PERSON><PERSON> dies, wenn Sie manuell die Berechtigung eines Nutzers in Auth0 (nicht über Ops-Center) bearbeitet haben oder wenn Sie an einer Drittanbieter-Integration wie Stream oder Slack Änderungen vorgenommen haben, die nicht wirksam geworden sind.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Die Optionen auf dieser Seite haben Auswirkungen auf den Live-Betrieb von Ops Center in der Produktion.", "global#description": "", "notPortalAdmin": "Du bist weder <PERSON><PERSON> noch <PERSON>, also solltest du das hier wahrscheinlich nicht bearbeiten... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Support Slack-<PERSON><PERSON> muss mit „#” beginnen, z. B. „#support-001-carbon”", "supportSlackLeadingHash#description": ""}}, "title": "Admin", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Drehpunkt-<PERSON><PERSON><PERSON><PERSON>", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Traktoren zuweisen", "orchestrateView#description": "", "showPivotHistory": "Drehpunkt-<PERSON><PERSON><PERSON><PERSON>", "showPivotHistory#description": ""}, "fetchFailed": "Laden der Standortdaten fehlgeschlagen", "fetchFailed#description": "", "goLive": "Live-Update", "goLive#description": "", "hideRows": "<PERSON><PERSON><PERSON> au<PERSON>", "hideRows#description": "", "jobDetails": {"assignmentsFailed": "Abrufen des Auftrags fehlgeschlagen, erneut versuchen?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Der Auftrag kann den Traktoren nicht mehr zugewiesen werden und muss neu erstellt werden.", "description#description": ""}, "customer": {"unknown": "<PERSON>nde unbekannt", "unknown#description": "", "withName": "Kunde: {{name}}", "withName#description": ""}, "farm": {"unknown": "Hof unbekannt", "unknown#description": "", "withName": "Hof: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON>ld unbekannt", "unknown#description": "", "withName": "Feld: {{name}}", "withName#description": ""}, "jobFinished": "Auftrag erledigt um {{time}}", "jobFinished#description": "", "jobStarted": "Auftrag begonnen um {{time}}", "jobStarted#description": "", "openInFarmView": "In Hofansicht öffnen", "openInFarmView#description": "", "state": "Status: {{state}}", "state#description": "", "type": "Auftragstyp: {{type}}", "type#description": ""}, "lastPolled": "Zuletzt abgefragt", "lastPolled#description": "", "live": "Live", "live#description": "", "objectiveFromOtherJob": "<PERSON><PERSON> von e<PERSON>m anderen Auftrag", "objectiveFromOtherJob#description": "", "rowWidthUnits": "Reihenbreite {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "<PERSON><PERSON><PERSON>", "farms#description": "", "tractors": "Traktoren", "tractors#description": ""}, "showRows": "<PERSON><PERSON><PERSON> anzeigen", "showRows#description": "", "stalePivots": "Drehpunkt-Informationen könnten veraltet sein", "stalePivots#description": "", "suggestedAssignments": "Empfohlene <PERSON>rä<PERSON>", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"hideThesePoints": "Diese Punkte ausblenden", "hideThesePoints#description": "", "onlyShowSelected": "Nur ausgewählte zeigen", "onlyShowSelected#description": "", "showAllPoints": "Alle Punkte zeigen", "showAllPoints#description": "", "showThesePoints": "Diese Punkte anzeigen", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "<PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Zentrum", "center#description": "", "centerPivot": "Zentraler Drehpunkt", "centerPivot#description": "", "endpointId": "Endpunkt-ID", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON>", "holes#description": "", "length": "Länge l", "length#description": "", "plantingHeading": "Pflanzrichtung", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "Punkte", "points#description": "", "width": "Breite", "width#description": ""}, "farm": "<PERSON><PERSON>", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Nicht fest", "none#description": "", "rtkFixed": "RTK Fest", "rtkFixed#description": "", "rtkFloat": "RTK Schwebend", "rtkFloat#description": "", "unknown": "Unbekannter Fest-Typ", "unknown#description": ""}, "selectionPanel": {"allPoints": "Alle Punkte", "allPoints#description": "", "boundary": "<PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Zentrum", "center#description": "", "centerPivot": "Zentraler Drehpunkt", "centerPivot#description": "", "endpointId": "Endpunkt-ID", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON>", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Pflanzrichtung", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "Punkte", "points#description": "", "width": "Breite", "width#description": ""}, "unnamedPoint": "<PERSON><PERSON> ben<PERSON><PERSON> <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "Hofgrenze", "farmBoundary#description": "", "field": "<PERSON><PERSON>", "field#description": "", "headland": "Vorgewende", "headland#description": "", "obstacle": "<PERSON><PERSON><PERSON>", "obstacle#description": "", "privateRoad": "Privatstraße", "privateRoad#description": "", "unknown": "Unbekannter Bereichstyp", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON><PERSON><PERSON><PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "Die Linie sollte genau zwei Punk<PERSON> haben", "exactlyTwoPoints#description": "", "wrongFieldType": "Feld \"{{field}}\" muss {{want}} sein", "wrongFieldType#description": "", "wrongGeometryType": "Die Geometrie sollte Typ {{want}} sein", "wrongGeometryType#description": "", "wrongJsonType": "JSON sollte ein Objekt sein", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "<PERSON><PERSON> online", "empty#description": ""}, "title": "Aufgaben-Überwachung", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Änderungsverlauf zeigen", "open#description": "", "title": "Änderungsverlauf", "title#description": ""}, "errors": {"failed": "Konfigurationsbaum kann nicht geladen werden", "failed#description": ""}, "onlyChanged": "Nur Änderungen anzeigen", "onlyChanged#description": ""}, "errors": {"empty": "<PERSON><PERSON>er entspricht den ausgewählten Kriterien", "empty#description": ""}, "hardware": {"errors": {"old": "<PERSON>er meldet keine Computer-Seriennummern (wahrscheinlich veraltet)", "old#description": ""}, "fields": {"hostname": "Hostname", "hostname#description": ""}, "installedVersion": "Installierte Version:", "installedVersion#description": "", "ready": {"name": "Bereit zur Installation:", "name#description": "", "values": {"false": "<PERSON><PERSON><PERSON><PERSON><PERSON> …", "false#description": "", "installed": "Installiert", "installed#description": "", "true": "Bereit!", "true#description": ""}}, "tabs": {"computers": "Computer", "computers#description": "", "versions": "<PERSON>en", "versions#description": ""}, "targetVersion": "Ziel-Version:", "targetVersion#description": "", "title": "Hardware", "title#description": "", "updateHistory": "Versionsübersicht <0>demnächst verfügbar™️</0>", "updateHistory#description": ""}, "history": {"borders": "<PERSON><PERSON><PERSON>", "borders#description": "", "errors": {"invalidDate": "Gültigen Zeitraum auswählen", "invalidDate#description": "", "noJobs": "<PERSON>ine Aufgaben im gewählten Zeitraum gemeldet", "noJobs#description": "", "noMetrics": "<PERSON><PERSON> g<PERSON>", "noMetrics#description": ""}, "moreMetrics": "Mehr Metriken anzeigen", "moreMetrics#description": "", "navTitle": "<PERSON><PERSON><PERSON><PERSON>", "navTitle#description": "", "placeholder": "<PERSON><PERSON>hlen Sie eine Aufgabe oder ein Datum, um die entsprechenden Informationen anzuzeigen", "placeholder#description": "", "points": "Punkte", "points#description": "", "warnings": {"beta": {"description": "<PERSON><PERSON><PERSON>, deren Überprüfung noch aussteht, werden blau dargestellt", "description#description": ""}, "ongoing": "Metriken für dieses Datum sind noch nicht vollständig", "ongoing#description": ""}}, "status": "Status", "status#description": "", "summary": {"banding": {"definition": "Definition", "definition#description": "", "dynamic": "Dynamisch", "dynamic#description": "", "dynamicDisabled": "(Dynamische Bänderung wurde in der Konfig deaktiviert)", "dynamicDisabled#description": "", "rows": "<PERSON><PERSON><PERSON>", "rows#description": "", "static": "Statisch", "static#description": "", "type": "<PERSON><PERSON>", "type#description": "", "unknown": "Unbekannte Bänderung", "unknown#description": "", "v1": "V1", "v1#description": "", "v2": "V2", "v2#description": "", "version": "Version", "version#description": ""}, "config": {"changes#description": "", "changes_one": "{{count}} Almanach-Anpassungen", "changes_other": "{{count}} Änderungen im Almanach", "cpt": "Nutzpflanzen-Punkte Schwellenwert", "cpt#description": "", "default": "(STANDARDWERT: {{value}})", "default#description": "", "wpt": "Unkraut-Punkte Schwellenwert", "wpt#description": ""}, "encoders": {"backLeft": "Hinten links", "backLeft#description": "", "backRight": "<PERSON><PERSON>n rechts", "backRight#description": "", "frontLeft": "Vorne links", "frontLeft#description": "", "frontRight": "<PERSON><PERSON>e rechts", "frontRight#description": "", "title": "Rad-Encoder", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Roboter-Übersichtsseite konnte nicht geladen werden", "failed#description": "", "lasers": {"disabled#description": "", "disabled_one": "{{count}} Deaktivierter Laser", "disabled_other": "{{count}} Deaktivier<PERSON> Laser", "row": "<PERSON><PERSON><PERSON> {{row}}", "row#description": ""}, "machineHealth": "Maschinenzustand", "machineHealth#description": "", "navTitle": "Übersicht", "navTitle#description": "", "safetyRadius": {"driptape": "Tropfband", "driptape#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "sections": {"management": "Verwaltung", "management#description": "", "software": "Software", "software#description": ""}, "supportLinks": {"chipChart": "Splitterdiagramm", "chipChart#description": "", "datasetVisualization": "Datensatz Visualisierung", "datasetVisualization#description": "", "title": "Support-Links", "title#description": ""}}, "support": {"carbon": "Carbon Kundendienst", "carbon#description": "", "chatMode": {"legacy": "<PERSON><PERSON>", "legacy#description": "", "new": "<PERSON><PERSON><PERSON>", "new#description": ""}, "errors": {"failed": "Mitteilung konnte nicht geladen werden", "failed#description": "", "old": {"description": "{{serial}} l<PERSON><PERSON>t mit der Softwareversion {{version}}. Für die Nutzung des Support-Chats ist ein Update auf {{target}} er<PERSON>erlich.", "description#description": "", "title": "Roboter-Softwareversion nicht aktuell", "title#description": ""}}, "localTime": "Ortszeit: {{time}}", "localTime#description": "", "navTitle": "Technischer Kundendienst", "navTitle#description": "", "toCarbon": "Nachricht an den $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "<PERSON><PERSON><PERSON>t an einen $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} ist offline. Die Meldung wird dem Bediener erst angezeigt, wenn der Roboter wieder eingeschaltet wird.", "description#description": "", "title": "Der Roboter ist offline", "title#description": ""}}}, "toggleable": {"internal": "<PERSON><PERSON>", "internal#description": ""}, "uploads": {"errors": {"empty": "<PERSON><PERSON> zum Hochladen verfügbar", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "Filtername", "name#description": "", "otherRobots": "<PERSON><PERSON> Roboter ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "Angeheftete Roboter", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_one": "Filter", "fleetView_other": "Filter", "tableOnly": "Einige Spalten sind nur in der Tabellendarstellung verfügbar", "tableOnly#description": ""}}, "knowledge": {"title": "Wissensdatenbank", "title#description": ""}, "metrics": {"jobStatus": {"closed": "Geschlossen", "closed#description": "", "description": "Auftragsstatus", "description#description": "", "open": "<PERSON>en", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Geschätzte Feldmetriken", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Unser Modell nutzt experimentelle Pflanzendaten, die Ungenauigkeiten enthalten können. Wir verbessern ständig seine Verlässlichkeit.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Leistungs- und Maschinenkennzahlen", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "<PERSON><PERSON> von e<PERSON>m USB-Stick (oder anderem Gerät) hier<PERSON><PERSON><PERSON>", "drop#description": "", "file#description": "", "file_one": "<PERSON><PERSON>", "file_other": "<PERSON><PERSON>", "ingestDescription": "Carbon Mitarbeitende sollten den Ingest Service nutzen", "ingestDescription#description": "", "ingestLink": "Hochladen auf Ingest", "ingestLink#description": "", "select": "<PERSON><PERSON> au<PERSON>wählen", "select#description": "", "title": "Hochladen", "title#description": "", "upload": "Hochladen auf Carbon", "upload#description": "", "uploading": "Hochladen {{subject}} …", "uploading#description": ""}, "reports": {"explore": {"graph": "<PERSON><PERSON>", "graph#description": "", "groupBy": "Gruppieren nach", "groupBy#description": "", "title": "Durchsuchen", "title#description": ""}, "scheduled": {"authorCarbonBot": "Bot Carbon", "authorCarbonBot#description": "", "authorUnknown": "<PERSON>r unbekannt", "authorUnknown#description": "", "automation": {"customerReports": "Kundenberichte", "customerReports#description": "", "errorTitle": "Automatisch erstellter Bericht ungültig", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Kein Kunde ausgewählt", "none#description": ""}}, "reportDay": {"errors": {"none": "Kein Tag ausgewählt", "none#description": ""}, "name": "Tag Be<PERSON>t", "name#description": ""}, "reportEmails": {"errors": {"none": "<PERSON>ine E-Mail-Adressen zugewiesen", "none#description": ""}, "name": "E-Mail-<PERSON><PERSON><PERSON>", "name#description": ""}, "reportHour": {"errors": {"none": "<PERSON>ine Uhrzeit ausgewählt", "none#description": ""}, "name": "Uhrzeit Bericht", "name#description": ""}, "reportLookback": {"errors": {"none": "<PERSON><PERSON> definiert", "none#description": ""}, "name": "Berichtszeitraum", "name#description": ""}, "reportTimezone": {"errors": {"none": "Keine Zeitzone ausgewählt", "none#description": ""}, "name": "Zeitzone Bericht", "name#description": ""}, "warningDescription": "<PERSON><PERSON><PERSON><PERSON> jeden {{day}} um {{hour}} in {{timezone}} mit {{lookback}} Tagen Rückblick für alle aktiven {{customer}}-Roboter.", "warningDescription#description": "", "warningTitle": "Dies ist ein automatischer Bericht!", "warningTitle#description": ""}, "byline": "Von {{author}}", "byline#description": "", "editor": {"columnsHidden": "Ausgeblendete Spalten", "columnsHidden#description": "", "columnsVisible": "<PERSON><PERSON><PERSON><PERSON>", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_one": "Achtung: <PERSON>s gibt einen weiteren Bericht mit diesem Namen", "duplicateNames_other": "Warnung: es gibt {{count}} andere Berichte mit diesem Namen", "fields": {"automateWeekly": "Automatisch wöchentlich", "automateWeekly#description": "", "name": "Name <PERSON><PERSON>", "name#description": "", "showAverages": "Durchschnittswerte anzeigen", "showAverages#description": "", "showTotals": "Gesamtsummen anzeigen", "showTotals#description": ""}}, "errors": {"noReport": "Bericht existiert nicht oder Si<PERSON> haben keinen Zugriff", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} wird endgültig gelöscht.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "<PERSON>e sind nicht berechtigt, {{subject}} zu löschen.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "E-Mail wird nicht erneut gesendet", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Be<PERSON>t wird an diese E-Mail-Adressen gesendet", "publishEmailsHelperNew#description": ""}, "runAgain": "<PERSON>rneut ausführen", "runAgain#description": ""}, "table": {"errors": {"noColumns": "<PERSON>te wählen Si<PERSON> eine oder mehrere Spalten", "noColumns#description": "", "noEndDate": "Bitte wählen Sie das Enddatum", "noEndDate#description": "", "noRobots": "<PERSON>te wählen Si<PERSON> einen oder mehrere Roboter", "noRobots#description": "", "noStartDate": "Bitte wählen Sie das Anfangsdatum", "noStartDate#description": ""}, "fields": {"average": "Durchschnitt", "average#description": "", "averageShort": "DURCHSCHN.", "averageShort#description": "", "date": "Datum", "date#description": "", "group": "Seriennummer/Datum", "group#description": "", "groupJob": "Seriennummer/Aufgabe", "groupJob#description": "", "mixed": "(<PERSON><PERSON><PERSON><PERSON>)", "mixed#description": "", "total": "Gesamtsumme", "total#description": "", "totalShort": "SUM.", "totalShort#description": ""}, "unknownReport": "Bericht unbekannt", "unknownReport#description": ""}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "toLine": "für {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "<PERSON><PERSON>", "all#description": "", "select": "Bitte wählen Sie die Kennzahlen aus", "select#description": ""}, "robotsLabel": {"all": "<PERSON><PERSON> Roboter", "all#description": "", "none": "<PERSON><PERSON>", "none#description": "", "select": "<PERSON><PERSON> auswählen", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> über <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "Benutzernamen und Passwort", "auth0#description": "", "google": "Google OAut", "google#description": "", "unknown": "unbekannter Anbieter", "unknown#description": ""}, "cards": {"account": "Ko<PERSON>", "account#description": "", "advanced": "Erweiterte Einstellungen", "advanced#description": "", "localization": "Lokalisierung", "localization#description": ""}, "delete": {"deleteAccount": "Konto löschen", "deleteAccount#description": "", "dialog": {"description": "WARNHINWEIS: <PERSON><PERSON> Vorgang kann nicht rückgängig gemacht werden. Alle Daten gehen dabei verloren.", "description#description": ""}}, "fields": {"experimental": "Experimentelle Funktionen aktivieren", "experimental#description": "", "language": "<PERSON><PERSON><PERSON>", "language#description": "", "measurement": {"name": "Maßeinheit", "name#description": "", "values": {"imperial": "Imperial (<PERSON><PERSON>, Meilen pro <PERSON>, Morgen, Fahrenheit)", "imperial#description": "", "metric": "<PERSON><PERSON><PERSON> (Mill<PERSON>, Kilometer pro <PERSON>e, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "Abmelden", "logOut#description": "", "title": "Einstellungen", "title#description": "", "version": "Carbon Ops Center Version {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "<PERSON>utzer existieren nicht, oder Sie sind nicht berechtigt, sie anzuzeigen.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "<PERSON><PERSON>er in Auth0 verwalten", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Funktion und Berechtigungen", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Auftragnehm<PERSON>", "contractors#description": ""}}}}