{"components": {"AlarmTable": {"export": "{{robots}} Larmhistorik {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Spatial Metrics är tillgängliga gratis under utvärderingen men kan när som helst komma att ändras, tas bort eller uppgraderas. Data bör verifieras oberoende.", "description#description": "", "title": "Spatial Metrics Beta", "title#description": ""}, "tooltip": "Denna funktion är under utv<PERSON><PERSON>ring och kan komma att ändras eller tas bort när som helst", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Chatten gick inte att ladda: {{message}}", "failed#description": ""}, "machineTranslated": "Maskinö<PERSON>att", "machineTranslated#description": "", "machineTranslatedFrom": "Maskinöversatt från {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "<PERSON><PERSON> meddelande raderades.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} kommer att raderas permanent.", "description#description": "", "descriptionActive": "{{subject}} är aktiv så den går inte att ta bort.", "descriptionActive#description": ""}, "title": "<PERSON>r du säker?", "title#description": ""}, "CopyToClipboardButton": {"click": "<PERSON><PERSON><PERSON> för att kopiera", "click#description": "", "copied": "<PERSON><PERSON><PERSON>!", "copied#description": ""}, "CropEditor": {"failed": "Det gick inte att läsa in beskärningsredigeraren", "failed#description": "", "viewIn": "Vy i Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "Ren<PERSON>", "clear#description": "", "endDate": "Slut<PERSON><PERSON>", "endDate#description": "", "error": "Fel i datumintervallväljaren", "error#description": "", "invalid": "<PERSON><PERSON><PERSON><PERSON>", "invalid#description": "", "last7days": "Senaste 7 dagarna", "last7days#description": "", "lastMonth": "<PERSON><PERSON><PERSON>", "lastMonth#description": "", "lastWeek": "<PERSON><PERSON><PERSON> ve<PERSON>", "lastWeek#description": "", "minusDays": "{{days}} dagar sedan", "minusDays#description": "", "plusDays": "om {{days}} dagar", "plusDays#description": "", "startDate": "Startdatum", "startDate#description": "", "thisMonth": "<PERSON><PERSON>", "thisMonth#description": "", "thisWeek": "<PERSON><PERSON> vecka", "thisWeek#description": "", "today": "I dag", "today#description": "", "tomorrow": "I morgon", "tomorrow#description": "", "yesterday": "<PERSON> går", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "<PERSON><PERSON><PERSON><PERSON>, ov<PERSON><PERSON>t fel", "error#description": "", "queryLimitReached": "Återger partiell datauppsättning eftersom det kom tillbaka för mycket data. Kontakta supporten för hjälp", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Vad hände?", "comment#description": "", "feedback": "<PERSON><PERSON><PERSON>", "feedback#description": "", "submit": "<PERSON><PERSON><PERSON> in och ladda om", "submit#description": ""}, "GdprConsent": {"description": "Vänligen granska och godkänn för att fortsätta", "description#description": "", "statement": "<PERSON>ag godkänner <0>användarvillkoren</0> och <1>Sekretesspolicyn</1>", "statement#description": "", "title": "Användarvillkor och sekretesspolicy", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Kund krävs", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "<PERSON><PERSON><PERSON><PERSON> den här menyn", "help#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": ""}, "LaserTable": {"export": "{{robots}} Lasrar {{date}}", "export#description": "", "installedOnly": "Endast installerad", "installedOnly#description": "", "warnings": {"duplicate": "Denna robot har flera lasrar registrerade i följande fack: {{slots}}", "duplicate#description": "", "emptySlot": "Denna robot har ingen laser registrerad i följande fack: {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Ny kod", "new#description": ""}, "Loading": {"failed": "Det gick inte att ladda Carbon Ops Center.", "failed#description": "", "placeholder": "Laddar...", "placeholder#description": ""}, "ModelName": {"warning": "Varning: <PERSON>l med låg tillförlitlighet", "warning#description": ""}, "PendingActivationOverlay": {"description": "Vi aktiverar ditt konto. Du får ett e-postmeddelande när det är klart!", "description#description": "", "errors": {"carbon": {"description": "Carbon-e-post har upptäckts men inte verifierats på grund av inloggning med användarnamn/lösenord. Logga ut och använd alternativet \"Logga in med Google\" för att aktiveras automatiskt.", "description#description": "", "title": "Overifierat Carbonkonto", "title#description": ""}}, "hi": "Hej {{name}}!", "hi#description": "", "logOut": "Loggat in med fel konto? <0>Logga ut</0> .", "logOut#description": "", "title": "Väntar på aktivering", "title#description": ""}, "ResponsiveSubnav": {"more": "<PERSON><PERSON>", "more#description": ""}, "RobotImplementationSelector": {"status": "Status för implementering", "status#description": "", "title": "Ändra implementeringsstatus", "title#description": "", "warning": "Ändring av implementeringsstatus kan utlösa automatiserade arbetsflöden som påverkar kundupplevelsen. GÖR DET INTE OM DU INTE ÄR SÄKER!", "warning#description": ""}, "ShowLabelsButton": {"text": "Etiketter", "text#description": "", "tooltip": "Visa etiketter", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Visa metadata", "tooltip#description": ""}, "almanac": {"crops": {"new": "Lägg till ny beskärning", "new#description": "", "none": "Inga beskärningskategorier", "none#description": "", "sync#description": ""}, "cropsSynced": "<PERSON><PERSON><PERSON><PERSON>", "cropsSynced#description": "", "delete": {"description": "<PERSON>ta kan inte ångras", "description#description": ""}, "discard": {"description": "Vill du ignorera ändringar av {{title}}?", "description#description": "", "title": "Vill du ignorera ändringar?", "title#description": ""}, "fineTuneDescription": "Standard är 5, kan minska eller öka laserfotograferingstiden med ~20 % per steg", "fineTuneDescription#description": "", "fineTuneTitle": "Finjusteringsmultiplikator", "fineTuneTitle#description": "", "formulas": {"all": "<PERSON><PERSON>", "all#description": "", "copyFormula": "<PERSON><PERSON><PERSON> formel", "copyFormula#description": "", "copySize": "<PERSON><PERSON><PERSON>", "copySize#description": "", "exponent": {"description": "<PERSON><PERSON>jer radien i mm till denna exponent", "description#description": "", "label": "Exponent (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "<PERSON><PERSON> fr<PERSON>n 1-10, standard är 5, kan minska eller öka laserfotograferingstiden med ~20 % per steg. Detta är numret som används i grundläget", "description#description": "", "label": "Fine Tune Index (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Total multiplikator för ökningen/minskningen av Fine Tune Index", "description#description": "", "label": "Fine Tune Multiplier Value (FM)", "label#description": ""}, "laserTime": "<PERSON><PERSON><PERSON>", "laserTime#description": "", "maxTime": {"description": "Begränsa mängden tid att fotografera i ms", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "multiplier": {"description": "Multiplicerar mot radien i mm", "description#description": "", "label": "Multiplikator (A)", "label#description": ""}, "offset": {"description": "Antal millisekunder att lägga till oavsett radie", "description#description": "", "label": "Offset (b)", "label#description": ""}, "pasteFormula": "Klistra in formel", "pasteFormula#description": "", "pasteSize": "Klistra in storlekar", "pasteSize#description": "", "sync": "Synkronisera alla storlekar", "sync#description": "", "thresholds": "Tröskelvärden för <PERSON>", "thresholds#description": "", "title": "<PERSON><PERSON>", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Växla till Avancerat läge", "switchModeAdvanced#description": "", "switchModeBasic": "Växla till Basic Mode (grundläge)", "switchModeBasic#description": "", "warnings": {"admin": "Om du ändrar denna almanacka synkroniseras den med alla nuvarande och framtida produktionsenheter.", "admin#description": "", "carbon": "Detta är en almanacka som tillhandahålls av Carbon. Endast Finjusteringsindex kan ändras.", "carbon#description": "", "production": "Denna almanacka körs aktivt på en robot. Redigering träder i kraft i fältet omedelbart.", "production#description": ""}, "weeds": {"new": "Lägg till nytt ogräs", "new#description": "", "none": "Inga ogräskategorier", "none#description": "", "sync#description": ""}, "weedsSynced": "<PERSON><PERSON> og<PERSON><PERSON><PERSON>", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} har sparats. Aktivera med Operator App Quick Tune", "savedLong#description": "", "testResults": "Förhandsgranska resultat", "testResults#description": ""}, "filters": {"capturedAt": "Datum då bilden togs", "capturedAt#description": "", "diameter": "Diameter", "diameter#description": "", "filters#description": "", "unappliedFilters": "", "unappliedFilters#description": ""}, "images": {"allImages": "<PERSON>a bi<PERSON>", "allImages#description": "", "categorized": "Kategoriserad", "categorized#description": "", "scrollToTop": "Tillbaka till toppen", "scrollToTop#description": "", "sortBy": {"latest": "Senaste", "latest#description": ""}, "sortedBy": "Sorterat efter: {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Fel vid hämtning av status", "error#description": "", "ready": "Resultat klara", "ready#description": "", "session#description": "", "session_one": "Session", "session_other": "Sessioner", "showResults": "Visa resultat", "showResults#description": "", "status": "bearbetade {{processed}} / {{total}} resultat", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "Ändring av denna anläggningsprofil synkroniseras med alla nuvarande och framtida produktionsenheter.", "admin#description": "", "adminMeta": "Alla administratörsprofiler kommer att vara tillgängliga för alla kunder. Skapa inga felaktiga profiler!", "adminMeta#description": "", "production": "Denna anläggningskategoriprofil är igång på en robot. Redigering av den träder omedelbart i kraft i fältet.", "production#description": "", "protected": "Detta är en kolförsedd profil. Ingenting går att ändra.", "protected#description": "", "unsavedChanges": "Osparade ändringar. Tryck på spara för att tillämpa ändringar.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_one": "<PERSON><PERSON>", "changedKey_other": "<PERSON><PERSON><PERSON>", "newKey": "nytt {{key}} namn", "newKey#description": "", "stringReqs": "<PERSON>n bara inneh<PERSON> az, 0-9, ., - och _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "<PERSON><PERSON> n<PERSON>l har lagts till ovanpå standardinställningen.", "description#description": ""}, "keyMissing": {"description": "Saknade standard(er): {{keys}}", "description#description": ""}, "valueChanged": {"description": "Detta värde har ändrats från standardvärdet ({{default}})", "description#description": "", "title": "Konfiguration ändrad", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Det gick inte att läsa in kundredigeraren", "load#description": ""}}, "CustomerSelector": {"empty": "<PERSON><PERSON>", "empty#description": "", "title": "Byt kund", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "Skjut vs ignorera", "description#description": "", "label": "Filma", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON> konfigu<PERSON>", "copy#description": "", "ignorable": {"description": "Sk<PERSON>t endast om tiden så medger, beaktas inte i hastighetsrekommendationen", "description#description": "", "label": "<PERSON>n <PERSON>", "label#description": ""}, "paste": "Klistra in konfigurationer", "paste#description": ""}, "warnings": {"production": "Denna diskriminator körs aktivt på en robot. Redigering träder i kraft i fältet omedelbart.", "production#description": ""}}, "drawer": {"customerMode": "Kundläge", "customerMode#description": "", "error": "Det gick inte att läsa in navigeringen", "error#description": ""}, "filters": {"NumericalRange": {"max": "Max ({{units}})", "max#description": "", "min": "Min ({{units}})", "min#description": ""}, "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": ""}, "header": {"failed": "Det gick inte att läsa in rubriken", "failed#description": "", "mascot": "Carbon Robotics kycklingmaskot", "mascot#description": "", "search": {"failed": "Det gick inte att ladda sökningen", "failed#description": "", "focus": "Fokussökning", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "Storlek", "label#description": "", "larger": "st<PERSON><PERSON>", "larger#description": "", "smaller": "mindre", "smaller#description": ""}}, "map": {"bounds": {"reset": "Återställ vy", "reset#description": ""}, "errors": {"empty": "Inga platsdata rapporterade", "empty#description": "", "failed": "Det gick inte att läsa in kartan", "failed#description": ""}, "filters": {"customer_office": "<PERSON><PERSON><PERSON><PERSON>", "customer_office#description": "", "hq": "Carbons huvud<PERSON>tor", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "PO Box", "po_box#description": "", "shop": "Handla", "shop#description": "", "storage": "F<PERSON><PERSON>ring", "storage#description": "", "support_base": "Stödbas", "support_base#description": ""}, "fullscreen": "Helskärm", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "Fel i lagerförklaring", "legend#description": "", "notThinning": "GALLRAS INTE", "notThinning#description": "", "notWeeding": "RENSAR INTE OGRÄS", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "VÄRMEKARTFEL", "unknown#description": ""}, "fields": {"block": "Blockera: {{block}}", "block#description": "", "location": "Plats: {{latitude}}, {{longitude}}", "location#description": "", "size": "Storlek: {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Lager", "name#description": "", "rangeType#description": "", "relative": "Använd Relativt intervall", "relative#description": "", "relativeRange#description": ""}, "map": "Karta", "map#description": "", "measure": {"name": "<PERSON><PERSON><PERSON>", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "<PERSON><PERSON><PERSON> från vilken kategori?", "copyFromWhich#description": "", "splitCrops": "Delade grödor", "splitCrops#description": "", "splitWeeds": "Dela upp typer av ogr<PERSON>s", "splitWeeds#description": "", "syncCrops": "Synkronisera alla grödor", "syncCrops#description": "", "syncWeeds": "Synkronisera alla typer av ogräs", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Tröskelvärde för prediktionsförtroende för att använda en detektering i dynamisk bandning", "description#description": "", "label": "Tröskelvärde för bandning", "label#description": ""}, "minDoo": {"description": "Minsta upptäckt över möjlighet", "description#description": "", "label": "Minsta DOO", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Tröskelvärde för preditkionsförtroende för att använda en detektering vid gallring", "description#description": "", "label": "Tröskelvärde för gall<PERSON>", "label#description": ""}, "weed": {"description": "Förutsägelseförtroendetröskel för att använda en detektion för omvänt växtskydd", "description#description": "", "label": "Omvänd växtskyddströskel", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Tröskelvärde för prediktionsförtroende för att använda en detektion för växtskydd", "description#description": "", "label": "Tröskelvärde för vä<PERSON>tskydd", "label#description": ""}, "weed": {"description": "Tröskelvärde för preditkionsförtroende för att överväga ett ogräs", "description#description": "", "label": "Tröskelvärde för ogr<PERSON><PERSON>", "label#description": ""}}}, "errors": {"sync": "Inställningarna för denna modell har inte synkroniserats från LaserWeeder ännu. Vänta på synkronisering för att se och uppdatera inställningarna.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "<PERSON><PERSON> upp storlekar", "splitSizesLong#description": "", "splitSizesShort": "Dela upp", "splitSizesShort#description": "", "syncSizesLong": "Synkron<PERSON><PERSON> s<PERSON>", "syncSizesLong#description": "", "syncSizesShort": "Synkronisera", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Varning:{{stopEmphasis}} I dessa inställningar ingår osparade ändringar som inte reflekteras på roboten.", "exportingUnsavedChanges#description": "", "production": "<PERSON>na modell körs aktivt på en robot. Redigering träder i kraft i fältet omedelbart.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "<PERSON><PERSON><PERSON> larm", "unknown#description": ""}, "almanac": {"unknown": "Okänd almanacka", "unknown#description": "", "withName": "Almanacka: {{name}}", "withName#description": ""}, "autofixing": "Automatisk korrigering av fel", "autofixing#description": "", "banding": {"disabled": "Bandning inaktiverad", "disabled#description": "", "enabled": "Bandning aktiverad", "enabled#description": "", "none": "Ingen bandning", "none#description": "", "static": "(STATISK)", "static#description": "", "withName": "Bandning: {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Det gick inte att läsa in incheckningsstatus", "failed#description": "", "never": "Aldrig checkat in", "never#description": "", "withTime": "Checkade in {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Gr<PERSON><PERSON> a<PERSON> ({{pinned}} fästade)", "summary#description": ""}, "delivery": "Leverans", "delivery#description": "", "disconnected": "Frånkopplad", "disconnected#description": "", "discriminator": {"unknown": "Okänd diskriminator", "unknown#description": "", "withName": "Diskriminator: {{name}}", "withName#description": ""}, "failed": "Det gick inte att läsa in robotstatus", "failed#description": "", "failedShort": "<PERSON><PERSON><PERSON><PERSON>", "failedShort#description": "", "implementation": "Implementering", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Lager", "inventory#description": "", "job": {"none": "Inget jobb", "none#description": "", "withName": "Jobb: {{name}}", "withName#description": ""}, "lasers": "Lasrar online: {{online}}/{{total}}", "lasers#description": "", "lifetime": "Livstid:", "lifetime#description": "", "lifted": "Standby (lyft)", "lifted#description": "", "loading": "Belastning", "loading#description": "", "location": {"known": "Plats: <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Plats okänd", "unknown#description": ""}, "manufacturing": "Tillverkning", "manufacturing#description": "", "model": {"withName": "Modell: <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "<PERSON><PERSON><PERSON><PERSON>", "modelLoading#description": "", "notArmed": "Inte aktiverad", "notArmed#description": "", "off_season": "Lågsäsong", "off_season#description": "", "offline": "Offline i {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P Okänd", "unknown#description": ""}, "poweringDown": "Stänger av", "poweringDown#description": "", "poweringUp": "Slår på", "poweringUp#description": "", "pre_manufacturing": "Förtillverkning", "pre_manufacturing#description": "", "stale": "Gammal", "stale#description": "", "staleDescription": "Senast kända värde. Robot är offline.", "staleDescription#description": "", "standby": "Standby", "standby#description": "", "thinning": {"disabled": "Gallring inaktiverad", "disabled#description": "", "enabled": "Gallring aktiverad", "enabled#description": "", "none": "Ingen gallring", "none#description": "", "withName": "Gallring: {{name}}", "withName#description": ""}, "today": {"none": "Ingen ogräsrensning idag", "none#description": ""}, "unknown": "Okänd status", "unknown#description": "", "updating": "Uppdatering installeras", "updating#description": "", "version": {"values": {"unknown": "Okänd version", "unknown#description": "", "updateDownloading": "({{version}} laddar ner)", "updateDownloading#description": "", "updateReady": "({{version}} redo)", "updateReady#description": ""}}, "weeding": "Ogr<PERSON><PERSON><PERSON><PERSON>ning {{crop}}", "weeding#description": "", "weedingDisabled": "Ogräsrensning inaktiverad", "weedingDisabled#description": "", "weedingThinning": "Ogr<PERSON><PERSON><PERSON><PERSON><PERSON> och gallring {{crop}}", "weedingThinning#description": "", "winterized": "Undanställd för vintern", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "Finns redan", "exists#description": "", "unknownClass": "Okänd robotklass", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Skapa inte en ny konfiguration", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "{{class}} Mall", "templateForClass#description": "", "templateGeneric": "Robotmall", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Du bör bara fortsätta om en konfiguration för {{serial}} redan finns eller om du planerar att skapa den manuellt", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Avancerade hastighetsmätareinställningar", "advancedFormulaTitle#description": "", "formulaTitle": "Formel", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Minska automatiskt den föreslagna hastigheten med ditt angivna värde. Till exempel kommer en inmatning på 5 % att minska den föreslagna hastigheten på 1 mph till 0,95 mph", "description#description": "", "label": "Has<PERSON>ghetsutjämning", "label#description": ""}, "decreaseSmoothing": {"description": "Anpassa hastigheten med vilken hastigheten minskar. <PERSON> högre värde, desto mer sannolikt kommer hastighetsmätaren att fluktuera", "description#description": "", "label": "Retardationsutjämning", "label#description": ""}, "increaseSmoothing": {"description": "Anpassa hastigheten med vilken hastigheten ökar. <PERSON> högre värde, desto mer sannolikt kommer hastighetsmätaren att fluktuera", "description#description": "", "label": "Accelerationsutjämning", "label#description": ""}, "maxVelMph": {"description": "Ange den absolut högsta hastighet du är villig att resa med. Hastighetsrekommendationer kommer inte att vara över detta värde", "description#description": "", "label": "Maximal hastighet", "label#description": ""}, "minVelMph": {"description": "Ange den absolut lägsta hastighet du är villig att resa med. Hastighetsrekommendationer kommer inte att vara under detta värde", "description#description": "", "label": "<PERSON><PERSON> has<PERSON>", "label#description": ""}, "primaryKillRate": {"description": "Detta värde är din önskade procentandel av ogräs som bekämpats", "description#description": "", "label": "Idealisk bekämpningsfrekvens", "label#description": ""}, "primaryRange": {"description": "Öka detta värde om du vill nå din idealiska bekämpningsfrekvens oavsett hastighetspåverkan", "description#description": "", "label": "Gr<PERSON>n <PERSON>", "label#description": ""}, "rows": {"allRows": "<PERSON><PERSON> rader", "allRows#description": "", "row1": "Rad 1", "row1#description": "", "row2": "Rad 2", "row2#description": "", "row3": "Rad 3", "row3#description": ""}, "secondaryKillRate": {"description": "Detta värde är den lägsta acceptabla procentandelen bekämpat ogräs", "description#description": "", "label": "Lägsta bekämpningsfrekvens", "label#description": ""}, "secondaryRange": {"description": "Öka detta värde om du vill lägga till marginal innan du får ett meddelande om låg hastighet", "description#description": "", "label": "Gul buffert", "label#description": ""}, "sync": "Synkronisera alla rader", "sync#description": "", "warnings": {"admin": "Ändring av denna hastighetsestimatot kommer att synkroniseras med alla nuvarande och framtida produktionsenheter.", "admin#description": "", "production": "<PERSON><PERSON> hastighetsestimator kö<PERSON> aktivt på en robot. Redigering träder i kraft i fältet omedelbart.", "production#description": "", "protected": "Detta är en profil som tillhandahålls av Carbon. Ingenting kan ändras.", "protected#description": "", "unsavedChanges": "Ändringar som inte sparats. Tryck på spara för att tillämpa ändringar.", "unsavedChanges#description": ""}}, "slider": {"gradual": "<PERSON><PERSON><PERSON>", "gradual#description": "", "immediate": "<PERSON><PERSON><PERSON><PERSON>", "immediate#description": ""}, "visualization": {"targetSpeed": "Målhastighet", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_one": "larm", "alarm_other": "larm", "fields": {"code": "Kod", "code#description": "", "description": "Beskrivning", "description#description": "", "duration": {"name": "Varaktighet", "name#description": "", "values": {"ongoing": "PÅGÅENDE", "ongoing#description": ""}}, "identifier": "Identifierare", "identifier#description": "", "impact": {"name": "Inverkan", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Startade", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_one": "almanacka", "almanac_other": "almanackor", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_one": "uppgift", "assignment_other": "uppgifter", "autotractor": "Autotraktor", "autotractor#description": "", "fields": {"instructions": "Instruktioner", "instructions#description": ""}, "intervention#description": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"laserWeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "laserWeed#description": "", "unrecognized": "okänd typ ({{value}})", "unrecognized#description": ""}, "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Laser Weed-rad", "laserWeedRow#description": ""}, "objective_one": "<PERSON><PERSON><PERSON>", "objective_other": "<PERSON><PERSON><PERSON>", "states": {"acknowledged": "bekräftat", "acknowledged#description": "", "cancelled": "avbrutet", "cancelled#description": "", "completed": "slutfört", "completed#description": "", "failed": "misslyckat", "failed#description": "", "inProgress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inProgress#description": "", "new": "nytt", "new#description": "", "paused": "pausat", "paused#description": "", "pending": "väntar", "pending#description": "", "ready": "klart", "ready#description": "", "unrecognized": "okänd status ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Uppgift #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_one": "uppgift", "task_other": "uppgifter"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_one": "anläggningsprofil", "categoryCollectionProfile_other": "anläggningsprofiler", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON> från", "disregard#description": "", "name": "<PERSON><PERSON><PERSON>", "name#description": "", "requiredBaseCategories": "Du måste ha dessa exakta kategorier: ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Uppdaterad", "updatedAt#description": ""}, "metadata": {"capturedAt": "Datum då bilden togs", "capturedAt#description": "", "categoryId": "Kategori-ID", "categoryId#description": "", "imageId": "Bild-ID", "imageId#description": "", "pointId": "Punkt-<PERSON>", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "radius": "radie", "radius#description": "", "updatedAt": "Mottagen", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_one": "konfiguration", "config_other": "konfigurationer", "key#description": "", "key_one": "<PERSON><PERSON><PERSON><PERSON>", "key_other": "<PERSON><PERSON><PERSON><PERSON>", "template#description": "", "template_one": "konfigurationsmall", "template_other": "konfigurationsmallar", "value#description": "", "value_one": "värde", "value_other": "värden"}, "crops": {"categories": {"unknown": "Okänd gröda", "unknown#description": ""}, "crop#description": "", "crop_one": "<PERSON><PERSON><PERSON><PERSON>", "crop_other": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"confidence": {"fields": {"regionalImages": "Regionala bilder:", "regionalImages#description": "", "totalImages": "Totalt antal bilder:", "totalImages#description": ""}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Arkiverad", "archived#description": "", "unknown": "<PERSON><PERSON><PERSON>", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Anteckningar", "notes#description": "", "pinned": "Fästad", "pinned#description": "", "recommended": "Rekommenderad", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_one": "kund", "customer_other": "kunder", "fields": {"emails": {"errors": {"formatting": "Ett mejl per rad", "formatting#description": ""}, "name": "E-postmeddelanden", "name#description": ""}, "featureFlags": {"almanac": {"description": "Aktiverar flikarna Almanacka och Diskriminator för robotar (roboten måste också stödja almanacka och diskriminator)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Aktiverar fliken Anläggningsprofil för <PERSON>ar", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Funktionsflaggor möjliggör betafunktionalitet för alla användare hos en kund", "description#description": "", "explore": {"description": "Möjliggör kart- och grafutforskningsläge i rapporter", "description#description": "", "name": "Utforska läge", "name#description": ""}, "jobs": {"description": "<PERSON><PERSON><PERSON><PERSON> jobb (roboten måste också stödja jobb)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Visar en ny visuell design för robot<PERSON>ät<PERSON>", "description#description": "", "name": "Uppdatera mätvärden", "name#description": ""}, "name": "Funktionsflaggor", "name#description": "", "off": "AV", "off#description": "", "on": "PÅ", "on#description": "", "reports": {"description": "Aktiverar fliken Rapporter och funktioner inom", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Visa rumsliga data inklusive värmekartor och grafer", "description#description": "", "name": "Rumslig data", "name#description": ""}, "summary": "{{enabled}}/{{total}} Funktionsflaggor aktiverade", "summary#description": "", "unvalidatedMetrics": {"description": "Visa betastatistik som väntar på fältvalidering i certifierade mätvärden", "description#description": "", "name": "Betastatistik", "name#description": ""}, "velocityEstimator": {"description": "Möjliggör visning och redigering av målhastighetsestimeringsprofiler (roboten måste också stödja målhastighetsestimator)", "description#description": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>etsestimator", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "<PERSON><PERSON><PERSON>", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Om det är aktiverat körs rapporter varje vecka med följande inställningar för alla aktiva robotar", "description#description": "", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name#description": ""}, "weeklyReportHour": "K<PERSON>r kl", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Tillbakablick", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "<PERSON><PERSON><PERSON> om", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_one": "diskriminator", "discriminator_other": "diskriminatorer", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_one": "odla", "farm_other": "gårdar", "point#description": "", "point_one": "punkt", "point_other": "punkter", "zone#description": "", "zone_one": "zon", "zone_other": "zoner"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_one": "fältdefinition", "fieldDefinition_other": "fältdefinitioner", "fields": {"boundary": "Fältgräns", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Planteringsrubrik", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_one": "allmänt värde", "global_other": "allmänna värden", "values": {"plantProfileModelId": {"description": "Basmodell som används av alla kund- och administratörsprofiler för '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "$t(components.categoryCollectionProfile.actions.testResults) Modell-ID", "label#description": ""}}}, "images": {"fields": {"camera": "<PERSON><PERSON><PERSON>", "camera#description": "", "capturedAt": "Datum/tid", "capturedAt#description": "", "geoJson": "Plats", "geoJson#description": "", "url": "Öppna bild", "url#description": ""}, "image#description": "", "image_one": "bild", "image_other": "Bilder"}, "jobs": {"job#description": "", "job_one": "jobb", "job_other": "jobb"}, "lasers": {"fields": {"cameraId": "Kamera-ID", "cameraId#description": "", "error": {"values": {"false": "No<PERSON>ll", "false#description": ""}}, "installedAt": "Installerad", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Okänd serie", "unknown#description": ""}}, "lifetimeSec": "I tid", "lifetimeSec#description": "", "powerLevel": "Effektnivå", "powerLevel#description": "", "removedAt": "Borttagen", "removedAt#description": "", "rowNumber": "<PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "Brandrä<PERSON>ning", "totalFireCount#description": "", "totalFireTimeMs": "<PERSON><PERSON>", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired#description": "", "hours": "Timmar: {{installed}}/{{total}} ({{percent}} som å<PERSON>tår)", "hours#description": "", "hoursUnknown": "<PERSON>mar: <PERSON><PERSON><PERSON>", "hoursUnknown#description": "", "months": "Månader: {{installed}}/{{total}} ({{percent}} som å<PERSON>år)", "months#description": "", "monthsUnknown": "Månader: <PERSON><PERSON><PERSON>", "monthsUnknown#description": "", "unknown": "<PERSON><PERSON><PERSON> garanti", "unknown#description": ""}}}, "laser#description": "", "laser_one": "laser", "laser_other": "<PERSON><PERSON>"}, "models": {"model#description": "", "model_one": "modell", "model_other": "modeller", "none": "Ingen modell", "none#description": "", "p2p#description": "", "p2p_one": "P2P-modell", "p2p_other": "P2P-modeller", "unknown": "Okänd modell", "unknown#description": ""}, "reportInstances": {"fields": {"authorId": "<PERSON><PERSON><PERSON>", "authorId#description": "", "createdAt": "Publicerad", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_one": "rapportera körning", "run_other": "rapportera körningar"}, "reports": {"fields": {"authorId": "Ägare", "authorId#description": "", "automateWeekly": {"name": "Automatiserad", "name#description": "", "values": {"weekly": "<PERSON><PERSON><PERSON> ve<PERSON>a", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_one": "rapportera", "report_other": "rapporter"}, "robots": {"classes": {"buds#description": "", "buds_one": "<PERSON><PERSON><PERSON>", "buds_other": "K<PERSON>ppar", "moduleValidationStations#description": "", "moduleValidationStations_one": "Module Validation Station", "moduleValidationStations_other": "Module Validation Stations", "reapersCarbon#description": "", "reapersCarbon_one": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reapersCarbon_other": "Skördemaskiner", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_one": "Traktor", "rtcs_other": "Traktorer", "simulators#description": "", "simulators_one": "Simulator", "simulators_other": "Simulatorer", "slayersCarbon#description": "", "slayersCarbon_one": "Bekämpningsmedel", "slayersCarbon_other": "Bekämpningsmedel", "slayersCustomer#description": "", "slayersCustomer_one": "Laserogränsrensare", "slayersCustomer_other": "Laserogräsrensare", "unknown": "Okänd klass", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Lasrar offline", "lasersOffline#description": "", "lifetimeArea": "Livstidsområde", "lifetimeArea#description": "", "lifetimeTime": "Livstidstid", "lifetimeTime#description": "", "localTime": "Lokaltid", "localTime#description": "", "reportedAt": "Senast uppdaterad", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Programvaruversion", "softwareVersion#description": "", "supportSlack": "<PERSON><PERSON><PERSON>ö<PERSON>kanal", "supportSlack#description": "", "targetVersion": "Målversion", "targetVersion#description": ""}, "robot#description": "", "robot_one": "robot", "robot_other": "robotar", "unknown": "Okänd robot", "unknown#description": ""}, "users": {"activated": "Aktiverad", "activated#description": "", "fields": {"email": "E-post", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Aktivering", "name#description": "", "values": {"false": "I AVVAKTAN PÅ", "false#description": ""}}}, "operator#description": "", "operator_one": "operatör", "operator_other": "operatörer", "role#description": "", "role_one": "Roll", "role_other": "Roller", "roles": {"carbon_basic": "Carbon robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (teknisk)", "carbon_tech#description": "", "farm_manager": "G<PERSON>rdsförman", "farm_manager#description": "", "operator_advanced": "Operatör (avancerad)", "operator_advanced#description": "", "operator_basic": "Operatör", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "Okänd roll", "unknown_role#description": ""}, "staff": "Personal", "staff#description": "", "user#description": "", "user_one": "anvä<PERSON><PERSON>", "user_other": "anvä<PERSON><PERSON>"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_one": "hastighetsestimator", "velocityEstimator_other": "<PERSON><PERSON><PERSON><PERSON>est<PERSON><PERSON>"}, "weeds": {"categories": {"blossom": "Blomma", "blossom#description": "", "broadleaf": "Storbladig", "broadleaf#description": "", "fruit": "<PERSON><PERSON><PERSON>", "fruit#description": "", "grass": "<PERSON><PERSON><PERSON><PERSON>", "grass#description": "", "offshoot": "Sidoskott", "offshoot#description": "", "preblossom": "<PERSON><PERSON><PERSON> blo<PERSON>", "preblossom#description": "", "purslane": "Portlak", "purslane#description": "", "runner": "<PERSON><PERSON><PERSON><PERSON>", "runner#description": "", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknown#description": ""}, "weed#description": "", "weed_one": "<PERSON><PERSON><PERSON><PERSON>", "weed_other": "<PERSON><PERSON><PERSON><PERSON>"}}, "utils": {"actions": {"add": "<PERSON><PERSON><PERSON> till", "add#description": "", "addLong": "<PERSON><PERSON><PERSON> till {{subject}}", "addLong#description": "", "apply": "Tillämpa", "apply#description": "", "applyLong": "Tillämpa {{subject}}", "applyLong#description": "", "backLong": "till<PERSON><PERSON> till {{subject}}", "backLong#description": "", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel#description": "", "cancelLong": "Avbryt {{subject}}", "cancelLong#description": "", "clear": "Ren<PERSON>", "clear#description": "", "confirm": "Bekräfta", "confirm#description": "", "continue": "Fortsätt", "continue#description": "", "copy": "<PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON><PERSON> {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} skapat", "createdLong#description": "", "delete": "<PERSON><PERSON><PERSON>", "delete#description": "", "deleteLong": "Ta bort {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} raderade", "deletedLong#description": "", "disableLong": "Inaktivera {{subject}}", "disableLong#description": "", "discard": "<PERSON><PERSON><PERSON>", "discard#description": "", "edit": "Rediger<PERSON>", "edit#description": "", "editLong": "Redigera {{subject}}", "editLong#description": "", "enableLong": "Aktivera {{subject}}", "enableLong#description": "", "exit": "Utgång", "exit#description": "", "exitLong": "Utgång {{subject}}", "exitLong#description": "", "goToLong": "<PERSON><PERSON> till {{subject}}", "goToLong#description": "", "invite": "Bjud in", "invite#description": "", "inviteLong": "Bjud in {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} inbjuden", "invitedLong#description": "", "leaveUnchanged": "<PERSON><PERSON><PERSON><PERSON> oförändrad", "leaveUnchanged#description": "", "new": "Ny", "new#description": "", "newLong": "Ny {{subject}}", "newLong#description": "", "next": "<PERSON><PERSON><PERSON>", "next#description": "", "pause": "<PERSON><PERSON>", "pause#description": "", "play": "<PERSON><PERSON><PERSON>", "play#description": "", "previous": "Föregående", "previous#description": "", "ranLong": "{{subject}} körde", "ranLong#description": "", "reload": "Ladda om", "reload#description": "", "resetLong": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{subject}}", "resetLong#description": "", "retry": "Försök igen", "retry#description": "", "run": "<PERSON><PERSON><PERSON>", "run#description": "", "runLong": "<PERSON><PERSON><PERSON> {{subject}}", "runLong#description": "", "save": "Spara", "save#description": "", "saveLong": "Spara {{subject}}", "saveLong#description": "", "saved": "Sparad", "saved#description": "", "savedLong": "{{subject}} har sparats", "savedLong#description": "", "search": "S<PERSON><PERSON>ning", "search#description": "", "searchLong": "<PERSON><PERSON><PERSON> {{subject}}", "searchLong#description": "", "selectAll": "<PERSON><PERSON><PERSON><PERSON> alla", "selectAll#description": "", "selectLong": "V<PERSON><PERSON>j {{subject}}", "selectLong#description": "", "selectNone": "<PERSON><PERSON><PERSON><PERSON> ingen", "selectNone#description": "", "send": "<PERSON><PERSON><PERSON>", "send#description": "", "showLong": "Visa {{subject}}", "showLong#description": "", "submit": "Överlämna", "submit#description": "", "toggle": "<PERSON>ä<PERSON><PERSON>", "toggle#description": "", "toggleLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "toggleLong#description": "", "update": "Uppdatera", "update#description": "", "updated": "Uppdaterad", "updated#description": "", "updatedLong": "Uppdaterade {{subject}}", "updatedLong#description": "", "uploaded": "Uppladdad", "uploaded#description": "", "viewLong": "Visa {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Aktiv", "active#description": "", "critical": "Kritisk", "critical#description": "", "default": "Standard", "default#description": "", "degraded": "Försämrad", "degraded#description": "", "dense": "Tät", "dense#description": "", "disabled": "Inaktiverad", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Aktiverad", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "<PERSON><PERSON>", "error#description": "", "estopOff": "Aktiverat", "estopOff#description": "", "estopOn": "E-stoppad", "estopOn#description": "", "fast": "<PERSON><PERSON>bb", "fast#description": "", "few": "Få", "few#description": "", "good": "Bra", "good#description": "", "hidden": "<PERSON><PERSON>", "hidden#description": "", "high": "<PERSON><PERSON><PERSON>", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inaktiv", "inactive#description": "", "interlockSafe": "Skjutning tillåten", "interlockSafe#description": "", "interlockUnsafe": "Skjutning förbjuden", "interlockUnsafe#description": "", "large": "St<PERSON>", "large#description": "", "laserKeyOff": "<PERSON><PERSON><PERSON>", "laserKeyOff#description": "", "laserKeyOn": "Aktiverad", "laserKeyOn#description": "", "liftedOff": "S<PERSON>nkt", "liftedOff#description": "", "liftedOn": "Lyft", "liftedOn#description": "", "loading": "Belastning", "loading#description": "", "low": "<PERSON><PERSON><PERSON>", "low#description": "", "majority": "Majoritet", "majority#description": "", "medium": "Medium", "medium#description": "", "minority": "Minoritet", "minority#description": "", "name": "<PERSON><PERSON>", "name#description": "", "no": "Inga", "no#description": "", "none": "Ingen", "none#description": "", "offline": "Offline", "offline#description": "", "ok": "OK", "ok#description": "", "poor": "<PERSON><PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Framsteg", "progress#description": "", "serial": "Serie", "serial#description": "", "slow": "Långsam", "slow#description": "", "small": "Små", "small#description": "", "sparse": "<PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_one": "<PERSON><PERSON>", "type_other": "Typer", "unknown": "Okä<PERSON>", "unknown#description": "", "waterProtectNormal": "Normal fuktighet", "waterProtectNormal#description": "", "waterProtectTriggered": "Vatten upptäckt", "waterProtectTriggered#description": "", "yes": "<PERSON>a", "yes#description": ""}, "form": {"booleanType": "Måste vara en boolean", "booleanType#description": "", "copyConfigFrom": "Ko<PERSON>ra konfiguration från...", "copyConfigFrom#description": "", "integerType": "Måste vara ett heltal", "integerType#description": "", "maxLessThanMin": "<PERSON> måste vara större än min", "maxLessThanMin#description": "", "maxSize": "Kan inte överstiga {{limit}} tecken", "maxSize#description": "", "minGreaterThanMax": "<PERSON> måste vara mindre än max", "minGreaterThanMax#description": "", "moveDown": "<PERSON><PERSON> ner", "moveDown#description": "", "moveUp": "Flytta upp", "moveUp#description": "", "noOptions": "Inga alternativ", "noOptions#description": "", "numberType": "<PERSON><PERSON><PERSON> vara ett nummer", "numberType#description": "", "optional": "(frivillig)", "optional#description": "", "required": "Obligatoriskt", "required#description": "", "stringType": "Måste vara en sträng", "stringType#description": ""}, "lists": {"+3": "{{b}} och {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} och {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Ladda mer", "loadMore#description": "", "noMoreResults": "Inga fler resultat", "noMoreResults#description": "", "noResults": "Inga resultat", "noResults#description": ""}, "metrics": {"aggregates": {"max": "Max", "max#description": "", "min": "Min", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Genomsnittlig grödredie", "avgCropSizeMm#description": "", "avgSpeedMph": "Genomsnittlig reshastighet", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "Genomsnittlig filmningstid", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "Genomsnittlig filmningstid (oriktad)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Genomsnittlig ogräsradie", "avgWeedSizeMm#description": "", "bandingConfigName": "Bandning konfiguration", "bandingConfigName#description": "", "bandingEnabled": "Bandning", "bandingEnabled#description": "", "bandingPercentage": "Procent bandad", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Genomsnittlig täckningshastighet", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Gröddensitet", "cropDensitySqFt#description": "", "distanceWeededMeters": "Ogräsavstånd", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "<PERSON><PERSON><PERSON><PERSON>", "keptCrops#description": "", "killedWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "killedWeeds#description": "", "missedCrops": "Missade grödor", "missedCrops#description": "", "missedWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "missedWeeds#description": "", "notThinning": "Inte gallringsgrödor", "notThinning#description": "", "notWeeding": "Inte ogräsrensning", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Operatörens effektivitet", "operatorEffectiveness#description": "", "overallEfficiency": "Övergripande prestanda", "overallEfficiency#description": "", "skippedCrops": "<PERSON><PERSON><PERSON><PERSON> som <PERSON>s", "skippedCrops#description": "", "skippedWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> som ignorerats", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "<PERSON><PERSON><PERSON><PERSON> för <PERSON>", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "<PERSON><PERSON><PERSON> gr<PERSON><PERSON>", "thinnedCrops#description": "", "thinningEfficiency": "Gallringsprestanda", "thinningEfficiency#description": "", "timeEfficiency": "Driftseffektivitet", "timeEfficiency#description": "", "totalCrops": "<PERSON><PERSON><PERSON><PERSON> som hittats (totalt)", "totalCrops#description": "", "totalWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> hittat", "totalWeeds#description": "", "totalWeedsInBand": "<PERSON><PERSON><PERSON><PERSON><PERSON> (i band)", "totalWeedsInBand#description": "", "uptimeSeconds": "Upptid", "uptimeSeconds#description": "", "validCrops": "<PERSON><PERSON><PERSON><PERSON>", "validCrops#description": "", "weedDensitySqFt": "Ogrästäthet", "weedDensitySqFt#description": "", "weedingEfficiency": "Ogräsbekämpningsprestanda", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Ogräsbekämpningstid", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Ogrästyp: $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Ogrästyp: $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Ogrästyp: $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Ogrästyp: $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "<PERSON><PERSON> be<PERSON>nas före gallring om gallring har aktiverats", "avgCropSizeMm#description": "", "bandingConfigName": "<PERSON> senast valda bandningsprofil", "bandingConfigName#description": "", "crop": "<PERSON> senast valda g<PERSON>", "crop#description": "", "cropDensitySqFt": "<PERSON><PERSON> be<PERSON>nas före gallring om gallring har aktiverats", "cropDensitySqFt#description": "", "keptCrops": "Det uppskattade antalet grödor som behållits efter gallring", "keptCrops#description": "", "killedWeeds": "LaserWeeder identifierade objektet som ett ogräs och dödade det", "killedWeeds#description": "", "missedCrops": "Grödan markerades för gallring men missades. Vanliga orsaker är: för hö<PERSON> has<PERSON>, utom r<PERSON><PERSON><PERSON><PERSON> eller systemfel.", "missedCrops#description": "", "missedWeeds": "Ogräs identifierades men missades. Vanliga orsaker är: fö<PERSON> hö<PERSON>, utom r<PERSON><PERSON><PERSON><PERSON> eller <PERSON>fel.", "missedWeeds#description": "", "operatorEffectiveness": "<PERSON>r hur väl den faktiska körhastigheten stämmer överens med den målhastighet som rekommenderas av Velocity Estimator", "operatorEffectiveness#description": "", "overallEfficiency": "(og<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> + gallring) / 2, om du både rensar och gallrar", "overallEfficiency#description": "", "skippedCrops": "Grödan hoppades avsiktligt över under gallringen. Vanliga orsaker är: inaktiverad i Quick Tune, utanför bandet eller driptape i närheten.", "skippedCrops#description": "", "skippedWeeds": "Ogräs hoppades över med avsikt. Vanliga orsaker är: inaktiverad i  Quick Tune, eller utanför bandet.", "skippedWeeds#description": "", "thinningEfficiency": "(<PERSON><PERSON><PERSON> grödor + bevar<PERSON> grödor) / Be<PERSON>äk<PERSON> funna grödor × 100 %", "thinningEfficiency#description": "", "timeEfficiency": "(Aktiv arbetstid / Strömförsörjningstid) x 100 %", "timeEfficiency#description": "", "uptimeSeconds": "Den totala tiden som LaserWeeder var påslagen. Inklusive när den är i standby-läge och/eller upplyft.", "uptimeSeconds#description": "", "weedDensitySqFt": "Uppskat<PERSON> mängd ogräs som hittats (totalt) / täckning", "weedDensitySqFt#description": "", "weedingEfficiency": "(<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> / ogr<PERSON><PERSON> hittat i bandet) × 100 %", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Den tid som LaserWeeder aktivt rensade ogr<PERSON><PERSON> eller gallrade", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Effektiv hastighet", "operatorEffectiveness#description": "", "timeEfficiency": "Maskinanvändning", "timeEfficiency#description": "", "totalWeeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> hittat (totalt)", "totalWeedsInBand": "<PERSON><PERSON><PERSON><PERSON><PERSON> (inom band)", "totalWeedsInBand#description": "", "uptimeSeconds": "Strömförsörjningstid", "uptimeSeconds#description": "", "validCrops": "Beräknade gr<PERSON><PERSON>na", "validCrops#description": "", "weedingUptimeSeconds": "Aktiv arbetstid", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "Omfattning", "coverage#description": "", "field": "<PERSON><PERSON><PERSON>", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Prestanda", "performance#description": "", "speed": "<PERSON><PERSON><PERSON><PERSON>", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Användande", "usage#description": ""}, "metric#description": "", "metric_one": "mätvärde", "metric_other": "mätvärden", "spatial": {"heatmapWarning": "per ~{{area}} \"blockering\"", "heatmapWarning#description": "", "metrics": {"altitude": "<PERSON><PERSON><PERSON><PERSON> havet", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "E-Stopp", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "<PERSON><PERSON><PERSON><PERSON>", "interlock#description": "", "keptCropDensity": "<PERSON><PERSON><PERSON><PERSON> gr<PERSON><PERSON><PERSON>", "keptCropDensity#description": "", "laserKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Målhastighetskvot", "speed#description": "", "speedTargetMinimum": "Genomsnittlig målhastighet (minimum)", "speedTargetMinimum#description": "", "speedTargetRow1": "Genomsnittlig målhastighet (rad 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Genomsnittlig målhastighet (rad 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Genomsnittlig målhastighet (rad 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Genomsnttlig målhastighet", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "<PERSON><PERSON><PERSON>", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "Tid", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Vattenskydd", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "Ogrä<PERSON>ren<PERSON>ning", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Vald", "selected#description": "", "showAll": "Visa alla {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/tunnland", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/hektar", "/ha#description": "", "/in2": "tum²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "<PERSON>", "W#description": "", "WLong#description": "", "WLong_one": "watt", "WLong_other": "watt", "ac": "tunnland", "ac#description": "", "ac/h": "tunnland/timme", "ac/h#description": "", "acLong#description": "", "acLong_one": "tunnland", "acLong_other": "tunnland", "acres#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "d": "d", "d#description": "", "dLong#description": "", "dLong_one": "dag", "dLong_other": "dagar", "day#description": "", "days#description": "", "ft": "fot", "ft#description": "", "ft/s": "fot/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_one": "fot", "ftLong_other": "fot", "h": "t", "h#description": "", "hLong#description": "", "hLong_one": "timma", "hLong_other": "timmar", "ha": "<PERSON>ktar", "ha#description": "", "ha/h": "hektar/timme", "ha/h#description": "", "haLong#description": "", "haLong_one": "<PERSON>ktar", "haLong_other": "<PERSON>ktar", "hectares#description": "", "hours#description": "", "in": "i", "in#description": "", "in2": "tum²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/t", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_one": "meter", "mLong_other": "meter", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_one": "minut", "minLong_other": "minuter", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "<PERSON><PERSON><PERSON><PERSON>", "month#description": "", "monthLong#description": "", "monthLong_one": "<PERSON><PERSON><PERSON><PERSON>", "monthLong_other": "<PERSON><PERSON><PERSON><PERSON>", "mph": "mph", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_one": "sekund", "sLong_other": "<PERSON><PERSON>nder", "seconds#description": "", "watts#description": "", "week": "v", "week#description": "", "weekLong#description": "", "weekLong_one": "vecka", "weekLong_other": "veckor", "yd#description": "", "year": "<PERSON>r", "year#description": "", "yearLong#description": "", "yearLong_one": "<PERSON>r", "yearLong_other": "<PERSON>r"}}, "views": {"admin": {"alarms": {"allowWarning": "Genom att lägga till koder till godkännandelistan kommer larm att pinga stöd för Slack-kanaler", "allowWarning#description": "", "blockWarning": "Att lägga till koder till blockeringslistan kommer att för<PERSON><PERSON> larm för att pinga stöd för Slack-kanaler", "blockWarning#description": "", "lists": "Listor", "lists#description": "", "title": "Global larmtillåtelselista", "title#description": "", "titleAllow": "Tillståndslista för larm", "titleAllow#description": "", "titleBlock": "Larmblockeringslista", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "<PERSON><PERSON>", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> on <PERSON>, <tt>{row1,row2,row3}</tt> on Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "operation_other": "åtgärder", "operationsCount": "Åtgärder ({{count}})", "operationsCount#description": "", "operationsHint": "Välj en nod i konfigurationsschemat fär att lägga till en årgärd.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_one": "hittade {{count}} fel", "encounteredErrors_other": "hittade {{count}} fel", "noChanges": "inga <PERSON>", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_one": "uppdaterade {{count}} inställning", "updatedKeys_other": "uppdaterade  {{count}} inställningar"}, "outcomes": {"failure": "<PERSON><PERSON><PERSON><PERSON>", "failure#description": "", "partial": "Lyckades delvis", "partial#description": "", "success": "Lyckades", "success#description": ""}, "title": "Bulk Configs", "title#description": ""}, "clearCaches": {"action": "Uppdatera cache", "action#description": "", "description": "problem? <PERSON><PERSON><PERSON><PERSON><PERSON> att uppdatera Robot Syncer-cachen först.", "description#description": ""}, "warnings": {"global": "Om du ändrar denna konfiguration kommer det att påverka standardinställningarna och rekommendationerna för alla nuvarande och framtida {{class}}", "global#description": "", "notSimon": "Du är inte <PERSON>, så du borde förmodligen inte redigera det här... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Följande ändringar har ännu inte synkroniserats till {{serial}}:", "description#description": "", "title": "Osynkroniserade nycklar", "title#description": ""}}}, "portal": {"clearCaches": {"action": "Rensa cacher", "action#description": "", "description": "Rensar de interna cacherna för Ops Center. Detta **kommer** att sakta ner vissa frågor på kort sikt men **kan** rätta till problem med inaktuella data som fastnat", "description#description": "", "details": "Tryck på detta om du manuellt har redigerat en användares behörigheter i Auth0 (inte via Ops Center) eller gjort ändringar i en tredjepartsintegration som Stream eller Slack som inte återspeglas.", "details#description": ""}, "title": "Ops-Center", "title#description": "", "warnings": {"global": "Alternativen på den här sidan kommer att påverka driften av Ops Center i produktion.", "global#description": "", "notPortalAdmin": "Du är inte An<PERSON> eller <PERSON>, så du borde förmodligen inte redigera detta... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "<PERSON><PERSON><PERSON>-kanale<PERSON> bör bö<PERSON><PERSON> med \"#\": t.ex. \"#support-001-carbon\"", "supportSlackLeadingHash#description": ""}}, "title": "Administration", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "<PERSON><PERSON><PERSON><PERSON>", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Tilldela till traktorer", "orchestrateView#description": "", "showPivotHistory": "Visa pivothistorik", "showPivotHistory#description": ""}, "fetchFailed": "Det gick inte att läsa in platsdata", "fetchFailed#description": "", "goLive": "liveuppdatering", "goLive#description": "", "hideRows": "<PERSON><PERSON><PERSON><PERSON>", "hideRows#description": "", "jobDetails": {"assignmentsFailed": "<PERSON>lyckades med att hämta uppgift, försöka igen?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Jobbet kommer inte längre att kunna tilldelas traktorer och måste återskapas.", "description#description": ""}, "customer": {"unknown": "Kund okänd", "unknown#description": "", "withName": "Kund: {{name}}", "withName#description": ""}, "farm": {"unknown": "<PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Gård: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON><PERSON><PERSON>", "unknown#description": "", "withName": "Fält: {{name}}", "withName#description": ""}, "jobFinished": "<PERSON>b avs<PERSON>ades klockan {{time}}", "jobFinished#description": "", "jobStarted": "<PERSON>b startade klockan {{time}}", "jobStarted#description": "", "openInFarmView": "Öppna i gårdsvy", "openInFarmView#description": "", "state": "Status: {{state}}", "state#description": "", "type": "Jobbtyp: {{type}}", "type#description": ""}, "lastPolled": "Senast undersökt", "lastPolled#description": "", "live": "Direktuppdatering", "live#description": "", "objectiveFromOtherJob": "<PERSON><PERSON><PERSON> från ett annat jobb", "objectiveFromOtherJob#description": "", "rowWidthUnits": "Radbredd  {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Gårdar", "farms#description": "", "tractors": "Traktorer", "tractors#description": ""}, "showRows": "Visa rader", "showRows#description": "", "stalePivots": "Pivotinformation kan vara inaktuell", "stalePivots#description": "", "suggestedAssignments": "Föreslagna uppgifter", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"hideThesePoints": "<PERSON><PERSON><PERSON><PERSON>", "hideThesePoints#description": "", "onlyShowSelected": "Visa endast valda", "onlyShowSelected#description": "", "showAllPoints": "Visa alla punkter", "showAllPoints#description": "", "showThesePoints": "Visa dessa punkter", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "<PERSON><PERSON><PERSON><PERSON>", "boundary#description": "", "center": "<PERSON><PERSON>", "center#description": "", "centerPivot": "Mittpivot", "centerPivot#description": "", "endpointId": "<PERSON> för slutpunkt", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON>", "holes#description": "", "length": "Längd", "length#description": "", "plantingHeading": "Plantornas riktning", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "<PERSON><PERSON>", "points#description": "", "width": "Bredd", "width#description": ""}, "farm": "<PERSON><PERSON><PERSON>", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Ingen fix", "none#description": "", "rtkFixed": "RTK fixat", "rtkFixed#description": "", "rtkFloat": "RTK Flytande", "rtkFloat#description": "", "unknown": "Okänd fixtyp", "unknown#description": ""}, "selectionPanel": {"allPoints": "<PERSON>a punkter", "allPoints#description": "", "boundary": "<PERSON><PERSON><PERSON><PERSON>", "boundary#description": "", "center": "Centrum", "center#description": "", "centerPivot": "Mittpunkt", "centerPivot#description": "", "endpointId": "Slutpunkts-ID", "endpointId#description": "", "holes": "<PERSON><PERSON><PERSON>", "holes#description": "", "length": "Längd", "length#description": "", "plantingHeading": "Plantornas riktning", "plantingHeading#description": "", "point": "<PERSON><PERSON>", "point#description": "", "points": "<PERSON><PERSON>", "points#description": "", "width": "Bredd", "width#description": ""}, "unnamedPoint": "<PERSON><PERSON><PERSON><PERSON><PERSON>t <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "G<PERSON>rdsgrä<PERSON>", "farmBoundary#description": "", "field": "<PERSON><PERSON><PERSON>", "field#description": "", "headland": "<PERSON><PERSON><PERSON>", "headland#description": "", "obstacle": "Hinder", "obstacle#description": "", "privateRoad": "Privat väg", "privateRoad#description": "", "unknown": "Okänd zontyp", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "Linjen ska ha exakt två punkter", "exactlyTwoPoints#description": "", "wrongFieldType": "Fältet \"{{field}}\" ska vara {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "Geomet<PERSON> bör vara av typen {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON ska vara ett objekt", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Inga robotar online", "empty#description": ""}, "title": "Övervakning", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Se ändringshistorik", "open#description": "", "title": "Ändringshistorik", "title#description": ""}, "errors": {"failed": "Det gick inte att läsa in konfigurationsträdet", "failed#description": ""}, "onlyChanged": "Visa endast ändrat", "onlyChanged#description": ""}, "errors": {"empty": "<PERSON>ga robotar tilldelade", "empty#description": ""}, "hardware": {"errors": {"old": "Robot rapporterar inte datorserier (förmodligen för gammal)", "old#description": ""}, "fields": {"hostname": "Värdnamn", "hostname#description": ""}, "installedVersion": "Installerad version:", "installedVersion#description": "", "ready": {"name": "Programuppdateringsstatus", "name#description": "", "values": {"false": "Laddar ned...", "false#description": "", "installed": "Installerad", "installed#description": "", "true": "Redo!", "true#description": ""}}, "tabs": {"computers": "Da<PERSON><PERSON>", "computers#description": "", "versions": "Versioner", "versions#description": ""}, "targetVersion": "Målversion:", "targetVersion#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": "", "updateHistory": "Versionsuppdateringshistorik <0>kommer snart™️</0>", "updateHistory#description": ""}, "history": {"borders": "Gränser", "borders#description": "", "errors": {"invalidDate": "<PERSON><PERSON><PERSON><PERSON> ett giltigt datumintervall", "invalidDate#description": "", "noJobs": "Inga jobb har rapporterats i valt intervall", "noJobs#description": "", "noMetrics": "Inga mätvärden har rapporterats", "noMetrics#description": ""}, "moreMetrics": "Se fler mät<PERSON>rden", "moreMetrics#description": "", "navTitle": "Historia", "navTitle#description": "", "placeholder": "<PERSON><PERSON><PERSON><PERSON> ett jobb eller datum för att se data", "placeholder#description": "", "points": "Poäng", "points#description": "", "warnings": {"beta": {"description": "Mätvärden som väntar på validering visas i blått", "description#description": ""}, "ongoing": "Mätvärden för detta datum är inte slutgiltiga ännu", "ongoing#description": ""}}, "status": "Status", "status#description": "", "summary": {"banding": {"definition": "Definition", "definition#description": "", "dynamic": "Dynamisk", "dynamic#description": "", "dynamicDisabled": "(Dynamisk bandning inaktiverad i konfiguration)", "dynamicDisabled#description": "", "rows": "<PERSON><PERSON>", "rows#description": "", "static": "Statisk", "static#description": "", "type": "<PERSON><PERSON>", "type#description": "", "unknown": "Okänd bandning", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Version", "version#description": ""}, "config": {"changes#description": "", "changes_one": "{{count}} Almanackaändring", "changes_other": "{{count}} Almanackaändringar", "cpt": "Tröskelvärde för grödpunkt", "cpt#description": "", "default": "(STANDARD: {{value}})", "default#description": "", "wpt": "Tröskelvärde för ogräsp<PERSON>t", "wpt#description": ""}, "encoders": {"backLeft": "Tillbaka Vänster", "backLeft#description": "", "backRight": "Tillba<PERSON> höger", "backRight#description": "", "frontLeft": "Fram vänster", "frontLeft#description": "", "frontRight": "<PERSON><PERSON>", "frontRight#description": "", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Det gick inte att läsa in robotöversikten", "failed#description": "", "lasers": {"disabled#description": "", "disabled_one": "{{count}} Inaktiverad laser", "disabled_other": "{{count}} <PERSON><PERSON><PERSON><PERSON> las<PERSON>", "row": "Rad {{row}}", "row#description": ""}, "machineHealth": "Maskinhälsa", "machineHealth#description": "", "navTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navTitle#description": "", "safetyRadius": {"driptape": "Dropptejp", "driptape#description": "", "title": "Säkerhetsradie", "title#description": ""}, "sections": {"management": "Förvaltning", "management#description": "", "software": "<PERSON><PERSON><PERSON>", "software#description": ""}, "supportLinks": {"chipChart": "Bildfragment", "chipChart#description": "", "datasetVisualization": "Visualisering av dataset", "datasetVisualization#description": "", "title": "Supportlänkar", "title#description": ""}}, "support": {"carbon": "Carbon-support", "carbon#description": "", "chatMode": {"legacy": "<PERSON><PERSON><PERSON> chatt", "legacy#description": "", "new": "<PERSON><PERSON> chatt", "new#description": ""}, "errors": {"failed": "Det gick inte att läsa in meddelandet", "failed#description": "", "old": {"description": "{{serial}} kör <PERSON> {{version}}. <PERSON><PERSON><PERSON> vara {{target}} för att använda supportchatt.", "description#description": "", "title": "Otillräcklig robotversion", "title#description": ""}}, "localTime": "Lokal tid: {{time}}", "localTime#description": "", "navTitle": "Support", "navTitle#description": "", "toCarbon": "Meddelande till $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Meddelande till $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} körs offline. Operatören kommer inte att få meddelande förrän roboten har anslutning.", "description#description": "", "title": "Robot offline", "title#description": ""}}}, "toggleable": {"internal": "Inre", "internal#description": ""}, "uploads": {"errors": {"empty": "Inga uppladdningar", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "Filternamn", "name#description": "", "otherRobots": "<PERSON><PERSON> robotar ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "<PERSON><PERSON><PERSON>", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_one": "filtrera", "fleetView_other": "filter", "tableOnly": "Vissa kolumner är endast tillgängliga i tabellvy", "tableOnly#description": ""}}, "knowledge": {"title": "Kunskapsbas", "title#description": ""}, "metrics": {"jobStatus": {"closed": "Avslutat", "closed#description": "", "description": "<PERSON><PERSON><PERSON><PERSON>", "description#description": "", "open": "<PERSON><PERSON><PERSON>", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Beräknade mätvärden för fält", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Vår modell använder experimentella uppgifter om grödor som kan innehålla felaktigheter. Vi arbetar kontinuerligt med att förbättra dess tillförlitlighet.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Prestanda- och maskinmätning", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "<PERSON><PERSON> filer hit från <PERSON> (eller någon annanstans)", "drop#description": "", "file#description": "", "file_one": "fil", "file_other": "filer", "ingestDescription": "Carbon-anställda bör använda Ingest-tjänsten", "ingestDescription#description": "", "ingestLink": "Ladda upp till Ingest", "ingestLink#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": "", "title": "Uppladdning", "title#description": "", "upload": "Ladda upp till Carbon", "upload#description": "", "uploading": "Laddar upp {{subject}}...", "uploading#description": ""}, "reports": {"explore": {"graph": "Diagram", "graph#description": "", "groupBy": "Gruppera efter", "groupBy#description": "", "title": "Utforska", "title#description": ""}, "scheduled": {"authorCarbonBot": "<PERSON><PERSON><PERSON>", "authorCarbonBot#description": "", "authorUnknown": "<PERSON><PERSON>nd författare", "authorUnknown#description": "", "automation": {"customerReports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerReports#description": "", "errorTitle": "Ogiltig automatiserad rapport", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Ingen kund har valts", "none#description": ""}}, "reportDay": {"errors": {"none": "Ingen dag har valts", "none#description": ""}, "name": "Rapporteringsdag", "name#description": ""}, "reportEmails": {"errors": {"none": "Inga e-postmeddelanden tilldelade", "none#description": ""}, "name": "Kund-epostmeddelanden", "name#description": ""}, "reportHour": {"errors": {"none": "Ingen timme har valts", "none#description": ""}, "name": "Rapporteringstimma", "name#description": ""}, "reportLookback": {"errors": {"none": "Ingen tillbakablick definierad", "none#description": ""}, "name": "Rapportera tillbakablick", "name#description": ""}, "reportTimezone": {"errors": {"none": "Ingen tidszon har valts", "none#description": ""}, "name": "Rapportera tidszon", "name#description": ""}, "warningDescription": "Den kö<PERSON> varje {{day}} vid {{hour}} i {{timezone}} med {{lookback}} da<PERSON><PERSON> tillbakablick för alla aktiva {{customer}} robotar.", "warningDescription#description": "", "warningTitle": "Detta är en automatisk rapport!", "warningTitle#description": ""}, "byline": "Av {{author}}", "byline#description": "", "editor": {"columnsHidden": "<PERSON><PERSON>", "columnsHidden#description": "", "columnsVisible": "Synliga kolumner", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_one": "Varning: det finns en annan rapport med detta namn", "duplicateNames_other": "Varning: det finns {{count}} andra rapporter med detta namn", "fields": {"automateWeekly": "Automatiser<PERSON> veckovis", "automateWeekly#description": "", "name": "Rapportera namn", "name#description": "", "showAverages": "Visa medelvärden", "showAverages#description": "", "showTotals": "Visa totala värden", "showTotals#description": ""}}, "errors": {"noReport": "Rapporten finns inte eller också har du inte åtkomst", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} kommer att raderas permanent.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "Du är inte behörig att ta bort {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "E-post kommer inte att skickas igen", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Rapport kommer att skickas till dessa e-postadresser", "publishEmailsHelperNew#description": ""}, "runAgain": "<PERSON><PERSON><PERSON>", "runAgain#description": ""}, "table": {"errors": {"noColumns": "<PERSON><PERSON><PERSON><PERSON> en eller flera kolumner", "noColumns#description": "", "noEndDate": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "noEndDate#description": "", "noRobots": "V<PERSON><PERSON>j en eller flera robotar", "noRobots#description": "", "noStartDate": "<PERSON><PERSON><PERSON><PERSON>", "noStartDate#description": ""}, "fields": {"average": "Genomsnitt", "average#description": "", "averageShort": "Genomsnitt", "averageShort#description": "", "date": "Datum", "date#description": "", "group": "Serie/datum", "group#description": "", "groupJob": "Serie/jobb", "groupJob#description": "", "mixed": "(<PERSON><PERSON><PERSON>)", "mixed#description": "", "total": "Summa", "total#description": "", "totalShort": "BELOPP", "totalShort#description": ""}, "unknownReport": "Okänd rapport", "unknownReport#description": ""}, "title": "Schemalagd", "title#description": "", "toLine": "f<PERSON>r {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "Alla mät<PERSON>", "all#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": ""}, "robotsLabel": {"all": "<PERSON>a robotar", "all#description": "", "none": "Inga robotar", "none#description": "", "select": "<PERSON><PERSON><PERSON><PERSON>", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> via <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "användarnamn och lösenord", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "okänd leverantör", "unknown#description": ""}, "cards": {"account": "Ko<PERSON>", "account#description": "", "advanced": "Avancerad", "advanced#description": "", "localization": "Lokalisering", "localization#description": ""}, "delete": {"deleteAccount": "Ta bort konto", "deleteAccount#description": "", "dialog": {"description": "VARNING: <PERSON><PERSON> kan inte ång<PERSON>. All data kommer att gå förlorad.", "description#description": ""}}, "fields": {"experimental": "Aktivera experimentella funktioner", "experimental#description": "", "language": "Språk", "language#description": "", "measurement": {"name": "Måttenheter", "name#description": "", "values": {"imperial": "Imperial (in, mph, acres, fahrenheit)", "imperial#description": "", "metric": "Metriskt (mm, km/h, hektar, celsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "Logga ut", "logOut#description": "", "title": "Inställningar", "title#description": "", "version": "Carbon Ops Center-version {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "Anv<PERSON><PERSON><PERSON> finns inte, eller så har du inte behörighet att se dem.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "Hantera användare i Auth0", "manage#description": "", "title": "Administration", "title#description": ""}, "permissions": {"title": "Roll och beh<PERSON><PERSON><PERSON>ter", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Entreprenörer", "contractors#description": ""}}}}