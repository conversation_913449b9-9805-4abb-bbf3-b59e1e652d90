{"components": {"AlarmTable": {"export": "{{robots}} Historique des alarmes {{date}}", "export#description": ""}, "BetaFlag": {"spatial": {"description": "Les métriques spatiales sont disponibles gratuitement pendant l’évaluation, mais peuvent faire l’objet d’une modification, d’une suppression ou d’une mise à niveau à tout moment. Les données doivent être vérifiées de manière indépendante.", "description#description": "", "title": "Métriques spatiales Bêta", "title#description": ""}, "tooltip": "Cette fonctionnalité est en cours d’évaluation et peut faire l’objet de modifications ou de suppressions à tout moment", "tooltip#description": ""}, "Chat": {"errors": {"failed": "Impossible de charger le chat: {{message}}", "failed#description": ""}, "machineTranslated": "Traduit automatiquement", "machineTranslated#description": "", "machineTranslatedFrom": "Traduit automatiquement depuis {{language}}", "machineTranslatedFrom#description": "", "messageDeleted": "Ce message a été supprimé.", "messageDeleted#description": ""}, "ConfirmationDialog": {"delete": {"description": "{{subject}} va être supprimé définitivement.", "description#description": "", "descriptionActive": "{{subject}} est actif et ne peut donc pas être supprimé.", "descriptionActive#description": ""}, "title": "Êtes-vous sûr ?", "title#description": ""}, "CopyToClipboardButton": {"click": "Cliquez pour copier", "click#description": "", "copied": "Copié !", "copied#description": ""}, "CropEditor": {"failed": "Le chargement de l'éditeur de culture a échoué", "failed#description": "", "viewIn": "Afficher sur Veselka", "viewIn#description": ""}, "DateRangePicker": {"clear": "<PERSON><PERSON><PERSON><PERSON>", "clear#description": "", "endDate": "Date de fin", "endDate#description": "", "error": "<PERSON><PERSON><PERSON> de sélecteur de plage de dates", "error#description": "", "invalid": "Invalide", "invalid#description": "", "last7days": "7 derniers jours", "last7days#description": "", "lastMonth": "Le mois dernier", "lastMonth#description": "", "lastWeek": "La semaine dernière", "lastWeek#description": "", "minusDays": "Il y a {{days}} jours", "minusDays#description": "", "plusDays": "dans {{days}} jours", "plusDays#description": "", "startDate": "Date de début", "startDate#description": "", "thisMonth": "Ce mois-ci", "thisMonth#description": "", "thisWeek": "<PERSON><PERSON> se<PERSON>", "thisWeek#description": "", "today": "<PERSON><PERSON><PERSON>'hui", "today#description": "", "tomorrow": "<PERSON><PERSON><PERSON>", "tomorrow#description": "", "yesterday": "<PERSON>er", "yesterday#description": ""}, "EnvironmentFlag": {"beta": "BETA", "beta#description": "", "dev": "DEV", "dev#description": ""}, "ErrorBoundary": {"error": "<PERSON><PERSON><PERSON><PERSON>, erreur inattendue", "error#description": "", "queryLimitReached": "Rendu d'un ensemble de données partiel car trop de données ont été renvoyées. Contacter l'équipe d'assistance pour obtenir de l'aide", "queryLimitReached#description": ""}, "FeedbackDialog": {"comment": "Que s'est-il passé ?", "comment#description": "", "feedback": "Commentaires", "feedback#description": "", "submit": "Soumettre et recharger", "submit#description": ""}, "GdprConsent": {"description": "Veuillez vérifier et accepter pour continuer", "description#description": "", "statement": "J'accepte les <0>conditions d'utilisation</0> et <1>la politique de confidentialité</1>", "statement#description": "", "title": "Conditions d'utilisation et politique de confidentialité", "title#description": ""}, "InviteUser": {"errors": {"customerRequired": "Client requis", "customerRequired#description": ""}}, "JobSummary": {"multiDay": "{{startDate}} - {{endDate}}", "multiDay#description": "", "singleDay": "{{date}} {{startTime}} - {{endTime}}", "singleDay#description": ""}, "KeyboardShortcutsDialog": {"help": "Basculer ce menu", "help#description": "", "title": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "title#description": ""}, "LaserTable": {"export": "{{robots}} Lasers {{date}}", "export#description": "", "installedOnly": "Installé uniquement", "installedOnly#description": "", "warnings": {"duplicate": "Ce robot possède plusieurs lasers enregistrés dans le(s) logement(s) suivant(s) : {{slots}}", "duplicate#description": "", "emptySlot": "Ce robot n'a pas de laser enregistré dans le(s) logement(s) suivant(s) : {{slots}}", "emptySlot#description": ""}}, "ListManager": {"new": "Nouveau code", "new#description": ""}, "Loading": {"failed": "Désolé, échec du chargement par le Centre des Opérations de Carbon", "failed#description": "", "placeholder": "Téléchargement...", "placeholder#description": ""}, "ModelName": {"warning": "Attention : modèle à faible fiabilité", "warning#description": ""}, "PendingActivationOverlay": {"description": "Nous activons votre compte. Vous recevrez un e-mail lorsque ceci sera terminé !", "description#description": "", "errors": {"carbon": {"description": "L’e-mail Carbon a été détecté mais n’a pas été vérifié en raison de la connexion par nom d’utilisateur/mot de passe. Déconnectez-vous et utilisez l’option « Se connecter avec Google » pour être activé automatiquement.", "description#description": "", "title": "Compte carbon non vérifié", "title#description": ""}}, "hi": "Bonjour {{name}}!", "hi#description": "", "logOut": "Vous vous êtes connecté avec le mauvais compte ? <0>Déconnexion</0>.", "logOut#description": "", "title": "En attente d'activation", "title#description": ""}, "ResponsiveSubnav": {"more": "Plus", "more#description": ""}, "RobotImplementationSelector": {"status": "État de mise en œuvre", "status#description": "", "title": "Modifier l'état de mise en œuvre", "title#description": "", "warning": "La modification de l’état de la mise en œuvre peut déclencher des flux de travail automatisés affectant l’expérience client. NE LE FAITES PAS SI VOUS N’ÊTES PAS SÛR !", "warning#description": ""}, "ShowLabelsButton": {"text": "Étiquettes", "text#description": "", "tooltip": "Afficher les étiquettes", "tooltip#description": ""}, "ShowMetadataButton": {"tooltip": "Afficher les métadonnées", "tooltip#description": ""}, "almanac": {"crops": {"new": "Ajouter nouvelle culture", "new#description": "", "none": "Aucune catégorie de culture", "none#description": "", "sync#description": ""}, "cropsSynced": "Toutes les cultures", "cropsSynced#description": "", "delete": {"description": "<PERSON>ci ne peut pas être effectué", "description#description": ""}, "discard": {"description": "Ignorer les modifications à {{title}} ?", "description#description": "", "title": "Ignorer les modifications ?", "title#description": ""}, "fineTuneDescription": "La valeur par défaut est 5, peut diminuer ou augmenter la durée de tir du laser de ~20 % par incrément", "fineTuneDescription#description": "", "fineTuneTitle": "Multiplicateur d’ajustement fin", "fineTuneTitle#description": "", "formulas": {"all": "Toutes les dimensions", "all#description": "", "copyFormula": "Copier la formule", "copyFormula#description": "", "copySize": "Copier les dimensions", "copySize#description": "", "exponent": {"description": "Augmente le rayon en mm par rapport à cet exposant", "description#description": "", "label": "Exposant (e)", "label#description": ""}, "fineTuneMultiplier": {"description": "Nombre de 1 à 10, la valeur par défaut est 5, peut diminuer ou augmenter la durée de tir laser de ~20% par incrément. Il s'agit du nombre utilisé en mode de base", "description#description": "", "label": "Indice de réglage fin (FI)", "label#description": ""}, "fineTuneMultiplierVal": {"description": "Multiplicateur global pour l’incrémentation / décrémentation de l’indice de réglage fin", "description#description": "", "label": "Valeur de multiplicateur de réglage fin (FM)", "label#description": ""}, "laserTime": "Du<PERSON>e laser", "laserTime#description": "", "maxTime": {"description": "Limite de la durée de tir en ms", "description#description": "", "label": "<PERSON><PERSON><PERSON> max", "label#description": ""}, "multiplier": {"description": "Multiplie par rapport au rayon en mm", "description#description": "", "label": "Multiplicateur (A)", "label#description": ""}, "offset": {"description": "Nombre de millisecondes à ajouter quel que soit le rayon", "description#description": "", "label": "<PERSON><PERSON><PERSON><PERSON> (b)", "label#description": ""}, "pasteFormula": "Coller la formule", "pasteFormula#description": "", "pasteSize": "Coller les dimensions", "pasteSize#description": "", "sync": "Synchroniser toutes les dimensions", "sync#description": "", "thresholds": "Seuils de dimensions", "thresholds#description": "", "title": "Formules", "title#description": ""}, "protected#description": "", "switchModeAdvanced": "Passe rau mode avancé", "switchModeAdvanced#description": "", "switchModeBasic": "Passer au mode de base", "switchModeBasic#description": "", "warnings": {"admin": "La modification de cet almanach sera synchronisée avec toutes les unités de production actuelles et futures.", "admin#description": "", "carbon": "Il s’agit d’un almanach fourni par Carbon. Seul l’index de réglage fin peut être modifié", "carbon#description": "", "production": "Cet almanach est en cours d'exécution sur un robot. L’édition de celui-ci prendra effet immédiatement sur le terrain.", "production#description": ""}, "weeds": {"new": "Ajouter une nouvelle mauvaise herbe", "new#description": "", "none": "Aucune catégorie de mauvaise herbe", "none#description": "", "sync#description": ""}, "weedsSynced": "Toutes les mauvaises herbes", "weedsSynced#description": ""}, "categoryCollectionProfile": {"actions": {"savedLong": "{{subject}} sauvegardé. Activer via l'application opérateur Quick Tune", "savedLong#description": "", "testResults": "Aperçu des résultats", "testResults#description": ""}, "filters": {"capturedAt": "Date de capture", "capturedAt#description": "", "diameter": "<PERSON><PERSON><PERSON><PERSON>", "diameter#description": "", "filters#description": "", "unappliedFilters": "", "unappliedFilters#description": ""}, "images": {"allImages": "Toutes les images", "allImages#description": "", "categorized": "Catégor<PERSON>é", "categorized#description": "", "scrollToTop": "Retour en haut de page", "scrollToTop#description": "", "sortBy": {"latest": "Le plus récent", "latest#description": ""}, "sortedBy": "Trié par : {{sortBy}}", "sortedBy#description": ""}, "session": {"error": "Erreur dans l'obtention du statut", "error#description": "", "ready": "Les résultats sont prêts", "ready#description": "", "session#description": "", "session_many": "", "session_one": "Session", "session_other": "Sessions", "showResults": "Afficher les résultats", "showResults#description": "", "status": "traitement de{{processed}} / {{total}} résultats", "status#description": "", "statusLong": "", "statusLong#description": ""}, "session#description": "", "warnings": {"admin": "La modification de ce profil de plante sera synchronisée avec toutes les unités de production actuelles et futures.", "admin#description": "", "adminMeta": "Tous les profils administrateurs seront accessibles à tous les clients. Ne créez pas de désordre !", "adminMeta#description": "", "production": "Ce profil de plante est en cours d'exécution sur un robot. L'opérateur sera informé des mises à jour et pourra choisir d'utiliser les dernières modifications.", "production#description": "", "protected": "Ce profil est fourni par carbon. Aucune modification n'est possible.", "protected#description": "", "unsavedChanges": "Modifications non sauvegardées. Appuyez sur Enregistrer pour appliquer les modifications.", "unsavedChanges#description": ""}}, "config": {"changedKey#description": "", "changedKey_many": "", "changedKey_one": "Clé modifiée", "changedKey_other": "Clés modifiées", "newKey": "nouveau {{key}} nom", "newKey#description": "", "stringReqs": "Peut uniquement contenir a-z, 0-9, ., et _", "stringReqs#description": "", "warnings": {"keyExtra": {"description": "Cette clé a été ajoutée à la clé par défaut.", "description#description": ""}, "keyMissing": {"description": "<PERSON>é<PERSON><PERSON>(s) manquant(s) : {{keys}}", "description#description": ""}, "valueChanged": {"description": "Cette valeur a été modifiée par rapport à sa valeur par défaut ({{default}})", "description#description": "", "title": "Configuration modifiée", "title#description": ""}}}, "customers": {"CustomerEditor": {"errors": {"load": "Le chargement de l'éditeur client a échoué", "load#description": ""}}, "CustomerSelector": {"empty": "Non assigné", "empty#description": "", "title": "Changer Client", "title#description": ""}}, "discriminator": {"configs": {"avoid": {"description": "Tirer vs ignorer", "description#description": "", "label": "<PERSON><PERSON><PERSON>", "label#description": ""}, "copy": "<PERSON><PERSON><PERSON> Configs", "copy#description": "", "ignorable": {"description": "Tirer uniquement si le temps le permet, non considéré dans la recommandation de vitesse", "description#description": "", "label": "Ignorable", "label#description": ""}, "paste": "<PERSON><PERSON> Configs", "paste#description": ""}, "warnings": {"production": "Ce discriminateur est en cours d’exécution sur un robot. L'édition de celui-ci prendra effet immédiatement sur le terrain.", "production#description": ""}}, "drawer": {"customerMode": "Mode client", "customerMode#description": "", "error": "Chargement de la navigation a échoué", "error#description": ""}, "filters": {"NumericalRange": {"max": "Max ({{units}})", "max#description": "", "min": "Min ({{units}})", "min#description": ""}, "filters": "", "filters#description": "", "greaterOrEqualTo": "", "greaterOrEqualTo#description": "", "lessOrEqualTo": "", "lessOrEqualTo#description": "", "range": "", "range#description": ""}, "header": {"failed": "Chargement de l'en-tête a échoué", "failed#description": "", "mascot": "<PERSON><PERSON>tte poussin de Carbon Robotics", "mascot#description": "", "search": {"failed": "Charchement de la recherche a échoué", "failed#description": "", "focus": "Recherche ciblée", "focus#description": ""}}, "images": {"ImageSizeSlider": {"label": "<PERSON><PERSON>", "label#description": "", "larger": "<PERSON><PERSON><PERSON><PERSON>", "larger#description": "", "smaller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smaller#description": ""}}, "map": {"bounds": {"reset": "Réinitialiser Afficher", "reset#description": ""}, "errors": {"empty": "Aucune donnée de localisation rapportée", "empty#description": "", "failed": "Chargement de la carte a échoué", "failed#description": ""}, "filters": {"customer_office": "Bureau du client", "customer_office#description": "", "hq": "Siège Carbon", "hq#description": "", "name": "$t(views.fleet.views.fleetView_other)", "name#description": "", "po_box": "PO Box", "po_box#description": "", "shop": "Atelier", "shop#description": "", "storage": "Stockage", "storage#description": "", "support_base": "Base Assistance", "support_base#description": ""}, "fullscreen": "Plein écran", "fullscreen#description": "", "heatmaps": {"absoluteRange#description": "", "customRange#description": "", "editor": {}, "errors": {"invalidNumbers#description": "", "legend": "<PERSON><PERSON><PERSON> de légende de couche", "legend#description": "", "notThinning": "ÉCLAIRCIE NON EFFECTUÉE", "notThinning#description": "", "notWeeding": "DÉSHERBAGE NON EFFECTUÉ", "notWeeding#description": "", "outOfOrder#description": "", "unknown": "ERREUR CARTE THERMIQUE", "unknown#description": ""}, "fields": {"block": "Localisation : {{block}}", "block#description": "", "location": "Localisation: {{latitude}}, {{longitude}}", "location#description": "", "size": "Dimension : {{width}} × {{length}} ({{area}})", "size#description": ""}, "name": "Couches", "name#description": "", "rangeType#description": "", "relative": "Utiliser la plage relative", "relative#description": "", "relativeRange#description": ""}, "map": "<PERSON><PERSON>", "map#description": "", "measure": {"name": "Mesure", "name#description": ""}}, "modelinator": {"categories": {"copyFromWhich": "Co<PERSON>r de quelle catégorie ?", "copyFromWhich#description": "", "splitCrops": "<PERSON><PERSON><PERSON>er les cultures", "splitCrops#description": "", "splitWeeds": "<PERSON><PERSON><PERSON><PERSON> les mauvaises herbes", "splitWeeds#description": "", "syncCrops": "Synchroniser toutes les cultures", "syncCrops#description": "", "syncWeeds": "Synchroniser toutes les mauvaises herbes", "syncWeeds#description": ""}, "configs": {"bandingThreshold": {"description": "Seuil de confiance de prédiction pour l’utilisation d’une détection dans le bandage dynamique", "description#description": "", "label": "<PERSON><PERSON>", "label#description": ""}, "minDoo": {"description": "Détection minimale par rapport à l’opportunité", "description#description": "", "label": "<PERSON>", "label#description": ""}, "thinningThreshold": {"crop": {"description": "Seuil de confiance de prédiction pour l'utilisation d'une détection dans l'éclaircie", "description#description": "", "label": "Seuil d'éclaircie", "label#description": ""}, "weed": {"description": "Seuil de confiance de prédiction pour l'utilisation d'une détection pour la protection de culture inverse", "description#description": "", "label": "Seuil de protection de culture inverse", "label#description": ""}}, "weedingThreshold": {"crop": {"description": "Seuil de confiance de prédiction pour l'utilisation d'une détection pour la protection de culture", "description#description": "", "label": "Seuil de protection de culture", "label#description": ""}, "weed": {"description": "Seuil de confiance de prédiction pour considérer une mauvaise herbe", "description#description": "", "label": "<PERSON><PERSON>", "label#description": ""}}}, "errors": {"sync": "Les paramètres de ce modèle n’ont pas encore été synchronisés à partir du LaserWeeder. Veuillez attendre la synchronisation avant d'afficher et de mettre à jour les paramètres.", "sync#description": ""}, "formulas": {"categoryAndSize": "{{category}}: {{size}}", "categoryAndSize#description": "", "splitSizesLong": "<PERSON><PERSON><PERSON><PERSON> les dimensions", "splitSizesLong#description": "", "splitSizesShort": "<PERSON><PERSON><PERSON><PERSON>", "splitSizesShort#description": "", "syncSizesLong": "Synchroniser les dimensions", "syncSizesLong#description": "", "syncSizesShort": "Synchronisation", "syncSizesShort#description": ""}, "warnings": {"exportingUnsavedChanges": "{{startEmphasis}}Attention :{{stopEmphasis}} Ces paramètres incluent des modifications non enregistrées qui ne sont pas répercutées sur le robot.", "exportingUnsavedChanges#description": "", "production": "Ce modèle fonctionne activement sur un robot. L'édition de celui-ci prendra effet immédiatement sur le terrain.", "production#description": ""}}, "robots": {"RobotSummary": {"active": "$t(utils.descriptors.active)", "alarms": {"unknown": "Alarmes inconnues", "unknown#description": ""}, "almanac": {"unknown": "Almanach inconnu", "unknown#description": "", "withName": "Almanach : {{name}}", "withName#description": ""}, "autofixing": "Erreur de correction automatique", "autofixing#description": "", "banding": {"disabled": "Bandage désactivé", "disabled#description": "", "enabled": "Bandage activé", "enabled#description": "", "none": "Aucun bandage", "none#description": "", "static": "(STATIQUE)", "static#description": "", "withName": "Bandage : {{name}}", "withName#description": ""}, "checkedIn": {"failed": "Chargement du statut d'enregistrement a échoué", "failed#description": "", "never": "<PERSON><PERSON> enregistr<PERSON>", "never#description": "", "withTime": "Enregistré {{time}}", "withTime#description": ""}, "crop": {"summary": "{{enabled}}/{{total}} Cultures activées ({{pinned}} pinned)", "summary#description": ""}, "delivery": "<PERSON><PERSON><PERSON>", "delivery#description": "", "disconnected": "Déconnecté", "disconnected#description": "", "discriminator": {"unknown": "Discriminateur inconnu", "unknown#description": "", "withName": "Discriminateur : {{name}}", "withName#description": ""}, "failed": "Chargement du statut du robot a échoué", "failed#description": "", "failedShort": "<PERSON><PERSON><PERSON>", "failedShort#description": "", "implementation": "Implémentation", "implementation#description": "", "inactive": "$t(utils.descriptors.inactive)", "inventory": "Inventaire", "inventory#description": "", "job": {"none": "Aucun travail", "none#description": "", "withName": "Travail : {{name}}", "withName#description": ""}, "lasers": "Lasers en ligne :  {{online}}/{{total}}", "lasers#description": "", "lifetime": "Durée de vie", "lifetime#description": "", "lifted": "En attente (élevé)", "lifted#description": "", "loading": "Chargement en cours", "loading#description": "", "location": {"known": "Localisation : <0>{{latitude}}, {{longitude}}</0>", "known#description": "", "unknown": "Localisation inconnue", "unknown#description": ""}, "manufacturing": "Fabrication", "manufacturing#description": "", "model": {"withName": "Modèle :  <0>{{name}}</0>", "withName#description": ""}, "modelLoading": "Mod<PERSON>le en cours de chargement", "modelLoading#description": "", "notArmed": "Non activé", "notArmed#description": "", "off_season": "hors saison", "off_season#description": "", "offline": "Hors ligne pour {{duration}}", "offline#description": "", "p2p": {"known": "P2P: <0>{{p2p}}</0>", "known#description": "", "unknown": "P2P inconnu", "unknown#description": ""}, "poweringDown": "Mise hors tension", "poweringDown#description": "", "poweringUp": "Mise sous tension", "poweringUp#description": "", "pre_manufacturing": "Pré-fabrication", "pre_manufacturing#description": "", "stale": "<PERSON><PERSON><PERSON><PERSON>", "stale#description": "", "staleDescription": "Dernière valeur connue. Le robot est hors ligne.", "staleDescription#description": "", "standby": "En attente", "standby#description": "", "thinning": {"disabled": "Éclaircie désactivée", "disabled#description": "", "enabled": "Éclaircie activée", "enabled#description": "", "none": "Aucune éclaircie", "none#description": "", "withName": "Éclaircie : {{name}}", "withName#description": ""}, "today": {"none": "<PERSON><PERSON><PERSON> d<PERSON> aujourd'hui", "none#description": ""}, "unknown": "Statut inconnu", "unknown#description": "", "updating": "Mise à jour en cours d'installation", "updating#description": "", "version": {"values": {"unknown": "Version inconnue", "unknown#description": "", "updateDownloading": "({{version}} en cours de chargement)", "updateDownloading#description": "", "updateReady": "({{version}} prête)", "updateReady#description": ""}}, "weeding": "Désherbage {{crop}}", "weeding#description": "", "weedingDisabled": "<PERSON><PERSON><PERSON><PERSON> désactivé", "weedingDisabled#description": "", "weedingThinning": "Désherbage et éclaircie {{crop}}", "weedingThinning#description": "", "winterized": "Rangé pour l'hiver", "winterized#description": ""}, "dialogs": {"new": {"errors": {"exists": "<PERSON>ist<PERSON>", "exists#description": "", "unknownClass": "Classe de robot inconnue", "unknownClass#description": ""}, "fields": {"copyFrom": "$t(utils.form.copyConfigFrom)", "copyFrom#description": "", "ignoreConfig": "Ne créez pas de nouvelle config", "ignoreConfig#description": ""}, "template#description": "", "templateForClass": "<PERSON><PERSON><PERSON><PERSON> {{class}}", "templateForClass#description": "", "templateGeneric": "<PERSON><PERSON><PERSON><PERSON>", "templateGeneric#description": "", "warnings": {"ignoreConfig": "Continuez uniquement si une config pour {{serial}} existe déjà ou si vous prévoyez de la créer manuellement", "ignoreConfig#description": ""}}}}, "velocityEstimator": {"configs": {"card": {"advancedFormulaTitle": "Paramètres d'indicateur de vitesse avancés", "advancedFormulaTitle#description": "", "formulaTitle": "Formule", "formulaTitle#description": ""}, "cruiseOffsetPercent": {"description": "Diminuez automatiquement la vitesse suggérée de la valeur que vous avez entrée. Par exemple, une entrée de 5 % réduira la vitesse suggérée de 1 mi/h à 0,95 mi/h", "description#description": "", "label": "Décalage de vitesse", "label#description": ""}, "decreaseSmoothing": {"description": "Personnalisez la valeur à laquelle la vitesse diminue. Plus la valeur est élevée, plus l'indicateur de vitesse risque de fluctuer", "description#description": "", "label": "Lissage de décélération", "label#description": ""}, "increaseSmoothing": {"description": "Personnalisez la valeur à laquelle la vitesse augmente. Plus la valeur est élevée, plus l'indicateur de vitesse risque de fluctuer", "description#description": "", "label": "Lissage d'accélération", "label#description": ""}, "maxVelMph": {"description": "Entrez la vitesse la plus élevée absolue à laquelle vous êtes prêt à vous déplacer. Les recommandations de vitesse ne dépasseront pas cette valeur", "description#description": "", "label": "Vitesse maximale", "label#description": ""}, "minVelMph": {"description": "Entrez la vitesse la plus basse absolue à laquelle vous êtes prêt à vous déplacer. Les recommandations de vitesse ne seront pas inférieures à cette valeur", "description#description": "", "label": "Vitesse minimale", "label#description": ""}, "primaryKillRate": {"description": "Cette valeur correspond à votre pourcentage souhaité de mauvaises herbes exterminées", "description#description": "", "label": "Taux d'extermination idéal", "label#description": ""}, "primaryRange": {"description": "Augmentez cette valeur si vous voulez atteindre votre taux d'extermination idéal, quel que soit l’impact sur la vitesse", "description#description": "", "label": "Zone tampon vert", "label#description": ""}, "rows": {"allRows": "Toutes les rangées", "allRows#description": "", "row1": "Rangée 1", "row1#description": "", "row2": "Rangée 2", "row2#description": "", "row3": "Rangée 3", "row3#description": ""}, "secondaryKillRate": {"description": "Cette valeur correspond au pourcentage le plus bas acceptable de mauvaises herbes exterminées", "description#description": "", "label": "Taux d'extermination minimal", "label#description": ""}, "secondaryRange": {"description": "Augmentez cette valeur si vous souhaitez ajouter une marge de manœuvre avant de recevoir une notification de faible vitesse", "description#description": "", "label": "Zone tampon jaune", "label#description": ""}, "sync": "Synchroniser tous les rangs", "sync#description": "", "warnings": {"admin": "La modification de cet estimateur de vitesse sera synchronisée avec toutes les unités de production actuelles et futures.", "admin#description": "", "production": "Cet estimateur de vitesse est en cours d’exécution sur un robot. L’édition de celui-ci prendra effet immédiatement sur le terrain.", "production#description": "", "protected": "Il s'agit d'un profil fournit par carbon. Rien ne peut être modifié.", "protected#description": "", "unsavedChanges": "Modifications non enregistrées. Appuyez sur enregistrer pour appliquer les modifications.", "unsavedChanges#description": ""}}, "slider": {"gradual": "Progressivement", "gradual#description": "", "immediate": "Immédiatement", "immediate#description": ""}, "visualization": {"targetSpeed": "Vitesse cible", "targetSpeed#description": ""}}}, "models": {"alarms": {"alarm#description": "", "alarm_many": "", "alarm_one": "Alarme", "alarm_other": "Alarmes", "fields": {"code": "Code", "code#description": "", "description": "Description", "description#description": "", "duration": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"ongoing": "EN COURS", "ongoing#description": ""}}, "identifier": "Identifiant", "identifier#description": "", "impact": {"name": "Impact", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "degraded": "$t(utils.descriptors.degraded)", "none": "$t(utils.descriptors.none)", "none#description": "", "offline": "$t(utils.descriptors.offline)", "unknown": "$t(utils.descriptors.unknown)"}}, "level": {"name": "Niveau", "name#description": "", "values": {"critical": "$t(utils.descriptors.critical)", "hidden": "$t(utils.descriptors.hidden)", "high": "$t(utils.descriptors.high)", "low": "$t(utils.descriptors.low)", "medium": "$t(utils.descriptors.medium)", "unknown": "$t(utils.descriptors.unknown)"}}, "started": "Engagé", "started#description": ""}}, "almanacs": {"almanac#description": "", "almanac_many": "", "almanac_one": "Almanach", "almanac_other": "Almanachs", "fields": {"name": "$t(utils.descriptors.name)"}}, "autotractor": {"assignment#description": "", "assignment_many": "", "assignment_one": "affectation", "assignment_other": "affectations", "autotractor": "AutoTractor", "autotractor#description": "", "fields": {"instructions": "Instructions", "instructions#description": ""}, "intervention#description": "", "intervention_many": "", "intervention_one": "", "intervention_other": "", "job#description": "", "jobTypes": {"laserWeed": "Désherbage laser", "laserWeed#description": "", "unrecognized": "type inconnu ({{value}})", "unrecognized#description": ""}, "job_many": "", "job_one": "$t(models.jobs.job_one)", "job_other": "$t(models.jobs.job_other)", "manuallyAssisted": "", "manuallyAssisted#description": "", "objective#description": "", "objectiveTypes": {"laserWeedRow": "Ligne de désherbage au laser", "laserWeedRow#description": ""}, "objective_many": "", "objective_one": "objectif", "objective_other": "objectifs", "states": {"acknowledged": "reconnu", "acknowledged#description": "", "cancelled": "annulé", "cancelled#description": "", "completed": "terminé", "completed#description": "", "failed": "échec", "failed#description": "", "inProgress": "en cours", "inProgress#description": "", "new": "nouveau", "new#description": "", "paused": "en pause", "paused#description": "", "pending": "en attente", "pending#description": "", "ready": "pr<PERSON><PERSON>", "ready#description": "", "unrecognized": "état inconnu ({{value}})", "unrecognized#description": ""}, "task#description": "", "taskN": "Tâche #{{index}}", "taskN#description": "", "taskTypes": {"followPath": "", "followPath#description": "", "goToAndFace": "", "goToAndFace#description": "", "goToReversiblePath": "", "goToReversiblePath#description": "", "laserWeed": "", "laserWeed#description": "", "manual": "", "manual#description": "", "sequence": "", "sequence#description": "", "stopAutonomy": "", "stopAutonomy#description": "", "tractorState": "", "tractorState#description": "", "unknown": "", "unknown#description": ""}, "task_many": "", "task_one": "tâche", "task_other": "tâches"}, "categoryCollectionProfiles": {"categoryCollectionProfile#description": "", "categoryCollectionProfile_many": "", "categoryCollectionProfile_one": "profil de plante", "categoryCollectionProfile_other": "profils de plantes", "fields": {"categories": {"disregard": "<PERSON><PERSON><PERSON>", "disregard#description": "", "name": "Catégories", "name#description": "", "requiredBaseCategories": "Vous devez avoir ces catégories précises : ", "requiredBaseCategories#description": ""}, "categories#description": "", "name": "$t(utils.descriptors.name)", "updatedAt": "Mise à jour", "updatedAt#description": ""}, "metadata": {"capturedAt": "Capturé", "capturedAt#description": "", "categoryId": "ID de catégorie", "categoryId#description": "", "imageId": "ID image", "imageId#description": "", "pointId": "ID du point", "pointId#description": "", "ppcm": "ppcm", "ppcm#description": "", "radius": "rayon", "radius#description": "", "updatedAt": "<PERSON><PERSON><PERSON>", "updatedAt#description": "", "x": "x", "x#description": "", "y": "y", "y#description": ""}}, "configs": {"config#description": "", "config_many": "", "config_one": "Config", "config_other": "Configs", "key#description": "", "key_many": "", "key_one": "clé", "key_other": "clés", "template#description": "", "template_many": "", "template_one": "modèle de configuration", "template_other": "modèles de configuration", "value#description": "", "value_many": "", "value_one": "valeur", "value_other": "valeurs"}, "crops": {"categories": {"unknown": "Culture inconnue", "unknown#description": ""}, "crop#description": "", "crop_many": "", "crop_one": "culture", "crop_other": "cultures", "fields": {"confidence": {"fields": {"regionalImages": "Images régionales :", "regionalImages#description": "", "totalImages": "Toutes les images :", "totalImages#description": ""}, "name": "Confiance", "name#description": "", "values": {"HIGH": "$t(utils.descriptors.high)", "LOW": "$t(utils.descriptors.low)", "MEDIUM": "$t(utils.descriptors.medium)", "archived": "Archivé", "archived#description": "", "unknown": "Confiance inconnue", "unknown#description": ""}}, "id": "$t(utils.descriptors.id)", "id#description": "", "notes": "Notes", "notes#description": "", "pinned": "fixé", "pinned#description": "", "recommended": "Recommandé", "recommended#description": ""}}, "customers": {"customer#description": "", "customer_many": "", "customer_one": "client", "customer_other": "clients", "fields": {"emails": {"errors": {"formatting": "Un e-mail par ligne", "formatting#description": ""}, "name": "E-mails", "name#description": ""}, "featureFlags": {"almanac": {"description": "Active les onglets Almanach et Discriminateur pour les robots (le robot doit également prendre en charge l’almanach et le discriminateur)", "description#description": "", "name": "$t(models.almanacs.almanac_other)"}, "categoryCollection": {"description": "Active l'onglet de profils de plantes pour les robots", "description#description": "", "name": "$t(models.categoryCollectionProfiles.categoryCollectionProfile_other)"}, "description": "Les indicateurs de fonctionnalité activent la fonctionnalité bêta pour tous les utilisateurs d’un client", "description#description": "", "explore": {"description": "Mode exploration des cartes et graphiques activé dans les rapports", "description#description": "", "name": "Mode exploration", "name#description": ""}, "jobs": {"description": "Active les tâches (le robot doit également prendre en charge les tâches)", "description#description": "", "name": "$t(models.jobs.job_other)"}, "metricsRedesign": {"description": "Affiche un nouveau design visuel pour les mesures des robots", "description#description": "", "name": "Re<PERSON>nte des mesures", "name#description": ""}, "name": "Indicateurs de fonctionnalité", "name#description": "", "off": "OFF", "off#description": "", "on": "ON", "on#description": "", "reports": {"description": "Active l’onglet Rapports et les fonctionnalités comprises", "description#description": "", "name": "$t(models.reports.report_other)"}, "spatial": {"description": "Afficher les données spatiales, y compris les cartes thermiques et les graphiques", "description#description": "", "name": "Données spatiales", "name#description": ""}, "summary": "{{enabled}}/{{total}} Indicateurs de fonctionnalité activés", "summary#description": "", "unvalidatedMetrics": {"description": "Afficher les métriques bêta en attente de validation de champ dans les métriques certifiées", "description#description": "", "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "name#description": ""}, "velocityEstimator": {"description": "Permet d’afficher et de modifier les profils d’estimateur de vitesse cible (le robot doit également prendre en charge l’estimateur de vitesse cible)", "description#description": "", "name": "Estimateur de vitesse cible", "name#description": ""}}, "name": "$t(utils.descriptors.name)", "sfdcAccountId#description": "", "weeklyReportDay": "Exécuté le", "weeklyReportDay#description": "", "weeklyReportEnabled": {"description": "Si cette option est activée, les rapports seront exécutés chaque semaine avec les paramètres suivants pour tous les robots actifs", "description#description": "", "name": "Rapports hebdomadaires", "name#description": ""}, "weeklyReportHour": "Exécuté à", "weeklyReportHour#description": "", "weeklyReportLookbackDays": "Antériorit<PERSON>", "weeklyReportLookbackDays#description": "", "weeklyReportTimezone": "Exécution dans", "weeklyReportTimezone#description": ""}}, "discriminators": {"discriminator#description": "", "discriminator_many": "", "discriminator_one": "discriminateur", "discriminator_other": "discriminateurs", "fields": {"name": "$t(utils.descriptors.name)"}}, "farms": {"farm#description": "", "farm_many": "", "farm_one": "ferme", "farm_other": "fermes", "point#description": "", "point_many": "", "point_one": "point", "point_other": "points", "zone#description": "", "zone_many": "", "zone_one": "zone", "zone_other": "zones"}, "fieldDefinitions": {"fieldDefinition#description": "", "fieldDefinition_many": "", "fieldDefinition_one": "définition du champ", "fieldDefinition_other": "définitions du champ", "fields": {"boundary": "Limite du champ", "boundary#description": "", "name": "$t(utils.descriptors.name)", "plantingHeading": "Direction de semis", "plantingHeading#description": ""}}, "globals": {"global#description": "", "global_many": "", "global_one": "valeur globale", "global_other": "valeurs globales", "values": {"plantProfileModelId": {"description": "Modèle de base utilisé par tous les profils clients et administrateurs pour '$t(components.categoryCollectionProfile.actions.testResults)'", "description#description": "", "label": "ID modèle $t(components.categoryCollectionProfile.actions.testResults)", "label#description": ""}}}, "images": {"fields": {"camera": "Appareil photo", "camera#description": "", "capturedAt": "Date / Heure", "capturedAt#description": "", "geoJson": "Location", "geoJson#description": "", "url": "<PERSON>u<PERSON><PERSON>r l'image", "url#description": ""}, "image#description": "", "image_many": "", "image_one": "image", "image_other": "images"}, "jobs": {"job#description": "", "job_many": "", "job_one": "tâche", "job_other": "tâches"}, "lasers": {"fields": {"cameraId": "ID Appareil photo", "cameraId#description": "", "error": {"values": {"false": "Nominal", "false#description": ""}}, "installedAt": "Installé", "installedAt#description": "", "laserSerial": {"name": "$t(utils.descriptors.serial)", "values": {"unknown": "Série inconnue", "unknown#description": ""}}, "lifetimeSec": "Temps de marche", "lifetimeSec#description": "", "powerLevel": "Niveau de puissance", "powerLevel#description": "", "removedAt": "Retiré", "removedAt#description": "", "rowNumber": "<PERSON><PERSON><PERSON>", "rowNumber#description": "", "totalFireCount": "Nombre de tirs", "totalFireCount#description": "", "totalFireTimeMs": "Temps de tir", "totalFireTimeMs#description": "", "warranty": {"name": "<PERSON><PERSON><PERSON>", "name#description": "", "values": {"expired": "Expirée", "expired#description": "", "hours": "Heures: {{installed}}/{{total}} ({{percent}} restant)", "hours#description": "", "hoursUnknown": "Heures : Inconnu", "hoursUnknown#description": "", "months": "Mois : {{installed}}/{{total}} ({{percent}} restant)", "months#description": "", "monthsUnknown": "Mois : <PERSON><PERSON><PERSON>", "monthsUnknown#description": "", "unknown": "<PERSON><PERSON><PERSON> inconnue", "unknown#description": ""}}}, "laser#description": "", "laser_many": "", "laser_one": "laser", "laser_other": "lasers"}, "models": {"model#description": "", "model_many": "", "model_one": "mod<PERSON><PERSON>", "model_other": "mod<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON> mod<PERSON>", "none#description": "", "p2p#description": "", "p2p_many": "", "p2p_one": "Modèle P2P", "p2p_other": "Modèles P2P", "unknown": "<PERSON><PERSON><PERSON><PERSON> inconnu", "unknown#description": ""}, "reportInstances": {"fields": {"authorId": "Rédigé par", "authorId#description": "", "createdAt": "<PERSON><PERSON><PERSON>", "createdAt#description": "", "name": "$t(utils.descriptors.name)"}, "run#description": "", "run_many": "", "run_one": "rédaction de rapport", "run_other": "Rédactions de rapport"}, "reports": {"fields": {"authorId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authorId#description": "", "automateWeekly": {"name": "Automatisé", "name#description": "", "values": {"weekly": "Hebdomadaire", "weekly#description": ""}}, "name": "$t(utils.descriptors.name)"}, "report#description": "", "report_many": "", "report_one": "rapport", "report_other": "rapports"}, "robots": {"classes": {"buds#description": "", "buds_many": "", "buds_one": "<PERSON>", "buds_other": "Buds", "moduleValidationStations#description": "", "moduleValidationStations_many": "", "moduleValidationStations_one": "Station de validation de module", "moduleValidationStations_other": "Stations de validation des modules", "reapersCarbon#description": "", "reapersCarbon_many": "", "reapersCarbon_one": "Reaper", "reapersCarbon_other": "Reapers", "reapersCustomer_many": "", "reapersCustomer_one": "$t(models.robots.classes.slayersCustomer_one)", "reapersCustomer_other": "$t(models.robots.classes.slayersCustomer_other)", "rtcs#description": "", "rtcs_many": "", "rtcs_one": "<PERSON><PERSON><PERSON><PERSON>", "rtcs_other": "Tracteurs", "simulators#description": "", "simulators_many": "", "simulators_one": "Simulateur", "simulators_other": "Simulateurs", "slayersCarbon#description": "", "slayersCarbon_many": "", "slayersCarbon_one": "Slayer", "slayersCarbon_other": "Slayers", "slayersCustomer#description": "", "slayersCustomer_many": "", "slayersCustomer_one": "<PERSON><PERSON><PERSON><PERSON>", "slayersCustomer_other": "Laserweeders", "unknown": "Classification inconnue", "unknown#description": ""}, "fields": {"isThinning": "$t(utils.metrics.spatial.metrics.thinning)", "isThinning#description": "", "isWeeding": "$t(utils.metrics.spatial.metrics.weeding)", "isWeeding#description": "", "lasersOffline": "Lasers hors ligne", "lasersOffline#description": "", "lifetimeArea": "Superficie de durée de vie", "lifetimeArea#description": "", "lifetimeTime": "Temps de durée de vie", "lifetimeTime#description": "", "localTime": "Heure locale", "localTime#description": "", "reportedAt": "Dernière mise à jour", "reportedAt#description": "", "serial": "$t(utils.descriptors.serial)", "softwareVersion": "Version du logiciel", "softwareVersion#description": "", "supportSlack": "Canal Slack de support", "supportSlack#description": "", "targetVersion": "Version cible", "targetVersion#description": ""}, "robot#description": "", "robot_many": "", "robot_one": "robot", "robot_other": "robots", "unknown": "Robot inconnu", "unknown#description": ""}, "users": {"activated": "Activé", "activated#description": "", "fields": {"email": "E-mail", "email#description": "", "isActivated": "$t(models.users.activated)", "name": "$t(utils.descriptors.name)", "status": {"name": "Activation", "name#description": "", "values": {"false": "EN ATTENTE", "false#description": ""}}}, "operator#description": "", "operator_many": "", "operator_one": "opérateur", "operator_other": "opérateurs", "role#description": "", "role_many": "", "role_one": "Role", "role_other": "<PERSON><PERSON><PERSON>", "roles": {"carbon_basic": "Carbon Robotics", "carbon_basic#description": "", "carbon_tech": "Carbon Robotics (Technique)", "carbon_tech#description": "", "farm_manager": "Gestionnaire d'exploitation", "farm_manager#description": "", "operator_advanced": "Opérateur (avancé)", "operator_advanced#description": "", "operator_basic": "Opérateur", "operator_basic#description": "", "robot_role": "Robot", "robot_role#description": "", "unknown_role": "<PERSON><PERSON><PERSON> inconnu", "unknown_role#description": ""}, "staff": "Membres du personnel", "staff#description": "", "user#description": "", "user_many": "", "user_one": "utilisateur", "user_other": "utilisateurs"}, "velocityEstimators": {"fields": {"name": "$t(utils.descriptors.name)"}, "velocityEstimator#description": "", "velocityEstimator_many": "", "velocityEstimator_one": "<PERSON>ur de vitesse", "velocityEstimator_other": "estimateurs de vitesse"}, "weeds": {"categories": {"blossom": "<PERSON><PERSON><PERSON>", "blossom#description": "", "broadleaf": "Dicotylédones", "broadleaf#description": "", "fruit": "Fruit", "fruit#description": "", "grass": "Graminée", "grass#description": "", "offshoot": "Ramification", "offshoot#description": "", "preblossom": "Pré-flora<PERSON>", "preblossom#description": "", "purslane": "Pourpier", "purslane#description": "", "runner": "Stolon", "runner#description": "", "unknown": "Mauvaise herbe inconnue", "unknown#description": ""}, "weed#description": "", "weed_many": "", "weed_one": "mauvaise herbe", "weed_other": "mauvaises herbes"}}, "utils": {"actions": {"add": "Ajouter", "add#description": "", "addLong": "Ajouter {{subject}}", "addLong#description": "", "apply": "Appliquer", "apply#description": "", "applyLong": "Appliquer {{subject}}", "applyLong#description": "", "backLong": "Retour à {{subject}}", "backLong#description": "", "cancel": "Annuler", "cancel#description": "", "cancelLong": "Annuler {{subject}}", "cancelLong#description": "", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clear#description": "", "confirm": "Confirmer", "confirm#description": "", "continue": "<PERSON><PERSON><PERSON>", "continue#description": "", "copy": "<PERSON><PERSON><PERSON>", "copy#description": "", "copyLong": "<PERSON><PERSON>r {{subject}}", "copyLong#description": "", "create": "<PERSON><PERSON><PERSON>", "create#description": "", "createdLong": "{{subject}} c<PERSON><PERSON>", "createdLong#description": "", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete#description": "", "deleteLong": "Supprimer {{subject}}", "deleteLong#description": "", "deletedLong": "{{subject}} supprimé", "deletedLong#description": "", "disableLong": "Déactiver {{subject}}", "disableLong#description": "", "discard": "<PERSON><PERSON><PERSON>", "discard#description": "", "edit": "É<PERSON>er", "edit#description": "", "editLong": "Éditer {{subject}}", "editLong#description": "", "enableLong": "Activer {{subject}}", "enableLong#description": "", "exit": "Sortir", "exit#description": "", "exitLong": "Sortir de {{subject}}", "exitLong#description": "", "goToLong": "Aller à {{subject}}", "goToLong#description": "", "invite": "Inviter", "invite#description": "", "inviteLong": "Inviter {{subject}}", "inviteLong#description": "", "invitedLong": "{{subject}} invité", "invitedLong#description": "", "leaveUnchanged": "Ne pas modifier", "leaveUnchanged#description": "", "new": "Nouveau", "new#description": "", "newLong": "Nouveau {{subject}}", "newLong#description": "", "next": "Suivant", "next#description": "", "pause": "Pause", "pause#description": "", "play": "Lecture", "play#description": "", "previous": "Précédent", "previous#description": "", "ranLong": "{{subject}} exécuté", "ranLong#description": "", "reload": "Recharger", "reload#description": "", "resetLong": "Réinitialiser {{subject}}", "resetLong#description": "", "retry": "<PERSON><PERSON><PERSON><PERSON>", "retry#description": "", "run": "Exécution", "run#description": "", "runLong": "Exécuter {{subject}}", "runLong#description": "", "save": "<PERSON><PERSON><PERSON><PERSON>", "save#description": "", "saveLong": "<PERSON><PERSON><PERSON><PERSON> {{subject}}", "saveLong#description": "", "saved": "<PERSON><PERSON><PERSON><PERSON>", "saved#description": "", "savedLong": "{{subject}} sauve<PERSON><PERSON>", "savedLong#description": "", "search": "Recherche", "search#description": "", "searchLong": "Recherche {{subject}}", "searchLong#description": "", "selectAll": "S<PERSON><PERSON><PERSON><PERSON> tous", "selectAll#description": "", "selectLong": "Sélectionner {{subject}}", "selectLong#description": "", "selectNone": "Sélectionner Aucun", "selectNone#description": "", "send": "Envoyer", "send#description": "", "showLong": "Afficher {{subject}}", "showLong#description": "", "submit": "So<PERSON><PERSON><PERSON>", "submit#description": "", "toggle": "Commutation", "toggle#description": "", "toggleLong": "Commutation {{subject}}", "toggleLong#description": "", "update": "Mise à jour", "update#description": "", "updated": "Mis à jour", "updated#description": "", "updatedLong": "{{subject}} mis à jour", "updatedLong#description": "", "uploaded": "Télécharg<PERSON>", "uploaded#description": "", "viewLong": "Voir {{subject}}", "viewLong#description": ""}, "descriptors": {"active": "Actif", "active#description": "", "critical": "Critique", "critical#description": "", "default": "<PERSON><PERSON> <PERSON><PERSON>", "default#description": "", "degraded": "<PERSON><PERSON><PERSON><PERSON>", "degraded#description": "", "dense": "<PERSON><PERSON>", "dense#description": "", "disabled": "Désactivé", "disabled#description": "", "duration": "", "duration#description": "", "enabled": "Activé", "enabled#description": "", "ended": "", "ended#description": "", "endedAt": "", "endedAt#description": "", "error": "<PERSON><PERSON><PERSON>", "error#description": "", "estopOff": "En fonctionnement", "estopOff#description": "", "estopOn": "<PERSON><PERSON><PERSON><PERSON> d'urgence", "estopOn#description": "", "fast": "Rapide", "fast#description": "", "few": "Rare", "few#description": "", "good": "<PERSON>", "good#description": "", "hidden": "Caché", "hidden#description": "", "high": "<PERSON><PERSON><PERSON>", "high#description": "", "id": "ID", "id#description": "", "inactive": "Inactif", "inactive#description": "", "interlockSafe": "Tir autorisé", "interlockSafe#description": "", "interlockUnsafe": "Tir empêché", "interlockUnsafe#description": "", "large": "Grand", "large#description": "", "laserKeyOff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "laserKeyOff#description": "", "laserKeyOn": "<PERSON>cle<PERSON><PERSON>", "laserKeyOn#description": "", "liftedOff": "<PERSON><PERSON><PERSON><PERSON>", "liftedOff#description": "", "liftedOn": "<PERSON><PERSON><PERSON>", "liftedOn#description": "", "loading": "Chargement", "loading#description": "", "low": "Faible", "low#description": "", "majority": "Majorité", "majority#description": "", "medium": "<PERSON><PERSON><PERSON>", "medium#description": "", "minority": "Minorité", "minority#description": "", "name": "Nom", "name#description": "", "no": "Non", "no#description": "", "none": "Aucun", "none#description": "", "offline": "<PERSON><PERSON> ligne", "offline#description": "", "ok": "OK", "ok#description": "", "poor": "<PERSON><PERSON><PERSON><PERSON>", "poor#description": "", "progress": "Progression", "progress#description": "", "serial": "Série", "serial#description": "", "slow": "<PERSON><PERSON>", "slow#description": "", "small": "<PERSON>", "small#description": "", "sparse": "<PERSON><PERSON><PERSON>", "sparse#description": "", "started": "", "started#description": "", "startedAt": "", "startedAt#description": "", "type#description": "", "type_many": "", "type_one": "Type", "type_other": "Types", "unknown": "Inconnu", "unknown#description": "", "waterProtectNormal": "Humidité normale", "waterProtectNormal#description": "", "waterProtectTriggered": "<PERSON><PERSON>", "waterProtectTriggered#description": "", "yes": "O<PERSON>", "yes#description": ""}, "form": {"booleanType": "Doit être un booléen", "booleanType#description": "", "copyConfigFrom": "Copier la configuration de...", "copyConfigFrom#description": "", "integerType": "Doit être un entier", "integerType#description": "", "maxLessThanMin": "Le max doit être supérieur au min", "maxLessThanMin#description": "", "maxSize": "Ne peut pas dépasser {{limit}} caractères", "maxSize#description": "", "minGreaterThanMax": "Le min doit être inférieur au max", "minGreaterThanMax#description": "", "moveDown": "Descendre", "moveDown#description": "", "moveUp": "<PERSON><PERSON>", "moveUp#description": "", "noOptions": "Aucune option", "noOptions#description": "", "numberType": "Doit être un chiffre", "numberType#description": "", "optional": "(optionel)", "optional#description": "", "required": "Exigé", "required#description": "", "stringType": "Doit être une série", "stringType#description": ""}, "lists": {"+3": "{{b}}, et {{c}}", "+3#description": "", "1": "", "1#description": "", "2": "{{a}} et {{b}}", "2#description": "", "3+": "{{a}}, {{b}}", "3+#description": "", "loadMore": "Charger plus", "loadMore#description": "", "noMoreResults": "Plus aucun résultat", "noMoreResults#description": "", "noResults": "Aucun résultat", "noResults#description": ""}, "metrics": {"aggregates": {"max": "Max", "max#description": "", "min": "Min", "min#description": ""}, "certified": {"metrics": {"acresWeeded": "$t(utils.metrics.groups.coverage)", "avgCropSizeMm": "Rayon de culture moyen", "avgCropSizeMm#description": "", "avgSpeedMph": "Vitesse de déplacement moyenne", "avgSpeedMph#description": "", "avgTargetableReqLaserTime": "<PERSON><PERSON><PERSON> de tir moyenne", "avgTargetableReqLaserTime#description": "", "avgUntargetableReqLaserTime": "<PERSON><PERSON><PERSON> de tir moyenne (non ciblé)", "avgUntargetableReqLaserTime#description": "", "avgWeedSizeMm": "Rayon de mauvaise herbe moyen", "avgWeedSizeMm#description": "", "bandingConfigName": "Config bandage", "bandingConfigName#description": "", "bandingEnabled": "Bandage", "bandingEnabled#description": "", "bandingPercentage": "Pourcentage bandé", "bandingPercentage#description": "", "coverageSpeedAcresHr": "Vitesse de couverture moyenne", "coverageSpeedAcresHr#description": "", "crop": "$t(models.crops.crop_one)", "cropDensitySqFt": "Densité de culture", "cropDensitySqFt#description": "", "distanceWeededMeters": "Distance de désherbage", "distanceWeededMeters#description": "", "jobName": "$t(models.jobs.job_one)", "keptCrops": "Cultures restantes", "keptCrops#description": "", "killedWeeds": "Mauvaises herbes exterminées", "killedWeeds#description": "", "missedCrops": "Cultures manquées", "missedCrops#description": "", "missedWeeds": "Mauvaises herbes manquées", "missedWeeds#description": "", "notThinning": "Cultures non éclaircies", "notThinning#description": "", "notWeeding": "Mauvaises herbes non désherbées", "notWeeding#description": "", "notWeedingWeeds": "$t(utils.metrics.certified.metrics.notWeeding)", "operatorEffectiveness": "Efficacité des opérateurs", "operatorEffectiveness#description": "", "overallEfficiency": "Performance générale", "overallEfficiency#description": "", "skippedCrops": "Cultures ignorées", "skippedCrops#description": "", "skippedWeeds": "Mauvaises herbes ignorées", "skippedWeeds#description": "", "targetWeedingTimeSeconds": "Temps de désherbage cible", "targetWeedingTimeSeconds#description": "", "thinnedCrops": "Cultures éclaircies", "thinnedCrops#description": "", "thinningEfficiency": "Performance d'éclaircie", "thinningEfficiency#description": "", "timeEfficiency": "Efficacité opérationnelle", "timeEfficiency#description": "", "totalCrops": "Cultures trouvées", "totalCrops#description": "", "totalWeeds": "Mauvaises herbes trouvées", "totalWeeds#description": "", "totalWeedsInBand": "Mauvaises herbes trouvées (bande)", "totalWeedsInBand#description": "", "uptimeSeconds": "Temps de marche", "uptimeSeconds#description": "", "validCrops": "Cultures trouvées", "validCrops#description": "", "weedDensitySqFt": "Densité de mauvaise herbe", "weedDensitySqFt#description": "", "weedingEfficiency": "Performance de désher<PERSON>", "weedingEfficiency#description": "", "weedingUptimeSeconds": "Temps de dés<PERSON>bage", "weedingUptimeSeconds#description": "", "weedsTypeCountBroadleaf": "Type de mauvaise herbe : $t(models.weeds.categories.broadleaf)", "weedsTypeCountBroadleaf#description": "", "weedsTypeCountGrass": "Type de mauvaise herbe : $t(models.weeds.categories.grass)", "weedsTypeCountGrass#description": "", "weedsTypeCountOffshoot": "Type de mauvaise herbe : $t(models.weeds.categories.offshoot)", "weedsTypeCountOffshoot#description": "", "weedsTypeCountPurslane": "Type de mauvaise herbe : $t(models.weeds.categories.purslane)", "weedsTypeCountPurslane#description": ""}, "metricsHelp": {"avgCropSizeMm": "Cette valeur est calculée avant l'éclaircissage si l'éclaircissage a été activé.", "avgCropSizeMm#description": "", "bandingConfigName": "Votre profil banding sélectionné le plus récent", "bandingConfigName#description": "", "crop": "Votre dernière culture sélectionnée", "crop#description": "", "cropDensitySqFt": "Cette valeur est calculée avant l'éclaircissage si l'éclaircissage a été activé.", "cropDensitySqFt#description": "", "keptCrops": "Estimation du nombre de cultures conservées après l'éclaircissage", "keptCrops#description": "", "killedWeeds": "LaserWeeder a identifié l'objet comme étant une mauvaise herbe et l'a éliminé.", "killedWeeds#description": "", "missedCrops": "La culture a été identifiée pour l'éclaircissage mais n'a pas été trouvée. Les raisons les plus courantes sont les suivantes : trop grande vitesse, hors de portée ou erreur système.", "missedCrops#description": "", "missedWeeds": "La mauvaise herbe a été identifiée mais n'a pas été trouvée. Les raisons les plus courantes sont les suivantes : trop grande vitesse, hors de portée ou erreur système.", "missedWeeds#description": "", "operatorEffectiveness": "Indique dans quelle mesure la vitesse de déplacement réelle correspond à la vitesse cible recommandée par l'estimateur de vitesse.", "operatorEffectiveness#description": "", "overallEfficiency": "(performance de d<PERSON><PERSON><PERSON> + performance d'éclaircissage) / 2, si vous désherbez et éclaircissez simultanément", "overallEfficiency#description": "", "skippedCrops": "La culture a été intentionnellement écartée lors de l'éclaircissage. Les raisons les plus courantes sont les suivantes : désact<PERSON><PERSON> dans Quick Tune, hors bande, ou bande goutte-à-goutte proche.", "skippedCrops#description": "", "skippedWeeds": "La mauvaise herbe a été intentionnellement exclue. Les raisons les plus courantes sont les suivantes : désactivé dans Quick Tune ou hors bande.", "skippedWeeds#description": "", "thinningEfficiency": "(Cultures éclaircies + Cultures conservées) / Estimation des cultures trouvées × 100%.", "thinningEfficiency#description": "", "timeEfficiency": "(Durée de fonctionnement / sous tension) × 100%", "timeEfficiency#description": "", "uptimeSeconds": "La durée totale pendant laquelle LaserWeeder a été sous tension. Y compris lorsqu'il est en mode veille et/ou soulevé.", "uptimeSeconds#description": "", "weedDensitySqFt": "Estimation des mauvaises herbes trouvées (total) / Couverture", "weedDensitySqFt#description": "", "weedingEfficiency": "(Mauvaises herbes éliminées / Mauvaises herbes trouvées dans la bande) × 100", "weedingEfficiency#description": "", "weedingUptimeSeconds": "La durée pendant laquelle LaserWeeder a été actif pour le désherbage ou l'éclaircissage.", "weedingUptimeSeconds#description": ""}, "metricsRenamed": {"bandingConfigName": "", "bandingConfigName#description": "", "operatorEffectiveness": "Efficacité de la vitesse", "operatorEffectiveness#description": "", "timeEfficiency": "Utilisation de la machine", "timeEfficiency#description": "", "totalWeeds": "Mauvaises herbes trouvées (total)", "totalWeedsInBand": "Mauvaises herbes trouvées (dans la bande)", "totalWeedsInBand#description": "", "uptimeSeconds": "Du<PERSON><PERSON> de mise sous tension", "uptimeSeconds#description": "", "validCrops": "Estimation des cultures trouvées", "validCrops#description": "", "weedingUptimeSeconds": "Temps de travail actif", "weedingUptimeSeconds#description": ""}}, "groups": {"coverage": "Couverture", "coverage#description": "", "field": "<PERSON><PERSON>", "field#description": "", "hardware": "", "hardware#description": "", "performance": "Performance", "performance#description": "", "speed": "Vitesse", "speed#description": "", "speedDetails": "", "speedDetails#description": "", "usage": "Usage", "usage#description": ""}, "metric#description": "", "metric_many": "", "metric_one": "indicateur", "metric_other": "indicateurs", "spatial": {"heatmapWarning": "par bloc ~{{area}}", "heatmapWarning#description": "", "metrics": {"altitude": "Altitude", "altitude#description": "", "averageCropSize": "$t(utils.metrics.certified.metrics.avgCropSizeMm)", "averageWeedSize": "$t(utils.metrics.certified.metrics.avgWeedSizeMm)", "avgTargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgTargetableReqLaserTime)", "avgUntargetedReqLaserTime": "$t(utils.metrics.certified.metrics.avgUntargetableReqLaserTime)", "broadleaf": "$t(utils.metrics.certified.metrics.weedsTypeCountBroadleaf)", "coverage": "$t(utils.metrics.groups.coverage)", "cropDensity": "$t(utils.metrics.certified.metrics.cropDensitySqFt)", "cropsKept": "$t(utils.metrics.certified.metrics.keptCrops)", "cropsKilled": "$t(utils.metrics.certified.metrics.thinnedCrops)", "cropsMissed": "$t(utils.metrics.certified.metrics.missedCrops)", "cropsSkipped": "$t(utils.metrics.certified.metrics.skippedCrops)", "estopped": "E-Stop", "estopped#description": "", "grass": "$t(utils.metrics.certified.metrics.weedsTypeCountGrass)", "interlock": "Verrouillage", "interlock#description": "", "keptCropDensity": "Densité de culture conservée", "keptCropDensity#description": "", "laserKey": "Clé de laser", "laserKey#description": "", "lifted": "$t(utils.descriptors.liftedOn)", "offshoot": "$t(utils.metrics.certified.metrics.weedsTypeCountOffshoot)", "operatorEffectiveness": "$t(utils.metrics.certified.metrics.operatorEffectiveness)", "overallEfficiency": "$t(utils.metrics.certified.metrics.overallEfficiency)", "percentBanded": "$t(utils.metrics.certified.metrics.bandingPercentage)", "purslane": "$t(utils.metrics.certified.metrics.weedsTypeCountPurslane)", "speed": "Efficacité de vitesse", "speed#description": "", "speedTargetMinimum": "Vitese cible moyenne (Minimum)", "speedTargetMinimum#description": "", "speedTargetRow1": "Vitesse cible moyenne (Rangée 1)", "speedTargetRow1#description": "", "speedTargetRow2": "Vitesse cible moyenne (Rangée 2)", "speedTargetRow2#description": "", "speedTargetRow3": "Vitesse cible moyenne (Rangée 3)", "speedTargetRow3#description": "", "speedTargetSmoothed": "Vitesse cible moyenne", "speedTargetSmoothed#description": "", "speedTravel": "$t(utils.metrics.certified.metrics.avgSpeedMph)", "targetWeedingTimeSeconds": "$t(utils.metrics.certified.metrics.targetWeedingTimeSeconds)", "thinning": "Éclaircie", "thinning#description": "", "thinningEfficiency": "$t(utils.metrics.certified.metrics.thinningEfficiency)", "time": "<PERSON><PERSON>", "time#description": "", "totalCrops": "$t(utils.metrics.certified.metrics.totalCrops)", "totalCropsValid": "$t(utils.metrics.certified.metricsRenamed.validCrops)", "totalCropsValid#description": "", "totalWeeds": "$t(utils.metrics.certified.metrics.totalWeeds)", "totalWeedsInBand": "$t(utils.metrics.certified.metrics.totalWeedsInBand)", "waterProtect": "Protection de l'eau", "waterProtect#description": "", "weedDensity": "$t(utils.metrics.certified.metrics.weedDensitySqFt)", "weeding": "<PERSON><PERSON><PERSON><PERSON>", "weeding#description": "", "weedingEfficiency": "$t(utils.metrics.certified.metrics.weedingEfficiency)", "weedsKilled": "$t(utils.metrics.certified.metrics.killedWeeds)", "weedsMissed": "$t(utils.metrics.certified.metrics.missedWeeds)", "weedsSkipped": "$t(utils.metrics.certified.metrics.skippedWeeds)"}}}, "table": {"selected": "Sélectionné", "selected#description": "", "showAll": "Afficher tous les {{objects}}", "showAll#description": ""}, "units": {"%": "%", "%#description": "", "/ac": "/ac", "/ac#description": "", "/ft2": "/ft²", "/ft2#description": "", "/ha": "/ha", "/ha#description": "", "/in2": "/in²", "/in2#description": "", "/km2": "/km²", "/km2#description": "", "/m2": "/m²", "/m2#description": "", "/mi2": "/mi²", "/mi2#description": "", "W": "W", "W#description": "", "WLong#description": "", "WLong_many": "", "WLong_one": "watt", "WLong_other": "watts", "ac": "ac", "ac#description": "", "ac/h": "ac/h", "ac/h#description": "", "acLong#description": "", "acLong_many": "", "acLong_one": "acre", "acLong_other": "acres", "acres#description": "", "cm": "cm", "cm#description": "", "cm2": "cm²", "cm2#description": "", "d": "j", "d#description": "", "dLong#description": "", "dLong_many": "", "dLong_one": "jour", "dLong_other": "jours", "day#description": "", "days#description": "", "ft": "ft", "ft#description": "", "ft/s": "ft/s", "ft/s#description": "", "ft2": "ft²", "ft2#description": "", "ftLong#description": "", "ftLong_many": "", "ftLong_one": "pied", "ftLong_other": "pieds", "h": "h", "h#description": "", "hLong#description": "", "hLong_many": "", "hLong_one": "heure", "hLong_other": "heures", "ha": "ha", "ha#description": "", "ha/h": "ha/h", "ha/h#description": "", "haLong#description": "", "haLong_many": "", "haLong_one": "hectare", "haLong_other": "hectares", "hectares#description": "", "hours#description": "", "in": "in", "in#description": "", "in2": "in²", "in2#description": "", "km": "km", "km#description": "", "km/h": "km/h", "km/h#description": "", "km2": "km²", "km2#description": "", "kph#description": "", "m": "m", "m#description": "", "m/s": "m/s", "m/s#description": "", "m2": "m²", "m2#description": "", "mLong#description": "", "mLong_many": "", "mLong_one": "m<PERSON>tre", "mLong_other": "mètres", "mi": "mi", "mi#description": "", "mi2": "mi²", "mi2#description": "", "min": "min", "min#description": "", "minLong#description": "", "minLong_many": "", "minLong_one": "minute", "minLong_other": "minutes", "minutes#description": "", "mm": "mm", "mm#description": "", "month": "mo", "month#description": "", "monthLong#description": "", "monthLong_many": "", "monthLong_one": "mois", "monthLong_other": "mois", "mph": "mi/h", "mph#description": "", "ms": "ms", "ms#description": "", "s": "s", "s#description": "", "sLong#description": "", "sLong_many": "", "sLong_one": "seconde", "sLong_other": "secondes", "seconds#description": "", "watts#description": "", "week": "sem.", "week#description": "", "weekLong#description": "", "weekLong_many": "", "weekLong_one": "semaine", "weekLong_other": "semaines", "yd#description": "", "year": "an", "year#description": "", "yearLong#description": "", "yearLong_many": "", "yearLong_one": "ann<PERSON>", "yearLong_other": "ann<PERSON>"}}, "views": {"admin": {"alarms": {"allowWarning": "Ajouter des codes à la liste des autorisations permet la notification des alertes sur les canaux d'assistance Slack", "allowWarning#description": "", "blockWarning": "Ajouter des codes à la liste noire empêche la notification des alertes sur les canaux d'assistance Slack", "blockWarning#description": "", "lists": "Listes", "lists#description": "", "title": "Liste des alertes générales autorisées", "title#description": "", "titleAllow": "Liste des alertes autorisées", "titleAllow#description": "", "titleBlock": "Liste des alertes bloquées", "titleBlock#description": ""}, "config": {"bulk": {"actions": {"set": "Définir", "set#description": ""}, "allRows": "<all rows>", "allRows#description": "", "allRowsDescription": "<tt>rows/*</tt> sur <PERSON>, <tt>{row1,row2,row3}</tt> sur Slayer", "allRowsDescription#description": "", "listItems": "<list items>", "listItems#description": "", "operation#description": "", "operation_many": "", "operation_one": "opération", "operation_other": "opérations", "operationsCount": "Opérations ({{count}})", "operationsCount#description": "", "operationsHint": "Sélectionnez un nœud dans le schéma de configuration pour ajouter une opération.", "operationsHint#description": "", "outcomeDescriptions": {"encounteredErrors#description": "", "encounteredErrors_many": "", "encounteredErrors_one": "{{count}} erre<PERSON> renco<PERSON><PERSON>e", "encounteredErrors_other": "{{count}} erre<PERSON> rencont<PERSON>", "noChanges": "aucun changement", "noChanges#description": "", "updatedKeys#description": "", "updatedKeys_many": "", "updatedKeys_one": "{{count}} clé mise à jour", "updatedKeys_other": "{{count}} clés mises à jour"}, "outcomes": {"failure": "Échec", "failure#description": "", "partial": "Succès partiel", "partial#description": "", "success": "Su<PERSON>ès", "success#description": ""}, "title": "Configurations en masse", "title#description": ""}, "clearCaches": {"action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action#description": "", "description": "Des problèmes ? Essayez d'abord de rafraîchir le cache de Robot Syncer.", "description#description": ""}, "warnings": {"global": "Toute modification de cette config affectera les réglages par défauts et recommandations de l'ensemble des {{class}} actuels et futurs", "global#description": "", "notSimon": "Comme vous n'êtes pas Simon, vous ne devriez sans doute pas faire de modification ici... 👀", "notSimon#description": "", "unsyncedKeys": {"description": "Les modifications suivantes n'ont pas encore été synchronisées avec {{serial}} :", "description#description": "", "title": "Clés non synchronisées", "title#description": ""}}}, "portal": {"clearCaches": {"action": "Vider les caches", "action#description": "", "description": "Efface les caches internes pour Ops Center. Cela **ralentira** certaines requêtes à court terme, mais **pourra** résoudre les problèmes liés aux données périmées bloquées", "description#description": "", "details": "Appuyez sur ce bouton si vous avez modifié manuellement les autorisations d'un utilisateur dans Auth0 (pas via Ops Center), ou si vous avez apporté des modifications à une intégration tierce comme Stream ou Slack qui ne sont pas prises en compte.", "details#description": ""}, "title": "Ops Center", "title#description": "", "warnings": {"global": "Les options de cette page affecteront le fonctionnement d'Ops Center en production.", "global#description": "", "notPortalAdmin": "Vous n'êtes pas <PERSON><PERSON> ou <PERSON>, donc vous ne devriez probablement pas modifier cela.... 👀", "notPortalAdmin#description": ""}}, "robot": {"warnings": {"supportSlackLeadingHash": "Le canal Slack de support doit commencer par \"#\" : par exemple, \"#support-001-carbon\".", "supportSlackLeadingHash#description": ""}}, "title": "Administrateur", "title#description": ""}, "autotractor": {"actions": {"hidePivotHistory": "Masquer l'historique du pivot", "hidePivotHistory#description": "", "markComplete": "", "markComplete#description": "", "orchestrateView": "Affecter aux tracteurs", "orchestrateView#description": "", "showPivotHistory": "Afficher l'historique du pivot", "showPivotHistory#description": ""}, "fetchFailed": "Échec du chargement des données de localisation", "fetchFailed#description": "", "goLive": "Mise à jour en temps réel", "goLive#description": "", "hideRows": "Masquer les lignes", "hideRows#description": "", "jobDetails": {"assignmentsFailed": "Échec de récupération de la tâche, réessayer ?", "assignmentsFailed#description": "", "cancelDialog": {"description": "Le travail ne pourra plus être affecté aux tracteurs et devra être recréé.", "description#description": ""}, "customer": {"unknown": "Client inconnu", "unknown#description": "", "withName": "Client: {{name}}", "withName#description": ""}, "farm": {"unknown": "Ferme inconnue", "unknown#description": "", "withName": "Ferme: {{name}}", "withName#description": ""}, "field": {"unknown": "<PERSON><PERSON> inconnu", "unknown#description": "", "withName": "Champ: {{name}}", "withName#description": ""}, "jobFinished": "Travail terminé à {{time}}", "jobFinished#description": "", "jobStarted": "Travail commencé à {{time}}", "jobStarted#description": "", "openInFarmView": "Ouv<PERSON>r dans farm view", "openInFarmView#description": "", "state": "État: {{state}}", "state#description": "", "type": "Type de travail: {{type}}", "type#description": ""}, "lastPolled": "<PERSON><PERSON><PERSON> interrogation", "lastPolled#description": "", "live": "Live", "live#description": "", "objectiveFromOtherJob": "Objectif d'un autre travail", "objectiveFromOtherJob#description": "", "rowWidthUnits": "<PERSON>ur de ligne {{units}}", "rowWidthUnits#description": "", "selection": {"farms": "Fermes", "farms#description": "", "tractors": "Tracteurs", "tractors#description": ""}, "showRows": "Afficher les lignes", "showRows#description": "", "stalePivots": "Les informations sur les pivots peuvent être périmées", "stalePivots#description": "", "suggestedAssignments": "Tâches suggérées", "suggestedAssignments#description": "", "taskCriteria": {"gearStateValid": "", "gearStateValid#description": "", "headingValid": "", "headingValid#description": "", "hitchStateValid": "", "hitchStateValid#description": "", "posDistValid": "", "posDistValid#description": "", "posXteValid": "", "posXteValid#description": ""}, "unassignDialog": {"body": ""}}, "farms": {"actions": {"hideThesePoints": "Masquer ces points", "hideThesePoints#description": "", "onlyShowSelected": "Afficher uniquement la sélection", "onlyShowSelected#description": "", "showAllPoints": "Afficher tous les points", "showAllPoints#description": "", "showThesePoints": "Afficher ces points", "showThesePoints#description": ""}, "detailsPanel": {"boundary": "Limite", "boundary#description": "", "center": "Centre", "center#description": "", "centerPivot": "Pivot central", "centerPivot#description": "", "endpointId": "Id du point final", "endpointId#description": "", "holes": "Trous", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Direction de plantation", "plantingHeading#description": "", "point": "Point", "point#description": "", "points": "Points", "points#description": "", "width": "<PERSON><PERSON>", "width#description": ""}, "farm": "<PERSON><PERSON><PERSON>", "farm#description": "", "fixTypes": {"gps": "GPS", "gps#description": "", "none": "Non verrouillé", "none#description": "", "rtkFixed": "RTK verrouillé", "rtkFixed#description": "", "rtkFloat": "RTK Approximatif", "rtkFloat#description": "", "unknown": "Type de verrouillage inconnu", "unknown#description": ""}, "selectionPanel": {"allPoints": "Tous les points", "allPoints#description": "", "boundary": "Limite", "boundary#description": "", "center": "Centre", "center#description": "", "centerPivot": "Pivot central", "centerPivot#description": "", "endpointId": "Id du point final", "endpointId#description": "", "holes": "Trous", "holes#description": "", "length": "<PERSON><PERSON><PERSON>", "length#description": "", "plantingHeading": "Direction de plantation", "plantingHeading#description": "", "point": "Point", "point#description": "", "points": "Points", "points#description": "", "width": "<PERSON><PERSON>", "width#description": ""}, "unnamedPoint": "Point sans nom <0>{{pointId}}</0>", "unnamedPoint#description": "", "zoneTypes": {"farmBoundary": "<PERSON><PERSON> de la ferme", "farmBoundary#description": "", "field": "<PERSON><PERSON>", "field#description": "", "headland": "<PERSON><PERSON><PERSON>", "headland#description": "", "obstacle": "Obstacle", "obstacle#description": "", "privateRoad": "<PERSON>emin privé", "privateRoad#description": "", "unknown": "Type de zone inconnu", "unknown#description": ""}}, "fieldDefinitions": {"controls": {"draw": "<PERSON><PERSON><PERSON>", "draw#description": ""}, "errors": {"exactlyTwoPoints": "La ligne doit avoir exactement deux points", "exactlyTwoPoints#description": "", "wrongFieldType": "Le champ \"{{field}}\" doit être {{want}}", "wrongFieldType#description": "", "wrongGeometryType": "La géométrie doit être de type {{want}}", "wrongGeometryType#description": "", "wrongJsonType": "JSON doit être un objet", "wrongJsonType#description": ""}}, "fleet": {"missionControl": {"errors": {"empty": "Aucun robot en ligne", "empty#description": ""}, "title": "Contrôle de la mission", "title#description": ""}, "robots": {"config": {"auditLog": {"open": "Voir l'historique des modifications", "open#description": "", "title": "Historique des modifications", "title#description": ""}, "errors": {"failed": "Le téléchargement de l'arbre de config a échoué", "failed#description": ""}, "onlyChanged": "Afficher uniquement les modifications", "onlyChanged#description": ""}, "errors": {"empty": "Aucun robot affecté", "empty#description": ""}, "hardware": {"errors": {"old": "Ce robot ne signale aucun numéro de série d'ordinateur (sans doute périmé)", "old#description": ""}, "fields": {"hostname": "<PERSON><PERSON><PERSON>", "hostname#description": ""}, "installedVersion": "Version installée :", "installedVersion#description": "", "ready": {"name": "<PERSON>r<PERSON><PERSON> pour l'installation :", "name#description": "", "values": {"false": "Téléchargement en cours...", "false#description": "", "installed": "Installé", "installed#description": "", "true": "<PERSON><PERSON><PERSON><PERSON> !", "true#description": ""}}, "tabs": {"computers": "Ordinateurs", "computers#description": "", "versions": "Versions", "versions#description": ""}, "targetVersion": "Version cible :", "targetVersion#description": "", "title": "Matériel informatique", "title#description": "", "updateHistory": "Historique des mises à jour <0>prochainement ;)️</0>", "updateHistory#description": ""}, "history": {"borders": "Bordures", "borders#description": "", "errors": {"invalidDate": "Sélectionner des dates valables", "invalidDate#description": "", "noJobs": "Aucune tâche signalée aux dates sélectionnées", "noJobs#description": "", "noMetrics": "Aucun indicateur signalé", "noMetrics#description": ""}, "moreMetrics": "Voir plus d'indicateurs", "moreMetrics#description": "", "navTitle": "Historique", "navTitle#description": "", "placeholder": "Sé<PERSON><PERSON>ner une tâche ou une date pour afficher les données", "placeholder#description": "", "points": "Points", "points#description": "", "warnings": {"beta": {"description": "En attendant leur validation, les indicateurs sont affichés en bleu", "description#description": ""}, "ongoing": "Les indicateurs ne sont pas encore définitifs pour cette date", "ongoing#description": ""}}, "status": "Statut", "status#description": "", "summary": {"banding": {"definition": "Définition", "definition#description": "", "dynamic": "Dynamique", "dynamic#description": "", "dynamicDisabled": "(Bandage dynamique désactivé dans la config)", "dynamicDisabled#description": "", "rows": "Rangs", "rows#description": "", "static": "Statique", "static#description": "", "type": "Type", "type#description": "", "unknown": "Bandage inconnu", "unknown#description": "", "v1": "v1", "v1#description": "", "v2": "v2", "v2#description": "", "version": "Version", "version#description": ""}, "config": {"changes#description": "", "changes_many": "", "changes_one": "{{count}} modification d'almanach", "changes_other": "{{count}} modifications d'almanach", "cpt": "Seuil de confiance de détection d'une culture", "cpt#description": "", "default": "(PAR DÉFAUT : {{value}})", "default#description": "", "wpt": "Seuil de confiance de détection d'une mauvaise herbe", "wpt#description": ""}, "encoders": {"backLeft": "<PERSON><PERSON><PERSON> gauche", "backLeft#description": "", "backRight": "<PERSON><PERSON><PERSON> droite", "backRight#description": "", "frontLeft": "Avant gauche", "frontLeft#description": "", "frontRight": "Avant droite", "frontRight#description": "", "title": "Codeurs des roues", "title#description": "", "unknown": "?", "unknown#description": ""}, "failed": "Échec du téléchargement de la synthèse du robot", "failed#description": "", "lasers": {"disabled#description": "", "disabled_many": "", "disabled_one": "{{count}} laser dés<PERSON><PERSON><PERSON>", "disabled_other": "{{count}} lasers dés<PERSON><PERSON><PERSON>", "row": "Rang {{row}}", "row#description": ""}, "machineHealth": "Bilan de santé mécanique", "machineHealth#description": "", "navTitle": "Synthèse", "navTitle#description": "", "safetyRadius": {"driptape": "Irrigation par bandes perforées", "driptape#description": "", "title": "Rayon de sécurité", "title#description": ""}, "sections": {"management": "Gestion", "management#description": "", "software": "Logiciel", "software#description": ""}, "supportLinks": {"chipChart": "Graphique des éclats", "chipChart#description": "", "datasetVisualization": "Ensemble de données Visualisation", "datasetVisualization#description": "", "title": "Liens d'assistance", "title#description": ""}}, "support": {"carbon": "Assistance Carbon", "carbon#description": "", "chatMode": {"legacy": "Historique du chat", "legacy#description": "", "new": "Nouveau chat", "new#description": ""}, "errors": {"failed": "Echec du téléchargement du message", "failed#description": "", "old": {"description": "{{serial}} exécute la version {{version}} du logiciel. Version {{target}} nécessaire pour prendre en charge la discussion.", "description#description": "", "title": "Cette version du robot est insuffisante", "title#description": ""}}, "localTime": "Heure locale : {{time}}", "localTime#description": "", "navTitle": "Assistance", "navTitle#description": "", "toCarbon": "Message à $t(views.fleet.robots.support.carbon)", "toCarbon#description": "", "toOperator": "Message à $t(models.users.operator_one)", "toOperator#description": "", "warnings": {"offline": {"description": "{{serial}} fonctionne hors ligne. L'opérateur recevra votre message lorsque la connexion du robot sera rétablie.", "description#description": "", "title": "Robot hors ligne", "title#description": ""}}}, "toggleable": {"internal": "Interne(s)", "internal#description": ""}, "uploads": {"errors": {"empty": "Aucun téléchargement", "empty#description": ""}}}, "title": "<PERSON><PERSON><PERSON>", "title#description": "", "views": {"fields": {"name": "Nom du filtre", "name#description": "", "otherRobots": "Autres robots ({{robotCount}})", "otherRobots#description": "", "pinnedRobotIds": "<PERSON><PERSON>", "pinnedRobotIds#description": "", "viewMode": {"values": {"cards": "<PERSON><PERSON>", "cards#description": "", "table": "<PERSON><PERSON>", "table#description": ""}}}, "fleetView#description": "", "fleetView_many": "", "fleetView_one": "filtre", "fleetView_other": "filtres", "tableOnly": "Certaines colonnes ne sont disponibles qu'en mode tableau", "tableOnly#description": ""}}, "knowledge": {"title": "Base de connaissances", "title#description": ""}, "metrics": {"jobStatus": {"closed": "<PERSON><PERSON><PERSON>", "closed#description": "", "description": "Statut de la tâche", "description#description": "", "open": "Ouvert", "open#description": ""}, "sections": {"estimatedFieldMetrics": "Mesures estimées du champ", "estimatedFieldMetrics#description": "", "estimatedFieldMetricsDisclaimer": "Notre modèle utilise des données expérimentales sur les cultures qui peuvent présenter des inexactitudes. Nous améliorons constamment sa fiabilité.", "estimatedFieldMetricsDisclaimer#description": "", "performanceAndMachineStats": "Performances et mesures des machines", "performanceAndMachineStats#description": ""}}, "offline": {"drop": "Déposer des fichiers ici depuis une clé USB (ou depuis tout autre emplacement)", "drop#description": "", "file#description": "", "file_many": "", "file_one": "<PERSON><PERSON><PERSON>", "file_other": "fichiers", "ingestDescription": "Les Employés de Carbon doivent utiliser le service Ingest", "ingestDescription#description": "", "ingestLink": "Télécharger vers Ingest", "ingestLink#description": "", "select": "Sélectionner des fichiers", "select#description": "", "title": "Télécharger", "title#description": "", "upload": "Télécharger vers Carbon", "upload#description": "", "uploading": "Téléchargement de {{subject}} en cours...", "uploading#description": ""}, "reports": {"explore": {"graph": "Graphique", "graph#description": "", "groupBy": "Regrouper par", "groupBy#description": "", "title": "Exploration", "title#description": ""}, "scheduled": {"authorCarbonBot": "<PERSON><PERSON> de Carbon", "authorCarbonBot#description": "", "authorUnknown": "<PERSON><PERSON><PERSON> inconnu", "authorUnknown#description": "", "automation": {"customerReports": "Rapports client", "customerReports#description": "", "errorTitle": "Rapport automatisé invalide", "errorTitle#description": "", "reportCustomer": {"errors": {"none": "Pas de client sélectionné", "none#description": ""}}, "reportDay": {"errors": {"none": "Pas de jour sélectionné", "none#description": ""}, "name": "<PERSON>ur du rapport", "name#description": ""}, "reportEmails": {"errors": {"none": "<PERSON>s de courriel <PERSON>", "none#description": ""}, "name": "Courriels client", "name#description": ""}, "reportHour": {"errors": {"none": "Pas d'heure sélectionnée", "none#description": ""}, "name": "<PERSON><PERSON> du rapport", "name#description": ""}, "reportLookback": {"errors": {"none": "Pas d'anté<PERSON><PERSON>é dé<PERSON>ie", "none#description": ""}, "name": "Antériorité des rapports", "name#description": ""}, "reportTimezone": {"errors": {"none": "Pas de fuseau horaire sélectionné", "none#description": ""}, "name": "Fuseau horaire des rapports", "name#description": ""}, "warningDescription": "Exécuté tous les {{day}} à {{hour}}, heure de {{timezone}}, avec une antériorité de {{lookback}} jours pour tous les robots actifs de {{customer}}.", "warningDescription#description": "", "warningTitle": "Ceci est un rapport automatisé !", "warningTitle#description": ""}, "byline": "Par {{author}}", "byline#description": "", "editor": {"columnsHidden": "Colonnes masquées", "columnsHidden#description": "", "columnsVisible": "Colonnes visibles", "columnsVisible#description": "", "duplicateNames#description": "", "duplicateNames_many": "", "duplicateNames_one": "Attention : il existe un autre rapport portant ce nom", "duplicateNames_other": "Attention : il y a {{count}} autres rapports portant ce nom.", "fields": {"automateWeekly": "Rapport automatisé hebdomadaire", "automateWeekly#description": "", "name": "Intitulé du rapport", "name#description": "", "showAverages": "Afficher les moyennes", "showAverages#description": "", "showTotals": "Afficher les totaux", "showTotals#description": ""}}, "errors": {"noReport": "Ce rapport n'existe pas ou vous n'êtes pas autorisé à y accéder", "noReport#description": ""}, "reportList": {"deleteConfirmationDescription": "{{list}} va être supprimée définitivement.", "deleteConfirmationDescription#description": "", "errors": {"unauthorized": "Vous n'êtes pas autorisé à supprimer {{subject}}.", "unauthorized#description": ""}}, "runDialog": {"fields": {"publishEmailsHelperExisting": "Le courriel ne sera pas renvoyé", "publishEmailsHelperExisting#description": "", "publishEmailsHelperNew": "Le rapport sera adressé à ces courriels", "publishEmailsHelperNew#description": ""}, "runAgain": "<PERSON><PERSON>-exécuter", "runAgain#description": ""}, "table": {"errors": {"noColumns": "Sélectionner une ou plusieurs colonnes", "noColumns#description": "", "noEndDate": "Sélectionner la date de fin", "noEndDate#description": "", "noRobots": "Sélectionner un ou plusieurs robots", "noRobots#description": "", "noStartDate": "Sélectionner la date de début", "noStartDate#description": ""}, "fields": {"average": "<PERSON><PERSON><PERSON>", "average#description": "", "averageShort": "MOYENNE", "averageShort#description": "", "date": "Date", "date#description": "", "group": "Série/Date", "group#description": "", "groupJob": "Série/Tâche", "groupJob#description": "", "mixed": "(Mixte)", "mixed#description": "", "total": "Total", "total#description": "", "totalShort": "SOMME", "totalShort#description": ""}, "unknownReport": "Rapport inconnu", "unknownReport#description": ""}, "title": "Programmé", "title#description": "", "toLine": "pour {{customer}}", "toLine#description": ""}, "tools": {"metricsLabel": {"all": "<PERSON><PERSON> les indicateurs", "all#description": "", "select": "Sélectionner des indicateurs", "select#description": ""}, "robotsLabel": {"all": "Tous les robots", "all#description": "", "none": "Aucun robot", "none#description": "", "select": "Sélectionner des robots", "select#description": ""}}}, "settings": {"accountProvider": {"account": "<0>{{email}}</0> via <1>{{identityProvider}}</1>", "account#description": "", "apple": "Apple", "apple#description": "", "auth0": "nom d'utilisateur et mot de passe", "auth0#description": "", "google": "Google OAuth", "google#description": "", "unknown": "fournisseur inconnu", "unknown#description": ""}, "cards": {"account": "<PERSON><PERSON><PERSON>", "account#description": "", "advanced": "Avancés", "advanced#description": "", "localization": "Localisation", "localization#description": ""}, "delete": {"deleteAccount": "Supprimer le compte", "deleteAccount#description": "", "dialog": {"description": "AVERTISSEMENT : Cette action est irréversible. Toutes les données seront perdues.", "description#description": ""}}, "fields": {"experimental": "Activer les fonctionnalités expérimentales", "experimental#description": "", "language": "<PERSON><PERSON>", "language#description": "", "measurement": {"name": "Unités de mesure", "name#description": "", "values": {"imperial": "Système impérial (in (pouces), mph (miles/h), acres, degrés Fahrenheit)", "imperial#description": "", "metric": "Système métrique (mm, km/h, hectares, degré<PERSON>lsius)", "metric#description": ""}}, "showMascot#description": ""}, "logOut": "Deconnexion", "logOut#description": "", "title": "Paramètres", "title#description": "", "version": "Carbon Ops Center version {{version}} ({{hash}})", "version#description": ""}, "users": {"errors": {"notFound": "Cet utilisateur n'existe pas ou vous n'avez pas la permission d'y accéder.", "notFound#description": ""}, "manage#description": "", "sections": {"admin": {"manage": "<PERSON><PERSON><PERSON> utilisateur dans Auth0", "manage#description": "", "title": "Admin", "title#description": ""}, "permissions": {"title": "Rôle et autorisations", "title#description": ""}, "profile": {"title": "Profil", "title#description": ""}}, "toggleable": {"contractors": "Presta<PERSON><PERSON>", "contractors#description": ""}}}}