module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "19.21.0"

  cluster_name    = local.name
  cluster_version = "1.31"

  cluster_endpoint_public_access = true

  cluster_addons = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    vpc-cni = {
      most_recent = true
    }
  }

  # External encryption key
  create_kms_key = false
  cluster_encryption_config = {
    resources        = ["secrets"]
    provider_key_arn = module.kms.key_arn
  }

  iam_role_additional_policies = {
    additional = aws_iam_policy.additional.arn
  }

  vpc_id                   = module.vpc.vpc_id
  subnet_ids               = module.vpc.private_subnets
  control_plane_subnet_ids = module.vpc.intra_subnets

  # Extend cluster security group rules
  cluster_security_group_additional_rules = {
    ingress_nodes_ephemeral_ports_tcp = {
      description                = "Nodes on ephemeral ports"
      protocol                   = "tcp"
      from_port                  = 1025
      to_port                    = 65535
      type                       = "ingress"
      source_node_security_group = true
    }
    ingress_source_security_group_id = {
      description              = "Ingress from another computed security group"
      protocol                 = "tcp"
      from_port                = 22
      to_port                  = 22
      type                     = "ingress"
      source_security_group_id = aws_security_group.additional.id
    }
  }

  # Extend node-to-node security group rules
  node_security_group_additional_rules = {
    ingress_self_all = {
      description = "Node to node all ports/protocols"
      protocol    = "-1"
      from_port   = 0
      to_port     = 0
      type        = "ingress"
      self        = true
    }
    # Test: https://github.com/terraform-aws-modules/terraform-aws-eks/pull/2319
    ingress_source_security_group_id = {
      description              = "Ingress from another computed security group"
      protocol                 = "tcp"
      from_port                = 22
      to_port                  = 22
      type                     = "ingress"
      source_security_group_id = aws_security_group.additional.id
    }
  }

  eks_managed_node_group_defaults = {
    ami_type       = "AL2_x86_64"
    instance_types = ["m5.large"]

    attach_cluster_primary_security_group = false
    vpc_security_group_ids                = [aws_security_group.additional.id]
    iam_role_additional_policies = {
      additional = aws_iam_policy.additional.arn
    }
  }

  // Note - `desired_size` doesn't seem respected so i guess we need to manage that manually when it matters
  // this isn't a big deal because typically we don't change these.
  eks_managed_node_groups = {
    mg_0 = {
      node_group_name            = "mg_0"
      desired_size               = 2
      min_size                   = 1
      max_size                   = 2
      disk_size                  = 200
      instance_types             = ["r6i.xlarge"]
      subnet_ids                 = [module.vpc.private_subnets[0]] // us-west-2a
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        Worker = "default"
      }
    }
    mg_1 = {
      node_group_name            = "mg_1"
      desired_size               = 1
      min_size                   = 1
      max_size                   = 2
      disk_size                  = 200
      instance_types             = ["r6i.xlarge"]
      subnet_ids                 = [module.vpc.private_subnets[1]] //us-west-2b
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        Worker = "default"
      }
    }
    mg_2 = {
      node_group_name            = "mg_2"
      desired_size               = 1
      min_size                   = 1
      max_size                   = 2
      disk_size                  = 200
      instance_types             = ["r6i.xlarge"]
      subnet_ids                 = [module.vpc.private_subnets[2]] // us-west-2c
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        Worker = "default"
      }
    }

    analytics_workers_0 = {
      node_group_name            = "analytics_workers_0"
      desired_size               = 1
      min_size                   = 0
      max_size                   = 2
      disk_size                  = 200
      instance_types             = ["m4.2xlarge"]
      subnet_ids                 = [module.vpc.private_subnets[0]] // us-west-2a
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        Worker = "analytics"
      }
    }

    veselka_workers_0 = {
      node_group_name            = "veselka_workers_0"
      desired_size               = 1
      min_size                   = 1
      max_size                   = 2
      disk_size                  = 200
      instance_types             = ["r6i.4xlarge"]                 // bumped temporarily from r6i.2xlarge
      subnet_ids                 = [module.vpc.private_subnets[2]] // us-west-2c
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        Worker = "veselka"
      }
    }
    #    veselka_workers_1 = {
    #      node_group_name            = "veselka_workers_1"
    #      desired_size               = 1
    #      min_size                   = 1
    #      max_size                   = 2
    #      disk_size                  = 200
    #      instance_types             = ["r6i.2xlarge"]
    #      subnet_ids                 = [module.vpc.private_subnets[2]] // us-west-2c
    #      force_update_version       = true
    #      use_custom_launch_template = false
    #      update_config              = {
    #        max_unavailable_percentage = 50 # or set `max_unavailable`
    #      }
    #      labels = {
    #        Worker = "veselka"
    #      }
    #    }

    ci_0 = {
      capacity_type              = "SPOT"
      node_group_name            = "ci_0"
      desired_size               = 1
      min_size                   = 0
      max_size                   = 5
      disk_size                  = 200
      instance_types             = ["t3.large"]
      subnet_ids                 = [module.vpc.private_subnets[1]] // us-west-2b
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        carbon_ci = true
        ci_group  = 0
      }
    }

    robot_ci_0 = {
      node_group_name            = "robot_ci_0"
      desired_size               = 1
      min_size                   = 0
      max_size                   = 5
      disk_size                  = 200
      instance_types             = ["r6i.xlarge"]
      subnet_ids                 = [module.vpc.private_subnets[2]] // us-west-2c
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        carbon_ci = true
        ci_group  = "robot_0"
      }
    }

    robot_ci_0_arm = {
      ami_type                   = "AL2_ARM_64"
      node_group_name            = "robot_ci_0_arm"
      desired_size               = 2
      min_size                   = 0
      max_size                   = 5
      disk_size                  = 200
      instance_types             = ["m6g.xlarge"]
      subnet_ids                 = [module.vpc.private_subnets[0]] // us-west-2a
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        carbon_ci = true
        ci_group  = "robot_0_arm"
      }
    }

    production_0 = {
      node_group_name            = "production_0"
      desired_size               = 1
      min_size                   = 0
      max_size                   = 5
      disk_size                  = 200
      instance_types             = ["r6i.2xlarge"]                 // 8 cores (double Worker=Default nodes)
      subnet_ids                 = [module.vpc.private_subnets[0]] // us-west-2a
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        environment = "production"
      }
    }

    production_1 = {
      node_group_name            = "production_1"
      desired_size               = 1
      min_size                   = 0
      max_size                   = 5
      disk_size                  = 200
      instance_types             = ["r6i.2xlarge"]                 // 8 cores (double Worker=Default nodes)
      subnet_ids                 = [module.vpc.private_subnets[1]] // us-west-2b
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        environment = "production"
      }
    }

    production_2 = {
      node_group_name            = "production_2"
      desired_size               = 1
      min_size                   = 0
      max_size                   = 5
      disk_size                  = 200
      instance_types             = ["r6i.2xlarge"]                 // 8 cores (double Worker=Default nodes)
      subnet_ids                 = [module.vpc.private_subnets[2]] // us-west-2c
      force_update_version       = true
      use_custom_launch_template = false
      update_config = {
        max_unavailable_percentage = 50 # or set `max_unavailable`
      }
      labels = {
        environment = "production"
      }
    }
  }

  # aws-auth configmap
  manage_aws_auth_configmap = true

  aws_auth_roles = [
    #    {
    #      rolearn  = aws_iam_role.system_master_role.arn
    #      username = "${aws_iam_role.system_master_role.name}:{{SessionName}}"
    #      groups   = ["system:masters"]
    #    }
    {
      rolern   = data.aws_iam_role.eks_developer.arn
      username = "${data.aws_iam_role.eks_developer.name}:{{SessionName}}"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    }
  ]

  aws_auth_users = [
    {
      userarn  = "arn:aws:iam::************:user/scochrane"
      username = "scochrane"
      groups   = ["system:masters"]
    },
    {
      userarn  = "arn:aws:iam::************:user/nickk"
      username = "nickk"
      groups   = ["system:masters"]
    },
    {
      userarn  = "arn:aws:iam::************:user/seth-permission-test"
      username = "seth-permission-test"
      groups   = ["system:masters"]
    },

    {
      userarn  = "arn:aws:iam::************:user/paulm"
      username = "paulm"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users",
        "monitoring-users"
      ]
    },

    # Software Team
    {
      userarn  = "arn:aws:iam::************:user/vanessas"
      username = "vanessas"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/milok"
      username = "milok"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/wchargin"
      username = "wchargin"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/nishanta"
      username = "nishanta"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/bsweezer"
      username = "bsweezer"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/abadereddin"
      username = "abadereddin"
      groups = [
        "rtc-prod-users",
        "prod-users",
        "stage-users",
        "test-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/dschack"
      username = "dschack"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/cbayer"
      username = "cbayer"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users"
      ]
    },

    # Deeplearning Team
    {
      userarn  = "arn:aws:iam::************:user/asergeev"
      username = "asergeev"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users",
        "monitoring-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/zach"
      username = "zach"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/raven"
      username = "raven"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/evanb"
      username = "evanb"
      groups = [
        "test-users",
        "stage-users",
        "prod-users",
        "rtc-prod-users"
      ]
    },
    {
      userarn  = "arn:aws:iam::************:user/shoseia"
      username = "shoseia"
      groups = [
        "test-users"
      ]
    }
  ]

  aws_auth_accounts = []

  tags = local.tags
}

#resource "aws_iam_role" "system_master_role" {
#  name = "${local.name}-system-master-role"
#  assume_role_policy = jsonencode({
#    Version = "2012-10-17"
#    Statement = [
#      {
#        Sid    = "SystemMasterRole"
#        Action = "sts:AssumeRole"
#        Effect = "Allow"
#        Principal = { "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/seth-permission-test" }
#        Condition = { "Bool": { "aws:MultiFactorAuthPresent": "true" } }
#      }
#    ]
#  })
#  tags = local.tags
#}
#
#resource "aws_iam_policy" "system_master_policy" {
#  name = "${local.name}-system-master-policy"
#
#  policy = jsonencode({
#    Version = "2012-10-17"
#    Statement = [
#      {
#        Sid = "AssumeSystemMaster"
#        Action = "sts:AssumeRole"
#        Effect = "Allow"
#        Resource = [
#          aws_iam_role.system_master_role.arn
#        ]
#      }
#    ]
#  })
#
#  tags = local.tags
#}

resource "aws_security_group" "additional" {
  name_prefix = "${local.name}-additional"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port = 22
    to_port   = 22
    protocol  = "tcp"
    cidr_blocks = [
      "10.0.0.0/8",
      "**********/12",
      "***********/16",
    ]
  }

  tags = merge(local.tags, { Name = "${local.name}-additional" })
}

resource "aws_iam_policy" "additional" {
  name = "${local.name}-additional"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ec2:Describe*",
        ]
        Effect   = "Allow"
        Resource = "*"
      },
    ]
  })
}

module "kms" {
  source  = "terraform-aws-modules/kms/aws"
  version = "1.1.0"

  aliases               = ["eks/${local.name}"]
  description           = "${local.name} cluster encryption key"
  enable_default_policy = true
  key_owners            = local.key_owners

  tags = local.tags
}
