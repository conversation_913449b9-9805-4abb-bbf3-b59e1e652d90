locals {
  enable_kube_prometheus_stack     = local.enable_monitoring
  enable_loki                      = false
  enable_promtail                  = false
  enable_loki_stack                = local.enable_monitoring
  enable_actions_runner_controller = true
  enable_pyroscope                 = false
  enabled_sealed_secrets           = true

}

/**** Beware https://github.com/hashicorp/terraform-provider-helm/issues/735 ***
      Ensure the chart name does not have a commonly named directory.         */

module "kube_prometheus_stack" {
  source = "./kube_prometheus_stack"
  count  = local.enable_kube_prometheus_stack ? 1 : 0

  helm_config = {
    repository = "https://prometheus-community.github.io/helm-charts"
    version    = "53.0.0"
    namespace  = local.monitoring_namespace
    values = [
      templatefile("${path.module}/helm-values/kube_prometheus_stack.yaml", {
        grafana_host                      = "grafana.${var.eks_cluster_domain}"
        cloud_pd_service_key              = "cd5866d2128b4009c02fd737e5ac2708"
        sw_cloud_apps_service_key         = "5fb85824e9da440ec0ff3f8051f9f1c9"
        sw_mechanical_service_key         = "11e18fe285b84e09d0c4d3ed74a3ca83"
        sw_robot_service_key              = "bc41acead98e4c0cd0180ec8d7296a0c"
        manufacturing_service_key         = "f1d789bec07e4902c0080c8a205d54a0"
        enable_google_oauth               = true
        create_prometheus_service_account = false
        thanos_s3_bucket                  = local.thanos_bucket
        region                            = local.region
        enable_smtp                       = true
        smtp_host                         = "smtp.gmail.com:587"
        smtp_user                         = "<EMAIL>"
      })
    ]
  }
  addon_context = local.addon_context
  irsa_policies = [aws_iam_policy.prometheus_iam_policy.arn]
}

module "loki_stack" {
  source = "./loki_stack"
  count  = local.enable_loki_stack ? 1 : 0

  helm_config = {
    timeout          = 300
    version          = "2.10.2"
    namespace        = local.monitoring_namespace
    create_namespace = false
    values = [
      templatefile("${path.module}/helm-values/loki-stack.yaml", {
        s3-bucket = local.loki_bucket
        region    = local.region
      })
    ]
  }

  addon_context = local.addon_context
  irsa_policies = [aws_iam_policy.loki_stack.arn]
}

data "aws_iam_policy" "loki" {
  name = "LokiPolicy"
}
module "loki" {
  source = "./loki_helm"
  count  = local.enable_loki ? 1 : 0

  helm_config = {
    timeout          = 300
    namespace        = local.monitoring_namespace
    create_namespace = false
    values = [
      templatefile("${path.module}/helm-values/loki_values.yaml", {
        s3-bucket = local.loki_bucket
        region    = local.region
      })
    ]
  }

  addon_context = local.addon_context
  irsa_policies = [data.aws_iam_policy.loki.arn]
}

module "promtail" {
  source = "./promtail_helm"
  count  = local.enable_promtail ? 1 : 0

  helm_config = {
    namespace        = local.monitoring_namespace
    create_namespace = false
    values = [
      templatefile("${path.module}/helm-values/promtail_values.yaml", {
      })
    ]
  }

  addon_context = local.addon_context
}

module "actions-runner-controller" {
  count  = local.enable_actions_runner_controller ? 1 : 0
  source = "./actions_runner_controller"

  helm_config = {
    values = [
      templatefile("${path.module}/helm-values/actions_runner_controller.yaml", {

      })
    ]
  }

  addon_context = local.addon_context
}

module "pyroscope" {
  count  = local.enable_pyroscope ? 1 : 0
  source = "./pyroscope_io"

  helm_config = {
    values = [
      templatefile("${path.module}/helm-values/pyroscope.yaml", {

      })
    ]
  }

  addon_context = local.addon_context
}

module "sealed_secrets" {
  count  = local.enabled_sealed_secrets ? 1 : 0
  source = "./sealed_secrets"

  helm_config = {
    values = [
      templatefile("${path.module}/helm-values/sealed-secrets.yaml", {
        enable_metrics  = local.enable_monitoring
        ingress_enabled = true
        hostname        = "ss.${var.eks_cluster_domain}"
      })
    ]
  }

  addon_context = local.addon_context
}
